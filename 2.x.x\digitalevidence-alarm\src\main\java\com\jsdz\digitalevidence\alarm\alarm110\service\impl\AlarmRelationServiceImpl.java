package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

import java.sql.SQLException;
import java.util.Date;

/**
 * 
 * @类名: AlarmRelationServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-08-29 19:53:45
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.digitalevidence.activemq.model.DataExchange;
import com.jsdz.digitalevidence.activemq.model.UserApiMessage;
import com.jsdz.digitalevidence.activemq.service.DataExchangeService;
import com.jsdz.digitalevidence.activemq.utils.CustomJsonConfig;
import com.jsdz.digitalevidence.activemq.utils.MqTopic;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmInfoBean;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmRelationBean;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmInfoDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmRelationDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;
import com.jsdz.digitalevidence.alarm.alarm110.service.AlarmRelationService;
import com.jsdz.digitalevidence.document.dao.DocumentDao;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.digitalevidence.site.bean.CollectSiteBean;

import net.sf.json.JSONObject;

import com.jsdz.admin.org.model.Department;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("AlarmRelationServiceImpl")
public class AlarmRelationServiceImpl implements AlarmRelationService {

	@Autowired
	private AlarmRelationDao alarmRelationDao;
	@Autowired
	private AlarmInfoDao alarmInfoDao;
	@Autowired
	private DataExchangeService dataExchangeService;
	@Autowired
	private DocumentDao documentDao;
	
	//新增
	@Transactional
	public AjaxResult addAlarmRelation(AlarmRelation alarmRelation) {
		AjaxResult result = new AjaxResult(200,false,"",null);
		//检查为空
		if (alarmRelation.getAlarmInfo() == null){
			result.setMsg("警情必须提供。");
			return result;
		}
		
		if (alarmRelation.getMediaInfo() == null){
			result.setMsg("视频必须提供。");
			return result;
			
		}
		
		if (alarmRelation.getOperator() == null){
			result.setMsg("操作员必须提供。");
			return result;
		}
		
		//检查重复
		if (alarmRelationDao.findMutipleAlarmRelation(alarmRelation) != null){
			result.setMsg("此警情和视频已关联。");
			return result;
		}
		
		Long alarmId = alarmRelation.getAlarmInfo().getId();
		Long docId = alarmRelation.getMediaInfo().getId();
		
		alarmRelation.setRelationTime(new Date());
		alarmRelationDao.addAlarmRelation(alarmRelation);
		
		//更新警情为已关联状态
		AlarmInfo alarmInfo = alarmInfoDao.locateAlarmInfoById(alarmId);
		if (alarmInfo.getIsRelation() != 1){
			alarmInfo.setIsRelation(1);
			alarmInfoDao.update(alarmInfo);
		}
		
		Document doc = documentDao.get(docId);
		doc.setAlarmCode(alarmInfo.getAlarmCode());
		doc.setIsRelationType("1");
		documentDao.updateDocument(doc);
		
/*		String queryStr = "update AlarmInfo a set a.isRelation = 1 where a.id = :id";
		alarmRelationDao.exeCommHql(queryStr, new String[]{"id"}, 
				new Object[]{alarmId});
*/		
		//更新文档上的警情号
		//AlarmInfo alarmInfo = alarmInfoDao.locateAlarmInfoById(alarmRelation.getAlarmInfo().getId());
/*		String queryStr = "update com.jsdz.digitalevidence.document.model.Document d "
				+ " set d.alarmCode = :alarmCode where d.id = :id";
		alarmRelationDao.exeCommHql(queryStr, new String[]{"alarmCode","id"}, 
				new Object[]{alarmInfo.getAlarmCode(),docId});
*/
		
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateAlarmRelation(AlarmRelation alarmRelation) {
		AjaxResult result = new AjaxResult(200,false,"",null);
		alarmRelationDao.updateAlarmRelation(alarmRelation);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	@Transactional
	public AjaxResult deleteAlarmRelation(AlarmRelation alarmRelation) {
		AjaxResult result = new AjaxResult(200,false,"",null);
		if (null == alarmRelation){
			result.setMsg("没有数据可删除");
			return result;
		}
		
		Long alarmId =  alarmRelation.getAlarmInfo().getId();
		Long docId = alarmRelation.getMediaInfo().getId();

		alarmRelationDao.deleteAlarmRelation(alarmRelation);
		
		
		//更新警情为未关联
		String queryStr = "from AlarmRelation ar where ar.alarmInfo.id = :id ";
		String[] paramNames = {"id"};
		Object[] values = {alarmRelation.getAlarmInfo().getId()};
		List<AlarmRelation> list =  alarmRelationDao.findAlarmRelationsByCondition(queryStr,paramNames,values);
		if (list.size() == 0){
			//更新警情为未关联状态
			AlarmInfo alarmInfo = alarmInfoDao.locateAlarmInfoById(alarmId);
			if (alarmInfo.getIsRelation() == 1){
				alarmInfo.setIsRelation(0);
				alarmInfoDao.update(alarmInfo);
			}
			
			
/*			queryStr = "update AlarmInfo a set a.isRelation = 0 where a.id = :id";
			alarmRelationDao.exeCommHql(queryStr, new String[]{"id"}, 
					new Object[]{alarmId});
*/		}
		
		//更新文档未被关联
		queryStr = "from AlarmRelation ar where ar.mediaInfo.id = :id ";
		List<AlarmRelation> list1 =  alarmRelationDao.findAlarmRelationsByCondition(
				queryStr, new String[]{"id"},new Object[]{docId});
		if (list1.size() == 0){
			Document doc = documentDao.get(docId);
			doc.setAlarmCode(null);
			doc.setIsRelationType("0");
			documentDao.updateDocument(doc);

/*			queryStr = "update Document d set d.alarmCode = :alarmCode where d.id = :id";
			alarmRelationDao.exeCommHql(queryStr, 
					new String[]{"alarmCode","id"}, 
					new Object[]{null,docId});
*/		}
		
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public AlarmRelation findAlarmRelationById(Long id){

		return alarmRelationDao.findAlarmRelationById(id);
	}

	//按 id 查询
	public AlarmRelation locateAlarmRelationById(Long id) {
		return alarmRelationDao.locateAlarmRelationById(id);
	}

	//单个查询
	public AlarmRelation findAlarmRelationByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmRelationDao.findAlarmRelationByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<AlarmRelation> findAllAlarmRelations() {
		return alarmRelationDao.findAllAlarmRelations();
	}

	//列表查询
	public List<AlarmRelation> findAlarmRelationsByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmRelationDao.findAlarmRelationsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<AlarmRelation> findAlarmRelationsOnPage(Page<AlarmRelation> page, String queryStr, String[] paramNames, Object[] values) {
		Page<AlarmRelation> pos = alarmRelationDao.findAlarmRelationsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
