/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.model.push.strategy.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

import org.hibernate.transform.ResultTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.digitalevidence.assessment.AssessmentUtils;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocPoliceStrategy;
import com.jsdz.reportquery.ReportQueryDao;

/**
 * @类名: PushDocPolliceStrategyImpl
 * @说明: 视频推送警员查找策略
 *
 * <AUTHOR>
 * @Date	 2017年10月17日 下午6:59:43
 * 修改记录：
 *
 * @see 	 
 */
@Component
public class PushDocPolliceStrategyImpl implements PushDocPoliceStrategy {

	@Autowired
	private ReportQueryDao rqDao;
	
	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocPoliceStrategy#findPushingPolice()
	 */
	@Override
	@SuppressWarnings({ "unchecked" })
	public List<ShootingAssessmentSumBean> findPushingPolice(Date startDate, Date endDate, float assessRate, int topN) {
		List<ShootingAssessmentSumBean> emps = (List<ShootingAssessmentSumBean>) rqDao.queryNamedSQL(
				"findPushingPoliceTopN", 
				new String[] { "startDate", "endDate", "assessRate", "topN" },
				new Object[] { startDate, endDate, assessRate, topN }, 
				new ResultTransformer() {
					@Override
					public Object transformTuple(Object[] tuples, String[] aliases) {
						Long policeId = ((BigInteger) tuples[0]).longValue();
						Integer uploadDocs = AssessmentUtils.nullOrInteger((BigDecimal) tuples[1]);
						Integer assessDocs = AssessmentUtils.nullOrInteger((BigDecimal) tuples[2]);
						Float assessRate = AssessmentUtils.nullOrFloat((BigDecimal) tuples[3]);
						ShootingAssessmentSumBean bean = new ShootingAssessmentSumBean();
						bean.setPoliceId(policeId);
						bean.setAssessDocs(assessDocs);
						bean.setAssessRate(assessRate);
						bean.setUploadDocs(uploadDocs);
						return bean;
					}

					@Override
					public List transformList(List collection) {
						return collection;
					}
				});
		return emps;
	}

}
