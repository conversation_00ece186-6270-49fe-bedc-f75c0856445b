package com.jsdz.digitalevidence.alarm.alarm110.dao;

/**
 *
 * @类名: AlarmOrganizationDao
 * @说明:
 * @author: kenny
 * @Date 2018-08-29 21:08:19
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmOrganization;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface AlarmOrganizationDao extends GenericORMEntityDAO<AlarmOrganization,Long> {

	//新增
	public void addAlarmOrganization(AlarmOrganization alarmOrganization);

	//修改
	public void updateAlarmOrganization(AlarmOrganization alarmOrganization);

	//删除
	public void deleteAlarmOrganization(AlarmOrganization alarmOrganization);

	//按id查询,结果是游离状态的数据
	public AlarmOrganization findAlarmOrganizationById(Long id);

	//按id查询
	public AlarmOrganization locateAlarmOrganizationById(Long id);

	//单个查询
	public AlarmOrganization findAlarmOrganizationByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmOrganization> findAllAlarmOrganizations();

	//列表查询
	public List<AlarmOrganization> findAlarmOrganizationsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmOrganization> findAlarmOrganizationsOnPage(Page<AlarmOrganization> page,String queryStr,String[] paramNames,Object[] values);

	//执行指定的HQL文件
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);

}

