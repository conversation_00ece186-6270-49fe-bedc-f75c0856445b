package admin_security;

import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.dao.ActionDao;
import com.jsdz.admin.security.dao.OperateLogDao;
import com.jsdz.admin.security.dao.OperatorDao;
import com.jsdz.admin.security.dao.OperatorRoleDao;
import com.jsdz.admin.security.dao.ResourceDao;
import com.jsdz.admin.security.dao.RoleDao;
import com.jsdz.admin.security.dao.SystemMenuDao;
import com.jsdz.admin.security.model.Action;
import com.jsdz.admin.security.model.OperateLog;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.admin.security.model.Resource;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.SystemMenu;
import com.jsdz.admin.security.service.OperateLogService;

@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations={"classpath:/testApplicationContext-common.xml"})

public class JunitTest {
	@Autowired
	public SystemMenuDao systemMenuDao;
	@Autowired
	public ResourceDao resourceDao;

	@Autowired
	public OperatorDao operatorDao;
	@Autowired
	public RoleDao roleDao;
	@Autowired
	public OperatorRoleDao operatorRoleDao;
	@Autowired
	public ActionDao actionDao;
	@Autowired
	public OperateLogService operateLogService;
	
	SimpleDateFormat sdf =   new SimpleDateFormat( "yyyy-MM-dd" );
	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}
	
	
	@Test
	//@Transactional
    public void actionAdd(){
			Action a = new Action("系统参数设置","A",1);
			Action a1 = new Action("文件下载","B",2);
			Action a2 = new Action("系统维护","C",3);
			actionDao.addAction(a);
			actionDao.addAction(a1);
			actionDao.addAction(a2);
	}
	
	//@Test
	@Transactional
	public void testAddLog() {
		
	}	

	
	//@Test
	@Transactional
	public void testAddOperator() throws UnsupportedEncodingException {
		try {
			Operator op1 = new Operator("王洪军","hhj");
			Operator op2 = new Operator("赵宏","zh");
			
			op1.setLoginName("hhj");
			op1.setPassword("21218cca77804d2ba1922c33e0151105");
			op2.setLoginName("zh");
			op2.setPassword("21218cca77804d2ba1922c33e0151105");
			
			operatorDao.saveOrUpdate(op1);
			operatorDao.saveOrUpdate(op2);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
        
	}
	//@Test
	
	@Transactional
	public void testAddSystemMenu() {
		;
	}	

	
}
