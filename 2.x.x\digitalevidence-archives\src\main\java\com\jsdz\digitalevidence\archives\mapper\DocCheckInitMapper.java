package com.jsdz.digitalevidence.archives.mapper;

import com.jsdz.digitalevidence.archives.bean.DocCheckInit;
import com.jsdz.digitalevidence.archives.bean.param.SearchCheckDocInitParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * VIEW Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
public interface DocCheckInitMapper  {

    List<DocCheckInit> SearchCheckDocInit(@Param("param") SearchCheckDocInitParam param);

    DocCheckInit SearchCheckDocInitById(@Param("param") SearchCheckDocInitParam param);
}