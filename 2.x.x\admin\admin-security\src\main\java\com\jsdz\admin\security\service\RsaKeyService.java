package com.jsdz.admin.security.service;

/**
 * 
 * @类名: RsaKeyService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-06-02 11:38:34
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.RsaKey;
import com.jsdz.admin.security.token.Token;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface RsaKeyService {

	//新增
	public AjaxResult addRsaKey(RsaKey rsaKey);

	//修改
	public AjaxResult updateRsaKey(RsaKey rsaKey);

	//删除
	public AjaxResult deleteRsaKey(RsaKey rsaKey);

	//按id查询,结果是游离状态的数据
	public RsaKey findRsaKeyById(Long id);

	//按id查询
	public RsaKey locateRsaKeyById(Long id);

	//单个查询
	public RsaKey findRsaKeyByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<RsaKey> findAllRsaKeys();

	//列表查询
	public List<RsaKey> findRsaKeysByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RsaKey> findRsaKeysOnPage(Page<RsaKey> page, String queryStr,String[] paramNames,Object[] values);

	//查询是否已有Key
	public RsaKey findRsaKey();	
	
	//生成Token
	public String genToken();
	
	//检查Token
	public boolean checkToken(String token);
	
}

