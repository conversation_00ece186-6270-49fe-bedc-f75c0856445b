package com.jsdz.digitalevidence.archives.mapper;

import com.jsdz.digitalevidence.archives.model.ArchivesRelation;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-11
 */
public interface ArchivesRelationMapper{

    @Delete("delete from t_archives_relation where id = #{rid}")
    void removeByRid(Long rid);

    @Select("select * from t_archives_relation where archivesId = #{archivesId} and docId = #{docId}")
    List<ArchivesRelation> findArchivesDoc(@Param("archivesId") Long archivesId,@Param("docId")  String docId);

    @Insert("INSERT INTO t_archives_relation (archivesId,docId,relationUserId,relationUserName)VALUES ( #{archivesId}, #{docId}, #{relationUserId}, #{relationUserName})")
    void insertRelation(@Param("archivesId") Long archivesId,@Param("docId")  Long docId,@Param("relationUserId")  Long relationUserId,@Param("relationUserName")  String relationUserName);
}
