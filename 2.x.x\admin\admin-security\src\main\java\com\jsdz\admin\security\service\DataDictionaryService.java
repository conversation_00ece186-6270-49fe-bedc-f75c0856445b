package com.jsdz.admin.security.service;

/**
 * 
 * @类名: DataDictionaryService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-10-31 11:43:33
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.DataDictionary;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface DataDictionaryService {

	//新增
	public AjaxResult addDataDictionary(DataDictionary dataDictionary);

	//修改
	public AjaxResult updateDataDictionary(DataDictionary dataDictionary);

	//删除
	public AjaxResult deleteDataDictionary(DataDictionary dataDictionary);

	//按id查询,结果是游离状态的数据
	public DataDictionary findDataDictionaryById(Long id);

	//按id查询
	public DataDictionary locateDataDictionaryById(Long id);

	//单个查询
	public DataDictionary findDataDictionaryByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DataDictionary> findAllDataDictionarys();

	//列表查询
	public List<DataDictionary> findDataDictionarysByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DataDictionary> findDataDictionarysOnPage(Page<DataDictionary> page, String queryStr,String[] paramNames,Object[] values);

}

