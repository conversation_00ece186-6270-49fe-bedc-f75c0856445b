package com.jsdz.digitalevidence.alarm.common.service;

/**
 * 
 * @类名: DocumentInfoService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-11-06 16:23:56
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.common.bean.DocumentInfoParam;
import com.jsdz.digitalevidence.alarm.common.model.RecoderView;
import com.jsdz.digitalevidence.document.bean.DocumentInfoBean;
import com.jsdz.digitalevidence.document.model.DocumentInfo;

public interface DocumentInfoService {

	//新增
	public AjaxResult addDocumentInfo(DocumentInfo documentInfo);

	//修改
	public AjaxResult updateDocumentInfo(DocumentInfo documentInfo);

	//删除
	public AjaxResult deleteDocumentInfo(DocumentInfo documentInfo);

	//按id查询,结果是游离状态的数据
	public DocumentInfo findDocumentInfoById(Long id);

	//按id查询
	public DocumentInfo locateDocumentInfoById(Long id);

	//单个查询
	public DocumentInfo findDocumentInfoByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DocumentInfo> findAllDocumentInfos();

	//列表查询
	public List<DocumentInfo> findDocumentInfosByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DocumentInfo> findDocumentInfosOnPage(Page<DocumentInfo> page, String queryStr,String[] paramNames,Object[] values);

	//按条件查询所有的文档
	public Page<DocumentInfoBean> findDocumentInfosBeanFromSql(Page<DocumentInfoBean> page,String[] paramNames, Object[] values);
	
	//按查询视频文件不分页
	public List<Object> findDocumentInfosBeanFromSql(String[] paramNames, Object[] values);
	
	//编辑
	public AjaxResult editDocumentInfo(DocumentInfoBean docinfo,Operator operator);
	
	//审核
	public AjaxResult approveDocumentInfo(DocumentInfoParam docInfoParam,Operator operator);
	
	//行政复议
	public AjaxResult reeditDocumentInfo(DocumentInfoBean docinfo,Operator operator);
	
	public boolean existsRecoderView(RecoderView rv);
	
}

