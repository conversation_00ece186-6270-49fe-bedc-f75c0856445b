/**
 * %项目描述%
 * %版本信息%
 */
package com.jsdz.digitalevidence.assessment.test;

import static com.jsdz.digitalevidence.assessment.AssessmentUtils.nullOrInteger;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.hibernate.transform.ResultTransformer;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.AssessmentConsts;
import com.jsdz.digitalevidence.assessment.AssessmentUtils;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentReportBean;
import com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReport;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.ShootingItem;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.reportquery.ReportQueryDao;
import com.jsdz.ruleengine.dao.NodeDao;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: AssessmentReportQueryTest
 * @说明: 考核报表查询测试
 * 
 * <AUTHOR>
 * @Date 2012-6-27 下午5:21:18
 * @修改记录：
 * 
 * @see
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(
		locations={
				"/testApplicationContext-common.xml",
				"/testApplicationContext-cycle.xml",
				"/testApplicationContext-dynsql.xml",
				"/testApplicationContext-scheduler.xml",
				"/testApplicationContext-reportengine.xml",
				"/testApplicationContext-documenttype.xml"
				})
public class AssessmentReportQueryTest {
	
	@Resource(name="rqDao")
	private ReportQueryDao rqDao;
	
	@Autowired
    private NodeDao nodeDao;
	
	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
	}
	
	
/*	*//** 文档分类*//*
	private DocumentCate cate;
	*//** 拍摄数量*//*
	private Integer count;
	*//** 拍摄文件大小*//*
	private Long size;
	*//** 拍摄时长*//*
	private Long duration;*/
		
	@SuppressWarnings({ "unchecked", "serial" })
	@Test
	@Transactional(readOnly=true)
	public void testGetShootingItem() throws Exception {
		// 考核开始时间
		Date start = DateTimeUtils.StringToDate("2017-08-05 00:00:00");
		// 考核结束时间
		Date end = DateTimeUtils.StringToDate("2017-08-16 00:00:00");
		//
		List<ShootingItem> items  = (List<ShootingItem>)rqDao.queryNamedHQL("sumOfShootingItem", 
					new String[]{"policeId", "startTime", "endTime"}, 
					new Object[]{9L, start, end},
					new ResultTransformer() {
				        @Override
				        public Object transformTuple(
				            Object[] tuples, 
				            String[] aliases) {
				        	DocumentCate cate = (DocumentCate)tuples[0];
				        	Long count = (Long)tuples[1];
				        	Long size = (Long)tuples[2];
				        	Long duration = (Long)tuples[3];
				        	//
				        	ShootingItem bean =  new ShootingItem();
				        	bean.setCate(cate);
				        	bean.setCount(count.intValue());
				        	bean.setDuration(duration);
				        	bean.setSize(size);
				        	return bean;
				        }
	 
	        @SuppressWarnings("rawtypes")
			@Override
	        public List transformList(List collection) {
	            return collection;
	        }
    	} 
		);
		System.out.println(items);

	}
	
	// 获取拍摄考核统计
	@SuppressWarnings("unchecked")
	@Test
	@Transactional(readOnly=true)
	public void testGetShootingAssessmentSum() throws	 Exception {
		// 考核开始时间
		Date start = DateTimeUtils.StringToDate("2017-08-05 00:00:00");
		// 考核结束时间
		Date end = DateTimeUtils.StringToDate("2017-08-16 00:00:00");
		//
		List<ShootingAssessmentSummary> items  = 
				(List<ShootingAssessmentSummary>)rqDao.queryNamedSQL("sumOfShootingAssessment", 
					new String[]{"policeId", "startTime", "endTime"}, 
					new Object[]{10L, start, end},
					new ResultTransformer() {
			        @Override
			        public Object transformTuple(
			            Object[] tuples, 
			            String[] aliases) {
			        	Integer countOfWMGF = nullOrInteger((BigDecimal)tuples[0]);
			        	Integer countOfNoWFWJ = nullOrInteger((BigDecimal)tuples[1]);
			        	Integer countOfAYQPS = nullOrInteger((BigDecimal)tuples[2]);
			        	Integer countOfYYYY = nullOrInteger((BigDecimal)tuples[3]);
			        	Integer countOfShootingAngleA = nullOrInteger((BigDecimal)tuples[4]);
			        	Integer countOfShootingAngleG = nullOrInteger((BigDecimal)tuples[5]);
			        	Integer countOfShootingAngleE = nullOrInteger((BigDecimal)tuples[6]);
			        	Integer countOfShootingContentA = nullOrInteger((BigDecimal)tuples[7]);
			        	Integer countOfShootingContentG = nullOrInteger((BigDecimal)tuples[8]);
			        	Integer countOfShootingContentE = nullOrInteger((BigDecimal)tuples[9]);			        	
			        	Integer countOfShootingResolutionH = nullOrInteger((BigDecimal)tuples[10]);
			        	Integer countOfShootingResolutionG = nullOrInteger((BigDecimal)tuples[11]);
			        	Integer countOfShootingResolutionL = nullOrInteger((BigDecimal)tuples[12]);
			        	Integer totalAssessingDoc = nullOrInteger(((BigInteger)tuples[13]));
			        	Integer totalDoc = nullOrInteger(((BigInteger)tuples[14]));
			        	// 
			        	ShootingAssessmentSummary bean =  new ShootingAssessmentSummary();
			        	bean.setCountOfAYQPS(countOfAYQPS);
			        	bean.setCountOfNoWFWJ(countOfNoWFWJ);
			        	bean.setCountOfWMGF(countOfWMGF);
			        	bean.setCountOfYYYY(countOfYYYY);
			        	bean.setCountOfShootingAngleA(countOfShootingAngleA);
			        	bean.setCountOfShootingAngleE(countOfShootingAngleE);
			        	bean.setCountOfShootingAngleG(countOfShootingAngleG);
			        	
			        	bean.setCountOfShootingContentA(countOfShootingContentA);
			        	bean.setCountOfShootingContentE(countOfShootingContentE);
			        	bean.setCountOfShootingContentG(countOfShootingContentG);
			        	
			        	bean.setCountOfShootingResolutionG(countOfShootingResolutionG);
			        	bean.setCountOfShootingResolutionH(countOfShootingResolutionH);
			        	bean.setCountOfShootingResolutionL(countOfShootingResolutionL);
			        	
			        	bean.setTotalAssessingDoc(totalAssessingDoc);
			        	bean.setTotalDoc(totalDoc);
			        	
			        	return bean;
			        }
			 
			        @Override
			        public List transformList(List collection) {
			            return collection;
			        }
		    	});
		System.out.println(items);

	}
	
	// 获取考核概况Bean
	@SuppressWarnings({ "unchecked", "serial" })
	@Test
	@Transactional(readOnly=true)
	public void testGeShootingAssessmentSumtAll() throws	 Exception {
		// 考核开始时间
		Date start = DateTimeUtils.StringToDate("2017-08-05 00:00:00");
		// 考核结束时间
		Date end = DateTimeUtils.StringToDate("2017-09-6 00:00:00");		
		List<ShootingAssessmentSumBean> items  = 
				(List<ShootingAssessmentSumBean>)rqDao.queryNamedSQL(
						"sumOfShootingAssessmentAll", 
					//new String[]{"startDate", "endDate"}, 
					//new Object[]{start, end},
						new String[]{}, 
						new Object[]{},
					new ResultTransformer() {
				        @Override
				        public Object transformTuple(
				            Object[] tuples, 
				            String[] aliases) {
				        	String workNumber = (String)tuples[0];
				        	String policeName = (String)tuples[1];
				        	String position = (String)tuples[2];
				        	String sex = (String)tuples[3];
				        	Integer age = (Integer)tuples[4];
				        	Integer uploadDocs = ((BigInteger)tuples[5]).intValue();
				        	Integer assessDocs = ((BigInteger)tuples[6]).intValue();
				        	Float assessRate = AssessmentUtils.nullOrFloat((BigDecimal)tuples[7]);
				        	ShootingAssessmentSumBean bean =  new ShootingAssessmentSumBean();
				        	bean.setAge(age);
				        	bean.setPosition(position);
				        	bean.setAssessDocs(assessDocs);
				        	bean.setAssessRate(assessRate);
				        	bean.setName(policeName);
				        	bean.setSex(sex);
				        	bean.setUploadDocs(uploadDocs);
				        	bean.setPoliceCode(workNumber);
				        	return bean;
				        }
				 
				        @Override
				        public List transformList(List collection) {
				            return collection;
				        }
			    	} 
				);
		System.out.println(items);

	}
	
	// 打开考核
		@SuppressWarnings({ "unchecked", "serial" })
		@Test
		@Transactional(readOnly=true)
		public void testOpenAssessment() throws	 Exception {
			List<ShootingAssessmentBean> items  = 
					(List<ShootingAssessmentBean>)rqDao.queryNamedSQL(
						AssessmentConsts.SQL_KEY_Open_Assessment, 
						new String[]{"docId"}, 
						new Object[]{152L},
						new ResultTransformer() {
					        @Override
					        public Object transformTuple(
					            Object[] tuples, 
					            String[] aliases) {
					        	String workNumber = (String)tuples[0];
					        	String policeName = (String)tuples[1];
					        	String position = (String)tuples[2];
					        	String sex = (String)tuples[3];
					        	Integer age = (Integer)tuples[4];
					        	Long docId = (Long)tuples[5];
					        	ShootingAssessmentBean bean =  new ShootingAssessmentBean();
					        	bean.setPoliceCode(workNumber);
					        	bean.setAge(age);
					        	bean.setPosition(position);
					        	bean.setName(policeName);
					        	bean.setSex(sex);
							    bean.setDocId(docId);
					        	return bean;
					        }
					 
					        @Override
					        public List transformList(List collection) {
					            return collection;
					        }
				    	} 
					);
			System.out.println(items);

		}
    
	// 按考核周期，获取警员上传文档，带考核信息
	@SuppressWarnings("unchecked")
	@Test
	@Transactional(readOnly = true)
	public void testGetAssessmentDocumentBean() throws Exception {
		Page<AssessmentDocumentBean> page = new Page<AssessmentDocumentBean>();
		page.setPage(1);
		page.setPageSize(10);
		// 考核开始时间
		Date start = DateTimeUtils.StringToDate("2017-07-05 00:00:00");
		// 考核结束时间
		Date end = DateTimeUtils.StringToDate("2017-08-16 00:00:00");
		//
		page = rqDao.pageQueryNamedSQL(
				page,
				"queryAssessmentDocs", 
				new String[] { "policeId", "startTime", "endTime" }, 
				new Object[] { 9, start, end });
		System.out.println(page.getRows());
	}
	
	// 查找考核简要报告
	@SuppressWarnings("unchecked")
	@Test
	@Transactional(readOnly = true)
	public void testQueryAssessmentReportBean() throws Exception {
		Page<AssessmentReportBean> page = new Page<AssessmentReportBean>();
		page.setPage(1);
		page.setPageSize(10);
		// 考核开始时间
		Date start = DateTimeUtils.StringToDate("2016", "yyyy");
		// 考核结束时间
		Date end = DateTimeUtils.StringToDate("2018", "yyyy");
		//
		page = rqDao.pageQueryNamedHQLWithNewBean(
				page,
				AssessmentConsts.SQL_KEY_Query_Assessment_Report_Brief,
				new String[] { }, 
				new Object[] { });
		System.out.println(page.getRows());
	}
	
	// 查找考核完整报告Bean--报表
	@SuppressWarnings("unchecked")
	@Test
	public void testGetAssessmentReportBean() throws Exception {
		Long reportId = 6L;
		//
		List<CompleteAssessmentReport> reports = (List<CompleteAssessmentReport>)rqDao.queryNamedSQL(
				"getCompleteShootingAccessmentReport",
				new String[] { "reportId" }, 
				new Object[] { reportId });
		System.out.println(reports);
	}
	
	// 查找考核预警报告Bean
	@SuppressWarnings("unchecked")
	@Test
	public void testGetAssessmentReportAlertBean() throws Exception {
		Long alertId = 9L;
		//
		List<AssessmentAlertBean> reports = (List<AssessmentAlertBean>)rqDao.queryNamedSQL(
				"getShootingAccessmentAlertBean",
				new String[] { "alertId" }, 
				new Object[] { alertId });
		System.out.println(reports);
	}
	
	@SuppressWarnings("unchecked")
	@Test
	public void testGetAssessmentReportAlertItem() throws Exception {
		Long alertId = 9L;
		//
		List<AssessmentAlertItem> reports = (List<AssessmentAlertItem>)rqDao.queryNamedSQL(
				"getShootingAccessmentAlertItem",
				new String[] { "alertId" }, 
				new Object[] { alertId });
		System.out.println(reports);
	}
	
}
