package com.jsdz.digitalevidence.assessment.dao.impl;

/**
 *
 * @类名: AssessmentPlanPoliceDocumentDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2019-06-21 16:30:04
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanPoliceDocumentDao;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPoliceDocument;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class AssessmentPlanPoliceDocumentDaoImpl extends GenericEntityDaoHibernateImpl<AssessmentPlanPoliceDocument,Long> implements AssessmentPlanPoliceDocumentDao{

	/** 
 	 * 新增
	 */ 
 	public void addAssessmentPlanPoliceDocument(AssessmentPlanPoliceDocument assessmentPlanPoliceDocument) {
		this.saveOrUpdate(assessmentPlanPoliceDocument);
	}

	/** 
 	 * 删除
	 */ 
 	public void deleteAssessmentPlanPoliceDocument(AssessmentPlanPoliceDocument assessmentPlanPoliceDocument) {
		this.delete(assessmentPlanPoliceDocument);
	}

	/** 
 	 * 修改
	 */ 
 	public void updateAssessmentPlanPoliceDocument(AssessmentPlanPoliceDocument assessmentPlanPoliceDocument) {
		this.merge(assessmentPlanPoliceDocument);
	}

	/** 
 	 * 按id查询(游离状态)
	 */ 
 	public AssessmentPlanPoliceDocument findAssessmentPlanPoliceDocumentById(Long id){

		final String  hql = "from AssessmentPlanPoliceDocument d where d.id = :id";
		final Long oid = id;
		AssessmentPlanPoliceDocument data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlanPoliceDocument>() {
			public AssessmentPlanPoliceDocument doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AssessmentPlanPoliceDocument> list = query.list();
				AssessmentPlanPoliceDocument rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlanPoliceDocument locateAssessmentPlanPoliceDocumentById(Long id){

		final String  hql = "from AssessmentPlanPoliceDocument d where d.id = :id";
		final Long oid = id;
		AssessmentPlanPoliceDocument data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlanPoliceDocument>() {
			public AssessmentPlanPoliceDocument doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AssessmentPlanPoliceDocument> list = query.list();
				AssessmentPlanPoliceDocument rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 单个查询(根据其它字段查询)
	 */ 
 	public AssessmentPlanPoliceDocument findAssessmentPlanPoliceDocumentByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		AssessmentPlanPoliceDocument data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlanPoliceDocument>() {
		public AssessmentPlanPoliceDocument doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<AssessmentPlanPoliceDocument> list = query.list();
			AssessmentPlanPoliceDocument rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanPoliceDocument> findAllAssessmentPlanPoliceDocuments(){
		return this.find("from AssessmentPlanPoliceDocument assessmentPlanPoliceDocument ");
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanPoliceDocument> findAssessmentPlanPoliceDocumentsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<AssessmentPlanPoliceDocument> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanPoliceDocument> findAssessmentPlanPoliceDocumentsOnPage(Page<AssessmentPlanPoliceDocument> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<AssessmentPlanPoliceDocument>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	/** 
 	 * 执行自定义的hql
	 */ 
 	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
}
