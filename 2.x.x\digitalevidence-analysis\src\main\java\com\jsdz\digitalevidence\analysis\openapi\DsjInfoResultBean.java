package com.jsdz.digitalevidence.analysis.openapi;
/**
 * 
 * @类名: DsjInfoResultBean
 * @说明: 执法记录仪基本信息查询结果参数表
 *
 * @author: kenny
 * @Date	2017年6月7日下午3:46:01
 * 修改记录：
 *
 * @see
 */

import com.jsdz.admin.security.utils.JSDateFormatUtils;
import com.jsdz.core.Env;
import com.jsdz.digitalevidence.analysis.openapi.model.DsjInfo;
import com.jsdz.digitalevidence.document.model.Document;


public class DsjInfoResultBean {

	private String wjbh;//文件编号
	private String wjbm;//文件别名
	private String pssj;//拍摄时间(YYYY-MM-DD HH:mm:ss)
	private String wjdx;//文件大小
	private String wjlx;//文件类型
	private String jy_xm;//使用者姓名
	private String jybh;//警号
	private String jgdm;//单位编号
	private String dwmc;//单位名称
	private String cpxh;//产品序号
	private String ccwz;//存储位置
	private String bfwz;//播放位置
	private String wlwz;//物理位置
	private String scsj;//上传时间(YYYY-MM-DD HH:mm:ss)	
	private String bzlx;//标注类型	
	private String gzz_xh;//执法数据采集设备产品编码	
	private String gzz_ipdz;//执法数据采集设备IP
	
	
	public void assgin(Document src){
		try{
			this.setWjbh(src.getName());//文件编号
			this.setWjbm(src.getName());//文件别名
			this.setPssj(JSDateFormatUtils.formatDateTime(src.getCreateTime()));
			this.setWjdx(src.getSizeStr());
			
			this.setWjlx(src.getCate().name);
			if (src.getPolice() != null){
				this.setJy_xm(src.getPolice().getName());//使用者姓名
				this.setJybh(src.getPolice().getWorkNumber());//警号
			}
			if (src.getSite() != null){
				this.setGzz_xh(src.getSite().getSiteNo());//执法数据采集设备产品编码
				this.setGzz_ipdz(src.getSite().getSiteIP());//执法数据采集设备IP
				if (null != src.getSite().getOrganization()){
					this.setJgdm(src.getSite().getOrganization().getOrgCode());//单位编号
					this.setDwmc(src.getSite().getOrganization().getOrgName());//单位名称
				}
			}
			if (src.getRec() != null)
				this.setCpxh(src.getRec().getCode()); //产品序号
			
			
			this.setCcwz(src.getUri());//存储位置
			//物理位置
			this.setWlwz(Env.getProperty("document.storage.rootpath").toString() + src.getUri().toString());

			this.setScsj(JSDateFormatUtils.formatDateTime(src.getUploadTime()));
		}catch(Exception e){
			System.out.println("*** " + e);
		}
	}
	
	public void assgin(DsjInfo src){
		this.setWjbh(src.getWjbh());//文件编号
		this.setWjbm(src.getWjbm());//文件别名
		this.setPssj(JSDateFormatUtils.formatDateTime(src.getPssj()));//拍摄时间
		this.setWjdx(src.getWjdx());//文件大小
		this.setWjlx(src.getWjlx());//文件类型
		this.setJy_xm(src.getJy_xm());//使用者姓名
		this.setJybh(src.getJybh());//警号
		this.setJgdm(src.getJgdm());//单位编号
		this.setDwmc(src.getDwmc());//单位名称
		this.setCpxh(src.getCpxh());//产品序号
		this.setCcwz(src.getCcwz());//存储位置
		this.setBfwz(src.getBfwz());//播放位置
		this.setWlwz(src.getWlwz());//物理位置
		this.setScsj(JSDateFormatUtils.formatDateTime(src.getScsj()));//上传时间(YYYY-MM-DD HH:mm:ss)
		this.setBzlx(src.getBzlx());//标注类型
		this.setGzz_xh(src.getGzz_xh());//执法数据采集设备产品编码	
	}
	
	public String getWjbh() {
		return wjbh;
	}
	public void setWjbh(String wjbh) {
		this.wjbh = wjbh;
	}
	public String getWjbm() {
		return wjbm;
	}
	public void setWjbm(String wjbm) {
		this.wjbm = wjbm;
	}
	public String getPssj() {
		return pssj;
	}
	public void setPssj(String pssj) {
		this.pssj = pssj;
	}
	public String getWjdx() {
		return wjdx;
	}
	public void setWjdx(String wjdx) {
		this.wjdx = wjdx;
	}
	public String getWjlx() {
		return wjlx;
	}
	public void setWjlx(String wjlx) {
		this.wjlx = wjlx;
	}
	public String getJy_xm() {
		return jy_xm;
	}
	public void setJy_xm(String jy_xm) {
		this.jy_xm = jy_xm;
	}
	public String getJybh() {
		return jybh;
	}
	public void setJybh(String jybh) {
		this.jybh = jybh;
	}
	public String getJgdm() {
		return jgdm;
	}
	public void setJgdm(String jgdm) {
		this.jgdm = jgdm;
	}
	public String getDwmc() {
		return dwmc;
	}
	public void setDwmc(String dwmc) {
		this.dwmc = dwmc;
	}
	public String getCpxh() {
		return cpxh;
	}
	public void setCpxh(String cpxh) {
		this.cpxh = cpxh;
	}
	public String getCcwz() {
		return ccwz;
	}
	public void setCcwz(String ccwz) {
		this.ccwz = ccwz;
	}
	public String getBfwz() {
		return bfwz;
	}
	public void setBfwz(String bfwz) {
		this.bfwz = bfwz;
	}
	public String getWlwz() {
		return wlwz;
	}
	public void setWlwz(String wlwz) {
		this.wlwz = wlwz;
	}
	public String getScsj() {
		return scsj;
	}
	public void setScsj(String scsj) {
		this.scsj = scsj;
	}
	public String getBzlx() {
		return bzlx;
	}
	public void setBzlx(String bzlx) {
		this.bzlx = bzlx;
	}
	public String getGzz_xh() {
		return gzz_xh;
	}
	public void setGzz_xh(String gzz_xh) {
		this.gzz_xh = gzz_xh;
	}
	public String getGzz_ipdz() {
		return gzz_ipdz;
	}
	public void setGzz_ipdz(String gzz_ipdz) {
		this.gzz_ipdz = gzz_ipdz;
	}
		

	
}
