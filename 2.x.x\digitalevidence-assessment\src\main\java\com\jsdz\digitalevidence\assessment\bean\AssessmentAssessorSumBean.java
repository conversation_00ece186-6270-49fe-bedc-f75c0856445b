/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: AssessmentAssessorSumBean
 * @说明: 拍摄考核员考核概况Bean
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentAssessorSumBean extends AbstractDTO {

	private Long policeId;
	/** 考核量，包括推送未考核文档*/
	private Integer assessDocs;
	/** 视频推送数*/
	private Integer totalPushDocs;
	/** 未考核的推送视频数*/
	private Integer notAssessPushDocs;
	
	public Integer addPushDoc(int c) {
		totalPushDocs = totalPushDocs + c;
		notAssessPushDocs = notAssessPushDocs + c;
		assessDocs = assessDocs + c;
		return totalPushDocs;
	}
	
	public Long getPoliceId() {
		return policeId;
	}
	public void setPoliceId(Long policeId) {
		this.policeId = policeId;
	}
	public Integer getAssessDocs() {
		return assessDocs;
	}
	public void setAssessDocs(Integer assessDocs) {
		this.assessDocs = assessDocs;
	}
	public Integer getNotAssessPushDocs() {
		return notAssessPushDocs;
	}
	public void setNotAssessPushDocs(Integer notAssessPushDocs) {
		this.notAssessPushDocs = notAssessPushDocs;
	}
	public Integer getTotalPushDocs() {
		return totalPushDocs;
	}
	public void setTotalPushDocs(Integer totalPushDocs) {
		this.totalPushDocs = totalPushDocs;
	}	
	
}
