package com.jsdz.digitalevidence.analysis.openapi.service;

/**
 * 
 * @类名: CxDsjLogService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-06-09 16:03:20
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.analysis.openapi.model.CxDsjLog;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface CxDsjLogService {

	//新增
	public AjaxResult addCxDsjLog(CxDsjLog cxDsjLog);

	//修改
	public AjaxResult updateCxDsjLog(CxDsjLog cxDsjLog);

	//删除
	public AjaxResult deleteCxDsjLog(CxDsjLog cxDsjLog);

	//按id查询,结果是游离状态的数据
	public CxDsjLog findCxDsjLogById(Long id);

	//按id查询
	public CxDsjLog locateCxDsjLogById(Long id);

	//单个查询
	public CxDsjLog findCxDsjLogByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<CxDsjLog> findAllCxDsjLogs();

	//列表查询
	public List<CxDsjLog> findCxDsjLogsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<CxDsjLog> findCxDsjLogsOnPage(Page<CxDsjLog> page, String queryStr,String[] paramNames,Object[] values);

}

