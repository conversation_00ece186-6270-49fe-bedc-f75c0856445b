package TestService;
import java.util.Date;
/**
 * 
 * @类名: TestOrganization
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月10日上午10:51:21
 * 修改记录：
 *
 * @see
 */
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.dao.OrganizationDao;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.model.Region;
import com.jsdz.admin.org.service.OrganizationService;
import net.sf.json.JSONArray;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.TreeBean;

import net.sf.json.JSONArray;


@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { "classpath:/testApplicationContext-common.xml" })
public class TestOrganization {
	@Autowired
	private OrganizationService organizationService;
	@Autowired
	private OrganizationDao organizationDao;
	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}
	
	//@Test
	@Transactional
	public void testFindOrganizationOnTree()
	{
		List<TreeBean> list = organizationService.findOrganizationOnTree();

		JSONArray json = JSONArray.fromObject(list);//将java对象转换为json对象
		System.out.println("【结果】:\r\n" +  json.toString());//将json对象转换为字符串
	}
	
	@Test
	@Transactional
	public void testupdateOrganization()
	{
		Organization o1 = organizationDao.findOrganizationById(7L);
		Organization o2 = organizationDao.findOrganizationById(8L);
		o1.setParentOrg(o2);

		AjaxResult result = organizationService.updateOrganization(o1);

		JSONArray json = JSONArray.fromObject(result);//将java对象转换为json对象
		System.out.println("【结果】:\r\n" +  json.toString());//将json对象转换为字符串
		
		
	}
	
	//@Test
	@Transactional
	public void testaddOrganization()
	{
		Organization organization = new Organization();
		organization.setOrgName("龙华派出所");
		organization.setOrgType("派出所");
		organization.setCreateTime(new Date());
		//Region region = new Region();
		//region.setId(3L);
		//organization.setRegion(region);
		
		Organization parOrg = new Organization();
		parOrg.setId(11L);
		organization.setParentOrg(parOrg);
		
		JSONArray  S = JSONArray.fromObject(organization);//将java对象转换为json对象
		System.out.println("【Organization】:\r\n" +  S.toString());//将json对象转换为字符串

		
		AjaxResult result = organizationService.addOrganization(organization);

		JSONArray json = JSONArray.fromObject(result);//将java对象转换为json对象
		System.out.println("【结果】:\r\n" +  json.toString());//将json对象转换为字符串
	}	
}
