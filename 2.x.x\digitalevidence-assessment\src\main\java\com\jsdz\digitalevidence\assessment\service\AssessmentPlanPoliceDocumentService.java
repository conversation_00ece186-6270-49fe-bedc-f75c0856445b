package com.jsdz.digitalevidence.assessment.service;

/**
 * 
 * @类名: AssessmentPlanPoliceDocumentService
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-06-21 16:30:04
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPoliceDocument;

public interface AssessmentPlanPoliceDocumentService {

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addAssessmentPlanPoliceDocument(AssessmentPlanPoliceDocument assessmentPlanPoliceDocument);

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updateAssessmentPlanPoliceDocument(AssessmentPlanPoliceDocument assessmentPlanPoliceDocument);

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deleteAssessmentPlanPoliceDocument(AssessmentPlanPoliceDocument assessmentPlanPoliceDocument);

	/** 
 	 * 按id查询,结果是游离状态的数据
	 */ 
 	public AssessmentPlanPoliceDocument findAssessmentPlanPoliceDocumentById(Long id);

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlanPoliceDocument locateAssessmentPlanPoliceDocumentById(Long id);

	/** 
 	 * 单个查询
	 */ 
 	public AssessmentPlanPoliceDocument findAssessmentPlanPoliceDocumentByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanPoliceDocument> findAllAssessmentPlanPoliceDocuments();

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanPoliceDocument> findAssessmentPlanPoliceDocumentsByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanPoliceDocument> findAssessmentPlanPoliceDocumentsOnPage(Page<AssessmentPlanPoliceDocument> page, String queryStr,String[] paramNames,Object[] values);

}

