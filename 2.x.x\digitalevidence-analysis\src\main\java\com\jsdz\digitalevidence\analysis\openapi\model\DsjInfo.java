package com.jsdz.digitalevidence.analysis.openapi.model;
/**
 * 
 * @类名: DsjInfo
 * @说明: 执法记录仪基本信息上传实体类
 *
 * @author: kenny
 * @Date	2017年6月9日下午2:52:54
 */

import java.util.Date;

public class DsjInfo {
	
	private Long id; //
	private String wjbh;//文件编号
	private String wjbm;//文件别名
	private Date pssj;//拍摄时间(YYYY-MM-DD HH:mm:ss)
	private String wjdx;//文件大小
	private String wjlx;//文件类型
	private String jy_xm;//使用者姓名
	private String jybh;//警号
	private String cpxh;//产品序号
	private String jgdm;//单位编号
	private String dwmc;//单位名称
	private String ccfwq;//存储服务器
	private String ccwz;//存储位置
	private String bfwz;//播放位置
	private String wlwz;//物理位置
	private String gzz_xh;//执法数据采集设备产品编码
	private Date scsj;//上传时间	
	private String bzlx;//标注类型
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getWjbh() {
		return wjbh;
	}
	public void setWjbh(String wjbh) {
		this.wjbh = wjbh;
	}
	public String getWjbm() {
		return wjbm;
	}
	public void setWjbm(String wjbm) {
		this.wjbm = wjbm;
	}
	public Date getPssj() {
		return pssj;
	}
	public void setPssj(Date pssj) {
		this.pssj = pssj;
	}
	public String getWjdx() {
		return wjdx;
	}
	public void setWjdx(String wjdx) {
		this.wjdx = wjdx;
	}
	public String getWjlx() {
		return wjlx;
	}
	public void setWjlx(String wjlx) {
		this.wjlx = wjlx;
	}
	public String getJy_xm() {
		return jy_xm;
	}
	public void setJy_xm(String jy_xm) {
		this.jy_xm = jy_xm;
	}
	public String getJybh() {
		return jybh;
	}
	public void setJybh(String jybh) {
		this.jybh = jybh;
	}
	public String getCpxh() {
		return cpxh;
	}
	public void setCpxh(String cpxh) {
		this.cpxh = cpxh;
	}
	public String getJgdm() {
		return jgdm;
	}
	public void setJgdm(String jgdm) {
		this.jgdm = jgdm;
	}
	public String getDwmc() {
		return dwmc;
	}
	public void setDwmc(String dwmc) {
		this.dwmc = dwmc;
	}
	public String getCcfwq() {
		return ccfwq;
	}
	public void setCcfwq(String ccfwq) {
		this.ccfwq = ccfwq;
	}
	public String getCcwz() {
		return ccwz;
	}
	public void setCcwz(String ccwz) {
		this.ccwz = ccwz;
	}
	public String getBfwz() {
		return bfwz;
	}
	public void setBfwz(String bfwz) {
		this.bfwz = bfwz;
	}
	public String getWlwz() {
		return wlwz;
	}
	public void setWlwz(String wlwz) {
		this.wlwz = wlwz;
	}
	public String getGzz_xh() {
		return gzz_xh;
	}
	public void setGzz_xh(String gzz_xh) {
		this.gzz_xh = gzz_xh;
	}
	public Date getScsj() {
		return scsj;
	}
	public void setScsj(Date scsj) {
		this.scsj = scsj;
	}
	public String getBzlx() {
		return bzlx;
	}
	public void setBzlx(String bzlx) {
		this.bzlx = bzlx;
	}
	
	
}
