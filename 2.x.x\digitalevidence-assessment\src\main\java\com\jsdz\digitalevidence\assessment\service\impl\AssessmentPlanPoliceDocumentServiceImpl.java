package com.jsdz.digitalevidence.assessment.service.impl;

/**
 * 
 * @类名: AssessmentPlanPoliceDocumentServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-06-21 16:30:04
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanPoliceDocumentDao;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPoliceDocument;
import com.jsdz.digitalevidence.assessment.service.AssessmentPlanPoliceDocumentService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

@Service("AssessmentPlanPoliceDocumentServiceImpl")
public class AssessmentPlanPoliceDocumentServiceImpl implements AssessmentPlanPoliceDocumentService {

	@Autowired
	private AssessmentPlanPoliceDocumentDao assessmentPlanPoliceDocumentDao;

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addAssessmentPlanPoliceDocument(AssessmentPlanPoliceDocument assessmentPlanPoliceDocument) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			assessmentPlanPoliceDocumentDao.addAssessmentPlanPoliceDocument(assessmentPlanPoliceDocument);
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("新增保存成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updateAssessmentPlanPoliceDocument(AssessmentPlanPoliceDocument assessmentPlanPoliceDocument) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			assessmentPlanPoliceDocumentDao.updateAssessmentPlanPoliceDocument(assessmentPlanPoliceDocument);
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("修改成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deleteAssessmentPlanPoliceDocument(AssessmentPlanPoliceDocument assessmentPlanPoliceDocument) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			assessmentPlanPoliceDocumentDao.deleteAssessmentPlanPoliceDocument(assessmentPlanPoliceDocument);
			result.setSuccess(true);
			result.setCode(200);
			result.setMsg("删除成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 按id查询(游离数据)
	 */ 
 	public AssessmentPlanPoliceDocument findAssessmentPlanPoliceDocumentById(Long id){

		return assessmentPlanPoliceDocumentDao.findAssessmentPlanPoliceDocumentById(id);
	}

	/** 
 	 * 按 id 查询
	 */ 
 	public AssessmentPlanPoliceDocument locateAssessmentPlanPoliceDocumentById(Long id) {
		return assessmentPlanPoliceDocumentDao.locateAssessmentPlanPoliceDocumentById(id);
	}

	/** 
 	 * 单个查询
	 */ 
 	public AssessmentPlanPoliceDocument findAssessmentPlanPoliceDocumentByParam(String queryStr, String[] paramNames, Object[] values) {
		return assessmentPlanPoliceDocumentDao.findAssessmentPlanPoliceDocumentByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanPoliceDocument> findAllAssessmentPlanPoliceDocuments() {
		return assessmentPlanPoliceDocumentDao.findAllAssessmentPlanPoliceDocuments();
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanPoliceDocument> findAssessmentPlanPoliceDocumentsByParam(String queryStr, String[] paramNames, Object[] values) {
		return assessmentPlanPoliceDocumentDao.findAssessmentPlanPoliceDocumentsByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanPoliceDocument> findAssessmentPlanPoliceDocumentsOnPage(Page<AssessmentPlanPoliceDocument> page, String queryStr, String[] paramNames, Object[] values) {
		Page<AssessmentPlanPoliceDocument> pos = assessmentPlanPoliceDocumentDao.findAssessmentPlanPoliceDocumentsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
