package com.jsdz.digitalevidence.task.bean;

import com.jsdz.digitalevidence.task.model.TTask;
import com.jsdz.digitalevidence.task.model.TTaskProcess;
import org.springframework.beans.BeanUtils;

import java.util.List;

public class TaskInfoVo extends TaskApiVo {

    public TaskInfoVo () {

    }

    public TaskInfoVo (TTask task, TTaskProcess process) {
        BeanUtils.copyProperties(process, this);
        this.setTitle(task.getTitle());
        this.setContent(task.getContent());
        this.taskDocUrl = task.getUrlList();
        this.setTaskId(task.getId());
        this.uploadUrl = process.getUrlList();
        this.docIds = process.getDocIds();
    }

    /**
     * 执法仪编号
     */
    private String code;

    /**
     * 警员姓名
     */
    private String employeesName;

    /**
     * 任务内容
     */
    private String content;

    /**
     * 审核意见
     */
    private String advice;

    /**
     * 审核人
     */
    private String examineBy;

    /**
     * 警员姓名
     */
    private String examineName;

    /**
     * 任务反馈
     */
    private String result;

    /**
     * 任务附件
     */
    private List<String> taskDocUrl;

    /**
     * 任务上传附件
     */
    private List<String> uploadUrl;

    /**
     * 任务上传附件
     */
    private List<Long> docIds;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEmployeesName() {
        return employeesName;
    }

    public void setEmployeesName(String employeesName) {
        this.employeesName = employeesName;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getAdvice() {
        return advice;
    }

    public void setAdvice(String advice) {
        this.advice = advice;
    }

    public String getExamineBy() {
        return examineBy;
    }

    public void setExamineBy(String examineBy) {
        this.examineBy = examineBy;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getExamineName() {
        return examineName;
    }

    public void setExamineName(String examineName) {
        this.examineName = examineName;
    }

    public List<String> getTaskDocUrl() {
        return taskDocUrl;
    }

    public void setTaskDocUrl(List<String> taskDocUrl) {
        this.taskDocUrl = taskDocUrl;
    }

    public List<String> getUploadUrl() {
        return uploadUrl;
    }

    public void setUploadUrl(List<String> uploadUrl) {
        this.uploadUrl = uploadUrl;
    }

    public List<Long> getDocIds() {
        return docIds;
    }

    public void setDocIds(List<Long> docIds) {
        this.docIds = docIds;
    }
}