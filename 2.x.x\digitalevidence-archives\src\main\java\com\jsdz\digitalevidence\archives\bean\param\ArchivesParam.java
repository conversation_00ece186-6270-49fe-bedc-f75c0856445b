package com.jsdz.digitalevidence.archives.bean.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.archives.bean.ArchivesDataBean;
import com.jsdz.utils.DateTimeUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
public class ArchivesParam  implements Serializable{


    Page<ArchivesDataBean> page;
    private String orgPath;
    private String sortName;

    private String sortOrder;

    //是否包含下级 （0不，1包含）
    private String includeSub;

    private String archivesCode;

    private String archivesTitle;

    private Integer archivesType;

    private Long inputUserId;

    private String inputUserName;
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date inputTimeStart;
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date inputTimeEnd;

    private Integer status;

    private String remarks;

    private Integer saveMonth;
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date expirationTimeStart;
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date expirationTimeEnd;

    public Page<ArchivesDataBean> getPage() {
        return page;
    }

    public void setPage(Page<ArchivesDataBean> page) {
        this.page = page;
    }

    public String getSortName() {
        return sortName;
    }

    public void setSortName(String sortName) {
        this.sortName = sortName;
    }

    public String getSortOrder() {
        return sortOrder;
    }
    public String getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(String includeSub) {
        this.includeSub = includeSub;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getArchivesCode() {
        return archivesCode;
    }

    public void setArchivesCode(String archivesCode) {
        this.archivesCode = archivesCode;
    }

    public String getArchivesTitle() {
        return archivesTitle;
    }

    public void setArchivesTitle(String archivesTitle) {
        this.archivesTitle = archivesTitle;
    }

    public Integer getArchivesType() {
        return archivesType;
    }

    public void setArchivesType(Integer archivesType) {
        this.archivesType = archivesType;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public Long getInputUserId() {
        return inputUserId;
    }

    public void setInputUserId(Long inputUserId) {
        this.inputUserId = inputUserId;
    }

    public String getInputUserName() {
        return inputUserName;
    }

    public void setInputUserName(String inputUserName) {
        this.inputUserName = inputUserName;
    }

    public Date getInputTimeStart() {
        return inputTimeStart;
    }

    public void setInputTimeStart(Date inputTimeStart) {
        this.inputTimeStart = inputTimeStart;
    }

    public Date getInputTimeEnd() {
        return inputTimeEnd;
    }

    public void setInputTimeEnd(Date inputTimeEnd) {
        this.inputTimeEnd = inputTimeEnd;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getSaveMonth() {
        return saveMonth;
    }

    public void setSaveMonth(Integer saveMonth) {
        this.saveMonth = saveMonth;
    }

    public Date getExpirationTimeStart() {
        return expirationTimeStart;
    }

    public void setExpirationTimeStart(Date expirationTimeStart) {
        this.expirationTimeStart = expirationTimeStart;
    }

    public Date getExpirationTimeEnd() {
        return expirationTimeEnd;
    }

    public void setExpirationTimeEnd(Date expirationTimeEnd) {
        this.expirationTimeEnd = expirationTimeEnd;
    }

//    public String getPageQuery( PerLvlBean p) {
//
//        String hql = " from ArchivesDataBean ad"
//                + " left join fetch ad.organization o "
//                + " left join fetch ad.operator p "
//                + " left join fetch p.employees e "
//                + "where 1 = 1 " ;
//
//        //权限
//            if (p.getPermissionLevel() == 1 || p.getPermissionLevel() == 2){ //只能查本单位的数据
//                hql +=" and ad.inputUserId = "+p.getOperatorId() ;
//            }else if (p.getPermissionLevel() == 3){ //能查本单位及下级单位的数据
//                hql +=" and o.path like '"+p.getOrgPath()+"%'" ;
//            }else if (p.getPermissionLevel() < 1 ){ //所有都不显示
//                hql +=" and ad.inputUserId = "+p.getOperatorId() ;
//            }
//
//        //选择组织
//            if(StringUtils.isNoEmpty(getOrgPath())){
//                if(getIncludeSub()=="1"){
//                    hql +=" and o.path like '"+getOrgPath()+"%'" ;
//                }else {
//                    hql +=" and o.path = '"+getOrgPath()+"'" ;
//                }
//            }
//        //编号
//        if(StringUtils.isNoBlank(getArchivesCode())){
//            hql +=" and ad.archivesCode like '%"+getArchivesCode()+"%'" ;
//        }
//
//        //标题
//        if(StringUtils.isNoBlank(getArchivesTitle())){
//            hql +=" and ad.archivesTitle like '%"+getArchivesTitle()+"%'" ;
//        }
//
//        //时间开始
//        if(StringUtils.isNoBlank(getInputTimeStart())){
//            hql +=" and ad.inputTime >= '"+getInputTimeStart()+"'" ;
//        }
//
//        //时间结束
//        if(StringUtils.isNoBlank(getInputTimeEnd())){
//            hql +=" and ad.inputTime <= '"+getInputTimeEnd()+"'" ;
//        }
//
//        return hql;
//    }



}
