package com.jsdz.admin.security.service;

/**
 * 
 * @类名: SystemMenuService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-09-08 11:59:54
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.bean.OrganizationBean;
import com.jsdz.admin.security.bean.MenuTreeBean;
import com.jsdz.admin.security.bean.RoleForViewBean;
import com.jsdz.admin.security.bean.RoleMenuBean;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.SystemMenu;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface SystemMenuService {

	//新增
	public AjaxResult addSystemMenu(SystemMenu systemMenu);

	//修改
	public AjaxResult updateSystemMenu(SystemMenu systemMenu);

	//删除
	public AjaxResult deleteSystemMenu(SystemMenu systemMenu);

	//按id查询,结果是游离状态的数据
	public SystemMenu findSystemMenuById(Long id);

	//按id查询
	public SystemMenu locateSystemMenuById(Long id);

	//单个查询
	public SystemMenu findSystemMenuByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<SystemMenu> findAllSystemMenus();

	//列表查询
	public List<SystemMenu> findSystemMenusByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<SystemMenu> findSystemMenusOnPage(Page<SystemMenu> page, String queryStr,String[] paramNames,Object[] values);

	//按查询树形菜单数据
	//public List<MenuTreeBean> findAllSysMenu(Operator operator);
	
	//设置权限查询的菜单
	public List<RoleMenuBean> findAllMenuForRoleSet(Long roleId) throws Exception;
	
	//根据用户查询可用的菜单
    public List<MenuTreeBean> findSysMenuByOperator(Operator operator);
	
	//分页查询最上级数据给前端easyUi treegrid使用
	public Page<MenuTreeBean> findMenuByTopLevel(Page<MenuTreeBean> page, String[] paramNames, Object[] values);
	
	//按上级ID查询
	public  List<MenuTreeBean> findMenuByParentId(Long parentId);
	
	//根据操作员和菜单id查询页面权限
	public List<RoleForViewBean> findViewRole(Long userId,Long menuId);//
}

