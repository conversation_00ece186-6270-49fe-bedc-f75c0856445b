package com.jsdz.digitalevidence.analysis.model;
/**
 * 
 * @类名: DailyObject
 * @说明: 日结类型(如站点，记录仪，警员，分类，级别，内容类型)
 *
 * @author: kenny
 * @Date	2017年5月30日下午5:41:19
 * 修改记录：
 *
 * @see
 */
public enum DailyObject {
	DIALY_SITE(1, "站点日结"), 
	DIALY_RECORDER(2, "执法仪日结"),
	DIALY_POLICE(3,"警员日结");
	
	public int index;
	public String name;
	
	private DailyObject(int index, String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public DailyObject getDailyObject(int ordinal){
		if (ordinal==0)
			return DIALY_SITE;
		else if (ordinal==1)
			return DIALY_RECORDER;
		else if (ordinal==2)
			return DIALY_POLICE;
		else
			return DIALY_SITE;
	}
}
