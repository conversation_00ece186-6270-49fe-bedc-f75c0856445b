package com.jsdz.digitalevidence.activemq.bean;

import com.jsdz.admin.security.bean.ParamGenerateHql;
import com.jsdz.admin.security.utils.JSDateFormatUtils;
import com.jsdz.core.Page;
import com.jsdz.core.SortBean;
import com.jsdz.digitalevidence.activemq.model.SysLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @类名: SysLogParam
 * @说明:
 * @author: kenny
 * @Date 2018年2月1日上午9:13:25 修改记录：
 * @see
 */

public class SysLogParam extends ParamGenerateHql{
	private Page<SysLog> page;
	private Map<String, Object> kvs = new HashMap<String, Object>();
	private List<String> parLst = new ArrayList<String>();
	private String querySql;
	private String totalSql;

	private String logType;//类型
	private String topic; //主题
	private String keyWord; //关键字
	private String context; //内容
	private String createTimeFrom;
	private String createTimeTo;
	

	public SysLogParam(){
		page = new Page<SysLog>();
		page.setOffset(0);
		page.setPageSize(20);
	}	
	public String queryBeanToHql() {
		parLst.clear();
		
		querySql=" from SysLog s ";
		totalSql = " select count(*) from SysLog s";
		String p1 ="";
		if (this.getLogType() != null && !"".equals(this.getLogType())){
			p1 = " s.logType like :logType ";
			parLst.add(p1);
			kvs.put("logType", "%" + this.getLogType() + "%");
		}
		
		if (this.getTopic() != null && !"".equals(this.getTopic())){
			p1 = " s.topic like :topic ";
			parLst.add(p1);
			kvs.put("topic", "%" + this.getTopic() + "%");
		}
		
		if (this.getKeyWord() != null && !"".equals(this.getKeyWord())){
			p1 = " s.keyWord like :keyWord ";
			parLst.add(p1);
			kvs.put("keyWord", "%" + this.getKeyWord() + "%");
		}

		if (this.getContext() != null && !"".equals(this.getContext())){
			p1 = " s.context like :context ";
			parLst.add(p1);
			kvs.put("context", "%" + this.getContext() + "%");
		}
		
		if (this.getCreateTimeFrom() != null && !"".equals(this.getCreateTimeFrom())){
			p1 = " s.createTime >=  :createTimeFrom";
			parLst.add(p1);
			kvs.put("createTimeFrom", JSDateFormatUtils.parseDateHour(getCreateTimeFrom()));
		}

		if (this.getCreateTimeTo() != null && !"".equals(this.getCreateTimeTo())){
			p1 = " s.createTime <=  :createTimeTo";
			parLst.add(p1);
			kvs.put("createTimeTo", JSDateFormatUtils.parseDateHour(getCreateTimeTo()));
		}
		
		querySql = this.generateHql(page, parLst, querySql, totalSql);
		
		StringBuilder sb = new StringBuilder();
		if (page.getSort() != null && page.getSort().size() > 0 ){
			sb.append(" order by ");
			List<SortBean> list = page.getSort();
			for (SortBean sortBean : list){
				sb.append(sortBean);
				sb.append(" ");
				sb.append(sortBean.getOrder().toString());
			}
			
		}else{
			sb.append(" order by s.id desc ");
		}
		
		return querySql + sb;
	}	
	
	public Page<SysLog> getPage() {
		return page;
	}
	public void setPage(Page<SysLog> page) {
		this.page = page;
	}
	public Map<String, Object> getKvs() {
		return kvs;
	}
	public void setKvs(Map<String, Object> kvs) {
		this.kvs = kvs;
	}
	public String getLogType() {
		return logType;
	}
	public void setLogType(String logType) {
		this.logType = logType;
	}
	public String getTopic() {
		return topic;
	}
	public void setTopic(String topic) {
		this.topic = topic;
	}
	public String getKeyWord() {
		return keyWord;
	}
	public void setKeyWord(String keyWord) {
		this.keyWord = keyWord;
	}
	public String getContext() {
		return context;
	}
	public void setContext(String context) {
		this.context = context;
	}
	public String getCreateTimeFrom() {
		return createTimeFrom;
	}
	public void setCreateTimeFrom(String createTimeFrom) {
		this.createTimeFrom = createTimeFrom;
	}
	public String getCreateTimeTo() {
		return createTimeTo;
	}
	public void setCreateTimeTo(String createTimeTo) {
		this.createTimeTo = createTimeTo;
	}
	


	
}
