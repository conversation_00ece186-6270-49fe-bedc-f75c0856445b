package com.jsdz.digitalevidence.statistics.mapper;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.bean.param.ApprovedDatasParam;
import com.jsdz.digitalevidence.statistics.model.SysDeleteApprovalModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
public interface SysDeleteApprovalMapper extends BaseMapper<SysDeleteApprovalModel> {

    List<SysDeleteApprovalModel> approvedDatas(@Param("empId")Long empId, @Param("param") ApprovedDatasParam param);
    Integer approvedCount(@Param("empId")Long empId,@Param("param") ApprovedDatasParam param);

    @Select("select * from ADMIN_EMPLOYEES where ORGANIZATIONID = #{operatorId}")
    List<Employees> findAllEmployeess(@Param("operatorId") Long operatorId);

    @Delete("update t_doc set is_deleted = 1 where doc_id = #{docid}")
    void deletedocmentbyId(@Param("docid") long docid);
}
