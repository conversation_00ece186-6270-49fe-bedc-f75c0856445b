package com.jsdz.digitalevidence.task.bean;

import com.jsdz.digitalevidence.task.model.TTaskProcess;

import java.util.List;

public class TaskFeedbackDto {

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 执法仪编码
     */
    private String code;

    /**
     * 采集站编号
     */
//    private String storageNo;

    /**
     * 反馈内容
     */
    private String result;

    /**
     * 附件地址列表
     */
    private List<String> fileNames;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public List<String> getFileNames() {
        return fileNames;
    }

    public void setFileNames(List<String> fileNames) {
        this.fileNames = fileNames;
    }

    public TTaskProcess toProcessTask() {
        TTaskProcess taskProcess = new TTaskProcess();
        taskProcess.setTaskId(this.taskId);
        taskProcess.setResult(this.result);
        return taskProcess;
    }
}
