package com.jsdz.digitalevidence.assessment.service;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.bean.FiveRateBean;

/**
 * 
 * @类名: FiveRateService
 * @说明: 五率考核
 *
 * @author: kenny
 * @Date	2018年1月20日下午4:13:36
 * 修改记录：
 *
 * @see
 */
public interface FiveRateService {

	//1.配备率
	public Page<FiveRateBean> findAllocationRateFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values);

	//2.使用率
	public Page<FiveRateBean> findUsageRateFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values);

	//3.维护率
	public Page<FiveRateBean> findApprovedRateFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values);

	//4.在线率
	public Page<FiveRateBean> findOnlineRateFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values);
	
	//5.平均时长
	public Page<FiveRateBean> findAvgDurationFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values);
	
}
