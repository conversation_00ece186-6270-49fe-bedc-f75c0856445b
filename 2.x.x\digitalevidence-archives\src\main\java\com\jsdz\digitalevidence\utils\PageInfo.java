package com.jsdz.digitalevidence.utils;

import java.util.List;
import java.util.Map;

public class PageInfo<E>{
    private List<E> rows;//数据集
    private Integer total;//总数
    private String hql;//sql
    private Integer pageSize=10;//一页多少条
    private Integer pageNumber=1;//当前页
    private Integer maxPageNum;//最大页
    public Map<String,Object> param;//存放其他信息  查询条件等等
    //设置getter/setter方法 构造函数等等
    //注意一下这个方法 setTotal 重写内部方法

    //注意一下这个方法 setTotal 重写内部方法
    public void setTotal(Integer total){
        this.total=total;
        if(null==pageNumber||"undefined".equals(pageNumber)||pageNumber<=0){
            pageNumber=1;
        }else{
            if(total%pageSize==0){
                if(pageNumber>total/pageSize){
                    pageNumber=total/pageSize;
                }
                maxPageNum=total/pageSize;

            }else{
                if(pageNumber>total/pageSize+1){
                    pageNumber=total/pageSize+1;
                }
                maxPageNum=total/pageSize+1;
            }
        }
    }


    public List<E> getRows() {
        return rows;
    }

    public void setRows(List<E> rows) {
        this.rows = rows;
    }

    public Integer getTotal() {
        return total;
    }


    public String getHql() {
        return hql;
    }

    public void setHql(String hql) {
        this.hql = hql;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getMaxPageNum() {
        return maxPageNum;
    }

    public void setMaxPageNum(Integer maxPageNum) {
        this.maxPageNum = maxPageNum;
    }

    public Map<String, Object> getParam() {
        return param;
    }

    public void setParam(Map<String, Object> param) {
        this.param = param;
    }
}
