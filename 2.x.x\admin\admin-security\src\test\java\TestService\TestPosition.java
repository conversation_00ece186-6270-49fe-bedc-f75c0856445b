package TestService;
import java.util.Date;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
/**
 * 
 * @类名: TestPosition
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月10日下午3:01:02
 * 修改记录：
 *
 * @see
 */
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.model.Position;
import com.jsdz.admin.org.model.Region;
import com.jsdz.admin.org.service.PositionService;
import com.jsdz.admin.org.service.RegionService;
import com.jsdz.core.AjaxResult;

import net.sf.json.JSONArray;

@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { "classpath:/testApplicationContext-common.xml" })
public class TestPosition {

	@Autowired
	private PositionService positionService;
	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}

	@Test
	@Transactional
	public void testAddPosition()
	{
		Position position = new Position();
		position.setPosition("组长");
		position.setCreateTime(new Date());
		AjaxResult result = positionService.addPosition(position);
		

		JSONArray regionStr = JSONArray.fromObject(position);//将java对象转换为json对象
		System.out.println("【region】:\r\n" +  regionStr.toString());//将json对象转换为字符串
		JSONArray json = JSONArray.fromObject(result);//将java对象转换为json对象
		System.out.println("【结果】:\r\n" +  json.toString());//将json对象转换为字符串
	}	
	
}
