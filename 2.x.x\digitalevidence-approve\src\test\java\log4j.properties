#
log4j.rootCategory=DEBUG,file,errorfile,console
#

#
log4j.appender.console = org.apache.log4j.ConsoleAppender
log4j.appender.console.layout = org.apache.log4j.PatternLayout
log4j.appender.console.Threshold=INFO
log4j.appender.console.layout.ConversionPattern =[%-5p] %d{yyyy-MM-dd HH:mm:ss,SSS} method:%l%n%m%n

#
log4j.appender.file=org.apache.log4j.DailyRollingFileAppender
log4j.appender.file.DatePattern='.'yyyy-MM-dd
log4j.appender.file.File=logs/jsdz-info.log
log4j.appender.file.Append=true
log4j.appender.file.Threshold=INFO
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=[%-5p] %d{yyyy-MM-dd HH:mm:ss,SSS} method:%l%n%m%n

#
log4j.appender.errorfile=org.apache.log4j.DailyRollingFileAppender
log4j.appender.errorfile.DatePattern='.'yyyy-MM-dd
log4j.appender.errorfile.File=logs/jsdz-errors.log
log4j.appender.errorfile.Append=true
log4j.appender.errorfile.Threshold=ERROR
log4j.appender.errorfile.layout=org.apache.log4j.PatternLayout
log4j.appender.errorfile.layout.ConversionPattern=[%-5p] %d{yyyy-MM-dd HH:mm:ss,SSS} method:%l%n%m%n
