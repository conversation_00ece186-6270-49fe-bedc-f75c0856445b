/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.service;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.jsdz.core.AjaxResult;
import com.jsdz.digitalevidence.approve.model.BusinessToken;

/**
 * @类名: BusinessTokenService
 * @说明: 
 *
 * <AUTHOR>
 * @Date	 2017年11月18日 上午11:35:40
 * 修改记录：
 *
 * @see 	 
 */
@Service
public class BusinessTokenServiceImpl implements BusinessTokenService {

	@Override
	public AjaxResult checkToken(Map<String, Object> param, Long tokenId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<BusinessToken> getValidToken(Map<String, Object> param) {
		// TODO Auto-generated method stub
		return null;
	}

}
