package com.jsdz.digitalevidence.assessment.jdbc;

import java.sql.Connection;
import java.sql.SQLException;

import com.jsdz.digitalevidence.cache.utils.TaskHandle;
import com.jsdz.digitalevidence.cache.utils.JdbcFunction;
//import com.mysql.jdbc.Connection;
/**
 * @类名: ClearPlanDetailHandle
 * @说明: 多线程删除方案明细
 *
 * <AUTHOR>
 * @Date	 2019年6月21日下午5:12:19
 * 修改记录：
 *
 * @see 	 
 */
public class ClearPlanDetailHandle implements TaskHandle {
	private Long id;
	private Connection conn;
	
	public ClearPlanDetailHandle(Long planId){
		super();
		this.id = planId;
	}
	
	@Override
	public void process() {
		try{
			conn = JdbcFunction.getJdbcConnection();
			
			//doc
			JdbcFunction.executeStatement(
						conn, 
						"delete t_assessment_plan_doc where plan_id=?", 
						new Object[]{this.id});
			//police
			JdbcFunction.executeStatement(
					conn, 
					"delete t_assessment_plan_police where plan_id=?", 
					new Object[]{this.id});
			
			//police doc
			JdbcFunction.executeStatement(
					conn, 
					"delete t_assessment_plan_police_doc where plan_id=?", 
					new Object[]{this.id});
			
		}catch(Exception e){
			System.out.println("**** jdbc错误：" + e.toString());
		}finally{
			if (conn != null)
				try {
					conn.close();
				} catch (SQLException e) {
					e.printStackTrace();
				}
		}		
	}


	
}
