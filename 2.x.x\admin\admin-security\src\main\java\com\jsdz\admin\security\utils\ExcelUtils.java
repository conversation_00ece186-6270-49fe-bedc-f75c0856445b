package com.jsdz.admin.security.utils;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
/**
 *
 * @类名: ExcelUtils
 * @说明: EXCEL导入导出
 *
 * @author: kenny
 * @Date	2017年11月3日上午11:59:44
 * 修改记录：
 *
 * @see
 */
import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
//import org.apache.poi.ss.usermodel.CellType;

public class ExcelUtils {
	   public static final String OFFICE_EXCEL_2003_POSTFIX = "xls";
	    public static final String OFFICE_EXCEL_2010_POSTFIX = "xlsx";
	    public static final String EMPTY = "";
	    public static final String POINT = ".";
	    /**
	     * 获得path的后缀名
	     * @param path
	     * @return
	     */
	    public static String getPostfix(String path){
	        if(path==null || EMPTY.equals(path.trim())){
	            return EMPTY;
	        }
	        if(path.contains(POINT)){
	            return path.substring(path.lastIndexOf(POINT)+1,path.length());
	        }
	        return EMPTY;
	    }
	    /**
	     * 单元格格式
	     * @param hssfCell
	     * @return
	     */
	    @SuppressWarnings({ "static-access", "deprecation" })
	    public static String getHValue(HSSFCell hssfCell){
	         if (hssfCell.getCellType() == hssfCell.CELL_TYPE_BOOLEAN) {
	             return String.valueOf(hssfCell.getBooleanCellValue());
	         } else if (hssfCell.getCellType() == hssfCell.CELL_TYPE_NUMERIC) {
	             String cellValue = "";
	             if(HSSFDateUtil.isCellDateFormatted(hssfCell)){
	                 Date date = HSSFDateUtil.getJavaDate(hssfCell.getNumericCellValue());
	                 cellValue = JSDateFormatUtils.formatDateSlash(date);
	             }else{
	                 DecimalFormat df = new DecimalFormat("#.##");
	                 cellValue = df.format(hssfCell.getNumericCellValue());
	                 String strArr = cellValue.substring(cellValue.lastIndexOf(POINT)+1,cellValue.length());
	                 if(strArr.equals("00")){
	                     cellValue = cellValue.substring(0, cellValue.lastIndexOf(POINT));
	                 }
	             }
	             return cellValue;
	         } else {
	            return String.valueOf(hssfCell.getStringCellValue());
	         }
	    }
	    /**
	     * 单元格格式
	     * @param xssfCell
	     * @return
	     */
	    public static String getXValue(XSSFCell xssfCell){
	         if (xssfCell.getCellType() == Cell.CELL_TYPE_BOOLEAN) {
	             return String.valueOf(xssfCell.getBooleanCellValue());
	         } else if (xssfCell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
	             String cellValue = "";
	             if(XSSFDateUtil.isCellDateFormatted(xssfCell)){
	                 Date date = XSSFDateUtil.getJavaDate(xssfCell.getNumericCellValue());
	                 cellValue = JSDateFormatUtils.formatDateSlash(date);
	             }else{
	                 DecimalFormat df = new DecimalFormat("#.##");
	                 cellValue = df.format(xssfCell.getNumericCellValue());
	                 String strArr = cellValue.substring(cellValue.lastIndexOf(POINT)+1,cellValue.length());
	                 if(strArr.equals("00")){
	                     cellValue = cellValue.substring(0, cellValue.lastIndexOf(POINT));
	                 }
	             }
	             return cellValue;
	         } else {
	            return String.valueOf(xssfCell.getStringCellValue());
	         }
	    }

	    /**
	     * 自定义xssf日期工具类
	     * <AUTHOR>
	     *
	     */
	    static class XSSFDateUtil extends DateUtil{
	        protected static int absoluteDay(Calendar cal, boolean use1904windowing) {
	            return DateUtil.absoluteDay(cal, use1904windowing);
	        }
	    }


	    /***********************************************************************
	    * 导出EXCEL
	    **********************************************************************/
	    /*
	     * 写入2003版的EXCEL
	     *
	     */
	    public static void writeExcel03(
	    		HttpServletResponse response,
	    		String fileName,
	    		String[] titles,
	    		List<String[]> values) throws IOException{
	        //创建工作簿
	        HSSFWorkbook workBook = new HSSFWorkbook();
	        //创建工作表  工作表的名字叫helloWorld
	        HSSFSheet sheet = workBook.createSheet("sheet1");
	        //创建行,第3行

	        //第一行标题行
	        HSSFRow titleRow = sheet.createRow(0);
	        for(int i=0;i<titles.length;i++){
	        	HSSFCell cell = titleRow.createCell(i, Cell.CELL_TYPE_STRING);
	        	cell.setCellValue(titles[i]);
	        }

	        //数据行
	        for(int i=0;i<values.size();i++){
	        	HSSFRow valueRow = sheet.createRow(i+1);
	        	for (int j=0;j<values.get(i).length;j++ ){
		        	HSSFCell cell = valueRow.createCell(j, Cell.CELL_TYPE_STRING);
		        	cell.setCellValue(values.get(i)[j]);
	        	}
	        }
	        response.reset();  //先reset 刷新可能存在一些未关闭的getWriter().
	        response.setContentType("contentType=application/vnd.ms-excel");
            response.setHeader("Content-disposition","attachment;filename="+URLEncoder.encode(fileName,"utf-8"));

//	        response.setHeader("Content-disposition", "attachment; filename=analysisdata.xls");
//	        response.setContentType("application/msexcel");

	        OutputStream output=response.getOutputStream();
	        workBook.write(output);
	        output.flush();
	        output.close();

	        return;
	        //response.getWriter().close();

	    }

	    /*
	     * 写入2007版的EXCEL
	     *
	     */
	    public static void writeExcel07(
	    		String fileName,
	    		String[] titles,
	    		List<String[]> values) throws IOException{
	        //创建工作簿
	    	XSSFWorkbook workBook = new XSSFWorkbook();
	        //创建工作表  工作表的名字叫helloWorld
	    	XSSFSheet sheet = workBook.createSheet("sheet1");
	        //创建行,第3行

	        //第一行标题行
	    	XSSFRow titleRow = sheet.getRow(0);
	        for(int i=0;i<titles.length;i++){
	        	XSSFCell cell = titleRow.createCell(i,Cell.CELL_TYPE_STRING);
	        	cell.setCellValue(titles[i]);
	        }

	        //数据行
	        for(int i=0;i<values.size();i++){
	        	XSSFRow valueRow = sheet.createRow(i+1);
	        	for (int j=0;j<values.get(i).length;j++ ){
	        		XSSFCell cell = valueRow.createCell(j, Cell.CELL_TYPE_STRING);
		        	cell.setCellValue(values.get(i)[j]);
	        	}
	        }


	        //输出Excel文件
	        FileOutputStream output=new FileOutputStream("d:\\workbook.xlsx");
	        workBook.write(output);
	        output.flush();
	        output.close();
	    }
}
