package com.jsdz.admin.security.model;
/**
 * 
 * @类名: Operator
 * @说明: 操作员类
 *
 * @author: kenny
 * @Date	2017年4月26日上午10:45:50
 * 修改记录：
 *
 * @see
 */

import java.io.Serializable;
import java.util.Date;
public class Operator implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4312834419962439372L;
	private Long id;
	private String loginName; //登录名
	private String password; //密码
	private String state;  //状态(1禁用)
	private String rightContent=""; //权限
	private String description;//描述
	private String operatorRoleStr="";
	private Employees employees;//关联员工类 
	private Date createDate;//日期
	
	/** 更新时间*/
	private Date updateTime=new Date();

	/** 更新时间*/
	private Date pwdExpireTime;
	/** 是否已删除*/
	private Boolean isDeleted=false;
	/** 原code,为防止加入新编号重复 */
	private String deleteCode;//
	/** 删除时间 */
	private Date deleteTime;//
	/** 删除人 */
	private String deleteBy;//
	private String talkCode; // 语洽对讲编号
	
	public Operator(){
		super();
	}
	public Operator(String loginName,String description){
		this.setLoginName(loginName);
		this.setDescription(description); 
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getRightContent() {
		return rightContent;
	}
	public void setRightContent(String rightContent) {
		this.rightContent = rightContent;
	}	
	

	public String toString(){
		StringBuilder sb = new StringBuilder();
		sb.append("ID=");
		sb.append(this.getId());
		sb.append(",登录名=");
		sb.append(this.getLoginName());
		sb.append(",描述=");
		sb.append(description);
		return sb.toString();
	}
	public String getOperatorRoleStr() {
		return operatorRoleStr;
	}
	public void setOperatorRoleStr(String operatorRoleStr) {
		this.operatorRoleStr = operatorRoleStr;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public Employees getEmployees() {
		return employees;
	}
	public void setEmployees(Employees employees) {
		this.employees = employees;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public Boolean getIsDeleted() {
		return isDeleted;
	}
	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}
	public String getDeleteCode() {
		return deleteCode;
	}
	public void setDeleteCode(String deleteCode) {
		this.deleteCode = deleteCode;
	}
	public Date getDeleteTime() {
		return deleteTime;
	}
	public void setDeleteTime(Date deleteTime) {
		this.deleteTime = deleteTime;
	}
	public String getDeleteBy() {
		return deleteBy;
	}
	public void setDeleteBy(String deleteBy) {
		this.deleteBy = deleteBy;
	}
	public String getTalkCode() {
		return talkCode;
	}
	public void setTalkCode(String talkCode) {
		this.talkCode = talkCode;
	}

	public Date getPwdExpireTime() {
		return pwdExpireTime;
	}

	public void setPwdExpireTime(Date pwdExpireTime) {
		this.pwdExpireTime = pwdExpireTime;
	}
}
