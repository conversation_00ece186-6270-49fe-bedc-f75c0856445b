package com.jsdz.digitalevidence.alarm.common.bean;
/**
 * 
 * @类名: DocumentInfoParam
 * @说明: 
 *
 * @author: kenny
 * @Date	2018年3月1日下午5:14:34
 * 修改记录：
 *
 * @see
 */
import com.jsdz.digitalevidence.document.bean.DocumentInfoBean;

public class DocumentInfoParam {
	private DocumentInfoBean docinfo;
	private String startTimeStr; //回放开始时长
	private String endTimeStr; //回放结时长时间
	private String approveStr; //审核意见
	
	public DocumentInfoParam(){
		docinfo = new DocumentInfoBean();
	}
	public Integer getStartTime(){
		return getSecond(this.getStartTimeStr());
	}
	public Integer getEndTime(){
		return getSecond(this.getEndTimeStr());
	}
	
	//把时间转换为秒
	private Integer getSecond(String timeStr){
		Integer result = 0;
		Integer secNum = 0;
		String[] s = timeStr.split(":");
		for (int i=s.length;i>0;i--){
			if (i==s.length)
				secNum = 1;
			else
				secNum = secNum * 60;

			String s1 = s[i-1];
			result = result + Integer.valueOf(s1) * secNum;
		}
		return result;
	}
	
	//验证开始时间是否合法
	public String checkStartTimeStr(){
		return checkTimeStr(this.getStartTimeStr());
	}
	
	//验证结束时间是否合法
	public String checkEndTimeStr(){
		return checkTimeStr(this.getEndTimeStr());
	}
	
	//验证提供的时间是否合法
	private String checkTimeStr(String timeStr){
		String result = null;
		if (timeStr == null || timeStr.equals("")){
			return "时长必须输入，格式=小时:分:秒，如 10=10秒；10:10=10分10秒；10:10:10=10小时10分10秒";
		}
		
		String[] s = timeStr.split(":");
		if (s.length > 3){
			result = "时长=" + timeStr + " 格式错误";
			return result;
		}
		try{
			for (int i=0;i<s.length;i++){
				Integer num = Integer.valueOf(s[i]);
				if ((i==0 || i == 1) && num > 59 ){
					result = "时长=" + timeStr + " 分钟和秒钟数不能超过59";
					break;
				}
			}
		}catch(Exception e){
			result = "时长=" + timeStr + " 格式错误";
			return result;
		}
		
		return result;
	}
	
	public DocumentInfoBean getDocinfo() {
		return docinfo;
	}
	public void setDocinfo(DocumentInfoBean docinfo) {
		this.docinfo = docinfo;
	}
	public String getStartTimeStr() {
		return startTimeStr;
	}
	public void setStartTimeStr(String startTimeStr) {
		this.startTimeStr = startTimeStr;
	}
	public String getEndTimeStr() {
		return endTimeStr;
	}
	public void setEndTimeStr(String endTimeStr) {
		this.endTimeStr = endTimeStr;
	}
	public String getApproveStr() {
		return approveStr;
	}
	public void setApproveStr(String approveStr) {
		this.approveStr = approveStr;
	}
	
	
	

}
