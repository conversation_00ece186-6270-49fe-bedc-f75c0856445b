package com.jsdz.www.quartz;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.log4j.Logger;
import org.junit.Test;

public class TestString {
	
	
	private final Logger logger = Logger.getLogger(this.getClass());
	
	@Test
	public void test05(){
		
		final String formatter = "yyyy-MM-dd 00:00:00";
		final String[] fomatters = new String[] {"yyyy-MM-dd HH:mm:ss"};
		Calendar time = Calendar.getInstance();
		String endTimeStr = DateFormatUtils.format(time, formatter);
		time.add(Calendar.DAY_OF_MONTH, -1);
		String startTimeStr = DateFormatUtils.format(time, formatter);
		
		System.out.println("====time==="+time);
		System.out.println("====endTimeStr==="+endTimeStr);
		System.out.println("====startTimeStr==="+startTimeStr);
	}
	
	@Test
	public void test04(){
		
		List<String> list = new ArrayList<>();
		
		list.add("111");
		list.add("222");
		list.add("333");
		
		String next = list.iterator().next();
		
		System.out.println(next);
		
		
		
	}
	
	
	@Test
	public void test03(){
		
//		DocumentCate
		
		/*Long date= null;
		
		long num = new Date().getTime() + date;*/
		
	/*	Integer num = null;
		
		int count = num + 1;*/
		
	/*	Map num = new HashMap<>();
		int counts1 = (int) num.get(0);
		System.out.println(counts1);*/
		
	/*	int[] numbers = {1,2,5,6,8};
		for (int i : numbers) {
			int counts = (int) num.get(i);
			num.put(i, counts++);
		}*/
	}
	
	@Test
	public void test01(){
		String replaces = StringUtils.replace("2018-03-15 00:01:58.0", "-", "");
		String replacess = StringUtils.replace(replaces, " ", "");
		String replacesss = StringUtils.replace(replacess,":", "");
		String replacessss = StringUtils.replace(replacesss,".0","");
		System.out.println(replacessss);
	}
	
	@Test
	public void test02(){
		 Date currentTime = new Date();
		 SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");  
		 String dateString = formatter.format(currentTime);  
		 System.out.println(dateString);
		 
	}
	
	
	
	
	

}
