package com.jsdz.digitalevidence.attendance.bean.vomodel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AttendanceInfoBean implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 执行k考勤任务的id
     */
    private Long attendanceTaskId;

    /**
     * 单位
     */
    private Long attendanceOrgId;

    /**
     * 考勤人
     */
    private String attendanceUserId;

    /**
     * 考勤设备
     */
    private Long deviceId;

    /**
     * 考勤设备
     */
    private String deviceCode;

    /**
     * 是否允许自动打卡（0允许，1不允许）,如果任务不允许自动打卡这字段失效
     */
    private String isAutomatic;

    /**
     * 离考勤范围多少米可打卡,如果不在任务表范围内这字段失效
     */
    private String attendanceScopeRice;

    /**
     * 可提前多少分钟打卡,如果不在任务表范围内这字段失效
     */
    private String advanceByMinutes;

    /**
     * 最后一次打卡时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date lastAattendanceTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date updateTime;


    /**
     * 任务相关
     */
    private String attendanceName;
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date attendanceTime;
    private Integer advanceTaskByMinutes;
    private Integer isTaskAutomatic;
    private Double attendanceTaskScopeRice;
    private String attendanceDescribe;


    /**
     * 单位相关
     */
    private String orgName;
    private String orgPath;
    private String orgCode;
    private String orgId;




}
