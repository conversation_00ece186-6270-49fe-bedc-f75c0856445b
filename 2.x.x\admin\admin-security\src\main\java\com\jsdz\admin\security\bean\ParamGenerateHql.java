package com.jsdz.admin.security.bean;

import java.util.ArrayList;
import java.util.List;

import com.jsdz.admin.security.model.OperateLog;
import com.jsdz.core.Page;
import com.jsdz.core.SortBean;

import net.sf.json.JSONArray;

/**
 * 
 * @类名: ParamGenerateHql
 * @说明: 拼接Hql 的where语句
 *
 * @author: kenny
 * @Date	2017年5月11日上午11:46:35
 * 修改记录：
 *
 * @see
 */
public class ParamGenerateHql {
	
	public String generateHql(Page page,List<String> parLst,String querySql,String totalSql){
		
		StringBuilder qs = new StringBuilder();
		StringBuilder ts = new StringBuilder();
		

		for (int i=0;i<parLst.size() ;i++){
			if (i==0){
				qs.append(" WHERE ");
				ts.append(" WHERE ");
			}

			if (i > 0){
				qs.append(" AND ");
				ts.append(" AND ");
			}
	
			String paramsParse = parLst.get(i);
			qs.append(paramsParse);
			ts.append(paramsParse);
		}
		
		querySql = querySql + qs.toString();
		totalSql = totalSql + ts.toString();

		page.setTotalQueryString(totalSql.toString());
		return querySql.toString();
	}	 
	
	//判断某个字符串出现的次数
	public  int sameCount(String text,String sub){  
        int count =0, start =0;  
        while((start=text.indexOf(sub,start))>=0){  
            start += sub.length();  
            count ++;  
        }  
        return count;  
    }  
	
	//取完整的日期格式(开始日期)
	public String getFullBeginDate(String str){
		String result = str;
		if (sameCount(result,":")==0)
			result = result + " 00:00:00";//只有日期
		else if (sameCount(result,":")==1)
			result = result + ":00";//只有日期+时分
		return result;

	}
	//取完整的日期格式(结束日期)
	public String getFullEndDate(String str){
		String result = str;
		if (sameCount(result,":")==0)
			result = result + " 23:59:59";//只有日期
		else if (sameCount(result,":")==1)
			result = result + ":59";//只有日期+时分
		return result;

	}
}
