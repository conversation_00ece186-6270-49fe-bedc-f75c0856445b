package com.jsdz.digitalevidence.archives.bean;

import java.io.Serializable;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-09
 */
public class EquipmentCount implements Serializable {
	
	 private static final long serialVersionUID = 1L;

	    private Long id;

	    private String orgCode;

	    private String orgName;

	    private String path;

	    private Long parentId;

	    private Long siteNumOne;

	    private Long siteNum;

	    private Long storageNumOne;

	    private Long storageNum;

	    private Long employeesNumOne;

	    private Long employeesNum;

	    private Long recorderNumOne;

	    private Long recorderNum;

	    private String pblNum;

	    private String pblNumOne;

		public Long getId() {
			return id;
		}

		public void setId(Long id) {
			this.id = id;
		}

		public String getOrgCode() {
			return orgCode;
		}

		public void setOrgCode(String orgCode) {
			this.orgCode = orgCode;
		}

		public String getOrgName() {
			return orgName;
		}

		public void setOrgName(String orgName) {
			this.orgName = orgName;
		}

		public String getPath() {
			return path;
		}

		public void setPath(String path) {
			this.path = path;
		}

		public Long getParentId() {
			return parentId;
		}

		public void setParentId(Long parentId) {
			this.parentId = parentId;
		}

		public Long getSiteNumOne() {
			return siteNumOne;
		}

		public void setSiteNumOne(Long siteNumOne) {
			this.siteNumOne = siteNumOne;
		}

		public Long getSiteNum() {
			return siteNum;
		}

		public void setSiteNum(Long siteNum) {
			this.siteNum = siteNum;
		}

		public Long getStorageNumOne() {
			return storageNumOne;
		}

		public void setStorageNumOne(Long storageNumOne) {
			this.storageNumOne = storageNumOne;
		}

		public Long getStorageNum() {
			return storageNum;
		}

		public void setStorageNum(Long storageNum) {
			this.storageNum = storageNum;
		}

		public Long getEmployeesNumOne() {
			return employeesNumOne;
		}

		public void setEmployeesNumOne(Long employeesNumOne) {
			this.employeesNumOne = employeesNumOne;
		}

		public Long getEmployeesNum() {
			return employeesNum;
		}

		public void setEmployeesNum(Long employeesNum) {
			this.employeesNum = employeesNum;
		}

		public Long getRecorderNumOne() {
			return recorderNumOne;
		}

		public void setRecorderNumOne(Long recorderNumOne) {
			this.recorderNumOne = recorderNumOne;
		}

		public Long getRecorderNum() {
			return recorderNum;
		}

		public void setRecorderNum(Long recorderNum) {
			this.recorderNum = recorderNum;
		}

		public String getPblNum() {
			return pblNum;
		}

		public void setPblNum(String pblNum) {
			this.pblNum = pblNum;
		}

		public String getPblNumOne() {
			return pblNumOne;
		}

		public void setPblNumOne(String pblNumOne) {
			this.pblNumOne = pblNumOne;
		}

		public static long getSerialversionuid() {
			return serialVersionUID;
		}
	    
	    
}
