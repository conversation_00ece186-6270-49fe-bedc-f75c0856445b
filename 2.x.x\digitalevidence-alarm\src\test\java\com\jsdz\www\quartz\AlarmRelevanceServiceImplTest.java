package com.jsdz.www.quartz;

import java.util.HashMap;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.alarm110.bean.docAlarmCaseInfoBean;
import com.jsdz.digitalevidence.alarm.alarm110.service.impl.AlarmReleServiceImpl;
import com.jsdz.digitalevidence.alarm.alarm110.service.impl.AlarmRelevanceServiceImpl;
import com.jsdz.digitalevidence.alarm.alarm110.service.impl.CaseRelevanceServiceImpl;
import com.jsdz.digitalevidence.document.model.ImportantLevel;
import com.jsdz.utils.DateTimeUtils;

/**
 *  警情和视频自动绑定测试
 * <AUTHOR>
 *
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { 
		"/testApplicationContext-common.xml",
		//		"/testApplicationContext-quartz.xml",		
		"/testApplicationContext-documenttype.xml",
		"/testApplicationContext-dynsql.xml",
		"/testApplicationContext-reportengine.xml",
		"/testApplicationContext-scheduler.xml",
		"/testApplicationContext-storage.xml",
		"/testApplicationContext-redis.xml"
})
public class AlarmRelevanceServiceImplTest {

	@Autowired
	private AlarmRelevanceServiceImpl alarmRelevanceServiceImpl;

	@Autowired
	private CaseRelevanceServiceImpl caseRelevanceServiceImpl;

	@Autowired
	private AlarmReleServiceImpl alarmReleServiceImpl;

	@Test
	public void testAlarmBoundVideo() {

		System.out.println("对象为==="); 
		while(true){

		}
	}

	@Test
	public void testAlarmBouVid(){
		alarmRelevanceServiceImpl.alarmBouVid();
	}

	@Test
	public void  testCaseBouAlar(){

//		caseRelevanceServiceImpl.caseBouAlar();
	}

	@Test
	public void testAlaBouVid(){

		//		System.out.println("==="+alarmReleServiceImpl);
//		alarmReleServiceImpl.alaBouVid();
	}
	/**
	 * 
	 * @说明：视频关联分页查询
	 *
	 * <AUTHOR>
	 *
	 */
	@Test
	public void TetsDocAramCase(){
		Page<docAlarmCaseInfoBean> page = new Page<docAlarmCaseInfoBean>();
		page.setPage(1);
		page.setPageSize(3);
		//
		Map<String, Object> kvs = new HashMap<String, Object>();
		// 组织
		/*TreeField tr = new TreeField();
		tr.setPath(null);
		tr.setIncludeSub(true);
		kvs.put("orgId", tr);*/
		// );
		kvs.put("alarmCode", "5201002018031500041310085019998027693300");
		/*kvs.put("impLevel", ImportantLevel.GENERAL);*/
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		// 参数
		page =  alarmReleServiceImpl.searchdocAlarmCaseInfoBean(page, paramNames, values);
		//
		System.out.println("------------"+page.getRows().get(0).getAlarmCode());
	}
}
