package com.jsdz.digitalevidence.task.bean;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;

public class TaskProcessPageBean {

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 任务类别 1、抓拍
     */
    private Integer cate;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 任务执行状态 0、未接收 1、已接收 2、待审核 3、已完成 4、审核不通过 5、延期'
     */
    private Integer status;

    /**
     * 警号
     */
    private String policeCode;

    /**
     * 执法仪编号
     */
    private String code;

    /**
     * 权限等级
     */
    private PerLvlBean perLvlBean;

    /**
     * 分页参数
     */
    private Page page;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getCate() {
        return cate;
    }

    public void setCate(Integer cate) {
        this.cate = cate;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getPoliceCode() {
        return policeCode;
    }

    public void setPoliceCode(String policeCode) {
        this.policeCode = policeCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public PerLvlBean getPerLvlBean() {
        return perLvlBean;
    }

    public void setPerLvlBean(PerLvlBean perLvlBean) {
        this.perLvlBean = perLvlBean;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }
}
