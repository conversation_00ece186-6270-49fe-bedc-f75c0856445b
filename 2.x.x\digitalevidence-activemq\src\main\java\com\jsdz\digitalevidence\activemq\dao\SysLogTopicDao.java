package com.jsdz.digitalevidence.activemq.dao;

/**
 *
 * @类名: SysLogTopicDao
 * @说明:
 * @author: kenny
 * @Date 2018-03-15 14:58:15
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.activemq.model.SysLogTopic;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface SysLogTopicDao extends GenericORMEntityDAO<SysLogTopic,Long> {

	//新增
	public void addSysLogTopic(SysLogTopic sysLogTopic);

	//修改
	public void updateSysLogTopic(SysLogTopic sysLogTopic);

	//删除
	public void deleteSysLogTopic(SysLogTopic sysLogTopic);

	//按id查询,结果是游离状态的数据
	public SysLogTopic findSysLogTopicById(Long id);

	//按id查询
	public SysLogTopic locateSysLogTopicById(Long id);

	//单个查询
	public SysLogTopic findSysLogTopicByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<SysLogTopic> findAllSysLogTopics();

	//列表查询
	public List<SysLogTopic> findSysLogTopicsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<SysLogTopic> findSysLogTopicsOnPage(Page<SysLogTopic> page,String queryStr,String[] paramNames,Object[] values);

	//执行指定的HQL文件
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);

}

