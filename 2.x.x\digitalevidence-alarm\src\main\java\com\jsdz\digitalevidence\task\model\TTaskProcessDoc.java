package com.jsdz.digitalevidence.task.model;

import java.io.Serializable;

/**
 * 分发任务附件表(TTaskProcessDoc)实体类
 *
 * <AUTHOR>
 * @since 2023-09-28 11:43:30
 */
public class TTaskProcessDoc implements Serializable {
    private static final long serialVersionUID = -36103117627541173L;

    private Long id;
/**
     * 分发任务id
     */
    private Long processId;

/**
     * 抓拍照片场景图标记
     */
    private String sceneMark;

    /**
     * 文档路径
     */
    private String docUri;

    /**
     * 执法仪id
     */
    private Long recorderId;

    /**
     * 存储id
     */
    private Long storageId;

    /**
     * 警员id
     */
    private Long employeesId;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 抓拍时间
     */
    private String captureTime;

/**
     * 是否删除 0、未删除 1、已删除
     */
    private Byte isDeleted;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProcessId() {
        return processId;
    }

    public void setProcessId(Long processId) {
        this.processId = processId;
    }

    public Byte getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Byte isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getSceneMark() {
        return sceneMark;
    }

    public void setSceneMark(String sceneMark) {
        this.sceneMark = sceneMark;
    }

    public String getDocUri() {
        return docUri;
    }

    public void setDocUri(String docUri) {
        this.docUri = docUri;
    }

    public Long getRecorderId() {
        return recorderId;
    }

    public void setRecorderId(Long recorderId) {
        this.recorderId = recorderId;
    }

    public Long getStorageId() {
        return storageId;
    }

    public void setStorageId(Long storageId) {
        this.storageId = storageId;
    }

    public Long getEmployeesId() {
        return employeesId;
    }

    public void setEmployeesId(Long employeesId) {
        this.employeesId = employeesId;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getCaptureTime() {
        return captureTime;
    }

    public void setCaptureTime(String captureTime) {
        this.captureTime = captureTime;
    }
}

