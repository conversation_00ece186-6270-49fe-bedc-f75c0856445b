package com.jsdz.digitalevidence.analysis.dao.impl;
/**
 * 
 * @类名: DocDailySqlDaoImpl
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月31日上午8:50:39
 * 修改记录：
 *
 * @see
 */

import java.util.Collection;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.analysis.bean.DocDailyDto;
import com.jsdz.digitalevidence.analysis.dao.DocDailyDao;
import com.jsdz.digitalevidence.analysis.model.DocDaily;

public class DocDailySqlDaoImpl extends HibernateDaoSupport implements DocDailyDao{

	@Override
	public void flush() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void initialize(Object proxy) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public DocDaily load(Long id) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public DocDaily merge(Object object) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean contains(Object entity) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public void evict(Object entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public DocDaily get(Long pk) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public DocDaily get(DocDaily t) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public DocDaily insert(DocDaily entity) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void insertCascade(DocDaily entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void update(DocDaily entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void updateCascade(DocDaily entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void saveOrUpdate(DocDaily entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void saveOrUpdateCascade(DocDaily entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void delete(DocDaily entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void deleteCascade(DocDaily entity) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public List<DocDaily> find(DocDaily entity) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Page<DocDaily> pageQuerySQL(Page<DocDaily> page, String hql, String[] paramNames, Object[] values) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Page<DocDaily> pageQueryHQL(Page<DocDaily> page, String hql, String[] paramNames, Object[] values) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Page<DocDaily> pageQueryHQL(Page<DocDaily> page, String hql, Object[] values) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Page<DocDaily> pageQueryNamedSQL(Page<DocDaily> page, String sqlName) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Page<DocDaily> pageQueryNamedSQL(Page<DocDaily> page, String sqlName, String[] paramNames, Object[] values) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Page<DocDaily> pageQueryNamedHQL(Page<DocDaily> page, String sqlName) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Page<DocDaily> pageQueryNamedHQL(Page<DocDaily> page, String sqlName, String[] paramNames, Object[] values) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void batchInsert(List<DocDaily> list) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void batchUpdate(List<DocDaily> list) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void batchDelete(List<DocDaily> list) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void addDocDaily(DocDaily docDaily) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void updateDocDaily(DocDaily docDaily) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void deleteDocDaily(DocDaily docDaily) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public DocDaily findDocDailyById(Long id) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public DocDaily locateDocDailyById(Long id) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public DocDaily findDocDailyByCondition(String queryStr, String[] paramNames, Object[] values) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<DocDaily> findAllDocDailys() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<DocDaily> findDocDailysByCondition(String queryStr, String[] paramNames, Object[] values) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public Page<DocDaily> findDocDailysOnPage(Page<DocDaily> page, String queryStr, String[] paramNames,
			Object[] values) {
		// TODO Auto-generated method stub
		return null;
	}

	public List<DocDailyDto> findDocDailysBySql(final String sql,final String[] paramNames,final Object[] values ){
		List<DocDailyDto> data = getHibernateTemplate().execute(new HibernateCallback<List<DocDailyDto>>() {
			@SuppressWarnings("unchecked")
			public List<DocDailyDto> doInHibernate(Session session) throws HibernateException {
				Query queryObject = session.createSQLQuery(sql);
				// 设置实体转换
				SQLQuery sqlQuery = (SQLQuery) queryObject;
				sqlQuery.addEntity(DocDailyDto.class);
				//
				if (values != null) {
					for (int i = 0; i < values.length; i++) {
						applyNamedParameterToQuery(queryObject, paramNames[i], values[i]);
					}
				}
				return queryObject.list();
			}
		});
		return data;

	}
	
	@SuppressWarnings("rawtypes")
	protected void applyNamedParameterToQuery(Query queryObject, String paramName, Object value)
			throws HibernateException {

		if (value instanceof Collection) {
			queryObject.setParameterList(paramName, (Collection) value);
		} else if (value instanceof Object[]) {
			queryObject.setParameterList(paramName, (Object[]) value);
		} else if (value instanceof Enum) {
			int ordinal = ((Enum<?>) value).ordinal();
			queryObject.setParameter(paramName, ordinal);
		} else {
			queryObject.setParameter(paramName, value);
		}
	}

	@Override
	public Integer executeHql(String StringHql, String[] paramNames, Object[] values) {
		// TODO Auto-generated method stub
		return null;
	}	
}
