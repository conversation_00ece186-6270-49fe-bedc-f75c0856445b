package com.jsdz.digitalevidence.alarm.alarm110.service;

/**
 * 
 * @类名: AlarmRelationService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-08-29 19:53:45
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmRelationBean;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;

public interface AlarmRelationService {

	//新增
	public AjaxResult addAlarmRelation(AlarmRelation alarmRelation);

	//修改
	public AjaxResult updateAlarmRelation(AlarmRelation alarmRelation);

	//删除
	public AjaxResult deleteAlarmRelation(AlarmRelation alarmRelation);

	//按id查询,结果是游离状态的数据
	public AlarmRelation findAlarmRelationById(Long id);

	//按id查询
	public AlarmRelation locateAlarmRelationById(Long id);

	//单个查询
	public AlarmRelation findAlarmRelationByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmRelation> findAllAlarmRelations();

	//列表查询
	public List<AlarmRelation> findAlarmRelationsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmRelation> findAlarmRelationsOnPage(Page<AlarmRelation> page, String queryStr,String[] paramNames,Object[] values);

}

