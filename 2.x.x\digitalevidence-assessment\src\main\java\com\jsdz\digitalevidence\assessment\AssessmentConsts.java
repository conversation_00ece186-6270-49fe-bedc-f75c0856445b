package com.jsdz.digitalevidence.assessment;

/**
 * @类名: AssessmentConsts
 * @说明: 常量定义
 *
 * <AUTHOR>
 * @Date	 2017年8月18日 下午5:19:41
 * 修改记录：
 *
 * @see 	 
 */
public interface AssessmentConsts {
	
	/** 考核周期key*/
	public static final String Assessment_Cycle_Configuration_Key = "assessmentCycleConfiguration";
	
	/** 动态sql/hql key*/
	// 拍摄考核统计
	public static final String SQL_KEY_Sum_Of_ShootingAssessment = "sumOfShootingAssessment";
	// 当月考核概况
	public static final String SQL_KEY_Sum_Of_ShootingAssessment_All = "sumOfShootingAssessmentAll";
	// 往月考核概况
	public static final String SQL_KEY_Sum_Of_Pass_ShootingAssessment_All = "sumOfPassShootingAssessmentAll";
	// 查询考核资料
	public static final String SQL_KEY_Query_Assessment_Docs = "queryAssessmentDocs";
	// 打开考核
	public static final String SQL_KEY_Open_Assessment = "openAssessment";
	// 打开推送视频考核
	public static final String SQL_KEY_Open_Assessment_Pushing = "openAssessmentPushing";
	
	// 警员报告查询
	public static final String SQL_KEY_Query_Assessment_Report = "queryAssessmentReport";
	// 警员报告查询-简要
    public static final String SQL_KEY_Query_Assessment_Report_Brief = "queryAssessmentReportBrief";
    // 警员完整报告Bean查询
    public static final String SQL_KEY_Query_Assessment_Report_Bean = "findAssessmentReportBean";
    
    // 预警报告查询
    public static final String SQL_KEY_Query_Assessment_Alert = "queryAssessmentAlert";
    
    // 预警报告查询
    public static final String SQL_KEY_Search_Assessment_Alert = "searchAssessmentAlert";
    
    // 搜索
    public static final String SQL_KEY_Search_Assessment_Pushing_Doc = "searchAssessmentPushingDoc";
    
    // 月预警报告规则
    public static final String RULE_Assessment_ALert = "assessmentAlert";
    // 周预警报告规则
    public static final String RULE_Assessment_WeeklyALert = "assessmentWeeklyAlert";
    
    

}
