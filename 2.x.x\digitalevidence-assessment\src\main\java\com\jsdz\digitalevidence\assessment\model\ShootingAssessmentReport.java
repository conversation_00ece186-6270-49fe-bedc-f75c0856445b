/**
 * 
 */
package com.jsdz.digitalevidence.assessment.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.AbstractDTO;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: ShootingAssessmentReport
 * @说明: 拍摄考核报告
 *        
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午5:46:40
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingAssessmentReport extends AbstractDTO {
	
	/** Id*/
	private Long reportId;
	/** 被考核人*/
	private Employees police;
	/** 考核时段，开始、结束*/
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date startDate;
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date endDate;
	/** 报告生成日期*/
	@JsonFormat(pattern=DateTimeUtils.defaultPatten)
	private Date submitDate;
	/** 拍摄统计*/
	private ShootingSummary sumOfShooting;
	/** 拍摄考核统计*/
	private ShootingAssessmentSummary sumOfShootingAssessment;
	/** 评语*/
	private String comments;
	/** 是否已冻结*/
	private Boolean frozen;
	/** 报告类型*/
	private ReportType type;
	
	public Employees getPolice() {
		return police;
	}
	public void setPolice(Employees police) {
		this.police = police;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public ShootingSummary getSumOfShooting() {
		return sumOfShooting;
	}
	public void setSumOfShooting(ShootingSummary sumOfShooting) {
		this.sumOfShooting = sumOfShooting;
	}
	public ShootingAssessmentSummary getSumOfShootingAssessment() {
		return sumOfShootingAssessment;
	}
	public void setSumOfShootingAssessment(ShootingAssessmentSummary sumOfShootingAssessment) {
		this.sumOfShootingAssessment = sumOfShootingAssessment;
	}
	public Date getSubmitDate() {
		return submitDate;
	}
	public void setSubmitDate(Date submitDate) {
		this.submitDate = submitDate;
	}
	public Long getReportId() {
		return reportId;
	}
	public void setReportId(Long reportId) {
		this.reportId = reportId;
	}
	public Boolean isFrozen() {
		return frozen;
	}
	public Boolean getIsFrozen() {
		return frozen;
	}
	public void setFrozen(Boolean frozen) {
		this.frozen = frozen;
	}
	public void setIsFrozen(Boolean frozen) {
		this.frozen = frozen;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public ReportType getType() {
		return type;
	}
	public void setType(ReportType type) {
		this.type = type;
	}
	public Boolean getFrozen() {
		return frozen;
	}

}
