package com.jsdz.digitalevidence.attendance.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.param.AttendanceCheckParam;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceCheckBean;
import com.jsdz.digitalevidence.attendance.mapper.AttendanceCheckMapper;
import com.jsdz.digitalevidence.attendance.model.AttendanceCheckModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceInfoModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceScopeModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceTaskModel;
import com.jsdz.digitalevidence.attendance.service.IAttendanceCheckService;
import com.jsdz.digitalevidence.attendance.service.IAttendanceInfoService;
import com.jsdz.digitalevidence.attendance.service.IAttendanceScopeService;
import com.jsdz.digitalevidence.attendance.service.IAttendanceTaskService;
import com.jsdz.digitalevidence.utils.DateUtil;
import com.jsdz.digitalevidence.utils.*;
import com.jsdz.digitalevidence.utils.response.ResponseUtil;
import com.jsdz.digitalevidence.utils.response.Result;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.js.transfer.utils.Minor;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
// SqlUtils<AttendanceCheckModel>
@Service
public class AttendanceCheckServiceImpl  extends ServiceImpl<AttendanceCheckMapper, AttendanceCheckModel> implements IAttendanceCheckService {
    private final Logger logger = Logger.getLogger(this.getClass());
    @Resource
    private AttendanceCheckMapper attendanceCheckMapper;
    @Resource
    private IAttendanceInfoService attendanceInfoService;

    @Resource
    private IAttendanceTaskService attendanceTaskService;
    @Resource
    private IAttendanceScopeService attendanceScopeService;
    //设置表名
    private String tableName = "sys_attendance_check";


    SqlUtils sqlUtils = new SqlUtils<AttendanceCheckModel>();
    @Override
    public Page<AttendanceCheckBean> getDatas(AttendanceCheckParam param, PerLvlBean permLvl) {
        Page<AttendanceCheckBean> page = param.getPage();
        String countSql = " select count(1) countSum ";
        String dataSql  = " select t.*,o.orgName,o.path as orgPath,o.orgCode,o.Id as orgId ";
        String whereSql = " from "+tableName+" t LEFT JOIN sys_attendance_info n on t.attendanceInfoId = n.id  LEFT JOIN admin_organization o on n.attendanceOrgId = o.id where 1=1";
        //分页
        String limitSql = " limit "+param.getPage().getOffset()+" ,"+param.getPage().getPageSize();
        //排序条件，暂时无需求
        String orderSql = " order by t.createTime desc";
        //页面没指定单位权限，需使用用户权限
        if(StringUtils.isBlank(param.getOrgPath())){
            //whereSql = userLevel( whereSql,permLvl);
        }else if(StringUtils.isNoBlank(param.getOrgPath()) & StringUtils.isNoBlank(param.getIncludeSub())){
            String includeSub = param.getIncludeSub();
            whereSql+=" and o.path like '"+param.getOrgPath() +""+ ("1".equals(includeSub)?"%":"")  +"'";
        }
        if(StringUtils.isNoEmpty(param.getCreateTimeStart()) ){
            whereSql+=" and t.attendanceTime>= '"+DateUtil.dateStr(param.getCreateTimeStart())+"'";
        }
        if(StringUtils.isNoEmpty(param.getCreateTimeEnd())){
            whereSql+=" and t.attendanceTime<= '"+DateUtil.dateStr(param.getCreateTimeEnd())+"'";
        }

        //其他调教查询补充
        logger.info(dataSql + whereSql + orderSql + limitSql );
        List<AttendanceCheckBean> datas = attendanceCheckMapper.getDatas(dataSql + whereSql + orderSql + limitSql );
        Integer count = attendanceCheckMapper.getCount(countSql + whereSql  + orderSql);
        page.setRows(datas);
        page.setTotal(count);
        return page;
    }

    @Override
    public void saveData(AttendanceCheckModel model, PerLvlBean permLvl) {
        attendanceCheckMapper.insert(model);
        //attendanceCheckMapper.saveData(saveSql(model,tableName));
    }


    @Override
    public void updateDataById(AttendanceCheckModel model, PerLvlBean permLvl) {
        attendanceCheckMapper.updateById(model);
        //attendanceCheckMapper.updateData(updateSql(model,tableName,"id"));
    }

    @Override
    public AttendanceCheckModel getById(AttendanceCheckModel model, PerLvlBean permLvl) {
        return attendanceCheckMapper.selectById(model.getId());
        //return attendanceCheckMapper.getById(getByIdSql(model,tableName,"id"));
    }

    @Override
    public void deleteById(AttendanceCheckModel model, PerLvlBean permLvl) {
//        attendanceCheckMapper.deleteById(model.getId());
//        attendanceCheckMapper.deleteById(model);
        attendanceCheckMapper.deleteById(sqlUtils.deleteByIdSql(model,tableName,"id"));
    }

    @Override
    public List<AttendanceCheckModel> getDataByTime(List<Long> recorderId) {
        try {
            QueryWrapper<AttendanceCheckModel> wrapper = new QueryWrapper<>();
            wrapper.in("attendanceInfoId",recorderId);
            wrapper.gt("createTime",Minor.getDate0());
            return attendanceCheckMapper.selectList(wrapper);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 自动打卡
     *
     * @param deviceCode
     * @param currentTime
     * @param longitude
     * @param latitude
     */
    @Override
    public void attendanceAutomaticCheck(String deviceCode, String currentTime, String longitude, String latitude) {
        //查询当天未打卡并且允许自动打卡的任务的全部打卡任务
        List<AttendanceInfoModel> infos = attendanceInfoService.getInfos(deviceCode);logger.error(deviceCode+"-  设备编号 查询info完成"+infos.size());
        for (AttendanceInfoModel attendanceInfo : infos){
            if(StringUtils.isNoBlank(attendanceInfo.getLastAattendanceTime())&& attendanceInfo.getLastAattendanceTime().after(DateUtil.getDate000()))
               continue;
            Result result = SaveAttendanceAutomaticCheck(attendanceInfo, currentTime, longitude, latitude);
            Integer code = result.getCode();
            if(code.equals(200)){
                logger.info(deviceCode+"-   打卡成功");
            }
        }
    }

    /**
     * 自动打卡
     * @param attendanceInfo
     * @param currentTime
     * @param longitude
     * @param latitude
     * @return
     */
    public Result SaveAttendanceAutomaticCheck(AttendanceInfoModel attendanceInfo, String currentTime, String longitude, String latitude) {
            try {
                AttendanceTaskModel byId = attendanceTaskService.getById(attendanceInfo.getAttendanceTaskId());
                if(StringUtils.isEmpty(byId)||byId.getIsDelete()!=0)
                    return ResponseUtil.error(500,"打卡失败,任务不存在",attendanceInfo);
                Integer attendanceTime = Integer.parseInt(byId.getAttendanceTime());//打卡最后结束时间
                Integer advanceByMinutes = byId.getAdvanceByMinutes();//打卡前分钟
                Integer subMinutes = attendanceTime - advanceByMinutes;//最早打卡时间
               // Date subMinutes = DateUtil.dateSubMinutes(attendanceTime, advanceByMinutes);//最早打卡时间
                //Date parseDate = DateUtils.parseDate(currentTime);//打卡时间
                int minuteOfDate = DateUtil.calculateMinuteOfDate(DateUtil.parseDate(currentTime));//打卡时间

                if(minuteOfDate>=subMinutes &&  minuteOfDate<=attendanceTime){//时间满足打卡需求
                    List<AttendanceScopeModel> datas = attendanceScopeService.getDatas(attendanceInfo.getAttendanceTaskId());
                    if(StringUtils.isEmpty(datas)) return ResponseUtil.error(500,"打卡失败,自动打卡未设置坐标范围",attendanceInfo);
                    double[] dot = {Double.valueOf(longitude), Double.valueOf(latitude)};
                    double[][] dots = new double[datas.size()+1][2];
                    for (int i = 0; i < datas.size(); i++) {
                        dots[i][0] = datas.get(i).getLongitude();
                        dots[i][1] = datas.get(i).getLatitude();
                    }
                    //自定义初始点，形成闭环
                    dots[datas.size()][0] = datas.get(0).getLongitude();
                    dots[datas.size()][1] = datas.get(0).getLatitude();
                    //计算是否在打卡范围
                    boolean point = GeoDistanceCalculator.geometryContainsPoint(dot, dots);
                    //是否满足打卡任务
                    if(point){
                        AttendanceCheckModel attendanceCheckModel = new AttendanceCheckModel();
                        attendanceCheckModel.setLongitude(longitude);
                        attendanceCheckModel.setLatitude(latitude);
                        attendanceCheckModel.setAttendanceInfoId(attendanceInfo.getId());
                        attendanceCheckModel.setAttendanceTime(DateUtil.parseDate(currentTime));
                        attendanceCheckModel.setAttendanceDescribe("自动打卡");
                        attendanceCheckModel.setIsAutomatic(0);
                        attendanceCheckModel.setCreateTime(new Date());
                        attendanceCheckModel.setUpdateTime(new Date());
                        attendanceCheckMapper.insert(attendanceCheckModel);
                        attendanceInfo.setLastAattendanceTime( DateUtil.parseDate(currentTime));
                        attendanceInfoService.updateById(attendanceInfo);
                        return ResponseUtil.ok(200,"打卡成功",attendanceCheckModel);
                    }
                }
            }catch (Exception e){
                return ResponseUtil.error(500,"打卡失败",e.getMessage());
            }

        return ResponseUtil.error(500,"打卡失败",attendanceInfo);

    }

}







