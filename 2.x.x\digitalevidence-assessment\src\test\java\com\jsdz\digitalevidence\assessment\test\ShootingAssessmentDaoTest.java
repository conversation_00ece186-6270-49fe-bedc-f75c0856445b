package com.jsdz.digitalevidence.assessment.test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.dao.EmployeesDao;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertBean;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.dao.AssessmentAlertDao;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPushDocDao;
import com.jsdz.digitalevidence.assessment.dao.ShootingAssessmentDao;
import com.jsdz.digitalevidence.assessment.dao.ShootingAssessmentReportDao;
import com.jsdz.digitalevidence.assessment.model.AssessmentCate;
import com.jsdz.digitalevidence.assessment.model.ShootingAngleLevel;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessment;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.ShootingContentLevel;
import com.jsdz.digitalevidence.assessment.model.ShootingResolutionLevel;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.digitalevidence.assessment.model.push.AssessmentPushDoc;
import com.jsdz.digitalevidence.document.dao.DocumentDao;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.utils.BeanUtils;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: ShootingAssessmentDaoTest
 * @说明: 拍摄考核Dao单元测试
 *
 * <AUTHOR>
 * @Date	 2017年4月24日 下午2:00:03
 * 修改记录：
 *
 * @see 	 
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations={
		"/testApplicationContext-common.xml",
		"/testApplicationContext-cycle.xml",
		"/testApplicationContext-dynsql.xml",
		"/testApplicationContext-scheduler.xml",
		"/testApplicationContext-reportengine.xml",
		"/testApplicationContext-documenttype.xml"})
public class ShootingAssessmentDaoTest {

	/**拍摄考核dao*/
	@Autowired
	private ShootingAssessmentDao saDao;
	@Autowired
	private ShootingAssessmentReportDao reportDao;
	/** */
	@Autowired
	private EmployeesDao empDao;
	/** */
	@Autowired
	private DocumentDao docDao;
	@Autowired
	private AssessmentAlertDao alertDao;
	@Autowired
	private AssessmentPushDocDao pushDocDao;
	
	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
	}
	
	// 增加拍摄考核
	@Test
	@Transactional
	public void testAddShootingAssessment() {
		// 考核员
		Employees inspector = empDao.get(10L);
		// 资料
		Document doc = docDao.get(152L);
		//
		ShootingAssessment sa = new ShootingAssessment();
		sa.setInspector(inspector);
		// 考核日期
		sa.setAssessDate(new Date());
		// 按要求拍摄
		sa.setAYQPS(true);
		// 考核类型
		sa.setCate(AssessmentCate.NORMAL);
		// 考核资料
		sa.setDocument(doc);
		// 没有违法违纪
		sa.setNoWFWJ(true);
		// 文明规范
		sa.setWMGF(true);
		// 按要求运用语言
		sa.setYYYY(true);
		// 拍摄角度
		sa.setShootingAngle(ShootingAngleLevel.ACCURATE);
		sa.setShootingContent(ShootingContentLevel.ACCURATE);
		sa.setShootingResolution(ShootingResolutionLevel.HIGH);
		//
		saDao.saveOrUpdate(sa);
	}
	
	// 查看拍摄考核
	@Test
	@Transactional
	public void testGetShootingAssessment() {
		ShootingAssessment sa = saDao.get(1L);
		BeanUtils.printBean(sa);
	}
	
	// 增加拍摄考核报告
	@Test
	@Transactional
	public void testAddShootingAssessmentReport() {
		ShootingAssessmentReport report = new ShootingAssessmentReport();
		// 考核开始时间
		Date start = DateTimeUtils.StringToDate("2017-07-05 00:00:00");
		report.setStartDate(start);
		// 考核结束时间
		Date end = DateTimeUtils.StringToDate("2017-08-05 00:00:00");
		report.setEndDate(end);
		// 报告提交时间
		report.setSubmitDate(new Date());
		// 考核目标警员
		Employees police = empDao.locateEmployeesByWorkNumber("000002");
		report.setPolice(police);
		//
		reportDao.saveOrUpdate(report);
	}
	
	// 增加拍摄考核统计
	@Test
	@Transactional
	public void testAddShootingAssessmentSummary() {
		ShootingAssessmentSummary sum = new ShootingAssessmentSummary();
		
	}
	
	// 新建考核预警
	@SuppressWarnings("rawtypes")
	@Test
	@Transactional
	public void testAddShootingAssessmentAlert() {
		AssessmentAlert alert = new AssessmentAlert();
		// 考核目标警员
		Employees police = empDao.locateEmployeesByWorkNumber("041371");
		alert.setPolice(police);
		// 所属周期
		Cycle cycle = new Cycle();
		cycle.setStart(new Date());
		cycle.setEnd(new Date());
		alert.setStartDate(cycle.getStart());
		alert.setEndDate(cycle.getEnd());
		//
		alert.setCreateDate(new Date());
		//
		List<AssessmentAlertItem> items = new ArrayList<AssessmentAlertItem>();
		AssessmentAlertItem<Float> i1 = new AssessmentAlertItem<Float>();
		i1.setItem("预警项1");
		i1.setRef(0.75f);
		i1.setV(0.90f);
		items.add(i1);
		//
		AssessmentAlertItem<Integer> i2 = new AssessmentAlertItem<Integer>();
		i2.setItem("预警项2");
		i2.setRef(100);
		i2.setV(120);
		items.add(i2);
		//
		AssessmentAlertItem<String> i3 = new AssessmentAlertItem<String>();
		i3.setItem("预警项3");
		i3.setRef("xxx");
		i3.setV("yyy");
		items.add(i3);
		//
		alert.setItems(items);
		//
		alertDao.saveOrUpdate(alert);
	}
		
	// 查询考核预警Bean
	@Test
	@Transactional(readOnly=true)
	public void testGetAssessmentAlertBean() {
		AssessmentAlertBean alertBean = alertDao.getAssessmentAlertBean(3L);
		System.out.println(alertBean);
	}
		
	// 推送视频
	@Test
	@Transactional
	public void testAddPushDoc() {
		Document doc = this.docDao.get(152L);
		AssessmentPushDoc pushDoc = new AssessmentPushDoc();
		pushDoc.setDoc(doc);
		//
		Employees police = empDao.get(9L);
		//
		pushDoc.setPolice(police);
		//
		Employees assessor = empDao.locateEmployeesByWorkNumber("000020");
		//
		pushDoc.setAssessor(assessor);
		//
		pushDoc.setHasAssessed(false);
		//
		pushDocDao.saveOrUpdate(pushDoc);
	}
	
	// 
	@Test
	@Transactional
	public void testAddPushDocCreateTime() {
		AssessmentPushDoc pushDoc = pushDocDao.get(3L);
		pushDoc.setCreateTime(new Date());
		pushDocDao.saveOrUpdate(pushDoc);
		
	}
	
	// 查询考核预警Bean
	@Test
	@Transactional(readOnly=true)
	public void testGetAssessmentReport() {
		Date cycleDate = DateTimeUtils.StringToDate("2017-10-05", DateTimeUtils.defaultDatePatten2);
		ShootingAssessmentReport report = reportDao.getShootingAssessmentReport(4L, cycleDate);
		System.out.println(report);
	}
	
}
