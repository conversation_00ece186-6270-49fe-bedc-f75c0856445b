package com.jsdz.digitalevidence.analysis.openapi.dao;

/**
 *
 * @类名: StationInfoDao
 * @说明:
 * @author: kenny
 * @Date 2017-06-09 16:03:51
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.analysis.openapi.model.StationInfo;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface StationInfoDao extends GenericORMEntityDAO<StationInfo,Long> {

	//新增
	public void addStationInfo(StationInfo stationInfo);

	//修改
	public void updateStationInfo(StationInfo stationInfo);

	//删除
	public void deleteStationInfo(StationInfo stationInfo);

	//按id查询,结果是游离状态的数据
	public StationInfo findStationInfoById(Long id);

	//按id查询
	public StationInfo locateStationInfoById(Long id);

	//单个查询
	public StationInfo findStationInfoByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<StationInfo> findAllStationInfos();

	//列表查询
	public List<StationInfo> findStationInfosByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<StationInfo> findStationInfosOnPage(Page<StationInfo> page,String queryStr,String[] paramNames,Object[] values);

}

