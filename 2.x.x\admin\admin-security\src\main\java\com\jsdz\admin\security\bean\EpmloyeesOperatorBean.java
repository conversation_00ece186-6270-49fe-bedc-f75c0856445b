package com.jsdz.admin.security.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.utils.DateTimeUtils;

public class EpmloyeesOperatorBean {
	/**
	 * 
	 * @类名: EpmloyeesOperatorBean
	 * @说明: 警员用户封装类，新增修改警员时修改用户
	 *
	 * @author: kenny
	 * @Date	2017年9月12日下午5:56:00
	 * 修改记录：
	 *
	 * @see
	 */
	
	private Long id;
	private String name; //姓名
	private String male;//性别
	@JsonFormat(pattern=DateTimeUtils.defaultDatePatten2,timezone="GMT+8")
	private Date   birthDate;//性别
	private String idNum;   //身份证号
	private String workNumber;   //工号
	private String mail;    //e_mail
	private String mobile;  //移动电话
	private Integer age;    //年龄    
	private Long departmentId; //部门
	private String departmentName;  //部门名称
	private Long organizationId; //单位（行政单位）
	private String organizationName;  //单位名称
	private Long positionId; //职位ID	
	private String positionName;  
	private Date createTime;  //创建时间
	
	private Long operaotrId;
	private String loginName; //登录名
	private String password; //密码
	private String state;  //状态(1禁用)	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getMale() {
		return male;
	}
	public void setMale(String male) {
		this.male = male;
	}
	public Date getBirthDate() {
		return birthDate;
	}
	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}
	public String getIdNum() {
		return idNum;
	}
	public void setIdNum(String idNum) {
		this.idNum = idNum;
	}
	public String getWorkNumber() {
		return workNumber;
	}
	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}
	public String getMail() {
		return mail;
	}
	public void setMail(String mail) {
		this.mail = mail;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public Integer getAge() {
		return age;
	}
	public void setAge(Integer age) {
		this.age = age;
	}
	public Long getDepartmentId() {
		return departmentId;
	}
	public void setDepartmentId(Long departmentId) {
		this.departmentId = departmentId;
	}
	public String getDepartmentName() {
		return departmentName;
	}
	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}
	public Long getOrganizationId() {
		return organizationId;
	}
	public void setOrganizationId(Long organizationId) {
		this.organizationId = organizationId;
	}
	public String getOrganizationName() {
		return organizationName;
	}
	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}
	public Long getPositionId() {
		return positionId;
	}
	public void setPositionId(Long positionId) {
		this.positionId = positionId;
	}
	public String getPositionName() {
		return positionName;
	}
	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Long getOperaotrId() {
		return operaotrId;
	}
	public void setOperaotrId(Long operaotrId) {
		this.operaotrId = operaotrId;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}

	
	

	
	
}
