package com.jsdz.digitalevidence.task.dao.mapper;

import com.jsdz.digitalevidence.task.model.TTaskDoc;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 任务附件表(TTaskDoc)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-26 15:42:45
 */
public interface TTaskDocMapper {

    /**
     * 查询指定行数据
     *
     * @param tTaskDoc 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<TTaskDoc> queryAllByLimit(TTaskDoc tTaskDoc, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param tTaskDoc 查询条件
     * @return 总行数
     */
    long count(TTaskDoc tTaskDoc);

    /**
     * 新增数据
     *
     * @param tTaskDoc 实例对象
     * @return 影响行数
     */
    int insert(TTaskDoc tTaskDoc);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TTaskDoc> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TTaskDoc> entities);
}

