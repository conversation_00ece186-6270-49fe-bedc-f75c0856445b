package com.jsdz.digitalevidence.activemq.dao;

/**
 *
 * @类名: DataExchangeDao
 * @说明:
 * @author: kenny
 * @Date 2018-03-11 21:58:37
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.activemq.model.DataExchange;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface DataExchangeDao extends GenericORMEntityDAO<DataExchange,Long> {

	//新增
	public void addDataExchange(DataExchange dataExchange);

	//修改
	public void updateDataExchange(DataExchange dataExchange);

	//删除
	public void deleteDataExchange(DataExchange dataExchange);

	//按id查询,结果是游离状态的数据
	public DataExchange findDataExchangeById(Long id);

	//按id查询
	public DataExchange locateDataExchangeById(Long id);

	//单个查询
	public DataExchange findDataExchangeByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DataExchange> findAllDataExchanges();

	//列表查询
	public List<DataExchange> findDataExchangesByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DataExchange> findDataExchangesOnPage(Page<DataExchange> page,String queryStr,String[] paramNames,Object[] values);

	//执行指定的HQL文件
	public Integer exeCommHql(String queryStr, String[] paramNames, Object[] values);

}

