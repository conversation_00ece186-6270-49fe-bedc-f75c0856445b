<?xml version="1.0" encoding="utf-8"?>
<!-- 考核监督-预警动态sql -->
<sqlTemplates>
  <entry>
  	<!-- 查询考核预警 -->
    <string>queryAssessmentAlert</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
		 from AssessmentAlert a
		 where 1=1 {0} {1} {2} {3} {4}
	  </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeId</paramName>
                <t>and a.police.id = :policeId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 1, cycle start -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and a.cycle.start &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and a.cycle.end &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, hasFreeback -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>hasFreeback</paramName>
                <t>and a.feedBack is not null and :hasFreeback = :hasFreeback</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
       <!-- 4, notFreeback -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>notFreeback</paramName>
                <t>and a.feedBack is null and :notFreeback = :notFreeback</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
       </pieces>
    </sqlTemplate>
  </entry>

  <entry>
  	<!-- 
  	   查询考核预警, 总页数
  	-->
    <string>queryAssessmentAlertTotalPage</string>
    <sqlTemplate>
      <!-- sql模式 -->
      <sqlPattern>
		 select count(*) as total 
		 from AssessmentAlert a
		 where 1=1 {0} {1} {2} {3} {4}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeId</paramName>
                <t>and a.police.id = :policeId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 1, cycle start -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and a.cycle.start &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and a.cycle.end &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, hasFreeback -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>hasFreeback</paramName>
                <t>and a.feedBack is not null and :hasFreeback = :hasFreeback</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4, notFreeback -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>notFreeback</paramName>
                <t>and a.feedBack is null and :notFreeback = :notFreeback</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
       </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 搜索考核预警 -->
    <string>searchAssessmentAlert</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
		 select
		        d.ID alertId,
				b._workNumber policeCode,
				b._name policeName,
				c._position position,
				b._male sex,
				b._age age,
				o._orgName orgName,
				o._path orgPath,
				d1._name deptName,
				d.create_date createTime,
				case when d.fee_back is null then false else true end hasFeedback,
				d.report_type type
				from T_ASSESSMENT_ALERT d 
				left join admin_employees b on b._id = d.police_id
				left join admin_position c on b._positionId = c._id
				left join admin_organization o on o._id = b._organizationId
				left join admin_region r on o._regionId = r._id
				left join admin_department d1 on d1._id = b._departmentId
				where 1=1 {0} {1} {2} {3} {4} {5} {6} {7} {8}
		 
	  </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentAlertSumBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, police code -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeCode</paramName>
                <t>and b._workNumber = :policeCode</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, police name -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeName</paramName>
                <t>and a._name like :policeName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, org  -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>orgId</paramName>
                <t>and o._path like :orgId</t>
                <a class="IsTree"/>
              </path>
              <path>
                <paramName>orgId</paramName>
                <t>and o._path = :orgId</t>
                <a class="IsNotTree"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, deptId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>deptId</paramName>
                <t>and d1._id = :deptId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4, startDate -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and d.start_date &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 5, endDate, exclude -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and date_sub(d.end_date, interval 1 second) &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 6, hasFreeback -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>hasFreeback</paramName>
                <t>and a.feedBack is not null and :hasFreeback = :hasFreeback</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 7, notFreeback -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>notFreeback</paramName>
                <t>and a.feedBack is null and :notFreeback = :notFreeback</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 8, 报表类型 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>type</paramName>
                <t>and d.report_type = :type</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
       </pieces>
    </sqlTemplate>
  </entry>

  <entry>
  	<!-- 
  	   搜索考核预警概要, 总页数
  	-->
    <string>searchAssessmentAlertTotalPage</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
		 select
		        count(*) total
				from T_ASSESSMENT_ALERT d 
				left join admin_employees b on b._id = d.police_id
				left join admin_position c on b._positionId = c._id
				left join admin_organization o on o._id = b._organizationId
				left join admin_region r on o._regionId = r._id
				left join admin_department d1 on d1._id = b._departmentId
				where 1=1 {0} {1} {2} {3} {4} {5} {6} {7} {8}
		 
	  </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentAlertSumBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, police code -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeCode</paramName>
                <t>and b._workNumber = :policeCode</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, police name -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeName</paramName>
                <t>and a._name like :policeName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, org  -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>orgId</paramName>
                <t>and o._path like :orgId</t>
                <a class="IsTree"/>
              </path>
              <path>
                <paramName>orgId</paramName>
                <t>and o._path = :orgId</t>
                <a class="IsNotTree"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, deptId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>deptId</paramName>
                <t>and d1._id = :deptId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4, startDate -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and d.start_date &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 5, endDate, exclude -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and date_sub(d.end_date, interval 1 second) &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 6, hasFreeback -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>hasFreeback</paramName>
                <t>and a.feedBack is not null and :hasFreeback = :hasFreeback</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 7, notFreeback -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>notFreeback</paramName>
                <t>and a.feedBack is null and :notFreeback = :notFreeback</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 8, 报表类型 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>type</paramName>
                <t>and d.report_type = :type</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
       </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 获取考核预警报告预警项 -->
    <string>getShootingAccessmentAlertItem</string>
    <sqlTemplate>
      <!-- sql -->
      <sqlPattern>
      	  select *
			from T_ASSESSMENT_ALERT_ITEM 
			where 1=1 {0}
         
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, alertId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>alertId</paramName>
                <t>and alert_id = :alertId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 获取考核预警预警项 -->
    <string>getShootingAccessmentAlertItemTotalPage</string>
    <sqlTemplate>
      <!-- sql -->
      <sqlPattern>
          select count(*) total
			from T_ASSESSMENT_ALERT_ITEM 
			where 1=1 {0}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, alertId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>alertId</paramName>
                <t>and alert_id = :alertId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 获取考核预警报告预警项 -->
    <string>getShootingAccessmentAlertBean</string>
    <sqlTemplate>
      <!-- sql -->
      <sqlPattern>
      	  select
		        d.ID as alertId,
				b._workNumber as policeCode,
				b._name as policeName,
				c._position as position,
				b._male as sex,
				b._age as age,
				o._orgName as orgName,
				d1._name as deptName,
				d.create_date as createTime,
				d.start_date as startDate, 
				d.end_date as endDate,
				d.fee_back as feedback
				from T_ASSESSMENT_ALERT d
				left join admin_employees b on b._id = d.police_id
				left join admin_position c on b._positionId = c._id
				left join admin_organization o on o._id = b._organizationId
				left join admin_region r on o._regionId = r._id
				left join admin_department d1 on d1._id = b._departmentId
				where 1=1 and d.ID = :alertId
         
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentAlertBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, alertId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>alertId</paramName>
                <t>and alert_id = :alertId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>

</sqlTemplates>