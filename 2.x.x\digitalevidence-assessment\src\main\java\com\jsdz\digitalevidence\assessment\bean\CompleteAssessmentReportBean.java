/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.assessment.AssessmentUtils;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.ShootingSummary;
import com.jsdz.digitalevidence.document.DocumentUtils;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: CompleteAssessmentReportBean
 * @说明: 完整考核报告Bean
 *
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see
 */
public class CompleteAssessmentReportBean extends AbstractDTO {
	
	/********警员资料********/
	/** 考核预警Id*/
	private Long reportId;
	/** 警号*/
	private String policeCode;
	/** 姓名*/
	private String policeName;
	/** 性别*/
	private String sex;
	/** 年龄*/
	private Integer age;
	/** 局*/
	private String orgName;
	/** 部门*/
	private String deptName;
	/** 职位*/
	private String position;
    /** 报告生成日期*/
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime; 
    /** 考核周期*/
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date startDate;
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endDate;
	/** 报告生成日期*/
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date submitDate;
	/** 拍摄统计*/
	private ShootingSummary sumOfShooting;
	/** 拍摄考核统计*/
	private ShootingAssessmentSummary sumOfShootingAssessment;
	/** 评语*/
	private String comments;
	/** 是否冻结*/
	private Boolean frozen;
	// 总计
	// 时长
	public String getTotalLenthStr() {
		Long t = AssessmentUtils.zeroIfNull(sumOfShooting.getLengthOfVedio()) 
					+ AssessmentUtils.zeroIfNull(sumOfShooting.getLengthOfAudio());
		return DateTimeUtils.durationToStr(t);
	}
	// 大小
	public String getTotalSizeStr() {
		Long t = AssessmentUtils.zeroIfNull(sumOfShooting.getSizeOfLog())+
					+AssessmentUtils.zeroIfNull(sumOfShooting.getSizeOfPic())
					+AssessmentUtils.zeroIfNull(sumOfShooting.getSizeOfVedio())
					+AssessmentUtils.zeroIfNull(sumOfShooting.getCountOfAudio());
		return DocumentUtils.getFileSizeStr(t);
	}
	// 数量
	public Long getTotalCount() {
		Long t = (long)AssessmentUtils.zeroIfNull(sumOfShooting.getCountOfAudio())
						+AssessmentUtils.zeroIfNull(sumOfShooting.getCountOfLog())
						+AssessmentUtils.zeroIfNull(sumOfShooting.getCountOfPic())
						+AssessmentUtils.zeroIfNull(sumOfShooting.getCountOfVedio());
		return t;
	}
	
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public Integer getAge() {
		return age;
	}
	public void setAge(Integer age) {
		this.age = age;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getDeptName() {
		return deptName==null ? "-" : deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getPosition() {
		return position==null ? "-" : position;
	}
	public void setPosition(String position) {
		this.position = position;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Date getSubmitDate() {
		return submitDate;
	}
	public void setSubmitDate(Date submitDate) {
		this.submitDate = submitDate;
	}
	public Long getReportId() {
		return reportId;
	}
	public void setReportId(Long reportId) {
		this.reportId = reportId;
	}
	public ShootingSummary getSumOfShooting() {
		return sumOfShooting;
	}
	public void setSumOfShooting(ShootingSummary sumOfShooting) {
		this.sumOfShooting = sumOfShooting;
	}
	public ShootingAssessmentSummary getSumOfShootingAssessment() {
		return sumOfShootingAssessment;
	}
	public void setSumOfShootingAssessment(ShootingAssessmentSummary sumOfShootingAssessment) {
		this.sumOfShootingAssessment = sumOfShootingAssessment;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public Boolean getFrozen() {
		return frozen;
	}
	public void setFrozen(boolean frozen) {
		this.frozen = frozen;
	}
	
}
