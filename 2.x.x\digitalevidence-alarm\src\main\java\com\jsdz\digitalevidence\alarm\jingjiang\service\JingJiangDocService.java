package com.jsdz.digitalevidence.alarm.jingjiang.service;

import java.util.List;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.jingjiang.model.JingJiangDoc;

/**
 * 
 * @类名: JingJiangDocService
 * @说明: 靖江交警数据交换
 *
 * @author: kenny
 * @Date	2017年10月25日下午4:39:02
 * 修改记录：
 *
 * @see
 */
public interface JingJiangDocService {
	//新增
	public AjaxResult addJingJiangDoc(JingJiangDoc jingJiangDoc);

	//修改
	public AjaxResult updateJingJiangDoc(JingJiangDoc jingJiangDoc);

	//删除
	public AjaxResult deleteJingJiangDoc(JingJiangDoc jingJiangDoc);

	//按id查询,结果是游离状态的数据
	public JingJiangDoc findJingJiangDocById(Long id);

	//按id查询
	public JingJiangDoc locateJingJiangDocById(Long id);

	//单个查询
	public JingJiangDoc findJingJiangDocByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<JingJiangDoc> findAllJingJiangDocs();

	//列表查询
	public List<JingJiangDoc> findJingJiangDocsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<JingJiangDoc> findJingJiangDocsOnPage(Page<JingJiangDoc> page, String queryStr,String[] paramNames,Object[] values);

	//写入数据交换
	public void toJingJiang(Long id);

}
