package com.jsdz.digitalevidence.analysis.service.impl;
/**
 * 
 * @类名: DocDailyServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-31 15:31:04
 * 修改记录：
 *
 * @see
*/

import com.jsdz.admin.security.dao.EmployeesDao;
import com.jsdz.admin.security.utils.JSDateFormatUtils;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.analysis.bean.DocDailyDto;
import com.jsdz.digitalevidence.analysis.bean.SiteDocDailyDto;
import com.jsdz.digitalevidence.analysis.dao.DocDailyDao;
import com.jsdz.digitalevidence.analysis.model.DailyLog;
import com.jsdz.digitalevidence.analysis.model.DailyObject;
import com.jsdz.digitalevidence.analysis.model.DailyType;
import com.jsdz.digitalevidence.analysis.model.DocDaily;
import com.jsdz.digitalevidence.analysis.service.DailyLogService;
import com.jsdz.digitalevidence.analysis.service.DocDailyService;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.model.ImportantLevel;
import com.jsdz.reportquery.ReportQueryDao;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("DocDailyServiceImpl")
public class DocDailyServiceImpl implements DocDailyService {

	@Autowired
	private DocDailyDao docDailyDao;
	@Autowired
	private DailyLogService dailyLogService;
	@Autowired
	private EmployeesDao employeesDao;	
	
	
	
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<DocDailyDto> reportQueryDao;

	public static final Logger logger = Logger.getLogger(DocDailyServiceImpl.class);
	
	//新增
	public AjaxResult addDocDaily(DocDaily docDaily) {
		AjaxResult result = new AjaxResult();
		docDailyDao.addDocDaily(docDaily);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateDocDaily(DocDaily docDaily) {
		AjaxResult result = new AjaxResult();
		docDailyDao.updateDocDaily(docDaily);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteDocDaily(DocDaily docDaily) {
		AjaxResult result = new AjaxResult();
		docDailyDao.deleteDocDaily(docDaily);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public DocDaily findDocDailyById(Long id){

		return docDailyDao.findDocDailyById(id);
	}

	//按 id 查询
	public DocDaily locateDocDailyById(Long id) {
		return docDailyDao.locateDocDailyById(id);
	}

	//单个查询
	public DocDaily findDocDailyByParam(String queryStr, String[] paramNames, Object[] values) {
		return docDailyDao.findDocDailyByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<DocDaily> findAllDocDailys() {
		return docDailyDao.findAllDocDailys();
	}

	//列表查询
	public List<DocDaily> findDocDailysByParam(String queryStr, String[] paramNames, Object[] values) {
		return docDailyDao.findDocDailysByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<DocDaily> findDocDailysOnPage(Page<DocDaily> page, String queryStr, String[] paramNames, Object[] values) {
		Page<DocDaily> pos = docDailyDao.findDocDailysOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
	//日结操作
	@SuppressWarnings("unchecked")
	@Transactional
	public void dailyExecute(DailyType dailyType,DailyObject dailyObject, String dateBegin,String dateEnd){
		String sDateFrom = dateBegin + " 00:00:00";
		String sDateTo = dateEnd + " 23:59:59";
		Date dateFrom = JSDateFormatUtils.parseDateTime(sDateFrom);
		Date dateTo = JSDateFormatUtils.parseDateTime(sDateTo);
		
		
		//先删除日结表中相同的数据，重新计算重新保存
		docDailyDao.executeHql(
				"delete from DocDaily d where d.dailyType= " + String.valueOf(dailyType.ordinal()) //日结/月结/年结
				+ " and d.dailyObject = " + String.valueOf(dailyObject.ordinal()) //按站点汇总
				+ " and d.dateFrom >=:dateFrom "
				+ " and d.dateTo <= :dateTo", 
				new String[]{"dateFrom","dateTo"}, 
				new Object[]{dateFrom,dateTo});
		
		//开始查询
		Page<DocDailyDto> page = new Page<DocDailyDto>();
		page.setPageSize(10000);
		page.setOffset(0);
		
		String dnySqlName = "";
		if (dailyObject.ordinal() == 0)
			dnySqlName = "docQuerySiteForDialy";//站点
		else if (dailyObject.ordinal() == 1)
			dnySqlName = "docQueryRecorderForDialy";//执法仪
		else if (dailyObject.ordinal() == 2)
			dnySqlName = "docQueryPoliceForDialy"; //警员
		else 
			dnySqlName = "docQuerySiteForDialy";

		page = reportQueryDao.pageQueryNamedSQL(page, dnySqlName, 
				new String[]{"dataFrom","dataTo"}, 
				new Object[]{sDateFrom,sDateTo});
		
		
		//加入到DocDaily表
		for (DocDailyDto db : page.getRows()){
			DocDaily dd = new DocDaily();
			dd.setCate(getDocumentCate(db.getCate()));
			dd.setDailyObject(dailyObject);//按站点汇总
			dd.setDailyType(dailyType);//日结/月结/年结
			dd.setDateFrom(dateFrom);
			dd.setDateTo(dateTo);
			dd.setFileCount(db.getFileCount());
			dd.setFileSize(db.getFileSize());
			dd.setImpLevel(getImportantLevel(db.getImpLevel()));
			dd.setDuration(db.getVedioLen());
			dd.setObjectId(db.getObjectId());
			docDailyDao.addDocDaily(dd);
			;
		}
		
		
		//日结日志
		DailyLog dl = new DailyLog();
		dl.setCreateTime(new Date());
		dl.setDailyType(dailyType.name);
		dl.setDateFrom(dateFrom);
		dl.setDateTo(dateTo);
		dl.setObjectDesc(dailyObject.name);
		dailyLogService.addDailyLog(dl);
		
		
	}		
	
	//按站点查询统计
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Page<SiteDocDailyDto> findSiteAnalysis(Page page,String[] paramNames, Object[] values,
								String[] totalParamNames, Object[] totalValues){
		page = reportQueryDao.queryNamedNoTotalSQL(page,"siteDocDailyAnalysis",paramNames,values);
		Integer total = reportQueryDao.getTotalQueryNamedSQL("siteDocDailyAnalysisTotalPage",totalParamNames,totalValues);
		page.setTotal(total);
		
		return page;
	}
	
	//按执法仪查询统计
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Page<SiteDocDailyDto> findRecorderAnalysis(Page page,String[] paramNames, Object[] values,
			   String[] totalParamNames, Object[] totalValues){
		
		page = reportQueryDao.queryNamedNoTotalSQL(page,"recorderDocDailyAnalysis",paramNames,values);
		Integer total = reportQueryDao.getTotalQueryNamedSQL("recorderDocDailyAnalysisTotalPage",totalParamNames,totalValues);
		page.setTotal(total);
		

		return page;
	}
	
	//按警员查询统计
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Page<SiteDocDailyDto> findPoliceAnalysis(Page page,String[] paramNames, Object[] values,
			String[] totalParamNames, Object[] totalValues){
		
		
		page = reportQueryDao.queryNamedNoTotalSQL(page, "policeDocDailyAnalysis",paramNames,values);
		Integer total = reportQueryDao.getTotalQueryNamedSQL("policeDocDailyAnalysisTotalPage",totalParamNames,totalValues);		
		page.setTotal(total);
		return page;
	}
	
	//按类型查询统计
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public Page<SiteDocDailyDto> findCateAnalysis(Page page,String[] paramNames, Object[] values){
		page = reportQueryDao.pageQueryNamedSQL(page, "cateDocDailyAnalysis",paramNames,values);
		for(SiteDocDailyDto sdd : (List<SiteDocDailyDto>)page.getRows()){
			int ordinal = sdd.getObjectId().intValue(); 
			sdd.setCode(getDocumentCate(ordinal).name);
			sdd.setName(getDocumentCate(ordinal).name);
		}
		
		return page;
	}
	
	
	public DocumentCate getDocumentCate(int ordinal){
		if(ordinal==0)
			return DocumentCate.PIC;
		else if(ordinal==1)
			return DocumentCate.AUDIO;
		else if(ordinal==2)
			return DocumentCate.VEDIO;
		else if(ordinal==3)
			return DocumentCate.TXT;
		else
			return DocumentCate.PIC;
	}
	
	public ImportantLevel getImportantLevel(int ordinal){
		if(ordinal==0)
		   return ImportantLevel.IMPORTANCE;
		else if(ordinal==1)
		   return ImportantLevel.GENERAL;
		else
		   return ImportantLevel.IMPORTANCE;
	}	
}
