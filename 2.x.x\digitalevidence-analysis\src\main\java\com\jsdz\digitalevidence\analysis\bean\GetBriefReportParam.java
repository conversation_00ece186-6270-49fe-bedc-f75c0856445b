package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: GetBriefReportParam
 * @说明: 系统简报参数
 *
 * @author: kenny
 * @Date	2018年3月27日下午6:37:30
 * 修改记录：
 *
 * @see
 */

import java.util.HashMap;
import java.util.Map;

import com.jsdz.admin.security.bean.ParamGenerateHql;
import com.jsdz.core.Page;

public class GetBriefReportParam extends ParamGenerateHql{
	private Page<BriefReport> page;
	private Map<String, Object> kvs = new HashMap<String, Object>();
	private Long orgId;
	private String dateFrom;
	private String dateTo;
	
	public GetBriefReportParam(){
		page = new Page<BriefReport>();
		page.setOffset(0);
		page.setPageSize(10);
	}
	
	public String queryBeanToHql(){
		kvs.put("orgId", this.getOrgId()==null?0:this.getOrgId());
		
		if (this.getDateFrom() != null && !"".equals(this.getDateFrom())){
			kvs.put("dateFrom", this.getDateFrom());
		}

		if (this.getDateTo() != null && !"".equals(this.getDateTo())){
			kvs.put("dateTo", this.getDateTo());
		}
		
		return null;
	}

	public Page<BriefReport> getPage() {
		return page;
	}

	public void setPage(Page<BriefReport> page) {
		this.page = page;
	}

	public Map<String, Object> getKvs() {
		return kvs;
	}

	public void setKvs(Map<String, Object> kvs) {
		this.kvs = kvs;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getDateFrom() {
		return dateFrom;
	}

	public void setDateFrom(String dateFrom) {
		this.dateFrom = dateFrom;
	}

	public String getDateTo() {
		return dateTo;
	}

	public void setDateTo(String dateTo) {
		this.dateTo = dateTo;
	}
	
	
	

}
