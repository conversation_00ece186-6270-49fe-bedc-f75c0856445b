package com.jsdz.admin.security.dao;

/**
 *
 * @类名: ActionDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-03 19:35:10
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.Action;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface ActionDao extends GenericORMEntityDAO<Action,Long> {

	//新增
	public void addAction(Action action);

	//修改
	public void updateAction(Action action);

	//删除
	public void deleteAction(Action action);

	//单个查询
	public Action findActionByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询所有
	public List<Action> findALlActions(String queryStr);
	
	//列表查询
	public List<Action> findActionsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Action> findActionsOnPage(Page<Action> page,String queryStr,String[] paramNames,Object[] values);

	public List<Action> findAll();
}

