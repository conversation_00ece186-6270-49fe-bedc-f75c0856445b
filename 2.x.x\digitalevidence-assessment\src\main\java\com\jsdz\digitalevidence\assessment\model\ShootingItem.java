/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.model;

import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.document.model.DocumentCate;

/**
 * @类名: ShootingItem
 * @说明: 拍摄分类统计
 *
 * <AUTHOR>
 * @Date	 2017年8月16日 上午9:38:04
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingItem extends AbstractDTO {
	
	/** 文档分类*/
	private DocumentCate cate;
	/** 拍摄数量*/
	private Integer count;
	/** 拍摄文件大小*/
	private Long size;
	/** 拍摄时长*/
	private Long duration;
	
	public DocumentCate getCate() {
		return cate;
	}
	public void setCate(DocumentCate cate) {
		this.cate = cate;
	}
	public Long getSize() {
		return size;
	}
	public void setSize(Long size) {
		this.size = size;
	}
	public Long getDuration() {
		return duration;
	}
	public void setDuration(Long duration) {
		this.duration = duration;
	}
	public Integer getCount() {
		return count;
	}
	public void setCount(Integer count) {
		this.count = count;
	}

}
