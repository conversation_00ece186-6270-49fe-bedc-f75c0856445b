/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: AssessmentPushDocBean
 * @说明: 考核推送文档Bean
 *        
 *      
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentPushDocBean extends AbstractDTO {

	/** 考核推送文档Id*/
	private Long pushDocId;
	/** 警号*/
	private String policeCode;
	/** 姓名*/
	private String policeName;
	/** 局*/
	private String orgName;
	/** 部门*/
	private String deptName;
	/** 职位*/
	private String position;
    /** 文档Id&名称*/
    private Long docId;
    private String docName;
    /** 考核员*/
    private String assessorPoliceCode;
    private String assessorName;
    /** */
    private Boolean hasAssessed;
    /** 推送时间*/
    private Date createTime;
    
	public Long getPushDocId() {
		return pushDocId;
	}
	public void setPushDocId(Long pushDocId) {
		this.pushDocId = pushDocId;
	}
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getPosition() {
		return position;
	}
	public void setPosition(String position) {
		this.position = position;
	}
	public Long getDocId() {
		return docId;
	}
	public void setDocId(Long docId) {
		this.docId = docId;
	}
	public String getDocName() {
		return docName;
	}
	public void setDocName(String docName) {
		this.docName = docName;
	}
	public String getAssessorPoliceCode() {
		return assessorPoliceCode;
	}
	public void setAssessorPoliceCode(String assessorPoliceCode) {
		this.assessorPoliceCode = assessorPoliceCode;
	}
	public String getAssessorName() {
		return assessorName;
	}
	public void setAssessorName(String assessorName) {
		this.assessorName = assessorName;
	}
	public Boolean getHasAssessed() {
		return hasAssessed;
	}
	public void setHasAssessed(Boolean hasAssessed) {
		this.hasAssessed = hasAssessed;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
    
    

}
