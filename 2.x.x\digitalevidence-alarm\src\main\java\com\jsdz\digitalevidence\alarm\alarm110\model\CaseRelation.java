package com.jsdz.digitalevidence.alarm.alarm110.model;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.digitalevidence.document.model.Case;
import com.jsdz.digitalevidence.document.model.Document;

/**
 *  案件和视频关联
 * <AUTHOR>
 *
 */
public class CaseRelation implements Serializable , Cloneable{


	private static final long serialVersionUID = 5898289190247412514L;
	
	private Long id;
	// 案件
	private Case cas;
	// 视频
	private Document mediaInfo;
	// 关联时间
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date relationTime;
    // 操作员
    private Operator operator;    
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Case getCas() {
		return cas;
	}
	public void setCas(Case cas) {
		this.cas = cas;
	}
	public Document getMediaInfo() {
		return mediaInfo;
	}
	public void setMediaInfo(Document mediaInfo) {
		this.mediaInfo = mediaInfo;
	}
	public Date getRelationTime() {
		return relationTime;
	}
	public void setRelationTime(Date relationTime) {
		this.relationTime = relationTime;
	}
	public Operator getOperator() {
		return operator;
	}
	public void setOperator(Operator operator) {
		this.operator = operator;
	}
	
}
