package com.jsdz.digitalevidence.statistics.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.model.VAttendanceAllModel;
import com.jsdz.digitalevidence.statistics.bean.param.AbnormalParam;
import com.jsdz.digitalevidence.statistics.bean.param.VEvidenceStatisticsParam;
import com.jsdz.digitalevidence.statistics.model.SysAbnormalStatisticsModel;
import com.jsdz.digitalevidence.statistics.mapper.SysAbnormalStatisticsMapper;
import com.jsdz.digitalevidence.statistics.model.VEvidenceStatisticsModel;
import com.jsdz.digitalevidence.statistics.service.ISysAbnormalStatisticsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsdz.digitalevidence.utils.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Service
public class SysAbnormalStatisticsServiceImpl extends ServiceImpl<SysAbnormalStatisticsMapper, SysAbnormalStatisticsModel> implements ISysAbnormalStatisticsService {


    @Resource
    private  SysAbnormalStatisticsMapper sysAbnormalStatisticsMapper;




    public Page<SysAbnormalStatisticsModel> getDatas(AbnormalParam param, PerLvlBean permLvl) {
        Page<SysAbnormalStatisticsModel> page = param.getPage();

        String orgPath = param.getOrgPath();
        if (StringUtils.isEmpty(orgPath)) {
            param.setOrgPath(permLvl.getOrgPath());
            Integer permissionLevel = permLvl.getPermissionLevel();
            param.setIncludeSub((permissionLevel < 3 ? 0 : 1) + "");
        }
        // 使用CompletableFuture并行执行两个查询
        CompletableFuture<List<SysAbnormalStatisticsModel>> modelsFuture = CompletableFuture.supplyAsync(() -> {
            return sysAbnormalStatisticsMapper.selectModels(param);
        });
        CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> {
            return sysAbnormalStatisticsMapper.selectModelCount(param);
        });
        // 等待两个操作完成，并获取结果
        try {
            List<SysAbnormalStatisticsModel> vAttendanceAllModels = modelsFuture.get();
            Integer total = countFuture.get();
            // 处理模型数据
            // 更新Page对象
            page.setTotal(total); // 假设Page使用setTotalElements而不是setTotal
            page.setRows(vAttendanceAllModels); // 假设Page使用setContent而不是setRows

            return page;
        } catch (InterruptedException | ExecutionException e) {
            // 处理异常
            throw new RuntimeException("Error occurred while fetching data asynchronously", e);
        }
    }

    @Override
    public List<SysAbnormalStatisticsModel> UltraShortVideoStatistics() {
        return sysAbnormalStatisticsMapper.UltraShortVideoStatistics();
    }

    @Override
    public List<SysAbnormalStatisticsModel> UltraShortAlarmStatistics() {
        return sysAbnormalStatisticsMapper.UltraShortAlarmStatistics();
    }

    @Override
    public List<SysAbnormalStatisticsModel> UltraShortCaseStatistics() {
        return sysAbnormalStatisticsMapper.UltraShortCaseStatistics();
    }

    @Override
    public List<SysAbnormalStatisticsModel> deviceIdleStatistics(int day) {
        return sysAbnormalStatisticsMapper.deviceIdleStatistics(day);
    }

    @Override
    public void updateDoc(Long id) {
        sysAbnormalStatisticsMapper.updateDoc(id);
    }

    @Override
    public void updateAlarm(Long id) {
        sysAbnormalStatisticsMapper.updateAlarm(id);
    }

    @Override
    public void updateCase(Long id) {
        sysAbnormalStatisticsMapper.updateCase(id);
    }

    /**
     * 采集站在线时长低于rate统计
     * @param rate
     * @return
     */
    @Override
    public List<SysAbnormalStatisticsModel> siteOnlineStatistics(float rate) {
        return sysAbnormalStatisticsMapper.siteOnlineStatistics(rate);
    }

    /**
     * 采集站容量超rate统计
     * @param rate
     * @return
     */
    @Override
    public List<SysAbnormalStatisticsModel> siteCapacityStatistics(float rate) {
        return sysAbnormalStatisticsMapper.siteCapacityStatistics(rate);
    }

    /**
     * 修改采集站在线时长
     * @param siteNo 采集站编号
     * @param totalTime 累计在线时长
     */
    @Override
    public void updateSiteOnlineTime(String siteNo, Long totalTime) {
        sysAbnormalStatisticsMapper.updateSiteOnlineTime(siteNo, totalTime);
    }

    /**
     * 评分情况
     * @param rate
     * @return
     */
    @Override
    public List<SysAbnormalStatisticsModel> videoQualityStatistics(float rate) {
        return sysAbnormalStatisticsMapper.videoQualityStatistics(rate);
    }

    @Override
    @Transactional
    public void setVideoQualityAbnormal(SysAbnormalStatisticsModel t) {
        Long id = t.getId();
        t.setId(null);
        save(t);
        sysAbnormalStatisticsMapper.updateDocCheck(id);
    }
}



