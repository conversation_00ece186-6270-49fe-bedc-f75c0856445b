package com.jsdz.admin.security.service.impl;

import java.sql.Array;
import java.util.*;

/**
 * 
 * @类名: SystemMenuServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-09-08 11:59:54
 * 修改记录：
 *
 * @see
*/

import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.bean.MenuTreeBean;
import com.jsdz.admin.security.bean.RoleForViewBean;
import com.jsdz.admin.security.bean.RoleMenuBean;
import com.jsdz.admin.security.bean.SystemMenuBean;
import com.jsdz.admin.security.dao.SystemMenuDao;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.SystemMenu;
import com.jsdz.admin.security.service.SystemMenuService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.core.TreeBean;
import com.jsdz.reportquery.ReportQueryDao;
import com.jsdz.admin.security.utils.PermissonUtils;
import org.springframework.util.CollectionUtils;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("SystemMenuServiceImpl")
public class SystemMenuServiceImpl implements SystemMenuService {

	@Autowired
	private SystemMenuDao systemMenuDao;
	
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<SystemMenu> reportQueryDao;
	
	@Resource(name="rqDao")
	private ReportQueryDao<MenuTreeBean> reportQueryDao1;

	//新增
	public AjaxResult addSystemMenu(SystemMenu systemMenu) {
		AjaxResult result = new AjaxResult();
		
		if (systemMenu.getMenuName() == null || "".equals(systemMenu.getMenuName())){
			result.setMsg("菜单名必须输入。");
			return result;
		}
		
		if (systemMenu.getMenuType() == null){
			result.setMsg("菜单类型必须输入。");
			return result;
		}

		
		if (!checkEndlessLoop(systemMenu.getParentMenu(),systemMenu)){
			result.setSuccess(false);
			result.setMsg("上下级菜单有冲突");
			return result;
		}
		
		systemMenuDao.addSystemMenu(systemMenu);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	@Transactional
	public AjaxResult updateSystemMenu(SystemMenu systemMenu) {
		AjaxResult result = new AjaxResult(200,false,"",null);

		if (systemMenu.getMenuName() == null || "".equals(systemMenu.getMenuName())){
			result.setMsg("菜单名必须输入。");
			return result;
		}
		if (systemMenu.getMenuType() == null){
			result.setMsg("菜单类型必须输入。");
			return result;
		}
		
		if (!checkEndlessLoop(systemMenu.getParentMenu(),systemMenu)){
			result.setSuccess(false);
			result.setMsg("上下级菜单有冲突");
			return result;
		}
		
		SystemMenu m1=findSystemMenuById(systemMenu.getId());
		Integer isShow = m1.getIsShow();
		m1.setImageUrl(systemMenu.getImageUrl());
		m1.setMenuName(systemMenu.getMenuName());
		m1.setMenuType(systemMenu.getMenuType());
		m1.setParentMenu(systemMenu.getParentMenu());
		m1.setIsShow(systemMenu.getIsShow());
		m1.setSort(systemMenu.getSort());
		systemMenuDao.updateSystemMenu(m1);
		
		//禁用启用有改变时同改下级菜单的禁用、启用属性
		if (isShow != systemMenu.getIsShow()){
			updateSubMenuIsshow(systemMenu);
		}
		
		
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteSystemMenu(SystemMenu systemMenu) {
		AjaxResult result = new AjaxResult();
		systemMenuDao.deleteSystemMenu(systemMenu);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public SystemMenu findSystemMenuById(Long id){

		return systemMenuDao.findSystemMenuById(id);
	}

	//按 id 查询
	public SystemMenu locateSystemMenuById(Long id) {
		return systemMenuDao.locateSystemMenuById(id);
	}

	//单个查询
	public SystemMenu findSystemMenuByParam(String queryStr, String[] paramNames, Object[] values) {
		return systemMenuDao.findSystemMenuByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<SystemMenu> findAllSystemMenus() {
		return systemMenuDao.findAllSystemMenus();
	}

	//列表查询
	public List<SystemMenu> findSystemMenusByParam(String queryStr, String[] paramNames, Object[] values) {
		return systemMenuDao.findSystemMenusByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<SystemMenu> findSystemMenusOnPage(Page<SystemMenu> page, String queryStr, String[] paramNames, Object[] values) {
		Page<SystemMenu> pos = systemMenuDao.findSystemMenusOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	@Override
	public List<RoleMenuBean> findAllMenuForRoleSet(Long roleId) throws Exception{
	
		Map<String,Object> map = new HashMap<String,Object>();
    	if (roleId != null)
    		map.put("roleId", roleId);
    	String[] paramNames = map.keySet().toArray(new String[0]);
		Object[] values = map.values().toArray(new Object[0]);
		List<RoleMenuBean> result = new ArrayList<RoleMenuBean>(); 
		List<Object> list= reportQueryDao.QueryNamedAllDataSQL("findAllMenuForRoleSet",paramNames,values);
		for(Object o : list ){
			result.add((RoleMenuBean)o);
		}
		return result;
	}
	
    //根据操作查询可用的菜单
    @Override
    public List<MenuTreeBean> findSysMenuByOperator(Operator operator){
    	if( "admin".equals(operator.getLoginName()) || "admin@jsdz".equals(operator.getLoginName()))
//    	if (PermissonUtils.generatePerLvl(operator).getPermissionLevel()==9)
//    		return generateTreeList(0L,null);
    		return generateTreeListNew(null);
    	else
    		return generateTreeListNew(operator.getId());
    }

	private List<MenuTreeBean> generateTreeListNew(Long userId) {
		List<MenuTreeBean> result = null;

		Map<String,Object> map = new HashMap<String,Object>();

		if (userId != null)
			map.put("userId", userId);

		map.put("isShow", 1);
		map.put("menuType", 1);

		String[] paramNames = map.keySet().toArray(new String[0]);
		Object[] values = map.values().toArray(new Object[0]);

		List<Object> list= reportQueryDao.QueryNamedAllDataSQL("findMenuByParentId",paramNames,values);
		if(!CollectionUtils.isEmpty(list)) {
			result = new ArrayList<MenuTreeBean>();
			List<MenuTreeBean> finalResult = result;
			List<MenuTreeBean> all = new ArrayList<MenuTreeBean>();
			Map<String, MenuTreeBean> menuTreeBeanMap = list.stream().map(o -> {
				MenuTreeBean tree = new MenuTreeBean();
				SystemMenuBean sm = (SystemMenuBean) o;
				tree.setId(sm.getId().toString());
				tree.setText(sm.getMenuName());
				tree.setChecked(false);
				tree.setIconCls(sm.getImageUrl());
				tree.setPid(sm.getParentId() == null ? null : sm.getParentId().toString());
				tree.setMenuUrl(sm.getMenuUrl());
				tree.setMenuType(sm.getMenuType());
				tree.setMenuSort(sm.getSort());
				tree.setMenuIsshow(sm.getIsShow());
				tree.setIconCls(sm.getImageUrl());
				tree.setState("open");
				if (tree.getPid() == null) {
					finalResult.add(tree);
				}
				all.add(tree);
				return tree;
			}).collect(Collectors.toMap(MenuTreeBean::getId, Function.identity(), (o1, o2) -> o1));
			for (MenuTreeBean sm : all){
				if(sm.getPid() != null) {
					MenuTreeBean menuTreeBean = menuTreeBeanMap.get(sm.getPid());
					if(menuTreeBean != null) {
						List<MenuTreeBean> children = menuTreeBean.getChildren();
						if(children != null) {
							children.add(sm);
						} else {
							children = new ArrayList<MenuTreeBean>();
							children.add(sm);
							menuTreeBean.setChildren(children);
						}
						menuTreeBean.setState("closed");
					}
				}
			}
		}
		return result;
	}


	/**
     * @param pid 上级ID
     * @param userId 
     * @param notAllRecord 'yes' or null,yes 只查询可显示的菜单类型，null（不传）的有类型
     * @return
     */
    private List<MenuTreeBean> generateTreeList(Long pid,Long userId){
    	List<MenuTreeBean> result = null;
    	
    	Map<String,Object> map = new HashMap<String,Object>();

    	if (userId != null)
    		map.put("userId", userId);

    	map.put("parentId", pid);
    	map.put("isShow", 1);
    	map.put("menuType", 1);
 		
    	String[] paramNames = map.keySet().toArray(new String[0]);
		Object[] values = map.values().toArray(new Object[0]);

		List<Object> list= reportQueryDao.QueryNamedAllDataSQL("findMenuByParentId",paramNames,values);
   	 	if (list!=null && list.size() > 0){
   	 		result = new ArrayList<MenuTreeBean>(); 
   	 		for (Object o : list){
   	 			MenuTreeBean tree = new MenuTreeBean();
   	 		    SystemMenuBean sm = (SystemMenuBean)o;
   	 			tree.setId(sm.getId().toString());
   	 			tree.setText(sm.getMenuName());
   	 			tree.setChecked(false);
   	 			tree.setIconCls(sm.getImageUrl());
   	 			tree.setPid(sm.getParentId()==null?null:sm.getParentId().toString());
   	 			tree.setMenuUrl(sm.getMenuUrl());
   	 			tree.setMenuType(sm.getMenuType());
   	 			tree.setMenuSort(sm.getSort());
   	 			tree.setMenuIsshow(sm.getIsShow());
   	 			tree.setIconCls(sm.getImageUrl());
   	 			List<MenuTreeBean> child = generateTreeList(sm.getId(),userId);
   	 			tree.setState(child==null?"open":"closed");
   	 			tree.setChildren(child);
   	 			result.add(tree);
        	 }
   	 }
   	 return result;
     }	
    
	//分页查询最上级数据给前端easyUi treegrid使用
	public Page<MenuTreeBean> findMenuByTopLevel(Page<MenuTreeBean> page, String[] paramNames, Object[] values){
		
		page = reportQueryDao1.queryNamedNoTotalSQL(page,"findMenuByParentId",paramNames,values);
		
		//所有有子菜单的菜单ID
		List<Long> pids = systemMenuDao.findAllParentId();

		List<MenuTreeBean> beans = new ArrayList<MenuTreeBean>();
		for (Object o : page.getRows()){
	 			MenuTreeBean tree = new MenuTreeBean();
   	 		    SystemMenuBean sm = (SystemMenuBean)o;
   	 			tree.setId(sm.getId().toString());
   	 			tree.setText(sm.getMenuName());
   	 			tree.setChecked(false);
   	 			tree.setIconCls(sm.getImageUrl());
   	 			tree.setPid(sm.getParentId()==null?null:sm.getParentId().toString());
   	 			tree.setMenuUrl(sm.getMenuUrl());
   	 			tree.setMenuType(sm.getMenuType());
   	 			tree.setMenuSort(sm.getSort());
   	 			tree.setMenuIsshow(sm.getIsShow());
   	 			tree.setIconCls(sm.getImageUrl());
   	 		    tree.setState("open");
   	 		    for(Long l : pids){
   	 		    	if (l !=null && l.equals(sm.getId())){
   	 		    	   tree.setState("closed");
   	 		    	   break;
   	 		    	}
   	 		    }
   	 		    beans.add(tree);			
		}
		page.setRows(beans);
		
		//总数
		Map<String, Object> kvs = new HashMap<String, Object>();
		kvs.put("parentId", 0);//第一级
		String[] pns = kvs.keySet().toArray(new String[0]);
		Object[] vals = kvs.values().toArray(new Object[0]);
		Integer total = reportQueryDao1.getTotalQueryNamedSQL("findMenuByParentIdTotalPage",pns,vals);
		page.setTotal(total);
		return page;
	}
	
	//按上级ID查询
	public  List<MenuTreeBean> findMenuByParentId(Long parentId){
		
		Page<MenuTreeBean> page = new Page<MenuTreeBean>();
		page.setOffset(0);
		page.setPageSize(10000);
		Map<String, Object> kvs = new HashMap<String, Object>();
		kvs.put("parentId", parentId);
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		page = reportQueryDao1.queryNamedNoTotalSQL(page,"findMenuByParentId",paramNames,values);
		
		List<Long> pids = systemMenuDao.findAllParentId();
		List<MenuTreeBean> beans = new ArrayList<MenuTreeBean>();
		for (Object o : page.getRows()){
	 			MenuTreeBean tree = new MenuTreeBean();
   	 		    SystemMenuBean sm = (SystemMenuBean)o;
   	 			tree.setId(sm.getId().toString());
   	 			tree.setText(sm.getMenuName());
   	 			tree.setChecked(false);
   	 			tree.setIconCls(sm.getImageUrl());
   	 			tree.setPid(sm.getParentId()==null?null:sm.getParentId().toString());
   	 			tree.setMenuUrl(sm.getMenuUrl());
   	 			tree.setMenuType(sm.getMenuType());
   	 			tree.setMenuSort(sm.getSort());
   	 			tree.setMenuIsshow(sm.getIsShow());
   	 			tree.setIconCls(sm.getImageUrl());
   	 		    tree.setState("open");
   	 		    for(Long l : pids){
   	 		    	if (l !=null && l == sm.getId()){
   	 		    	   tree.setState("closed");
   	 		    	   break;
   	 		    	}
   	 		    }
   	 		    beans.add(tree);			
		}
		//page.setRows(beans);		
		
		return beans;
	}
	
	//检查下级关系冲突,返回true为正确,没有冲突
	public boolean checkEndlessLoop(SystemMenu parentMenu, SystemMenu curMenu ){
		boolean result = true;
		if (parentMenu == null){
			return result;
		}
		
		if (parentMenu.getId() == curMenu.getId()){
			return false;
		}
		
		List<SystemMenu> menus = systemMenuDao.findSystemMenusByCondition(
				"from SystemMenu s where s.parentMenu.id = :id",
				new String[]{"id"},new Object[]{curMenu.getId()});
		if (menus == null){
			return result;
		}
		
		for (int i=0;i<menus.size();i++){
			if (menus.get(i).getId() == parentMenu.getId()){
				result = false;
				break;
			}
			
			//递归查找
			result = checkEndlessLoop(parentMenu,menus.get(i));
			if (result == false){
				return result;
			}
		}
		return result;
	}
	
	//根据操作员和菜单id查询页面权限
    @Override
    public List<RoleForViewBean> findViewRole(Long userId,Long menuId){
    	List<RoleForViewBean> result = new ArrayList<RoleForViewBean>();
    	List<Object> list = null;
    	if (userId == null){
    		list= reportQueryDao.QueryNamedAllDataSQL("findRoleForViewByAdmin",
        			new String[]{"parentId"},new Object[]{menuId});
    		
    	}else
    		list= reportQueryDao.QueryNamedAllDataSQL("findRoleForView",
    			new String[]{"parentId","userId"},new Object[]{menuId,userId});
		for(Object o : list ){
			result.add((RoleForViewBean)o);
		}
    	return result; 
    }
    
	//递归设置子菜单的禁用启用
	private void updateSubMenuIsshow(SystemMenu systemMenu){
		List<SystemMenu> list =  systemMenuDao.findSystemMenusByCondition(
				"from SystemMenu s where s.parentMenu.id = :id ",
				new String[]{"id"},
				new Object[]{systemMenu.getId()});
		
		if (list != null && list.size() > 0)
			for (SystemMenu  sm: list){
				if (sm.getIsShow() != systemMenu.getIsShow()){
					sm.setIsShow(systemMenu.getIsShow());
					systemMenuDao.updateSystemMenu(sm);
				}
				updateSubMenuIsshow(sm);
			}
		
	}
}
