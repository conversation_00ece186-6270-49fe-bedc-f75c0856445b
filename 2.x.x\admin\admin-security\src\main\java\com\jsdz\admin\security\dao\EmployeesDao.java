package com.jsdz.admin.security.dao;

/**
 *
 * @类名: EmployeesDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-13 13:48:18
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface EmployeesDao extends GenericORMEntityDAO<Employees,Long> {

	//新增
	public void addEmployees(Employees employees);

	//修改
	public void updateEmployees(Employees employees);

	//删除
	public void deleteEmployees(Employees employees);
	
	//按id查询(游离)
	public Employees findEmployeesById(Long id);
	
	//按id查询
	public Employees locateEmployeesById(Long id);
	
	//按警号查询
	public Employees locateEmployeesByWorkNumber(String workNumber);

	//单个查询
	public Employees findEmployeesByCondition(final String queryStr,final String[] paramNames,final Object[] values);
	
	//查询全部
	public List<Employees> findAllEmployeess();
	public List<Employees> findAllEmployeess(Long orgId);

	//列表查询
	public List<Employees> findEmployeessByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Employees> findEmployeessOnPage(Page<Employees> page,String queryStr,String[] paramNames,Object[] values);

	//按岗位查询
	public List<Employees> findEmployeessByPostId(Long id);
	
	/**
	 * 删除警员，仅打删除标志
	 * @param employees
	 * @return
	 */
	public Employees setDeleteFlag(Employees employees);
}

