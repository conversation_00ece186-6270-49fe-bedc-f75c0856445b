package com.jsdz.admin.security.service.impl;

/**
 * 
 * @类名: EmployeesImportServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-11-04 18:25:26
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.admin.security.dao.EmployeesImportDao;
import com.jsdz.admin.security.model.EmployeesImport;
import com.jsdz.admin.security.model.SystemMenu;
import com.jsdz.admin.security.service.EmployeesImportService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.reportquery.ReportQueryDao;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("EmployeesImportServiceImpl")
public class EmployeesImportServiceImpl implements EmployeesImportService {

	@Autowired
	private EmployeesImportDao employeesImportDao;

	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<Object> reportQueryDao;

	//新增
	public AjaxResult addEmployeesImport(EmployeesImport employeesImport) {
		AjaxResult result = new AjaxResult();
		employeesImportDao.addEmployeesImport(employeesImport);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateEmployeesImport(EmployeesImport employeesImport) {
		AjaxResult result = new AjaxResult();
		employeesImportDao.updateEmployeesImport(employeesImport);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteEmployeesImport(EmployeesImport employeesImport) {
		AjaxResult result = new AjaxResult();
		employeesImportDao.deleteEmployeesImport(employeesImport);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public EmployeesImport findEmployeesImportById(Long id){

		return employeesImportDao.findEmployeesImportById(id);
	}

	//按 id 查询
	public EmployeesImport locateEmployeesImportById(Long id) {
		return employeesImportDao.locateEmployeesImportById(id);
	}

	//单个查询
	public EmployeesImport findEmployeesImportByParam(String queryStr, String[] paramNames, Object[] values) {
		return employeesImportDao.findEmployeesImportByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<EmployeesImport> findAllEmployeesImports() {
		return employeesImportDao.findAllEmployeesImports();
	}

	//列表查询
	public List<EmployeesImport> findEmployeesImportsByParam(String queryStr, String[] paramNames, Object[] values) {
		return employeesImportDao.findEmployeesImportsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<EmployeesImport> findEmployeesImportsOnPage(Page<EmployeesImport> page, String queryStr, String[] paramNames, Object[] values) {
		Page<EmployeesImport> pos = employeesImportDao.findEmployeesImportsOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
	//执行简单的Hql
	@Override
	public int execSimpleHql(String execHql,String[] paramNames, Object[] values){
		return employeesImportDao.execSimpleHql(execHql, paramNames, values);
	}

	////执行SQL
	@Override
	public List<Object> execSqlName(String sqlName,String[] paramNames, Object[] values){
		return  reportQueryDao.QueryNamedAllDataSQL(sqlName,paramNames,values);
	}
	
	

}
