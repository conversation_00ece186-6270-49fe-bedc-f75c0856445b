package com.jsdz.digitalevidence.task.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.digitalevidence.task.model.TTask;
import com.jsdz.digitalevidence.task.model.TTaskProcess;

import java.util.Date;
import java.util.List;

public class TaskApiVo {

    public TaskApiVo() {

    }

    public TaskApiVo(TTask task, TTaskProcess process) {
        this.title = task.getTitle();
        this.taskId = task.getId();
        this.startTime = process.getStartTime();
        this.endTime = process.getEndTime();
        this.status = process.getStatus();
        this.urlList = task.getUrlList();
        this.cate = task.getCate();
    }

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 标题
     */
    private String title;

    /**
     * 开始时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date endTime;

    /**
     * 任务类型 1、人脸
     */
    private Integer cate;

    /**
     * 任务状态
     */
    private Integer status;

    /**
     * 任务等级
     */
    private Integer level;

    /**
     * 任务附件列表
     */
    private List<String> urlList;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCate() {
        return cate;
    }

    public void setCate(Integer cate) {
        this.cate = cate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<String> getUrlList() {
        return urlList;
    }

    public void setUrlList(List<String> urlList) {
        this.urlList = urlList;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }
}
