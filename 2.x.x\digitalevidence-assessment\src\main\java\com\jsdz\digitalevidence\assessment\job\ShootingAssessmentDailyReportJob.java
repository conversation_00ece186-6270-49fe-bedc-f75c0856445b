/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.job;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.model.ReportType;
import com.jsdz.digitalevidence.assessment.service.AssessmentService;
import com.sun.xml.bind.v2.TODO;

/**
 * @类名: ShootingAssessmentDailyReportJob
 * @说明: 考核报告生成作业
 *        每日生成当期当前考核报告
 *
 * <AUTHOR>
 * @Date	 2017年6月5日 下午4:10:36
 * 修改记录：
 *
 * @see 	
 * 
 * TODO
 *   *. 分块
 */
public class ShootingAssessmentDailyReportJob extends BaseAssessmentJob {
	
	public static final String KEY_PARAM_DATE = "date";
	
	@Autowired
	private AssessmentService assessmentService;


	/* (non-Javadoc)
	 * @see com.jsdz.scheduler.job.AbstractJob#execute(org.quartz.JobExecutionContext)
	 */
/*	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException { 
		// 传入参数
		Map<String, Object> params = this.getParams();
		// 日期
		Date date = (Date) params.get(KEY_PARAM_DATE);
		// [start, date)
		if(date==null)
			date = new Date();
		Cycle c = cc.fromDate(date);
		// 警员
		List<Employees> emps = this.getPolice();
		if(emps==null)
			emps = this.empService.findAllEmployeess();
		try {
			for (Employees emp : emps)
				assessmentService.generateAndSaveAccessmentReport(emp.getId(), 
									c.getStart(), date, ReportType.Daily);
		} catch (Exception e) {
			throw new JobExecutionException(e.getMessage());
		}

	}*/

}
