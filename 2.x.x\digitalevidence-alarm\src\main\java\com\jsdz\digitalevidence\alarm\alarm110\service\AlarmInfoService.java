package com.jsdz.digitalevidence.alarm.alarm110.service;

/**
 * 
 * @类名: AlarmInfoService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-08-29 19:34:11
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmApiVo;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmInfoVo;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.digitalevidence.alarm.alarm110.bean.DeviceAlarmDto;
import com.jsdz.digitalevidence.site.model.Recorder;

public interface AlarmInfoService {

	//新增
	public AjaxResult addAlarmInfo(AlarmInfo alarmInfo);

	//修改
	public AjaxResult updateAlarmInfo(AlarmInfo alarmInfo);

	//删除
	public AjaxResult deleteAlarmInfo(AlarmInfo alarmInfo);

	//按id查询,结果是游离状态的数据
	public AlarmInfo findAlarmInfoById(Long id);

	//按id查询
	public AlarmInfo locateAlarmInfoById(Long id);

	//单个查询
	public AlarmInfo findAlarmInfoByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmInfo> findAllAlarmInfos();

	//列表查询
	public List<AlarmInfo> findAlarmInfosByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmInfo> findAlarmInfosOnPage(Page<AlarmInfo> page, String queryStr,String[] paramNames,Object[] values);

	/**
	 * 添加警情发送记录
	 *
	 * @param alarmInfo
	 * @param devices
	 * @param status
	 */
	void addSendRecord(AlarmInfo alarmInfo, List<Recorder> devices, Integer status);

	/**
	 * 根据警情号获取警情详情
	 * @param alarmCode
	 * @return
	 */
	AlarmInfo findAlarmInfoByCode(String alarmCode);

	/**
	 * 根据执法仪编号获取警情列表
	 * @param code
	 * @return
	 */
	List<AlarmInfoVo> getAlarmListByRecorderCode(String code);

	/**
	 * 执法仪根据警情编号获取警情详情
	 * @param code 执法仪编号
	 * @param alarmCode 警情编号
	 * @return
	 */
    AlarmInfoVo findRecorderAlarm(String code, String alarmCode);

	/**
	 * 根据警情id获取警情
	 * @param id
	 * @return
	 */
	AlarmApiVo getAlarmInfo(Long id);

	/**
	 * 获取出警状态
	 * @param dto
	 * @return
	 */
    Integer alarmStatus(DeviceAlarmDto dto);

	/**
	 * 执法仪扫描给情执行发送接警
	 * @param dto
	 */
	AjaxResult sendReciveAlarmToQZH(DeviceAlarmDto dto);
}

