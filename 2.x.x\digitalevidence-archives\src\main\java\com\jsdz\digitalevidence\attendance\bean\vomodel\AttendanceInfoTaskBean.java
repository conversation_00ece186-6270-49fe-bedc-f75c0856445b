package com.jsdz.digitalevidence.attendance.bean.vomodel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */

public class AttendanceInfoTaskBean implements Serializable {

    private static final long serialVersionUID = 1L;


    private Long attendanceInfoId;

    /**
     * 执行k考勤任务的id
     */
    private Long attendanceTaskId;

    private Integer attendanceTime;

    private String attendanceName;
    private String attendanceDescribe;

    private Integer advanceByMinutes;
    //最后一次打卡时间
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date todayClockTime;
    //最早打卡时间
    private String startTime;
    //最晚打卡时间
    private String endTime;

    public String getAttendanceDescribe() {
        return attendanceDescribe;
    }

    public void setAttendanceDescribe(String attendanceDescribe) {
        this.attendanceDescribe = attendanceDescribe;
    }

    public Long getAttendanceInfoId() {
        return attendanceInfoId;
    }

    public void setAttendanceInfoId(Long attendanceInfoId) {
        this.attendanceInfoId = attendanceInfoId;
    }

    public Long getAttendanceTaskId() {
        return attendanceTaskId;
    }

    public void setAttendanceTaskId(Long attendanceTaskId) {
        this.attendanceTaskId = attendanceTaskId;
    }

    public Integer getAttendanceTime() {
        return attendanceTime;
    }

    public void setAttendanceTime(Integer attendanceTime) {
        this.attendanceTime = attendanceTime;
    }

    public String getAttendanceName() {
        return attendanceName;
    }

    public void setAttendanceName(String attendanceName) {
        this.attendanceName = attendanceName;
    }

    public Integer getAdvanceByMinutes() {
        return advanceByMinutes;
    }

    public void setAdvanceByMinutes(Integer advanceByMinutes) {
        this.advanceByMinutes = advanceByMinutes;
    }


    public Date getTodayClockTime() {
        return todayClockTime;
    }

    public void setTodayClockTime(Date todayClockTime) {
        this.todayClockTime = todayClockTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
