package com.jsdz.www.quartz;

import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

public class RedisService {
	
	private static JedisPool pool = null;
	
	static{
		
		 JedisPoolConfig config = new JedisPoolConfig();
		 
		  // 设置最大连接数
        config.setMaxTotal(200);
        
        // 设置最大空闲数
        config.setMaxIdle(8);
        
        // 设置最大等待时间
        config.setMaxWaitMillis(1000 * 100);
        
        // 在borrow一个jedis实例时，是否需要验证，若为true，则所有jedis实例均是可用的
        config.setTestOnBorrow(true);
        pool = new JedisPool(config, "127.0.0.1", 6379, 3000);
        
	}
	
	RedisDistributed lock = new RedisDistributed(pool); 
	
	 int n = 500;

	    public void seckill() {
	        // 返回锁的value值，供释放锁时候进行判断
//	        String indentifier = lock.lockWithTimeout("resource", 5000, 1000);
//	        System.out.println("值为:"+indentifier);
//	        System.out.println(Thread.currentThread().getName() + "获得了锁");
//	        System.out.println(--n);
//	        
//	        SimpleDateFormat mat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//	        Date date = new Date();
//	        System.out.println("当前时间为"+mat.format(date));
//	        System.out.println();
	        
	        Jedis jedis = pool.getResource();
	        jedis.setnx("key001", "val001");
	        
//	        try {
//				Thread.sleep(100);
//			} catch (InterruptedException e) {
//				e.printStackTrace();
//			}
	        
//	        lock.releaseLock("resource", indentifier);
	    }
	
}
