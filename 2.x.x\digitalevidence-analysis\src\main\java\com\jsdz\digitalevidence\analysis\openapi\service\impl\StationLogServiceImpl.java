package com.jsdz.digitalevidence.analysis.openapi.service.impl;

/**
 * 
 * @类名: StationLogServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-06-09 16:04:15
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.digitalevidence.analysis.openapi.dao.StationLogDao;
import com.jsdz.digitalevidence.analysis.openapi.model.StationLog;
import com.jsdz.digitalevidence.analysis.openapi.service.StationLogService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("StationLogServiceImpl")
public class StationLogServiceImpl implements StationLogService {

	@Autowired
	private StationLogDao stationLogDao;

	//新增
	public AjaxResult addStationLog(StationLog stationLog) {
		AjaxResult result = new AjaxResult();
		stationLogDao.addStationLog(stationLog);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateStationLog(StationLog stationLog) {
		AjaxResult result = new AjaxResult();
		stationLogDao.updateStationLog(stationLog);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteStationLog(StationLog stationLog) {
		AjaxResult result = new AjaxResult();
		stationLogDao.deleteStationLog(stationLog);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public StationLog findStationLogById(Long id){

		return stationLogDao.findStationLogById(id);
	}

	//按 id 查询
	public StationLog locateStationLogById(Long id) {
		return stationLogDao.locateStationLogById(id);
	}

	//单个查询
	public StationLog findStationLogByParam(String queryStr, String[] paramNames, Object[] values) {
		return stationLogDao.findStationLogByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<StationLog> findAllStationLogs() {
		return stationLogDao.findAllStationLogs();
	}

	//列表查询
	public List<StationLog> findStationLogsByParam(String queryStr, String[] paramNames, Object[] values) {
		return stationLogDao.findStationLogsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<StationLog> findStationLogsOnPage(Page<StationLog> page, String queryStr, String[] paramNames, Object[] values) {
		Page<StationLog> pos = stationLogDao.findStationLogsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
