package com.jsdz.digitalevidence.assessment.model.plan;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jsdz.digitalevidence.document.model.Document;
/**
 * @类名: AssessmentPlanDocment
 * @说明: 考核文档
 *
 * <AUTHOR>
 * @Date	 2019年6月19日下午3:05:43
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentPlanDocment implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -8465840967449937058L;
	private Long id;
	private Long planId;
	private Document doc;
	private Integer alloted;
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getPlanId() {
		return planId;
	}
	public void setPlanId(Long planId) {
		this.planId = planId;
	}
	public Document getDoc() {
		return doc;
	}
	public void setDoc(Document doc) {
		this.doc = doc;
	}
	public Integer getAlloted() {
		return alloted;
	}
	public void setAlloted(Integer alloted) {
		this.alloted = alloted;
	}
	
	
	
}
