package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: BriefReportChartsBean
 * @说明: 系统简报图表封装类
 *
 * @author: kenny
 * @Date	2018年3月27日下午9:01:51
 * 修改记录：
 *
 * @see
 */

import java.util.ArrayList;
import java.util.List;

public class BriefReportChartsBean {
	
	List<String> codes; //单位名称
	List<Integer> fileCount;//文件数量
	List<Double> assessmentRate ; //考核率
	List<Double> signRate;//标注率
	List<Double> alarmRate;//警情关联率
	List<Double> caseRate;//案件关联率

	public BriefReportChartsBean(){
		codes = new ArrayList<String>();
		assessmentRate = new ArrayList<Double>();
		signRate = new ArrayList<Double>();
		alarmRate = new ArrayList<Double>();
		caseRate = new ArrayList<Double>();
		fileCount = new ArrayList<Integer>();
	}
	
	public void assign(BriefReport src){
		this.getCodes().add(src.getOrgName());
		double douValue = 0;
		douValue = src.getAssessmentRate()==null?0:Math.round(src.getAssessmentRate()*100.00)/100.00;  
		this.getAssessmentRate().add(douValue);
		douValue = src.getSignRate()==null?0:Math.round(src.getSignRate()*100.00)/100.00; 
		this.getSignRate().add(douValue);
		douValue = src.getAlarmRate()==null?0:Math.round(src.getAlarmRate()*100.00)/100.00; 
		this.getAlarmRate().add(douValue);
		douValue = src.getCaseRate()==null?0:Math.round(src.getCaseRate()*100.00)/100.00; 
		this.getCaseRate().add(douValue);
		this.getFileCount().add(src.getFileCount()==null?0:src.getFileCount().intValue());
	}


	public List<String> getCodes() {
		return codes;
	}

	public void setCodes(List<String> codes) {
		this.codes = codes;
	}

	public List<Integer> getFileCount() {
		return fileCount;
	}

	public void setFileCount(List<Integer> fileCount) {
		this.fileCount = fileCount;
	}

	public List<Double> getAssessmentRate() {
		return assessmentRate;
	}

	public void setAssessmentRate(List<Double> assessmentRate) {
		this.assessmentRate = assessmentRate;
	}

	public List<Double> getSignRate() {
		return signRate;
	}

	public void setSignRate(List<Double> signRate) {
		this.signRate = signRate;
	}

	public List<Double> getAlarmRate() {
		return alarmRate;
	}

	public void setAlarmRate(List<Double> alarmRate) {
		this.alarmRate = alarmRate;
	}

	public List<Double> getCaseRate() {
		return caseRate;
	}

	public void setCaseRate(List<Double> caseRate) {
		this.caseRate = caseRate;
	}
	

}
