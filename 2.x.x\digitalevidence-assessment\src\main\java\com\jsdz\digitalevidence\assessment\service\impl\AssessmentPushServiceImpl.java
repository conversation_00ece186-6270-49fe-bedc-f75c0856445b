/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.service.impl;

import static com.jsdz.digitalevidence.assessment.AssessmentUtils.nullOrLong;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.jsdz.admin.security.model.Operator;
import org.hibernate.transform.ResultTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.utils.SessionUtils;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.AssessmentConsts;
import com.jsdz.digitalevidence.assessment.AssessmentUtils;
import com.jsdz.digitalevidence.assessment.bean.AssessmentPushDocBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentPushDocQueryBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentOfPushDocBean;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.cycle.CycleConfiguration;
import com.jsdz.digitalevidence.assessment.cycle.CycleSelector;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPushDocDao;
import com.jsdz.digitalevidence.assessment.dao.ShootingAssessmentDao;
import com.jsdz.digitalevidence.assessment.model.AssessmentCate;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessment;
import com.jsdz.digitalevidence.assessment.model.push.AssessmentPushDoc;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocStrategy;
import com.jsdz.digitalevidence.assessment.service.AssessmentPushService;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.digitalevidence.document.service.DocumentService;
import com.jsdz.reportquery.ChainFilter;
import com.jsdz.reportquery.NullFilter;
import com.jsdz.reportquery.ReportQueryDao;
import com.jsdz.reportquery.ReportQueryUtils;
import com.jsdz.reportquery.dynsql.fork.support.tree.TreeField;
import com.jsdz.utils.DTOUtils.Filter;

/**
 * @类名: AssessmentPushServiceImpl
 * @说明: 考核视频推送服务实现
 *
 * <AUTHOR>
 * @Date	 2017年9月26日 上午11:16:00
 * 修改记录：
 *
 * @see 	 
 */
@Service
public class AssessmentPushServiceImpl implements AssessmentPushService {
	
	/** */
	@Autowired
	private PushDocStrategy pushDocStrategy;
	@Autowired
	private DocumentService docService;
	@Autowired
	private CycleConfiguration cc;
	@Autowired
	private ReportQueryDao rqDao;
	@Autowired
	private AssessmentPushDocDao pushDocDao;
	@Autowired
	private ShootingAssessmentDao saDao;
	
	@SuppressWarnings("unchecked")
	@Override
	public Page<AssessmentPushDocBean> searchPushDoc(Page<AssessmentPushDocBean> page, AssessmentPushDocQueryBean qb) 
					throws Exception {
		//
		Long assessorId = SessionUtils.getCurrentOperator().getEmployees().getId();
		// 转换参数
		Map<String, Object> kvs = ReportQueryUtils.queryBean3Map(qb,
				new ChainFilter(
						// 过滤Null参数
						new NullFilter(),
						// 过滤TreeField参数
						new Filter() {
							public boolean accept(String propName, Object value) {
								if (value instanceof TreeField) {
									TreeField tr = (TreeField) value;
									if (tr.getPath() == null)
										return false;
									if (tr.getIncludeSub() == null)
										tr.setIncludeSub(false);
								}
								return true;
							}
						},
						// 过滤周期参数
						new Filter() {
							@Override
							public boolean accept(String propName, Object value) {
								if ("cycleDate".equals(propName) || "cycleSelector".equals(propName))
									return false;
								return true;
							}
						}));
		// 周期参数
		if (qb != null && qb.getCycleDate() != null) {
			Cycle cycle = getQueryCycle(qb.getCycleDate(), qb.getCycleSelector());
			kvs.put("startDate", cycle.getStart());
			kvs.put("endDate", cycle.getEnd());
		}
		// 考核员
		kvs.put("assessorId", assessorId);
		// 参数
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		return (Page<AssessmentPushDocBean>) rqDao.pageQueryNamedSQL(page,
				AssessmentConsts.SQL_KEY_Search_Assessment_Pushing_Doc, paramNames, values);
	}
	
	@Override
	public void pushDoc() {
		Cycle cycle = cc.getCurrentCycle();
		pushDocStrategy.push(cycle);
	}

	@SuppressWarnings("unchecked")
	@Override
	@Transactional(readOnly=true)
	public ShootingAssessmentOfPushDocBean openPushDocAssessment(Long pushingDocId, Operator operator, String ip) {
		// 检查是否已冻结考核报告
		// ...
		// 文档路径
		AssessmentPushDoc pushDoc = this.pushDocDao.get(pushingDocId);
		String docUrl = docService.getPlayPath(pushDoc.getDoc().getId(), operator, ip);
		//
		List<ShootingAssessmentOfPushDocBean> items = (List<ShootingAssessmentOfPushDocBean>) rqDao.queryNamedSQL(
				AssessmentConsts.SQL_KEY_Open_Assessment_Pushing, new String[] { "pushingDocId" }, 
										new Object[] { pushingDocId },
				new ResultTransformer() {
					@Override
					public Object transformTuple(Object[] tuples, String[] aliases) {
						Long pushingDocId = AssessmentUtils.nullOrLong((BigInteger)tuples[0]);
						String workNumber = (String) tuples[1];
						String policeName = (String) tuples[2];
						String position = (String) tuples[3];
						String sex = (String) tuples[4];
						Integer age = ((Integer) tuples[5]);
						Long docId = AssessmentUtils.nullOrLong((BigInteger) tuples[6]);
						String alarmCode = (String) tuples[7];
						String alarmName = (String) tuples[8];
						String alarmTel = (String) tuples[9];
						Long alarmId = nullOrLong((BigInteger) tuples[10]);
						String alarmContext = (String) tuples[11];
						Integer isRelation = ((Integer) tuples[12]);
						String contentType = ((String) tuples[13]);
						ShootingAssessmentOfPushDocBean bean = new ShootingAssessmentOfPushDocBean();
						bean.setPushDocId(pushingDocId);
						bean.setPoliceCode(workNumber);
						bean.setAge(age);
						bean.setPosition(position);
						bean.setName(policeName);
						bean.setSex(sex);
						bean.setDocId(docId);
						bean.setAlarmCode(alarmCode);
						bean.setAlarmName(alarmName);
						bean.setAlarmTel(alarmTel);
						bean.setAlarmId(alarmId);
						bean.setAlarmContext(alarmContext);
						bean.setIsRelation(isRelation);
						bean.setContentType(contentType);
						return bean;
					}

					@Override
					public List transformList(List collection) {
						return collection;
					}
				});
		//
		ShootingAssessmentOfPushDocBean bean = items.get(0);
		bean.setDocUrl(docUrl);
		// 设置考核类型，随机
		bean.setCate(AssessmentCate.RANDOM);
		return bean;

	}

	@Override
	@Transactional
	public void submitPushDocAssessment(Long pushingDocId, ShootingAssessment assessment) {
		// 考核员
		Employees police = SessionUtils.getCurrentOperator().getEmployees();
		//
		Document doc = docService.getDocument(assessment.getDocument().getId());
		//
		assessment.setDocument(doc);
		assessment.setInspector(police);
		assessment.setAssessDate(new Date());
		//
		saDao.saveOrUpdate(assessment);
		// 写入推送文档考核状态
		this.pushDocDao.updatePushDocStatus(pushingDocId, true);

	}
	
	private Cycle getQueryCycle(Date cycleDate, CycleSelector selector) {
		Cycle cycle = cc.fromDate(cycleDate);
		if(selector==null)
			return cycle;
		if(selector.equals(CycleSelector.PREV))
			cycle = cc.prevCycle(cycle);
		else if(selector.equals(CycleSelector.NEXT))
			cycle = cc.nextCycle(cycle);
		return cycle;
	}

}
