package com.jsdz.digitalevidence.assessment.bean.plan;


import com.jsdz.admin.security.model.Employees;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPolice;
/**
 * @类名: AssessmentPlanPoliceBean
 * @说明: 
 *
 * <AUTHOR>
 * @Date	 2019年6月22日上午9:16:47
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentPlanPoliceBean {

	private Long id;
	private Long planId;
	private Long policeId;
	private String policeCode;
	private String policeName;
	private String orgCode;
	private String orgName;
	
	
	public void assign(AssessmentPlanPolice sur){
		this.setId(sur.getId());
		this.setPlanId(sur.getPlanId());
		if (null != sur.getPolice()){
			this.setPoliceId(sur.getPolice().getId());
			this.setPoliceCode(sur.getPolice().getWorkNumber());
			this.setPoliceName(sur.getPolice().getName());
			if (null != sur.getPolice().getOrganization()){
				this.setOrgCode(sur.getPolice().getOrganization().getOrgCode());
				this.setOrgName(sur.getPolice().getOrganization().getOrgName());
			}
		}
	}
	public AssessmentPlanPolice assignTo(){
		AssessmentPlanPolice dest = new AssessmentPlanPolice();
		assignTo(dest);
		return dest;
	}
			
	public void assignTo(AssessmentPlanPolice dest){
		dest.setId(this.id);
		dest.setPlanId(this.getPlanId());
		if (null==this.getPoliceId()){
			Employees police = new Employees();
			police.setId(this.getPoliceId());
			dest.setPolice(police);
		}
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getPlanId() {
		return planId;
	}
	public void setPlanId(Long planId) {
		this.planId = planId;
	}
	public Long getPoliceId() {
		return policeId;
	}
	public void setPoliceId(Long policeId) {
		this.policeId = policeId;
	}
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	
	
}
