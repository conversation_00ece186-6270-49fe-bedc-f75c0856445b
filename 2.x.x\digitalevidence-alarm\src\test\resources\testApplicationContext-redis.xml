<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
	
	<!-- 构建连接池配置信息 -->
	<bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
		<!-- 设置最大连接数 -->
		<property name="maxTotal" value="${redis.maxTotal}"></property>
		<!-- 设置最大空闲数 -->
		<property name="maxIdle" value="${redis.maxIdle}"></property>
		<!-- 设置最大等待时间-->
		<property name="maxWaitMillis" value="${redis.maxWait}"></property>
		<!-- 在borrow一个jedis实例时，是否需要验证，若为true，则所有jedis实例均是可用的-->
		<property name="testOnBorrow" value="${redis.testOnBorrow}"></property>
	</bean>
	
	<!-- 定义集群连接池 -->
	<bean  class="redis.clients.jedis.JedisPool">
		<constructor-arg index="0"   ref="jedisPoolConfig"></constructor-arg>
		<constructor-arg index="1" value="${redis.host}" ></constructor-arg>
		<constructor-arg index="2" value="${redis.port}"></constructor-arg>
		<constructor-arg index="3" value="${redis.timeout}"></constructor-arg>
	</bean>
	
	
</beans>
