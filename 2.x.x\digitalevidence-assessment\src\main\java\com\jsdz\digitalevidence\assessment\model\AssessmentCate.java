package com.jsdz.digitalevidence.assessment.model;

/**
 * 
 * @类名: AssessmentCate
 * @说明: 评核类型
 *
 * <AUTHOR>
 * @Date	 2017年4月13日 下午5:46:34
 * 修改记录：
 *
 * @see
 */
public enum AssessmentCate {
	
	NORMAL(1, "日常"), // 日常监督
	RANDOM(2, "随机"); // 随机抽查
	
	public int index;
	public String name;
	
	private AssessmentCate(int index, String name) {
		this.name = name;
	}
	public int getIndex() {
		return index;
	}
	public void setIndex(int index) {
		this.index = index;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
}
