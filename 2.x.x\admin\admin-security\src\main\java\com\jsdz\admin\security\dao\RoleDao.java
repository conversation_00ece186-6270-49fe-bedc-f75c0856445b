package com.jsdz.admin.security.dao;

/**
 *
 * @类名: RoleDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-05 17:43:37
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.Role;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface RoleDao extends GenericORMEntityDAO<Role,Long> {

	//新增
	public void addRole(Role role);

	//修改
	public void updateRole(Role role);

	//删除
	public void deleteRole(Role role);
	
	//查询所有
	public List<Role> findAll();
	
	//按id查询
	public Role locateRoleById(Long id);
	//单个查询
	public Role findRoleByCondition(String queryStr,String[] paramNames,Object[] values);

	//列表查询
	public List<Role> findRolesByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Role> findRolesOnPage(Page<Role> page,String queryStr,String[] paramNames,Object[] values);

}

