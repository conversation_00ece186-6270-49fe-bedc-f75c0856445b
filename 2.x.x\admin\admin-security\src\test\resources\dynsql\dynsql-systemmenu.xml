<?xml version="1.0" encoding="utf-8"?>
<sqlTemplates>
	<entry>
		
		<string>findMenuByParentId</string>
		
		<sqlTemplate>
			<sqlPattern>
				select m.id,          
				m.menu_name as menuName,     
				m.menu_url as menuUrl,          
				m.menu_type as menuType,
				m.is_show as isShow,  m.sort as sort,              
				m.create_time as createTime,    
				m.parent_id as parentId,
				m.image_url imageUrl,
				m2.menu_name as parentName
				from admin_systemmenu m
				{0}
				left join admin_systemmenu m2 on m.parent_id = m2.id
				where m.is_show = 1 and m.menu_type = 1
				{1}    
	            order by m.sort ASC 
			</sqlPattern>
			<itemClass>com.jsdz.admin.security.bean.SystemMenuBean</itemClass>
			<pieces>
				<!-- {0} -->
         		<sqlPiece> <fork><paths>         			
         			<path>
           			<paramName>userId</paramName>
           			<t>inner join(
					select t1.menu_id as id from admin_role_menu t1,admin_operatorroles t2
						where t1.role_id = t2._roleId and t2._operatorId =  :userId
						group by t1.menu_id
						order by t1.menu_id
	     			) m1 on m.id = m1.id</t>
           			<a class="IsNotNull"/>
         			</path>
         			
         			</paths> </fork></sqlPiece>
         		<!-- {1} -->
         		<sqlPiece> <fork><paths><path>
           		<paramName>parentId</paramName>
           			<t>and  IFNULL(m.parent_id,0) = :parentId</t>
           			<a class="IsNotNull"/>
         		</path></paths> </fork></sqlPiece>
			
 			</pieces>
		</sqlTemplate>
	</entry>
</sqlTemplates>