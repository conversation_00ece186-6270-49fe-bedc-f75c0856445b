/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.dao.hibernate;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.approve.dao.ApproveBusinessDao;
import com.jsdz.digitalevidence.approve.model.ApproveBusiness;

/**
 * @类名: ApproveBusinessDaoImpl
 * @说明: 审批业务Dao
 *
 * <AUTHOR>
 * @Date	 2017年4月17日 下午5:18:16
 * 修改记录：
 *
 * @see 	 
 */
@Repository
public class ApproveBusinessDaoImpl extends GenericEntityDaoHibernateImpl<ApproveBusiness, Long>
					implements ApproveBusinessDao {

	@Override
	public List<ApproveBusiness> getAllApproveBusiness() {
		String hql = "from ApproveBusiness b ";
		//
		return find(hql);
		
	}
	
}
