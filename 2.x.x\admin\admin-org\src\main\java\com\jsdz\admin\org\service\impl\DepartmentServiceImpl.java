package com.jsdz.admin.org.service.impl;

import java.util.Date;

/**
 * 
 * @类名: DepartmentServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-10 08:34:34
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.admin.org.dao.DepartmentDao;
import com.jsdz.admin.org.model.Department;
import com.jsdz.admin.org.service.DepartmentService;
import com.jsdz.admin.org.utils.MD5;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

@Service("DepartmentServiceImpl")
public class DepartmentServiceImpl implements DepartmentService {

	@Autowired
	private DepartmentDao departmentDao;

	//新增
	public AjaxResult addDepartment(Department department) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == department){
			result.setMsg("没有数据可保存");
			return result;
		}
		
		if (null == department.getName() || "".equals(department.getName())){
			result.setMsg("部门名称必须输入");
			return result ;
		}
		
		if (null != departmentDao.findDepartmentByCondition(
				  "from Department d where d.name=:name",
				  new String[]{"name"},new Object[]{department.getName()})){
			result.setMsg("此部门名称已存在");
			return result;
		}
		
		department.setCreateTime(new Date());
		departmentDao.addDepartment(department); 
		result.setSuccess(true);
		result.setData(department);
		result.setMsg("新增保存成功。");
		return result;  
	}

	//修改
	public AjaxResult updateDepartment(Department department) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == department){
			result.setMsg("没有数据可修改");
			return result;
		}
		
		Department department1 =departmentDao.locateDepartmentById(department.getId());
		if (null == department1){
			result.setMsg("不存在的部门资料");
			return result;
		}

		if (null == department.getName() || "".equals(department.getName())){
			result.setMsg("部门名称必须输入");
			return result ;
		}
		
		if (null != departmentDao.findDepartmentByCondition(
					"from Department d where d.name=:name and d.id != :id",
				  new String[]{"name","id"},new Object[]{department.getName(),department.getId()})){
			result.setMsg("部门名称重复");
			return result;
		}

		department1.setName(department.getName());
		department1.setDescription(department.getDescription());
		departmentDao.updateDepartment(department1);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 	
	}

	//删除
	public AjaxResult deleteDepartment(Department department) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == department){
			result.setMsg("没有数据可删除");
			return result;
		}
		if (null == departmentDao.locateDepartmentById(department.getId())){
			result.setMsg("不存在的部门资料");
			return result;
		}
		
		
		departmentDao.deleteDepartment(department);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 	}

	//按 id 查询
	public Department locateDepartmentById(Long id) {
		return departmentDao.locateDepartmentById(id);
	}

	//单个查询
	public Department findDepartmentByParam(String queryStr, String[] paramNames, Object[] values) {
		return departmentDao.findDepartmentByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<Department> findAllDepartments() {
		return departmentDao.findAllDepartments();
	}

	//列表查询
	public List<Department> findDepartmentsByParam(String queryStr, String[] paramNames, Object[] values) {
		return departmentDao.findDepartmentsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<Department> findDepartmentsOnPage(Page<Department> page, String queryStr, String[] paramNames, Object[] values) {
		Page<Department> pos = departmentDao.findDepartmentsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
