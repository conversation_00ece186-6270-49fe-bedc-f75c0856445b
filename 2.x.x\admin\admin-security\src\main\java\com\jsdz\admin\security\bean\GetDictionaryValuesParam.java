package com.jsdz.admin.security.bean;
/**
 * 
 * @类名: GetDictionaryValuesParam
 * @说明: 字典内容查询参数土封装类
 *
 * @author: kenny
 * @Date	2017年10月31日下午6:50:39
 * 修改记录：
 *
 * @see
 */
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.jsdz.admin.security.model.DictionaryValues;
import com.jsdz.core.Page;
import com.jsdz.core.SortBean;


public class GetDictionaryValuesParam extends ParamGenerateHql{
	private Page<DictionaryValues> page;
	private Map<String, Object> kvs = new HashMap<String, Object>();
	private List<String> parLst = new ArrayList<String>();
	private String querySql;
	private String totalSql;
	private Long dictionaryId;
	private String value;
	private String code;
	
	public GetDictionaryValuesParam(){
		page = new Page<DictionaryValues>();
		page.setOffset(0);
		page.setPageSize(20);
	}	
	public String queryBeanToHql(){
		parLst.clear();
		
		querySql=" from DictionaryValues d left join fetch d.dictionary c ";
		totalSql = "select count(*) from DictionaryValues d left join d.dictionary c";
		String p1 ="";
		if (this.getDictionaryId() != null && this.getDictionaryId() != 0){
			p1 = " c.id = :id ";
			parLst.add(p1);
			kvs.put("id", this.getDictionaryId());
			
		}
		if (this.getCode() != null && !"".equals(this.getCode())){
			p1 = " c.code = :code ";
			parLst.add(p1);
			kvs.put("code", this.getCode());
		}
		
		if (this.getValue() != null && !"".equals(this.getValue())){
			p1 = " d.value like :value ";
			parLst.add(p1);
			kvs.put("value", "%" + this.getValue().toString() + "%");
		}
		
		querySql = this.generateHql(page, parLst, querySql, totalSql);
		
		StringBuilder sb = new StringBuilder();
		if (page.getSort() != null && page.getSort().size() > 0 ){
			sb.append(" order by ");
			List<SortBean> list = page.getSort();
			for (SortBean sortBean : list){
				sb.append(sortBean);
				sb.append(" ");
				sb.append(sortBean.getOrder().toString());
			}
			
		}else{
			sb.append(" order by d.order asc ");
		}
		
		return querySql + sb;
	}


	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getQuerySql() {
		return querySql;
	}
	public void setQuerySql(String querySql) {
		this.querySql = querySql;
	}
	public String getTotalSql() {
		return totalSql;
	}
	public void setTotalSql(String totalSql) {
		this.totalSql = totalSql;
	}

	public Long getDictionaryId() {
		return dictionaryId;
	}

	public void setDictionaryId(Long dictionaryId) {
		this.dictionaryId = dictionaryId;
	}
	public Page<DictionaryValues> getPage() {
		return page;
	}
	public void setPage(Page<DictionaryValues> page) {
		this.page = page;
	}
	public Map<String, Object> getKvs() {
		return kvs;
	}
	public void setKvs(Map<String, Object> kvs) {
		this.kvs = kvs;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	
	

}
