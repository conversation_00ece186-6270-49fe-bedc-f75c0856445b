package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: PostDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-12-30 11:53:22
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;

import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.dao.PostDao;
import com.jsdz.admin.security.model.Post;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class PostDaoImpl extends GenericEntityDaoHibernateImpl<Post,Long> implements PostDao{

	//新增
	public void addPost(Post post) {
		this.saveOrUpdate(post);
	}

	//删除
	public void deletePost(Post post) {
		this.delete(post);
	}

	//修改
	public void updatePost(Post post) {
		this.merge(post);
	}

	//按id查询(游离状态)
	public Post findPostById(Long id){

		final String  hql = "from Post p where p.id = :id";
		final Long oid = id;
		Post data = getHibernateTemplate().execute(new HibernateCallback<Post>() {
			public Post doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<Post> list = query.list();
				Post rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public Post locatePostById(Long id){

		final String  hql = "from Post p where p.id = :id";
		final Long oid = id;
		Post data = getHibernateTemplate().execute(new HibernateCallback<Post>() {
			public Post doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<Post> list = query.list();
				Post rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public Post findPostByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		Post data = getHibernateTemplate().execute(new HibernateCallback<Post>() {
		public Post doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<Post> list = query.list();
			Post rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<Post> findAllPosts(){
		return this.find("from Post post ");
	}

	//列表查询
	public List<Post> findPostsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<Post> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<Post> findPostsOnPage(Page<Post> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<Post>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	//更新下级的path
	public void updatePostPath(String oldPath,String newPath){
		//2、取得所有下级单位并更新path
		List<Post> list = findPostsByCondition(
				"from Post p where p.path like :path ",
				new String[]{"path"},
				new Object[]{oldPath + '%'});
		for (Post p : list){
			String path = p.getPath().replaceFirst(oldPath,newPath);
			p.setPath(path);
			this.merge(p);
		}
		
	} 

}
