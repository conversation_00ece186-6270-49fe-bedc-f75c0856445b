package com.jsdz.digitalevidence.alarm.alarm110.bean;

import java.util.Date;

import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.model.EnforceType;
import com.jsdz.digitalevidence.document.model.ImportantLevel;
import com.jsdz.digitalevidence.document.model.VideoClarity;
import com.jsdz.utils.DateTimeUtils;

/**
 * 
 * @类名: docAlarmCaseInfoBean
 * @说明: 视频关联类DTO
 *
 * <AUTHOR>
 * @Date	 2018年4月24日 上午11:18:38
 * 修改记录：
 *
 * @see
 */
public class docAlarmCaseInfoBean extends AbstractDTO{
	private Long id;
	/** 警员编号*/
	private String policeCode;
	private String policeName;
	private String siteCode;
	/** 警员所属单位*/
	private String orgName;
	private String orgPath;
	/** 类型，计算于ContentType*/
	private String type;
	private Long enforceTypeId;
	private String enforceTypeName;
	/** 重要级别*/
	private ImportantLevel impLevelRec;
	private ImportantLevel impLevel;
	/** 资料文件名称*/
	private String docName;
	private String thumbnail;
	private Date uploadTime;
	/** 拍摄时间*/
	private Date createTime;
	/** 执法类型*/
	private EnforceType enforceType;
	private Long duration;
	private DocumentCate cate;
	private VideoClarity clarity;
	private String equimentCode;
	/** 文件大少，单位：M*/ 
	private Long fileM;
	/** 站点地址*/
	private String siteAddr;
	/** 说明*/
	private String comments;

	private Integer storageType = 1;
	//案件编号
	private String caseCode;
	//警情编号
	private String alarmCode;
	public String getDurationStr() {
		return DateTimeUtils.durationToStr(duration);
	}

	public String getFileSizeStr() {
		if(fileM==null)
			return "-";
		long k = fileM / 1024;
		if(k==0)
			return "<1K";
		long m = k / 1024;
		k = k % 1024;
		if(m==0)
			return k+"K";
		long g = m / 1024;
		m = m % 1024;
		if(g==0)
			return m+"M"+k+"K";
		return g+"G"+ m+"M"+k+"K";

	}

	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public String getSiteCode() {
		return siteCode;
	}
	public void setSiteCode(String siteCode) {
		this.siteCode = siteCode;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getDocName() {
		return docName;
	}
	public void setDocName(String docName) {
		this.docName = docName;
	}
	public Date getUploadTime() {
		return uploadTime;
	}
	public void setUploadTime(Date uploadTime) {
		this.uploadTime = uploadTime;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public EnforceType getEnforceType() {
		return enforceType;
	}
	public void setEnforceType(EnforceType enforceType) {
		this.enforceType = enforceType;
	}
	public VideoClarity getClarity() {
		return clarity;
	}
	public void setClarity(VideoClarity clarity) {
		this.clarity = clarity;
	}
	public String getEquimentCode() {
		return equimentCode;
	}
	public void setEquimentCode(String equimentCode) {
		this.equimentCode = equimentCode;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getFileM() {
		return fileM;
	}
	public void setFileM(Long fileM) {
		this.fileM = fileM;
	}
	public String getSiteAddr() {
		return siteAddr;
	}
	public void setSiteAddr(String siteAddr) {
		this.siteAddr = siteAddr;
	}
	public ImportantLevel getImpLevel() {
		return impLevel;
	}
	public void setImpLevel(ImportantLevel impLevel) {
		this.impLevel = impLevel;
	}

	public DocumentCate getCate() {
		return cate;
	}

	public void setCate(DocumentCate cate) {
		this.cate = cate;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgPath() {
		return orgPath;
	}

	public void setOrgPath(String orgPath) {
		this.orgPath = orgPath;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Long getDuration() {
		return duration;
	}

	public void setDuration(Long duration) {
		this.duration = duration;
	}

	public Long getEnforceTypeId() {
		return enforceTypeId;
	}

	public void setEnforceTypeId(Long enforceTypeId) {
		this.enforceTypeId = enforceTypeId;
	}

	public String getEnforceTypeName() {
		return enforceTypeName;
	}

	public void setEnforceTypeName(String enforceTypeName) {
		this.enforceTypeName = enforceTypeName;
	}

	public ImportantLevel getImpLevelRec() {
		return impLevelRec;
	}

	public void setImpLevelRec(ImportantLevel impLevelRec) {
		this.impLevelRec = impLevelRec;
	}

	public Integer getStorageType() {
		return storageType;
	}

	public void setStorageType(Integer storageType) {
		this.storageType = storageType;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}

	public String getCaseCode() {
		return caseCode;
	}

	public void setCaseCode(String caseCode) {
		this.caseCode = caseCode;
	}

	public String getAlarmCode() {
		return alarmCode;
	}

	public void setAlarmCode(String alarmCode) {
		this.alarmCode = alarmCode;
	}

}
