package com.jsdz.digitalevidence.utils.response;


/**
 * 
 * <AUTHOR>
 * @create 2023/12/19 11:40:52
 *
 */
public class ResponseUtil {
	/**
	 * 操作成功
	 * @param data
	 * @return
	 */
	public static <T> Result<T> ok(T data) {
		Result<T> res = new Result<>();
		Integer code = ResultEnumCode.SUCCESS.getCode();
		String name = ResultEnumCode.getName(code);
		res.setCode(code);
		res.setMsg(name);
		res.setData(data);
		return res;
	}

	/**
	 * 操作成功
	 * @param code
	 * @param msg
	 * @param data
	 * @return
	 */
	public static <T> Result<T> ok(Integer code, String msg,T data) {
		Result<T> res = new Result<>();
		res.setCode(code);
		res.setMsg(msg);
		res.setData(data);
		return res;
	}

	/**
	 * 操作失败
	 * @param data
	 * @return
	 */
	public static <T> Result<T> fall(T data) {
		Result<T> res = new Result<>();
		Integer code = ResultEnumCode.FAIL.getCode();
		String name = ResultEnumCode.getName(code);
		res.setCode(code);
		res.setMsg(name);
		res.setData(data);
		return res;
	}

	/**
	 * 操作失败
	 * @param code
	 * @param msg
	 * @param data
	 * @return
	 */
	public static <T> Result<T> fall(Integer code, String msg,T data) {
		Result<T> res = new Result<>();
		res.setCode(code);
		res.setMsg(msg);
		res.setData(data);
		return res;
	}
	
	/**
	 * 操作错误异常
	 * @param code
	 * @param msg
	 * @param data
	 * @return
	 */
	public static <T> Result<T> error(Integer code, String msg, T data) {
		Result<T> res = new Result<>();
		res.setCode(code);
		res.setMsg(msg);
		res.setData(data);
		return res;
	}
	/**
	 * 系统级异常
	 * @param code
	 * @param msg
	 * @param data
	 * @return
	 */
	public static <T> Result<T> exception(Integer code, String msg, T data) {
		Result<T> res = new Result<>();
		res.setCode(code);
		res.setMsg(msg);
		res.setData(data);
		return res;
	}



}
