/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment;

/**
 * @类名: AssessmentReportHasBeenSubmitException
 * @说明: 考核报告已提交异常
 * 
 * <AUTHOR>
 * @Date	 2017年4月27日 下午2:05:19
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentReportHasBeenSubmitException extends AssessmentException {

	public AssessmentReportHasBeenSubmitException() {
		super();
		// TODO Auto-generated constructor stub
	}

	public AssessmentReportHasBeenSubmitException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
		// TODO Auto-generated constructor stub
	}

	public AssessmentReportHasBeenSubmitException(String message, Throwable cause) {
		super(message, cause);
		// TODO Auto-generated constructor stub
	}

	public AssessmentReportHasBeenSubmitException(String message) {
		super(message);
		// TODO Auto-generated constructor stub
	}

	public AssessmentReportHasBeenSubmitException(Throwable cause) {
		super(cause);
		// TODO Auto-generated constructor stub
	}
	
	

}
