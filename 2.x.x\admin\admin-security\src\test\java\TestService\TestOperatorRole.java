package TestService;
/**
 * 
 * @类名: TestOperatorRole.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月3日下午12:10:08
 * 修改记录：
 *
 * @see
 */

import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.service.OperatorRoleService;
import com.jsdz.admin.security.service.OperatorService;
import com.jsdz.admin.security.service.RoleService;

@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { "classpath:/testApplicationContext-common.xml" })

public class TestOperatorRole {

	@Autowired
	private OperatorRoleService operatorRoleService;
	@Autowired
	private OperatorService operatorService;
	@Autowired
	private RoleService roleService;
	

	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}
	//add
	
	//@Test
	@Transactional
	public void AddOperatorRole()
	{
		List<Operator> os = operatorService.findOperatorsByParam("from Operator o where o.id=:id",
				new String[]{"id"}, 
				new Object[]{(long)3});
		List<Role> rs = roleService.findRolesByParam("from Role r where r.id = :id", 
				new String[]{"id"}, 
				new Object[]{(long)1});
		
		Operator operator = os == null || os.size() <=0 ?null:os.get(0);
		Role role = rs == null || rs.size() <=0 ?null:rs.get(0);
		
		OperatorRole operatorRole = new OperatorRole();
		operatorRole.setOperator(operator);
		operatorRole.setRole(role);
		
		AjaxResult result = operatorRoleService.addOperatorRole(operatorRole);
		System.out.println("【"+result.isSuccess() + ";" + result.getMsg().toString()+"】");
	}
	
	//upd
	//@Test
	@Transactional
	public void UpdateOperatorRole()
	{
		OperatorRole operatorRole = operatorRoleService.findOperatorRoleByParam("from OperatorRole o where o.id=:id", 
				new String[]{"id"},new Object[]{(long)3});
		
	
		//operatorRole.setRole(null);
		
		Operator operator = operatorService.findOperatorByParam("from Operator o where o.id=:id",
				new String[]{"id"},new Object[]{(long)3});
		
		operatorRole.setOperator(operator);
		
		
		AjaxResult result = operatorRoleService.updateOperatorRole(operatorRole);
		System.out.println("【"+result.isSuccess() + ";" + result.getMsg().toString()+"】");
		
	}	
	//delete
	@Test
	@Transactional
	public void deleteOperatorRole()
	{
		OperatorRole operatorRole = operatorRoleService.findOperatorRoleByParam("from OperatorRole o where o.id=:id", 
				new String[]{"id"},new Object[]{(long)13});
		
	
		//operatorRole.setRole(null);
		
		//Operator operator = operatorService.findOperatorByParam("from Operator o where o.id=:id",
		//		new String[]{"id"},new Object[]{(long)3});
		
		//operatorRole.setOperator(operator);
		
		
		AjaxResult result = operatorRoleService.deleteOperatorRole(operatorRole);
		System.out.println("【"+result.isSuccess() + ";" + result.getMsg().toString()+"】");
		
	}	
	//list
	
	
}
