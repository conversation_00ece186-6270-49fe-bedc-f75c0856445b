package com.jsdz.digitalevidence.statistics.mapper;

import com.jsdz.digitalevidence.statistics.bean.param.AbnormalParam;
import com.jsdz.digitalevidence.statistics.model.SysAbnormalStatisticsModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface SysAbnormalStatisticsMapper extends BaseMapper<SysAbnormalStatisticsModel> {

    Integer selectModelCount(@Param("param")AbnormalParam param);

    List<SysAbnormalStatisticsModel> selectModels(@Param("param")AbnormalParam param);

    @Select("select * from v_ultra_short_video_statistics limit 1000")
    List<SysAbnormalStatisticsModel> UltraShortVideoStatistics();

    @Select("select * from v_ultra_short_alarm_statistics limit 1000")
    List<SysAbnormalStatisticsModel> UltraShortAlarmStatistics();

    @Select("select * from v_ultra_short_case_statistics limit 1000")
    List<SysAbnormalStatisticsModel> UltraShortCaseStatistics();

    List<SysAbnormalStatisticsModel> deviceIdleStatistics(int day);

    @Update("update t_doc set IS_ERROR_VERIFICATION = 1 where doc_id = #{id}")
    void updateDoc(@Param("id")Long id);

    @Update("update t_alarm_info set IS_ERROR_VERIFICATION = 1 where id = #{id}")
    void updateAlarm(@Param("id")Long id);

    @Update("update t_case set IS_ERROR_VERIFICATION = 1 where case_id = #{id}")
    void updateCase(@Param("id")Long id);

    List<SysAbnormalStatisticsModel> siteOnlineStatistics(float rate);

    List<SysAbnormalStatisticsModel> siteCapacityStatistics(float rate);

    /**
     * 修改采集站在线时长
     * @param siteNo 采集站编号
     * @param totalTime 累计在线时长
     */
    @Update("update t_site set TOTAL_ONLINE_TIME = (CASE WHEN TOTAL_ONLINE_TIME > #{totalTime} THEN TOTAL_ONLINE_TIME + #{totalTime} ELSE #{totalTime} END) where SITE_NO = #{siteNo}")
    void updateSiteOnlineTime(@Param("siteNo") String siteNo, @Param("totalTime") Long totalTime);

    List<SysAbnormalStatisticsModel> videoQualityStatistics(float rate);

    /**
     * 设置为已进行异常统计
     * @param id
     */
    @Update("update t_doc_check set IS_ERROR_VERIFICATION = 1 where docId = #{id}")
    void updateDocCheck(@Param("id") Long id);
}
