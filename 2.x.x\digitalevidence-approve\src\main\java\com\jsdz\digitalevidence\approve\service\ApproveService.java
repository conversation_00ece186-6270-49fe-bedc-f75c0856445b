/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.service;

import java.util.Map;

import com.jsdz.digitalevidence.approve.model.ApproveBusiness;

/**
 * @类名: ApproveService
 * @说明: 业务审批应用服务接口
 *
 * <AUTHOR>
 * @Date	 2017年5月24日 下午7:56:24
 * 修改记录：
 *
 * @see 	 
 */
public interface ApproveService {
	
	/**
	 * @说明：获取所有审批业务
	 *
	 * <AUTHOR>
	 * @return
	 * 
	 */
	public Map<String, ApproveBusiness> getAllApproveBusiness();
	
}

