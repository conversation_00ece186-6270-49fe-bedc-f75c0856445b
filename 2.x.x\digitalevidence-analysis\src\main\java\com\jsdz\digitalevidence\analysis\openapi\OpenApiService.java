package com.jsdz.digitalevidence.analysis.openapi;
/**
 * 
 * @类名: OpenApiService
 * @说明: 接口的Service类
 *
 * @author: kenny
 * @Date	2017年6月8日上午11:58:04
 * 修改记录：
 *
 * @see
 */

import java.util.List;

import com.jsdz.core.Page;

public interface OpenApiService {
  
	
	//查询接口: 执法记录仪日志信息查询
	public List<CxdsjLogResultBean> findCXDSJLOG(String[] paramNames,Object[] values) ;
	;
	
	//查询接口: 执法数据采集设备基本信息查询
	public List<StationInfoResultBean> findQUERYSTATIONINFO(String[] paramNames,Object[] values);
	
	//查询接口: 执法数据采集设备日志信息查询
	public List<StationLogResultBean> findQUERYSTATIONLOG(String[] paramNames,Object[] values);
	
}
