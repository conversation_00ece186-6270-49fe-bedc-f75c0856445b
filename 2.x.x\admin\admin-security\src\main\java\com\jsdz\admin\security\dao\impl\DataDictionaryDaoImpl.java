package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: DataDictionaryDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-10-31 11:43:33
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.DataDictionaryDao;
import com.jsdz.admin.security.model.DataDictionary;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class DataDictionaryDaoImpl extends GenericEntityDaoHibernateImpl<DataDictionary,Long> implements DataDictionaryDao{

	//新增
	public void addDataDictionary(DataDictionary dataDictionary) {
		this.saveOrUpdate(dataDictionary);
	}

	//删除
	public void deleteDataDictionary(DataDictionary dataDictionary) {
		this.delete(dataDictionary);
	}

	//修改
	public void updateDataDictionary(DataDictionary dataDictionary) {
		this.merge(dataDictionary);
	}

	//按id查询(游离状态)
	public DataDictionary findDataDictionaryById(Long id){

		final String  hql = "from DataDictionary d where d.id = :id";
		final Long oid = id;
		DataDictionary data = getHibernateTemplate().execute(new HibernateCallback<DataDictionary>() {
			public DataDictionary doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DataDictionary> list = query.list();
				DataDictionary rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public DataDictionary locateDataDictionaryById(Long id){

		final String  hql = "from DataDictionary d where d.id = :id";
		final Long oid = id;
		DataDictionary data = getHibernateTemplate().execute(new HibernateCallback<DataDictionary>() {
			public DataDictionary doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DataDictionary> list = query.list();
				DataDictionary rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public DataDictionary findDataDictionaryByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		DataDictionary data = getHibernateTemplate().execute(new HibernateCallback<DataDictionary>() {
		public DataDictionary doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<DataDictionary> list = query.list();
			DataDictionary rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<DataDictionary> findAllDataDictionarys(){
		return this.find("from DataDictionary dataDictionary ");
	}

	//列表查询
	public List<DataDictionary> findDataDictionarysByCondition(String queryStr,String[] paramNames,Object[] values){
		List<DataDictionary> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<DataDictionary> findDataDictionarysOnPage(Page<DataDictionary> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<DataDictionary>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
