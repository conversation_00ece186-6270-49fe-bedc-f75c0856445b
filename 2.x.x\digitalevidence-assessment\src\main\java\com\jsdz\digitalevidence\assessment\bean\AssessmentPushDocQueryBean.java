/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.assessment.cycle.CycleSelector;
import com.jsdz.reportquery.dynsql.fork.support.tree.TreeField;

/**
 * @类名: AssessmentPushDocQueryBean
 * @说明: 考核预警查询Bean
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentPushDocQueryBean extends AbstractDTO {
	
	/** 警号*/
	private String policeCode;
	/** 警员姓名*/
	private String policeName;
	/** 组织*/
	private TreeField orgId;
	/** 考核周期*/
	/** 当前周期日*/
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date cycleDate;
	/** 周期选择，上一个/下一个*/
	private CycleSelector cycleSelector;
	/** 是否已考核*/
	private Boolean hasAssessed;
	/** 是否未考核*/
	private Boolean notAssessed;
	
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public TreeField getOrgId() {
		return orgId;
	}
	public void setOrgId(TreeField orgId) {
		this.orgId = orgId;
	}
	public Date getCycleDate() {
		return cycleDate;
	}
	public void setCycleDate(Date cycleDate) {
		this.cycleDate = cycleDate;
	}
	public CycleSelector getCycleSelector() {
		return cycleSelector;
	}
	public void setCycleSelector(CycleSelector cycleSelector) {
		this.cycleSelector = cycleSelector;
	}
	public Boolean getHasAssessed() {
		return hasAssessed;
	}
	public void setHasAssessed(Boolean hasAssessed) {
		this.hasAssessed = hasAssessed;
	}
	public Boolean getNotAssessed() {
		return notAssessed;
	}
	public void setNotAssessed(Boolean notAssessed) {
		this.notAssessed = notAssessed;
	}
	
}
