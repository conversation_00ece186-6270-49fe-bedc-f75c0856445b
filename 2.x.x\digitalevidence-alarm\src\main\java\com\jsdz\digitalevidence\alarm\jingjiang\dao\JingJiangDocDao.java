package com.jsdz.digitalevidence.alarm.jingjiang.dao;
/**
 * 
 * @类名: JingJiangDocDao
 * @说明: 靖江数据交换Dao
 *
 * @author: kenny
 * @Date	2017年10月25日下午3:56:24
 * 修改记录：
 *
 * @see
 */
import java.util.List;

import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.alarm.jingjiang.model.JingJiangDoc;

public interface JingJiangDocDao extends GenericORMEntityDAO<JingJiangDoc,Long> {

	//新增
	public void addJingJiangDoc(JingJiangDoc jingJiangDataExchange);

	//修改
	public void updateJingJiangDoc(JingJiangDoc jingJiangDataExchange);

	//删除
	public void deleteJingJiangDoc(JingJiangDoc jingJiangDataExchange);

	//按id查询,结果是游离状态的数据
	public JingJiangDoc findJingJiangDocById(Long id);

	//按id查询
	public JingJiangDoc locateJingJiangDocById(Long id);

	//单个查询
	public JingJiangDoc findJingJiangDocByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<JingJiangDoc> findAllJingJiangDocs();

	//列表查询
	public List<JingJiangDoc> findJingJiangDocsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<JingJiangDoc> findJingJiangDocsOnPage(Page<JingJiangDoc> page,String queryStr,String[] paramNames,Object[] values);

}

