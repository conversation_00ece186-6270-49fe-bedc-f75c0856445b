/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.service;

import java.util.Date;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertSumBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertQueryBean;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert;

/**
 * @类名: AssessmentServiceAlertService
 * @说明: 拍摄考核预警服务
 *        
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午6:04:13
 * 修改记录：
 *
 * @see 	 
 */
public interface AssessmentAlertService {
	
	/**
	 * @说明：获取警员考核预警
	 *
	 * <AUTHOR>
	 * @param policeId
	 * @param cycleDate 周期日
	 * @return
	 * 
	 */
	public AssessmentAlert getAssessmentAlertOfPolice(Long policeId, Date cycleDate);
	
	// 考核员查找预警, 返回实体
	public Page<AssessmentAlert> queryAssessmentAlert(Page<AssessmentAlert> page, AssessmentAlertQueryBean qb) throws Exception;
	
	// 获取我的预警
	public Page<AssessmentAlertSumBean> searchMyAssessmentAlert(Page<AssessmentAlertSumBean> page, AssessmentAlertQueryBean qb) throws Exception;
	
	/**
	 * @说明：打开预警报告
	 *
	 * <AUTHOR>
	 * @param alertId
	 * @return
	 * 
	 */
	public AssessmentAlertBean openAssessmentAlert(Long alertId);
	
	// 生成预警
	// 月预警报告
	public AssessmentAlert genAssessmentAlertOfPolice(Long policeId, Date startDate, Date endDate) throws Exception;
	// 月预警 报告
	public AssessmentAlert genAssessmentAlertOfPoliceIfNotExist(Long policeId, Date startDate, Date endDate) throws Exception;
	// 周预警报告
	public AssessmentAlert genAssessmentWeeklyAlertOfPolice(Long policeId, Date reportData) throws Exception;
	// 反馈预警
	public AssessmentAlert feedback(Long alertId, String feedback);
	
	/**
	 * @说明：搜索考核预警，分页
	 *
	 * <AUTHOR>
	 * @param page
	 * @param qb
	 * @return 考核预警列表
	 * @throws Exception 
	 * 
	 */
	public Page<AssessmentAlertSumBean> searchAssessmentAlert(Page<AssessmentAlertSumBean> page, 
					AssessmentAlertQueryBean qb) throws Exception;

}
