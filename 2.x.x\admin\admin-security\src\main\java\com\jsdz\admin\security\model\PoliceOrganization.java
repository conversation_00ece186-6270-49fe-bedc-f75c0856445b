package com.jsdz.admin.security.model;

import java.io.Serializable;

import com.jsdz.admin.org.model.Organization;
/**
 * 说明：警员对应多单位
 * @类名: PoliceOrganization
 *
 * <AUTHOR>
 * @Date	 2019年12月19日下午2:52:40
 */
public class PoliceOrganization implements Serializable{

	private static final long serialVersionUID = 3150388094399557738L;
	
	private Long id;
	private Employees police;
	private Organization organization;
	
	public PoliceOrganization(){
		super();
	}
	
	public PoliceOrganization(Employees police,Organization organization){
		super();
		this.police = police;
		this.organization = organization;
	}
	public PoliceOrganization(Long policceId,Long organizationId){
		super();
		Employees emp = new Employees();
		emp.setId(policceId);
		Organization org = new Organization();
		org.setId(organizationId); 
		this.police = emp;
		this.organization = org;
	}
	

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Organization getOrganization() {
		return organization;
	}
	public void setOrganization(Organization organization) {
		this.organization = organization;
	}	
	
	
	public Employees getPolice() {
		return police;
	}

	public void setPolice(Employees police) {
		this.police = police;
	}

	@Override
	public boolean equals(Object obj){
		if (obj == null)
			return false;
		
		PoliceOrganization dest = (PoliceOrganization)obj;
	
		if (this.getOrganization() != null){
			if (dest.getOrganization() == null)
				return false;
			
			if (!this.getOrganization().getId().equals(dest.getOrganization().getId()))
				return false;
		}else{
			if (dest.getOrganization() != null)
				return false;
		}
		
		if (this.getPolice() != null){
			if (dest.getPolice() == null)
				return false;
			
			if (!this.getPolice().getId().equals(dest.getPolice().getId()))
				return false;
		}else{
			if (dest.getPolice() != null)
				return false;
		}
		
		return true;
	}
	
}
