package com.jsdz.digitalevidence.analysis.openapi;

import com.jsdz.digitalevidence.analysis.openapi.model.StationInfo;

/**
 * 
 * @类名: StationInfoResultBean
 * @说明: 执法数据采集设备基本信息查询结果  
 *
 * @author: kenny
 * @Date	2017年6月8日下午4:46:11
 * 修改记录：
 *
 * @see
 */

public class StationInfoResultBean {

	private Long id;
	private String gzz_xh;// 执法数据采集设备产品编码
	private String gzz_ipdz;//执法数据采集设备IP
	private String zxzt;// 在线状态 0，不在线；1，在线
	private String qyzt;//启用状态 0，未启用；1，启用
	private String ybkjdx;//硬盘空间大小
	private String jssykjdx;//即时剩余空间大小
	
	public void assign(StationInfo src){
		this.setGzz_xh(src.getGzz_xh());
		this.setGzz_ipdz(src.getGzz_ipdz());
		this.setZxzt(src.getZxzt());
		this.setQyzt(src.getQyzt());
		this.setYbkjdx(src.getYbkjdx());
		this.setJssykjdx(src.getJssykjdx());
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getGzz_xh() {
		return gzz_xh;
	}
	public void setGzz_xh(String gzz_xh) {
		this.gzz_xh = gzz_xh;
	}
	public String getGzz_ipdz() {
		return gzz_ipdz;
	}
	public void setGzz_ipdz(String gzz_ipdz) {
		this.gzz_ipdz = gzz_ipdz;
	}
	public String getZxzt() {
		return zxzt;
	}
	public void setZxzt(String zxzt) {
		this.zxzt = zxzt;
	}
	public String getQyzt() {
		return qyzt;
	}
	public void setQyzt(String qyzt) {
		this.qyzt = qyzt;
	}
	public String getYbkjdx() {
		return ybkjdx;
	}
	public void setYbkjdx(String ybkjdx) {
		this.ybkjdx = ybkjdx;
	}
	public String getJssykjdx() {
		return jssykjdx;
	}
	public void setJssykjdx(String jssykjdx) {
		this.jssykjdx = jssykjdx;
	}
}
