package com.jsdz.digitalevidence.activemq.utils;
/**
 * 
 * @类名: OptionType
 * @说明:操作类型 
 *
 * @author: kenny
 * @Date	2018年2月6日下午10:54:04
 * 修改记录：
 *
 * @see
 */

public enum OptionType {
	/**/ 
	ADD(1, "ADD"), UPDATE(2, "UPDATE"),DELETE(3,"DELETE");
	
	public int index;
	public String name;
	private OptionType(int index, String name) {
		this.index=index;
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
