package com.jsdz.digitalevidence.archives.model;

import java.math.BigDecimal;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VSiteOrgCount implements Serializable {

    private static final long serialVersionUID = 1L;

    private String orgname;

    private String orgcode;

    private String path;

    private Long sumall;

    private BigDecimal sumstorage;

    private BigDecimal sumsite;

    private Double cpu;

    private Double diskuse;

    private Double diskall;

    private Double memoryuse;

    private Double memoryall;


}
