package com.jsdz.digitalevidence.alarm.alarm110.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmStamp;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmStampDao;

@Repository
public class AlarmStampDaoImpl extends GenericEntityDaoHibernateImpl<AlarmStamp,Long> implements AlarmStampDao{
	
	//查询AlarmStamp信息
	public AlarmStamp findAlarmStamp(String queryString) {
		
		List<AlarmStamp> list = this.find(queryString);
		
		if(list != null && list.size() >0 ){
			
//			System.out.println("最大时间为"+list.get(list.size()-1).getStampTime());
			return list.get(list.size()-1);
		}
		
		return null;
	}
	
	//更新AlarmStamp信息
	public void updateStamp(AlarmStamp alarmStamp01) {
		this.update(alarmStamp01);
		
	}
	
	// 插入AlarmStamp信息
	public void insertStamp(AlarmStamp alarmStamp01) {
		this.insert(alarmStamp01);
		
	}

}
