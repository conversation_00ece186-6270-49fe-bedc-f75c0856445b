package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: AnalysisDocumentBean
 * @说明: 统计分析报表的数据封装类
 *
 * @author: kenny
 * @Date  2018年6月22日下午3:00:40
 * 修改记录：
 *
 * @see
 */
import java.io.Serializable;
import java.util.Date;

public class AnalysisDocumentBean implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -1349970682005772299L;
	private Long id;//对象id
	private Date dateFrom; //时间段从
	private Date dateTo;//时间到
	private String code;//对象代码 
	private String name;//对象名称
	private String orgName;//单位名称
	private Long totalDuration;//总时长
	private Long vedioDuration;//视频时长
	private Long audioDuration;//音频时长
	private Long totalFileSize;//文件总长度
	private Long totalCount;//文件总数量
	private Long vedioCount;//视频数量
	private Long audioCount;//音频数量
	private Long picCount;//图片数量
	private Long txtCount;//日志数量
	private Long importanceCount;//重要文件 数量
	private Long generalCount;//一般文件 数量
	private Long signCount;//标注文件 数量
	private Long relateCount;//关联文件 数量
	private Long accessmentCount;//考核文件 数量

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Date getDateFrom() {
		return dateFrom;
	}
	public void setDateFrom(Date dateFrom) {
		this.dateFrom = dateFrom;
	}
	public Date getDateTo() {
		return dateTo;
	}
	public void setDateTo(Date dateTo) {
		this.dateTo = dateTo;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Long getTotalDuration() {
		return totalDuration;
	}
	public void setTotalDuration(Long totalDuration) {
		this.totalDuration = totalDuration;
	}
	public Long getVedioDuration() {
		return vedioDuration;
	}
	public void setVedioDuration(Long vedioDuration) {
		this.vedioDuration = vedioDuration;
	}
	public Long getAudioDuration() {
		return audioDuration;
	}
	public void setAudioDuration(Long audioDuration) {
		this.audioDuration = audioDuration;
	}
	public Long getTotalFileSize() {
		return totalFileSize;
	}
	public void setTotalFileSize(Long totalFileSize) {
		this.totalFileSize = totalFileSize;
	}
	public Long getTotalCount() {
		return totalCount;
	}
	public void setTotalCount(Long totalCount) {
		this.totalCount = totalCount;
	}
	public Long getVedioCount() {
		return vedioCount;
	}
	public void setVedioCount(Long vedioCount) {
		this.vedioCount = vedioCount;
	}
	public Long getAudioCount() {
		return audioCount;
	}
	public void setAudioCount(Long audioCount) {
		this.audioCount = audioCount;
	}
	public Long getPicCount() {
		return picCount;
	}
	public void setPicCount(Long picCount) {
		this.picCount = picCount;
	}
	public Long getTxtCount() {
		return txtCount;
	}
	public void setTxtCount(Long txtCount) {
		this.txtCount = txtCount;
	}
	public Long getImportanceCount() {
		return importanceCount;
	}
	public void setImportanceCount(Long importanceCount) {
		this.importanceCount = importanceCount;
	}
	public Long getGeneralCount() {
		return generalCount;
	}
	public void setGeneralCount(Long generalCount) {
		this.generalCount = generalCount;
	}
	public Long getSignCount() {
		return signCount;
	}
	public void setSignCount(Long signCount) {
		this.signCount = signCount;
	}
	public Long getRelateCount() {
		return relateCount;
	}
	public void setRelateCount(Long relateCount) {
		this.relateCount = relateCount;
	}
	public Long getAccessmentCount() {
		return accessmentCount;
	}
	public void setAccessmentCount(Long accessmentCount) {
		this.accessmentCount = accessmentCount;
	}
	
	/**
	 * 计算总时长
	 * @return
	 */
	public Long getCalclTotalDuration(){
		Long totalDuration1 = this.getVedioDuration()==null?0:this.getVedioDuration();
		totalDuration1 = totalDuration1 + (this.getAudioDuration()==null?0:this.getAudioDuration());
		return totalDuration1;

	}
	
	/**
	 * 计算文件总数
	 * @return
	 */
	public Long getCalcTotalFileCount(){
		Long fileCount1 = this.getVedioCount()==null?0:this.getVedioCount();
		fileCount1 = fileCount1 + (this.getAudioCount()==null?0:this.getAudioCount());
		fileCount1 = fileCount1 + (this.getPicCount()==null?0:this.getPicCount());
		fileCount1 = fileCount1 + (this.getTxtCount()==null?0:this.getTxtCount());
		return fileCount1;
		
	}
}
