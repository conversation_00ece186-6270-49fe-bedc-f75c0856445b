package com.jsdz.digitalevidence.activemq.model;

import java.util.Map;

import javax.jms.MessageListener;

/**
 * 
 * @类名: TopicMapper
 * @说明: 定义主题注册
 *
 * @author: kenny
 * @Date	2017年10月27日下午6:51:42
 * 修改记录：
 *
 * @see
 */
public class TopicMapper {
	
	private String topicName = null;
	//private Map<String, Class<MessageListener>> ListenerMapper;
	private Map<String, String> ListenerMapper;
	
	
	public String getTopicName() {
		return topicName;
	}
	public void setTopicName(String topicName) {
		this.topicName = topicName;
	}
	public Map<String, String> getListenerMapper() {
		return ListenerMapper;
	}
	public void setListenerMapper(Map<String, String> listenerMapper) {
		ListenerMapper = listenerMapper;
	}	
	
	
	
}
