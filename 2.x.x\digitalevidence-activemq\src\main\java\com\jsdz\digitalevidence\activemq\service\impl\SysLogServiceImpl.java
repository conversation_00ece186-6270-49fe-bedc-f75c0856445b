package com.jsdz.digitalevidence.activemq.service.impl;

import java.util.Calendar;
import java.util.Date;

/**
 * 
 * @类名: SysLogServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-01-31 15:36:14
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.activemq.dao.SysLogDao;
import com.jsdz.digitalevidence.activemq.dao.SysLogTopicDao;
import com.jsdz.digitalevidence.activemq.model.SysLog;
import com.jsdz.digitalevidence.activemq.model.SysLogTopic;
import com.jsdz.digitalevidence.activemq.service.SysLogService;
import com.jsdz.digitalevidence.activemq.utils.LogType;
import org.apache.log4j.Logger;

@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("SysLogServiceImpl")
public class SysLogServiceImpl implements SysLogService {

	@Autowired
	private SysLogDao sysLogDao;
	@Autowired
	private SysLogTopicDao sysLogTopicDao;
	//新增
	public AjaxResult addSysLog(SysLog sysLog) {
		registeredTopic(sysLog.getTopic());
		
		AjaxResult result = new AjaxResult();
		sysLog.setCreateTime(new Date());
		sysLogDao.addSysLog(sysLog);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateSysLog(SysLog sysLog) {
		AjaxResult result = new AjaxResult();
		sysLogDao.updateSysLog(sysLog);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteSysLog(SysLog sysLog) {
		AjaxResult result = new AjaxResult();
		sysLogDao.deleteSysLog(sysLog);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public SysLog findSysLogById(Long id){

		return sysLogDao.findSysLogById(id);
	}

	//按 id 查询
	public SysLog locateSysLogById(Long id) {
		return sysLogDao.locateSysLogById(id);
	}

	//单个查询
	public SysLog findSysLogByParam(String queryStr, String[] paramNames, Object[] values) {
		return sysLogDao.findSysLogByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<SysLog> findAllSysLogs() {
		return sysLogDao.findAllSysLogs();
	}

	//列表查询
	public List<SysLog> findSysLogsByParam(String queryStr, String[] paramNames, Object[] values) {
		return sysLogDao.findSysLogsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<SysLog> findSysLogsOnPage(Page<SysLog> page, String queryStr, String[] paramNames, Object[] values) {
		Page<SysLog> pos = sysLogDao.findSysLogsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

	/**
	 * findGroupByTopic
	 * 按指定字段分级查询
	 * @return
	 */
	@Override
	public List<String> findGroupBy(String groupByName){
		return sysLogDao.findGroupBy(groupByName);
	}

    @Override 
  	 public void setInfoLog(SysLog sysLog,String msg){
   	 	setLog(sysLog,LogType.INFO,msg);
    }

    @Override 
  	 public void setErrorLog(SysLog sysLog,String msg){
   	 	setLog(sysLog,LogType.ERROR,msg);
    }
   
  	 public void setInfoLog(SysLog sysLog,String keyWord,String msg){
  		 setLog(sysLog,LogType.INFO,keyWord,msg);
    }

    @Override 
  	 public void setErrorLog(SysLog sysLog,String keyWord,String msg){
   	 	setLog(sysLog,LogType.ERROR,keyWord,msg);
    }
    @Override 
  	public void setLog(SysLog sysLog,LogType logType,String msg){
 		SysLog sl = new SysLog();
 		sl.setJavaClass(sysLog.getJavaClass());
 		sl.setTopic(sysLog.getTopic());
 		if (msg.length() > 1000)
 			msg = msg.substring(0,950);
 		sl.setContext(msg);
 		sl.setCreateTime(new Date());
 		sl.setKeyWord(sysLog.getKeyWord());
 		sl.setLogType(logType.getName());
 		this.addSysLog(sl);
 		Logger logger = Logger.getLogger(sysLog.getJavaClass());
 		if (logType == LogType.INFO){
 			logger.info( "***提示：" + msg);
 		}else{
 			logger.error("***错误：" + msg);
 		}
 	}     
    
	@Override
	//登记提示或错误信息
	public void setLog(SysLog sysLog,LogType logType,String keyWord,String msg){
		SysLog sl = new SysLog();
		sl.setJavaClass(sysLog.getJavaClass());
		sl.setTopic(sysLog.getTopic());
 		if (msg.length() > 1000)
 			msg = msg.substring(0,950);
 		sl.setContext(msg);

		sl.setCreateTime(new Date());
		sl.setKeyWord(keyWord);
		sl.setLogType(logType.getName());
		this.addSysLog(sl);
		Logger logger = Logger.getLogger(sysLog.getJavaClass());
		if (logType == LogType.INFO){
			logger.info( "***提示：" + msg);
		}else{
			logger.error("***错误：" + msg);
		}
		
	}

	@Override
 	public void setLog(LogType logType,String topic,String clazz,String keyWord,String msg){
 		SysLog sl = new SysLog();
 		sl.setJavaClass(clazz);
 		sl.setTopic(topic);
 		
 		if(msg != null){
 			if (msg.length() > 1000)
 	 			msg = msg.substring(0,950);
 		}
 		sl.setContext(msg);
 		sl.setCreateTime(new Date());
 		sl.setKeyWord(keyWord);
 		sl.setLogType(logType.getName());
 		this.addSysLog(sl);
 		Logger logger = Logger.getLogger(clazz);
 		if (logType == LogType.INFO){
 			logger.info( "***提示：" + msg);
 		}else{
 			logger.error("***错误：" + msg);
 		}
 	}
	
	//根据系统参数设置定时删除时间过得太久的系统日志
	public Integer deleteSysLogBySetAnyDays(){
/*		String s = (String)SysSource.getInstance().getSysConfigParam("LOG_SAVE_DADS");
		
		Integer saveDays;
		try{
			saveDays = Integer.valueOf(s);
		}catch(Exception e){
			saveDays = 60;//默认60天
		}
		
		saveDays = saveDays==null?60:saveDays;
*/
		//系统日志保存一个月就可以了
		Integer saveDays=10;
		//当前日期减去saveDays天
		Date beginDate = new Date();
		Calendar date = Calendar.getInstance();
		date.setTime(beginDate);
		date.set(Calendar.DATE, date.get(Calendar.DATE) - saveDays);
		Date logCreateTime = date.getTime();
		
		String hql = "delete from SysLog s where s.createTime <=:createTime ";
		String[] params = new String[]{"createTime"};
		Object[] values = new Object[]{logCreateTime};
		Integer result = sysLogDao.execHqlByCondition(hql, params, values);
		return result;
	}
	
	//
	/**
	 * 注册日志主题
	 */
	@Override
	public void registeredTopic(String topic){
		if (topic == null || topic.equals("")){
			return;
		}
		String queryStr = "from SysLogTopic s where s.topic=:topic";
		if (sysLogTopicDao.findSysLogTopicByCondition(queryStr,new String[]{"topic"},new Object[]{topic}) == null){
			SysLogTopic sysLogTopic = new SysLogTopic();
			sysLogTopic.setTopic(topic);
			sysLogTopicDao.insert(sysLogTopic);
		}
	}

}
