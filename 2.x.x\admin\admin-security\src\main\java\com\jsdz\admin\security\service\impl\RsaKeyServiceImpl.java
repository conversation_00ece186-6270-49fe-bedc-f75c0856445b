package com.jsdz.admin.security.service.impl;

import java.util.Date;



import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.admin.security.dao.RsaKeyDao;
import com.jsdz.admin.security.model.RsaKey;
import com.jsdz.admin.security.service.RsaKeyService;
import com.jsdz.admin.security.token.Token;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
/**
 * 
 * @类名: RsaKeyServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-06-02 11:38:34
 * 修改记录：
 *
 * @see
*/
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("RsaKeyServiceImpl")
public class RsaKeyServiceImpl implements RsaKeyService {

	@Autowired
	private RsaKeyDao rsaKeyDao;

	//新增
	public AjaxResult addRsaKey(RsaKey rsaKey) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		rsaKey.setUniqueKey(1);

		if (null==rsaKey.getPublicKey() || "".equals(rsaKey.getPublicKey())){
			result.setMsg("PublicKey必须提供");
			return result;
		}
		
		if (null==rsaKey.getPrivateKey() || "".equals(rsaKey.getPrivateKey())){
			result.setMsg("PrivateKey必须提供");
			return result;
		}
		
		RsaKey rk = rsaKeyDao.findRsaKeyByCondition("from RsaKey r where r.uniqueKey = 1", new String[]{}, new Object[]{});
		if (rk != null){
			rk.setPublicKey(rsaKey.getPublicKey());
			rk.setPrivateKey(rsaKey.getPrivateKey());
			rk.setCreateTime(new Date());
			rsaKeyDao.update(rk);
		}else{
			rsaKeyDao.addRsaKey(rsaKey);
		}
		
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateRsaKey(RsaKey rsaKey) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		rsaKey.setUniqueKey(1);

		if (null==rsaKey.getPublicKey() || "".equals(rsaKey.getPublicKey())){
			result.setMsg("PublicKey必须提供");
			return result;
		}
		
		if (null==rsaKey.getPrivateKey() || "".equals(rsaKey.getPrivateKey())){
			result.setMsg("PrivateKey必须提供");
			return result;
		}
		
		RsaKey rk = rsaKeyDao.findRsaKeyByCondition("from RsaKey r where r.uniqueKey = 1", new String[]{}, new Object[]{});
		if (rk != null){
			rk.setPublicKey(rsaKey.getPublicKey());
			rk.setPrivateKey(rsaKey.getPrivateKey());
			rk.setAesKey(rsaKey.getAesKey());
			rk.setCreateTime(new Date());
			rsaKeyDao.update(rk);
		}else{
			rsaKeyDao.addRsaKey(rsaKey);
		}
		
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteRsaKey(RsaKey rsaKey) {
		AjaxResult result = new AjaxResult();
		//rsaKeyDao.deleteRsaKey(rsaKey);
		result.setSuccess(false);
		result.setMsg("不允许删除。");
		return result; 
	}

	//按id查询(游离数据)
	public RsaKey findRsaKeyById(Long id){

		return rsaKeyDao.findRsaKeyById(id);
	}

	//按 id 查询
	public RsaKey locateRsaKeyById(Long id) {
		return rsaKeyDao.locateRsaKeyById(id);
	}

	//单个查询
	public RsaKey findRsaKeyByParam(String queryStr, String[] paramNames, Object[] values) {
		return rsaKeyDao.findRsaKeyByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<RsaKey> findAllRsaKeys() {
		return rsaKeyDao.findAllRsaKeys();
	}

	//列表查询
	public List<RsaKey> findRsaKeysByParam(String queryStr, String[] paramNames, Object[] values) {
		return rsaKeyDao.findRsaKeysByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<RsaKey> findRsaKeysOnPage(Page<RsaKey> page, String queryStr, String[] paramNames, Object[] values) {
		Page<RsaKey> pos = rsaKeyDao.findRsaKeysOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	//单个查询
	public RsaKey findRsaKey() {
		return rsaKeyDao.findRsaKeyByCondition("from RsaKey r where r.uniqueKey = 1", new String[]{}, new Object[]{});
		
	}
	
	//生成Token
	public String genToken(){
		return Token.genToken();
	}
	
	//检查Token
	public boolean checkToken(String token){
		return Token.checkToken(token);
	}
	
}
