package com.jsdz.admin.security.model;

import java.util.Date;

/**
 * 
 * @类名: SysConfig
 * @说明: 系统参数配置
 *
 * @author: kenny
 * @Date	2017年5月16日下午8:42:05
 * 修改记录：
 *
 * @see
 */

public class SysConfig {
	private Long id;
	private String sysConfigKey;
	private String sysConfigValue;
	private String sysConfigDesc;
	private boolean sysReserved;//不能删除的
	private Date sysConfigCreateTime;
	
	public SysConfig(){
		super();
		this.sysReserved = true;
		this.sysConfigCreateTime = new Date();
	}
	public SysConfig(String sysConfigKey,String sysConfigValue,String sysConfigDesc){
		super();
		this.sysConfigKey = sysConfigKey;
		this.sysConfigValue = sysConfigValue;
		this.sysConfigDesc = sysConfigDesc;
		this.sysReserved = true;
		this.sysConfigCreateTime = new Date();
	}
	
	public boolean equals(Object obj) {
        if(this==obj)//地址相同
            return true;
        else if(obj!=null&&obj instanceof SysConfig) {
        	SysConfig s=(SysConfig)obj;
            return this.sysConfigKey.equals(s.sysConfigKey);
        }
        else
            return false;
    }
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getSysConfigKey() {
		return sysConfigKey;
	}
	public void setSysConfigKey(String sysConfigKey) {
		this.sysConfigKey = sysConfigKey;
	}
	public String getSysConfigDesc() {
		return sysConfigDesc;
	}
	public void setSysConfigDesc(String sysConfigDesc) {
		this.sysConfigDesc = sysConfigDesc;
	}
	public String getSysConfigValue() {
		return sysConfigValue;
	}
	public void setSysConfigValue(String sysConfigValue) {
		this.sysConfigValue = sysConfigValue;
	}
	public Date getSysConfigCreateTime() {
		return sysConfigCreateTime;
	}
	public void setSysConfigCreateTime(Date sysConfigCreateTime) {
		this.sysConfigCreateTime = sysConfigCreateTime;
	}
	public boolean isSysReserved() {
		return sysReserved;
	}
	public void setSysReserved(boolean sysReserved) {
		this.sysReserved = sysReserved;
	}	
	
	
}
