package com.jsdz.digitalevidence.analysis.openapi.service;

/**
 * 
 * @类名: StationLogService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-06-09 16:04:15
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.analysis.openapi.model.StationLog;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface StationLogService {

	//新增
	public AjaxResult addStationLog(StationLog stationLog);

	//修改
	public AjaxResult updateStationLog(StationLog stationLog);

	//删除
	public AjaxResult deleteStationLog(StationLog stationLog);

	//按id查询,结果是游离状态的数据
	public StationLog findStationLogById(Long id);

	//按id查询
	public StationLog locateStationLogById(Long id);

	//单个查询
	public StationLog findStationLogByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<StationLog> findAllStationLogs();

	//列表查询
	public List<StationLog> findStationLogsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<StationLog> findStationLogsOnPage(Page<StationLog> page, String queryStr,String[] paramNames,Object[] values);

}

