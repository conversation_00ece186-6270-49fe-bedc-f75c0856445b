package com.jsdz.digitalevidence.test.proced;

import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.codec.binary.Base64;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.digitalevidence.approve.model.ApproveBusiness;
import com.jsdz.digitalevidence.approve.service.ApproveService;
import com.jsdz.serializer.Serializer;
import com.jsdz.serializer.SeriallizeException;
import com.jsdz.utils.BeanUtils;

/**
 * @类名: ApproveServiceTest
 * @说明: 业务审批服务单元测试
 *
 * <AUTHOR>
 * @Date	 2017年4月24日 下午2:00:03
 * 修改记录：
 *
 * @see 	 
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations={
		"/testApplicationContext-common.xml",
		"/testApplicationContext-activiti.xml",
		"/testApplicationContext-dynsql.xml",
		"/testApplicationContext-serializer.xml"
		})
public class ApproveServiceTest {

	@Autowired
	private ApproveService approveService;
	
	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
		
	}
	
	// 获取所有审批业务
	@Test
	@Transactional
	public void testGetAllApproveBusiness() {
		Map<String, ApproveBusiness> m = approveService.getAllApproveBusiness();
		BeanUtils.printBean(m);
	}
	
	String paramBase64="PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48bWFwPgogIDxlbnRyeT4KICAgIDxzdHJpbmc+ZG9jSWQ8L3N0cmluZz4KICAgIDxzdHJpbmctYXJyYXk+CiAgICAgIDxzdHJpbmc+MTQwPC9zdHJpbmc+CiAgICA8L3N0cmluZy1hcnJheT4KICA8L2VudHJ5PgogIDxlbnRyeT4KICAgIDxzdHJpbmc+bmFtZTwvc3RyaW5nPgogICAgPHN0cmluZy1hcnJheT4KICAgICAgPHN0cmluZz5KODAwMDE5XzAwMDAxOTIwMTYxMDI1MTIyMzUwXzAwMTEuTVA0PC9zdHJpbmc+CiAgICA8L3N0cmluZy1hcnJheT4KICA8L2VudHJ5Pgo8L21hcD4=";
	
	@Resource(name="methodParamSer")
	private Serializer ser;
	
	@Test
	@Transactional
	public void testParamSer() throws SeriallizeException {
		byte[] bytes = Base64.decodeBase64(paramBase64);
		Object obj = ser.Unmarshal(bytes);
		BeanUtils.printBean(obj);
	}
	
}
