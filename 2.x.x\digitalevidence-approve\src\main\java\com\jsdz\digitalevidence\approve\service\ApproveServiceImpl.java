/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jsdz.digitalevidence.approve.dao.ApproveBusinessDao;
import com.jsdz.digitalevidence.approve.model.ApproveBusiness;

/**
 * @类名: ApproveServiceImpl
 * @说明: 业务审批应用服务实现
 *
 * <AUTHOR>
 * @Date	 2017年11月18日 上午11:35:40
 * 修改记录：
 *
 * @see 	
 * 
 * TODO
 *   *. 审批业务分布式缓存
 */
@Service
public class ApproveServiceImpl implements ApproveService {
	
	@Autowired
	private ApproveBusinessDao abDao;

	@Override
	public Map<String, ApproveBusiness> getAllApproveBusiness() {
		Map<String, ApproveBusiness> m = new HashMap<String, ApproveBusiness>();
		//
		List<ApproveBusiness> abs = abDao.getAllApproveBusiness();
		for(ApproveBusiness b : abs) {
			m.put(b.getUri(), b);
		}
		return m;
	}

}
