/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.model;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: BusinessObjectType
 * @说明: 业务对象类型
 *
 * <AUTHOR>
 * @Date	 2017年11月17日 上午10:20:05
 * 修改记录：
 *
 * @see 	 
 */
public class BusinessObjectType extends AbstractDTO {

	/** Id*/
	private Long typeId;
	/** 对象类型*/
	private Class<?> clazz;
	/** 类型描述*/
	private String comment;
	/** 对象查看api地址*/
	private String url;
	
	public Long getTypeId() {
		return typeId;
	}
	public void setTypeId(Long typeId) {
		this.typeId = typeId;
	}
	public Class<?> getClazz() {
		return clazz;
	}
	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public String getComment() {
		return comment;
	}
	public void setComment(String comment) {
		this.comment = comment;
	}
	
	
	
}
