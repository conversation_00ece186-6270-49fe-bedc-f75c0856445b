package com.jsdz.admin.security.service.impl;

import java.util.Date;

/**
 * 
 * @类名: OperatorRoleServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-03 14:48:29
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import com.jsdz.admin.security.dao.OperatorDao;
import com.jsdz.admin.security.dao.OperatorRoleDao;
import com.jsdz.admin.security.dao.RoleDao;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.service.OperatorRoleService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.cache.encrypt.MD5;

@Service("OperatorRoleServiceImpl")
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class OperatorRoleServiceImpl implements OperatorRoleService {

	@Autowired
	private OperatorRoleDao operatorRoleDao;
	@Autowired
	private OperatorDao operatorDao;
	@Autowired
	private RoleDao roleDao;

	//新增
	public AjaxResult addOperatorRole(OperatorRole operatorRole) {
		AjaxResult result = new AjaxResult();
		if (null == operatorRole){
			result.setSuccess(false);
			result.setMsg("没有数据可保存。");
			return result;
		}
		
		if (null == operatorRole.getRole()){
			result.setSuccess(false);
			result.setMsg("角色必须输入。");
			return result;
		}
		
		if (null == operatorRole.getOperator()){
			result.setSuccess(false);
			result.setMsg("操作员必须输入。");
			return result;
		}
		List<OperatorRole> ors = operatorRoleDao.findOperatorRolesByCondition(
				"from OperatorRole o where o.role.id=:rid "
				+ " and o.operator.id=:oid ",
				new String[]{"rid","oid"},
				new Object[]{operatorRole.getRole().getId(),operatorRole.getOperator().getId()});
		if (null == ors || ors.size() > 0){
			result.setSuccess(false);
			result.setMsg("此角色和操作员关系已存在。");
			return result;
		}
		
		operatorRoleDao.addOperatorRole(operatorRole);
		result.setSuccess(true);
		result.setMsg("保存成功。");
		return result;
	}

	//修改
	public AjaxResult updateOperatorRole(OperatorRole operatorRole) {
		AjaxResult result = new AjaxResult();
		if (null == operatorRole){
			result.setSuccess(false);
			result.setMsg("没有数据可修改。");
			return result;
		}
		if (null == operatorRole.getRole()){
			result.setSuccess(false);
			result.setMsg("角色必须输入。");
			return result;
		}
		
		if (null == operatorRole.getOperator()){
			result.setSuccess(false);
			result.setMsg("操作员必须输入。");
			return result;
		}
		List<OperatorRole> ors = operatorRoleDao.findOperatorRolesByCondition(
				"from OperatorRole o where o.role.id=:rid "
				+ " and o.operator.id=:oid "
				+ " and o.id != :id ",
				new String[]{"rid","oid","id"},
				new Object[]{operatorRole.getRole().getId(),operatorRole.getOperator().getId(),operatorRole.getId()});
		if (null != ors && ors.size() > 0){
			result.setSuccess(false);
			result.setMsg("此角色和操作员关系重复。");
			return result;
		}
		
		operatorRoleDao.updateOperatorRole(operatorRole);
		result.setSuccess(true);
		result.setMsg("保存成功。");
		return result;
	}

	//删除
	public AjaxResult deleteOperatorRole(OperatorRole operatorRole) {
		AjaxResult result = new AjaxResult();
		if (null == operatorRole){
			result.setSuccess(false);
			result.setMsg("没有数据可删除。");
			return result;
		}
		
		operatorRoleDao.deleteOperatorRole(operatorRole);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result;
	}

	//查单个
	public OperatorRole findOperatorRoleByParam(String queryStr,String[] paramNames,Object[] values){
		return operatorRoleDao.findOperatorRoleByCondition(queryStr,paramNames,values);
	}
	
	//查列表
	public List<OperatorRole> findOperatorRolesByParam(String queryStr, String[] paramNames, Object[] values) {
		return operatorRoleDao.findOperatorRolesByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<OperatorRole> findOperatorRolesOnPage(Page<OperatorRole> page, String queryStr, String[] paramNames, Object[] values) {
		Page<OperatorRole> pos = operatorRoleDao.findOperatorRolesOnPage(page, queryStr, paramNames, values);
		return pos;
	}

	//按用户查询所有的角色
	public List<OperatorRole> findOperatorRolesByOperator(Operator operator){
		return findOperatorRolesByParam(
				"from OperatorRole operatorrole "
				+ " left join fetch operatorrole.operator o"
				+ " left join fetch operatorrole.role r "
				+ " where o.id = :id", 
				new String[]{"id"}, new Object[]{operator.getId()});
//from com.jsdz.admin.security.model.OperatorRole operatorrole left join fetch or.operator o left join fetch or.role r where o.id = :id
		
	}

	//批量设置用户角色
	@Transactional
	public AjaxResult updateBatchOperatorRole(Long oid,Long[] rids,Operator createBy){
		AjaxResult result = new AjaxResult();
		try{
			result.setSuccess(false);
			List<Operator> ops = operatorDao.findByCondition("from Operator o where o.isDeleted = false and o.id = :id", new String[]{"id"},new Object[]{oid});
			Operator operator = ops.get(0);
			//先移除该用户关联的所有角色
			operatorRoleDao.deleteAllRoleByOperator(operator);
			
			StringBuilder operatorRoleStr = new StringBuilder();
			if (rids != null){
				//再一个一个地加
				for (Long id : rids){
					Role role = roleDao.findRoleByCondition("from  Role r where r.id = :id ", new String[]{"id"},new Object[]{id});
					if (role != null){
						operatorRoleStr.append(role.getRoleName());
						operatorRoleStr.append("; ");
						OperatorRole operatorRole = new OperatorRole();
						operatorRole.setOperator(operator);
						operatorRole.setRole(role);
						operatorRole.setCreateBy(createBy);
						operatorRole.setCreateTime(new Date());
						operatorRoleDao.addOperatorRole(operatorRole);
					}
				}			
			}
			operator.setOperatorRoleStr(operatorRoleStr.toString());
			operatorDao.updateOperator(operator);
			
		}catch(Exception e){
			//好象不会自动回滚，这里加手动，以后再仔细研究
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly(); 
			result.setMsg("操作失败：" + e.toString());
			return result ;			
		}

		result.setSuccess(true);
		result.setMsg("设置成功");
		return result ;
	}

	//查询与该操作员关联的角色
	public List<OperatorRole> findRoleByOperator(Operator operator){
		return operatorRoleDao.findRoleByOperator(operator);
	}
	
	//查询最高级别的权限
	public Map<String,Object> findMaxPermission(Long userId){
		
		List<Map<String,Object>> list = operatorRoleDao.findMaxPermission(userId);
		if (list != null && list.size() > 0){
			return (Map<String,Object>)list.get(0);
		}else
			return null;
		
	}
	
}
