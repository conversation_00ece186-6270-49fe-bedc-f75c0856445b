package com.jsdz.digitalevidence.analysis.dao.impl;

/**
 *
 * @类名: DocDailyDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-31 15:31:04
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;

import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.analysis.dao.DocDailyDao;
import com.jsdz.digitalevidence.analysis.model.DocDaily;

@Repository
public class DocDailyDaoImpl extends GenericEntityDaoHibernateImpl<DocDaily,Long> implements DocDailyDao{

	//新增
	public void addDocDaily(DocDaily docDaily) {
		this.saveOrUpdate(docDaily);
	}

	//删除
	public void deleteDocDaily(DocDaily docDaily) {
		this.delete(docDaily);
	}

	//修改
	public void updateDocDaily(DocDaily docDaily) {
		this.merge(docDaily);
	}

	//按id查询(游离状态)
	public DocDaily findDocDailyById(Long id){

		final String  hql = "from DocDaily s where s.id = :id";
		final Long oid = id;
		DocDaily data = getHibernateTemplate().execute(new HibernateCallback<DocDaily>() {
			public DocDaily doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DocDaily> list = query.list();
				DocDaily rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public DocDaily locateDocDailyById(Long id){

		final String  hql = "from DocDaily s where s.id = :id";
		final Long oid = id;
		DocDaily data = getHibernateTemplate().execute(new HibernateCallback<DocDaily>() {
			public DocDaily doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DocDaily> list = query.list();
				DocDaily rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public DocDaily findDocDailyByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		DocDaily data = getHibernateTemplate().execute(new HibernateCallback<DocDaily>() {
		public DocDaily doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<DocDaily> list = query.list();
			DocDaily rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<DocDaily> findAllDocDailys(){
		return this.find("from DocDaily docDaily ");
	}

	//列表查询
	public List<DocDaily> findDocDailysByCondition(String queryStr,String[] paramNames,Object[] values){
		List<DocDaily> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<DocDaily> findDocDailysOnPage(Page<DocDaily> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<DocDaily>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	//执行非查询的Hql语句
	public Integer executeHql(final String StringHql,final String[] paramNames, final Object[] values){
		Integer data = getHibernateTemplate().execute(new HibernateCallback<Integer>() {      	 
   	    public Integer doInHibernate(Session session)throws HibernateException, SQLException {  
    	    	// 获取查询对象
				Query queryObject = session.createQuery(StringHql);
				if (values != null) {
					for (int i = 0; i < values.length; i++) {
						applyNamedParameterToQuery(queryObject, paramNames[i], values[i]);
					}
				}
				//queryObject.setParameter("id", id);
				return (Integer)queryObject.executeUpdate();
    	    }  
    	});
		return data;
	}

}
