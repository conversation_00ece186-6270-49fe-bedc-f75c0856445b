package com.jsdz.digitalevidence.analysis.openapi.dao;

/**
 *
 * @类名: StationLogDao
 * @说明:
 * @author: kenny
 * @Date 2017-06-09 16:04:15
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.analysis.openapi.model.StationLog;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface StationLogDao extends GenericORMEntityDAO<StationLog,Long> {

	//新增
	public void addStationLog(StationLog stationLog);

	//修改
	public void updateStationLog(StationLog stationLog);

	//删除
	public void deleteStationLog(StationLog stationLog);

	//按id查询,结果是游离状态的数据
	public StationLog findStationLogById(Long id);

	//按id查询
	public StationLog locateStationLogById(Long id);

	//单个查询
	public StationLog findStationLogByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<StationLog> findAllStationLogs();

	//列表查询
	public List<StationLog> findStationLogsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<StationLog> findStationLogsOnPage(Page<StationLog> page,String queryStr,String[] paramNames,Object[] values);

}

