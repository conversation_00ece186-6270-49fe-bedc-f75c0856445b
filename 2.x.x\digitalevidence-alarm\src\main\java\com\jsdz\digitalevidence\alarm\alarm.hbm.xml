<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">


<hibernate-mapping>
	<!-- 警情模型 -->
	<!-- 警情类 -->
	<class name="com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo" table="T_alarm_info">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<!-- 名称属性 -->
		<property name="alarmCode" column="alarm_code"></property>
		<property name="alarmTime" column="alarm_time"></property>
		<property name="alarmName" column="alarm_name"></property>
		<property name="alarmTel" column="alarm_tel"></property>
		<property name="alarmContext" column="alarm_context"></property>
		<property name="isRelation" column="is_relation"></property>
		<property name="createTime" column="create_time"></property>
		<property name="alarmResult" column="alarm_result"></property>
		<property name="alarmAddress" column="alarm_address"></property>
		<property name="caseId" column="case_id"></property>
		<property name="alarmSuperviseType" column="alarm_supervise_type"></property>
		<property name="alarmProccessCode" column="alarm_proccess_code"></property>
		<property name="alarmReceiveCode" column="alarm_receive_code"></property>
		<property name="alarmFeebackCode" column="alarm_feeback_code"></property>
		<property name="processTime" column="process_time"></property>

		<property name="archiveCode" column="archive_code"></property>
		<property name="alarmPolices" column="alarm_polices"></property>
		<property name="orgPaths" column="org_paths"></property>
		<property name="receiveTime" column="receive_time"></property>
		<property name="endTime" column="end_time"></property>
		<property name="status" column="status"></property>
		<property name="title" column="title"></property>
		<!--<property name="orgId" column="org_id"></property>-->
		<!-- 反馈单位  -->
		<many-to-one name="org" cascade="none" lazy="false"
					 class="com.jsdz.admin.org.model.Organization" column="org_id">
		</many-to-one>



		<!-- /处警单位  -->
<!-- 		<many-to-one name="reveiceOrg" cascade="none"-->
<!--		 	class="com.jsdz.admin.org.model.Organization" column="org_id">-->
<!--		</many-to-one>-->
		<!-- /处理关联人  -->
<!-- 		<many-to-one name="police" cascade="none"
		 	class="com.jsdz.admin.security.model.Operator" column="operator_id">
		</many-to-one> -->
	</class>
	
	<!-- 处警人类 -->
	<class name="com.jsdz.digitalevidence.alarm.alarm110.model.AlarmPolice" table="t_alarm_police">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<!-- 创建时间 -->
		<property name="createTime" column="create_time"></property>
		
		<!-- 警情  -->
		<many-to-one name="alarmInfo" cascade="none"
		 	class="com.jsdz.digitalevidence.alarm.alarm110.model.AlarmFeedback" column="feedback_id">
		</many-to-one>
		
		<!-- 警员  -->
		<many-to-one name="police" cascade="none"
		 	class="com.jsdz.admin.security.model.Employees" column="police_id">
		</many-to-one>
	</class>	
	
	<!--  警情视频关联类 （吉林版）-->
	<class name="com.jsdz.digitalevidence.alarm.alarm110.model.AlaBiDoc" table="T_ala_rela">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		
		<!--  音视频编号 -->
		<property name="docId" column="doc_id"></property>
		
		<!-- 警情编号 -->
		<property name="alarmCode" column="alarm_code"></property>
		
		<!-- 案件编号 -->
		<property name="caseCode" column="case_code"></property>
		
		<!-- 音视频类别 -->
		<property name="type" column="t_type"></property>
		
		<!-- 采集时间  -->
		<property name="collTime" column="coll_time"></property>
		
		<!-- 上传时间 -->
		<property name="uplTime" column="upl_time"></property>
		
		<!-- 关联时间 -->
		<property name="relationTime" column="relation_time"></property>
		
		<!-- 单位代码 -->
		<property name="orgCode" column="org_code"></property>
		
		<!-- 备注信息 -->
		<property name="msg" column="t_msg"></property>
		
	</class>
	
	
	
	<!-- 警情视频关联类 -->
	<class name="com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation" table="T_alarm_relation">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<!-- 关联时间 -->
		<property name="relationTime" column="relation_time"></property>
		
		<!-- 警单  -->
		<many-to-one name="alarmInfo" cascade="none"
		 	class="com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo" column="alarminfo_id">
		</many-to-one>
		<!-- 音视频  -->
		<many-to-one name="mediaInfo" cascade="none"
		 	class="com.jsdz.digitalevidence.document.model.Document" column="document_id">
		</many-to-one>
		
		<!-- 操作员  -->
		<many-to-one name="operator" cascade="none"
		 	class="com.jsdz.admin.security.model.Operator" column="operator_id">
		</many-to-one>
	</class>
	
	<!-- 警情反馈单类 -->
	<class name="com.jsdz.digitalevidence.alarm.alarm110.model.AlarmFeedback" table="t_alarm_feedback">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<!-- 警情  -->
		<many-to-one name="alarmInfo" cascade="none"
		 	class="com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo" column="alarm_id">
		</many-to-one>
		<!-- 反馈单位  -->
		<many-to-one name="feedbackOrg" cascade="none"
		 	class="com.jsdz.admin.org.model.Organization" column="org_id">
		</many-to-one>
		<!-- 反馈单编号 -->
		<property name="feedbackCode" column="feedback_code"></property>
		<!-- 反馈时间 -->
		<property name="feedbackTime" column="feedback_time"></property>
		<!-- 处警人 -->
		<property name="alarmPolices" column="alarm_polices"></property>
		<!-- 处理结果 -->
		<property name="alarmResult" column="alarm_result"></property>
		<!-- 更新时间 -->
		<property name="createTime" column="create_time"></property>
	</class>	
	
	<!-- 警情单位类 -->
	<class name="com.jsdz.digitalevidence.alarm.alarm110.model.AlarmOrganization" table="t_alarm_organization">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<!-- 警情  -->
		<many-to-one name="alarmInfo" cascade="none"
		 	class="com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo" column="alarm_id">
		</many-to-one>
		<!-- 反馈单位  -->
		<many-to-one name="org" cascade="none"
		 	class="com.jsdz.admin.org.model.Organization" column="org_id">
		</many-to-one>
	</class>	
	
	<!-- 靖江数据交换 -->
	<class name="com.jsdz.digitalevidence.alarm.jingjiang.model.JingJiangDoc" table="T_jingjiang_doc">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<!-- 名称属性 -->
		<property name="alarmCode" column="alarm_code"></property>
		<property name="policeCode" column="police_code"></property>
		<property name="PoliceName" column="Police_name"></property>
		<property name="captureTime" column="capture_time"></property>
		<property name="uploadTime" column="upload_time"></property>
		<property name="orgCode" column="org_code"></property>
		<property name="mediaUrl" column="media_url"></property>
		<property name="fileSize" column="file_size"></property>
		<property name="docId" column="doc_id"></property>
		<property name="createTime" column="create_time"></property>
		<property name="readFlag" column="read_flag"></property>
	</class>	
	
	<!-- 视频监督回放模型 -->
	<!-- 监督回放类 -->
	<class name="com.jsdz.digitalevidence.alarm.common.model.RecoderView" table="T_gueiyang_recoderview">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<!-- 名称属性 -->
		<property name="doId" column="doId"></property>
		<property name="glbm" column="glbm"></property>
		<property name="gjbh" column="gjbh"></property>
		<property name="xm" column="xm"></property>
		<property name="gxsj" column="gxsj"></property>
		<property name="start" column="start"></property>
		<property name="end" column="end"></property>
		<property name="spwt" column="spwt"></property>
		<property name="spyj" column="spyj"></property>
		<property name="reserved1" column="reserved1"></property>
		<property name="reserved2" column="reserved2"></property>
	</class>
	
	<!-- 用来保存时间戳 -->
	<class name="com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmStamp"  table="T_Alarm_Stamp" >
		<id name="id" column="id" type="long">
			<generator class="native"></generator>
		</id>
		<property name="alaId" column="ALA_ID"></property>
		<property name="casId" column="CAS_ID"></property>
		<property name="docId" column="DOC_ID"></property>
		<property name="polId" column="POL_ID"></property>
		<property name="stampTime" column="stamp_time"></property>
	</class>
	
	<!-- 案件关联视频类 -->
	<class name="com.jsdz.digitalevidence.alarm.alarm110.model.CaseRelation" table="T_case_relation">
		<id name="id" column="id" type="long" >
			<generator class="native" />
		</id>
		
		<!-- 关联时间 -->
		<property name="relationTime" column="relation_time"></property>
		
		<!-- 案件 -->
		<many-to-one name="cas" cascade="none"
			class="com.jsdz.digitalevidence.document.model.Case" column="case_id">
		</many-to-one>
		
		<!--  音频 -->
		<many-to-one name="mediaInfo" cascade="none"
		 	class="com.jsdz.digitalevidence.document.model.Document" column="document_id">
		</many-to-one>
		
		<!-- 操作员  -->
		<many-to-one name="operator" cascade="none"
		 	class="com.jsdz.admin.security.model.Operator" column="operator_id">
		</many-to-one>
	</class>
	
	<!-- 视频关联DTO -->
	<class name="com.jsdz.digitalevidence.alarm.alarm110.bean.docAlarmCaseInfoBean">
		<id name="id">
			<generator class="assigned" />
		</id>
		<property name="orgName" type="string" />
		<property name="orgPath" type="string" />
		<property name="policeCode" type="string" />
		<property name="policeName" type="string" />
		<property name="siteCode" type="string" />
		<property name="type" type="string" />
		<property name="impLevelRec">
			<type name="org.hibernate.type.EnumType">
				<param name="enumClass">com.jsdz.digitalevidence.document.model.ImportantLevel</param>
			</type>
		</property>
		<property name="impLevel">
			<type name="org.hibernate.type.EnumType">
				<param name="enumClass">com.jsdz.digitalevidence.document.model.ImportantLevel</param>
			</type>
		</property>
		<property name="cate">
			<type name="org.hibernate.type.EnumType">
				<param name="enumClass">com.jsdz.digitalevidence.document.model.DocumentCate</param>
			</type>
		</property>
		<property name="docName" type="string" />
		<property name="uploadTime" type="date" />
		<property name="createTime" type="date" />
		<property name="clarity">
			<type name="org.hibernate.type.EnumType">
				<param name="enumClass">com.jsdz.digitalevidence.document.model.VideoClarity</param>
			</type>
		</property>
		<property name="duration" />
		<property name="equimentCode" />
		<property name="fileM" />
		<property name="siteAddr" />
		<property name="comments" />
		<property name="enforceTypeId" />
		<property name="enforceTypeName" />
		<property name="storageType" />
		<property name="thumbnail" />
		<property name="caseCode" />
		<property name="alarmCode" />
	</class>
	
</hibernate-mapping>