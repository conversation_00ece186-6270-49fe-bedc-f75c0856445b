/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.job;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.cycle.CycleConfiguration;
//import com.jsdz.scheduler.job.AbstractJob;
import com.sun.xml.bind.v2.TODO;

/**
 * @类名: AssessmentAlertJob
 * @说明: 考核预警作业，
 *        生成考核预警，考核报告冻结后
 *
 * <AUTHOR>
 * @Date	 2017年6月5日 下午4:10:36
 * 修改记录：
 *
 * @see 	 
 * 
 * TODO
 *  *. 分块
 */
//public abstract class BaseAssessmentJob extends AbstractJob {
public abstract class BaseAssessmentJob {
	
	public static final String KEY_PARAM_CYCLE = "cycle";
	public static final String KEY_PARAM_POLICE = "police";
	
	@Autowired
	protected CycleConfiguration cc;
	@Autowired
	protected EmployeesService empService;
	
	// 
/*	public Cycle getCycle() {
		// 传入参数
		Map<String, Object> params = this.getParams();
		// 周期
		Cycle c = (Cycle) params.get(BaseAssessmentJob.KEY_PARAM_CYCLE);
		//
		return c;
	}
*/	
	// 获取警员
/*	@SuppressWarnings("unchecked")
	public List<Employees> getPolice() {	
		//
		Map<String, Object> params = this.getParams();
		// 警员
		List<Employees> emps = (List<Employees>) params.get(BaseAssessmentJob.KEY_PARAM_POLICE);
		//
		if(emps==null)
			return empService.findAllEmployeess();
		//
		return emps;
	}*/

}
