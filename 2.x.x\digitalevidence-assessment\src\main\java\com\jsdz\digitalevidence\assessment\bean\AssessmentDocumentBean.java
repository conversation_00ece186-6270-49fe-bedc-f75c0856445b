/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.digitalevidence.assessment.model.AssessmentCate;
import com.jsdz.digitalevidence.document.bean.DocumentBean;

/**
 * @类名: AssessmentDocumentBean
 * @说明: 考核资料Bean，
 *        执法资料，带考核信息
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentDocumentBean extends DocumentBean {

	/** 是否已考核*/
	private boolean isAssessed;
	/** 考核类型，isAssessd=true时有效*/
	private AssessmentCate assessmentCate;
	/** 考核时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date assessDate;

	public Date getAssessDate() {
		return assessDate;
	}

	public void setAssessDate(Date assessDate) { 
		this.assessDate = assessDate;
	}

	public boolean isAssessed() {
		return isAssessed;
	}
	
	public boolean getIsAssessed() {
		return isAssessed;
	}

	public void setAssessed(boolean isAssessed) {
		this.isAssessed = isAssessed;
	}
	
	public void setIsAssessed(boolean isAssessed) {
		this.isAssessed = isAssessed;
	}

	public AssessmentCate getAssessmentCate() {
		return assessmentCate;
	}

	public void setAssessmentCate(AssessmentCate assessmentCate) {
		this.assessmentCate = assessmentCate;
	}

	
}
