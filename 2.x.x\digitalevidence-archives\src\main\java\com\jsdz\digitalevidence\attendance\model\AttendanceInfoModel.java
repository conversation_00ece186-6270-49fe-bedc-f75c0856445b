package com.jsdz.digitalevidence.attendance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 打卡信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_attendance_info")
public class AttendanceInfoModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 执行k考勤任务的id
     */
    private Long attendanceTaskId;

    /**
     * 单位
     */
    private Long attendanceOrgId;

    /**
     * 考勤人
     */
    private String attendanceUserId;

    /**
     * 考勤设备
     */
    private Long deviceId;

    /**
     * 设备编号
     */
    private String deviceCode;

    /**
     * 新增是否删除，改为逻辑删除
     */
    private int isDelete;

    /**
     * 是否允许自动打卡（0允许，1不允许）,如果任务不允许自动打卡这字段失效
     */
    private String isAutomatic;

    /**
     * 离考勤范围多少米可打卡,如果不在任务表范围内这字段失效
     */
    private String attendanceScopeRice;

    /**
     * 可提前多少分钟打卡,如果不在任务表范围内这字段失效
     */
    private String advanceByMinutes;

    /**
     * 最后一次打卡时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date lastAattendanceTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date updateTime;


}
