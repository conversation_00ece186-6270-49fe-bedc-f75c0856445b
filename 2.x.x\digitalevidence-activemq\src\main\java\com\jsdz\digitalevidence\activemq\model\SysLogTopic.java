package com.jsdz.digitalevidence.activemq.model;
/**
 * 
 * @类名: SysLogTopic
 * @说明: 日志主题表
 *
 * @author: kenny
 * @Date	2018年3月15日下午2:51:20
 * 修改记录：
 *
 * @see
 */
public class SysLogTopic {
	private Long id;
	private String topic;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getTopic() {
		return topic;
	}
	public void setTopic(String topic) {
		this.topic = topic;
	}
	
	
}
