package com.jsdz.digitalevidence.analysis.service.impl;

/**
 * 
 * @类名: DocStatiticalServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年6月13日下午2:32:25
 * 修改记录：
 *
 * @see
 */
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.digitalevidence.analysis.bean.DocStatisicalDto;
import com.jsdz.digitalevidence.analysis.dao.DocStatisicalDao;
import com.jsdz.digitalevidence.analysis.service.DocStatisicalService;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("DocStatisicalServiceImpl")

public class DocStatisicalServiceImpl implements  DocStatisicalService {
	@Autowired
	private DocStatisicalDao docStatisicalDao;
	
	@Override
	public List<DocStatisicalDto>findDocStatisical(String sql,String[] paramNames, Object[] values){
		 return docStatisicalDao.QueryNamedAllDataSQL(sql, paramNames, values);
	}
}
