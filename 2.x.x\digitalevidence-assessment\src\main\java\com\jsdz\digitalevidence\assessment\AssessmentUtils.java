package com.jsdz.digitalevidence.assessment;

import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * @类名: AssessmentUtils
 * @说明: 考核工具类
 *
 * <AUTHOR>
 * @Date	 2017年11月15日 下午3:33:46
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentUtils {
	
	public static Integer nullOrInteger(Object o) {
		if(o==null)
			return null;
		if(o instanceof Integer)
			return (Integer)o;
		else if(o instanceof BigInteger)
			return AssessmentUtils.nullOrInteger((BigInteger)o);
		else
			return AssessmentUtils.nullOrInteger((BigDecimal)o);
	}
	
	public static Integer nullOrInteger(BigDecimal d) {
		return d.intValue();
			
	}
	
	public static Integer nullOrInteger(BigInteger d) {
		return d.intValue();
			
	}
	
	public static Long nullOrLong(Object o) {
		if(o==null)
			return null;
		if(o instanceof Long)
			return (Long)o;
		else if(o instanceof BigInteger)
			return AssessmentUtils.nullOrLong((BigInteger)o);
		else
			return AssessmentUtils.nullOrLong((BigDecimal)o);
	}
	
	public static Long nullOrLong(BigDecimal d) {
		return d.longValue();
	}
	
	public static Long nullOrLong(BigInteger d) {
		return d.longValue();
			
	}
	
	public static Float nullOrFloat(BigDecimal d) {
		if(d==null)
			return null;
		else
			return d.floatValue();
	}
	
	public static int zeroIfNull(Integer i) {
		if(i==null)
			return 0;
		else
			return i;
	}
	
	public static long zeroIfNull(Long l) {
		if(l==null)
			return 0;
		else
			return l;
	}

}
