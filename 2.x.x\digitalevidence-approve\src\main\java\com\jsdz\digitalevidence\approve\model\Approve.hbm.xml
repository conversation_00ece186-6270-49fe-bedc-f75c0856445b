<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

	<!-- *********业务审批模型********* -->
	<!-- 审批业务 -->
	<class name="com.jsdz.digitalevidence.approve.model.ApproveBusiness" table="T_APPROVE_BUSINESS">
		<id name="id" column="ID" >
            <generator class="native"/>
        </id>
        <!-- 编码 -->
		<property name="code" column="CODE"></property>
        <!-- url -->
		<property name="uri" column="URI"></property>
		<!-- 描述 -->
		<property name="comment" column="REMARK"></property>
	</class>
	
	<!-- 业务令牌 -->
	<class 
		name="com.jsdz.digitalevidence.approve.model.BusinessToken" table="T_APPROVE_TOKEN">
		<id name="tokenId" column="TOKEN_ID" >
            <generator class="native"/>
        </id>
        
        <!-- 参数 -->
		<property name="params" column="PARAMS"></property>
		
        <!-- 拥有者 -->
		<many-to-one name="owner" cascade="none"
		 	class="com.jsdz.admin.security.model.Employees" column="POLICE_ID">
		</many-to-one>
		
		<!-- 业务 -->
		<many-to-one name="business" cascade="none"
		 	class="com.jsdz.digitalevidence.approve.model.ApproveBusiness" column="BUSINESS_ID">
		</many-to-one>
		
        <!-- 业务对象 -->
        <list name="bos" table="T_APPROVE_BUSINESS_OBJECT"> 
		    <key column="TOKEN_ID"/>
		    <index column="_index"/>
		    <composite-element class="com.jsdz.digitalevidence.approve.model.BusinessObject">
		        <property name="objId" column="OBJ_ID" type="com.jsdz.core.PersistentObject" />  
		        <!-- 业务对象类型 -->
				<many-to-one name="objType" cascade="none"
				 	class="com.jsdz.digitalevidence.approve.model.BusinessObjectType" column="OBJECT_TYPE_ID">
				</many-to-one>
		    </composite-element>  
		</list>
        
        <!-- 流程实例Id -->
		<property name="procInstId" column="PROC_INSTANCE_ID"></property>
		
     	<!-- 创建时间 -->
		<property name="createTime" column="CREATE_TIME" type="timestamp"></property>
        <!-- 生效日期 -->
		<property name="startDate" column="START_DATE" type="timestamp"></property>
        <!-- 失效日期 -->
		<property name="endDate" column="END_DATE"></property>
		<!-- 最近使用时间 -->
		<property name="updateTime" column="UPDATE_TIME"></property>
        <!-- 使用次数 -->
		<property name="times" column="TIMES"></property>
		
	</class>

	<!-- 适用流程 -->
	<class name="com.jsdz.digitalevidence.approve.model.ApplyProcess" table="T_APPROVE_APPLY_PROCESS">
		<id name="id" column="ID" >
            <generator class="native"/>
        </id>
        <!-- 流程定义Id -->
		<property name="procDefId" column="PROC_DEF_ID"></property>
		<!-- 状态 -->
		<property name="status" column="STATUS">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass">
            			com.jsdz.digitalevidence.approve.model.ApproveStatus
            		</param>
            	</type>
		</property>
		<!-- 适用业务 -->
		<many-to-one name="business" cascade="none"
		 	class="com.jsdz.digitalevidence.approve.model.ApproveBusiness" column="BUSINESS_ID">
		</many-to-one>
		<!-- 适用组织 -->
		<many-to-one name="org" cascade="none"
		 	class="com.jsdz.admin.org.model.Organization" column="ORG_ID">
		</many-to-one>
	</class>
	
	<!-- 业务对象类型 -->
	<class name="com.jsdz.digitalevidence.approve.model.BusinessObjectType" 
					table="T_APPROVE_BUSINESS_OBJECT_TYPE">
		<id name="typeId" column="_ID" type="long">
			<generator class="native"/>
		</id>
		<!-- 对象类型 -->
		<property name="clazz" column="CLAZZ" type="com.jsdz.core.PersistentClass"></property>
		<!-- 对象查看api -->
		<property name="url" column="URL"></property>
		<!-- 说明 -->
		<property name="comment" column="COMMENT"></property>
	</class>
	
</hibernate-mapping>