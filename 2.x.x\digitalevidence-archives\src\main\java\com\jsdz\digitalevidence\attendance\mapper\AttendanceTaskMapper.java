package com.jsdz.digitalevidence.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceTaskBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceScopeModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceTaskModel;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AttendanceTaskMapper  extends BaseMapper<AttendanceTaskModel> {
    @Select("${sql}")
    List<AttendanceTaskBean> getDatas(@Param("sql") String sql);

    @Select("${sql}")
    Integer getCount(@Param("sql") String sql);

    @Insert("${sql}")
    void saveData(@Param("sql") String sql);

    @Update("${sql}")
    void updateData(@Param("sql") String sql);

    @Select("${sql}")
    AttendanceTaskModel getById(@Param("sql") String sql);

    @Delete("${sql}")
    void deleteById(@Param("sql") String sql);
}
