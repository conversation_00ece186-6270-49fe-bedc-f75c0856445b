package com.jsdz.digitalevidence.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceInfoBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceCheckModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceInfoModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceScopeModel;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AttendanceScopeMapper extends BaseMapper<AttendanceScopeModel> {

    @Select("${sql}")
    List<AttendanceScopeModel> getDatas(@Param("sql") String sql);

    @Select("${sql}")
    Integer getCount(@Param("sql") String sql);

    @Insert("${sql}")
    void saveData(@Param("sql") String sql);

    @Update("${sql}")
    void updateData(@Param("sql") String sql);

    @Select("${sql}")
    AttendanceScopeModel getById(@Param("sql") String sql);

    @Delete("${sql}")
    void deleteById(@Param("sql") String sql);
    @Delete("DELETE FROM SYS_ATTENDANCE_SCOPE T WHERE T.TASKID =#{taskId}")
    void deleteByTaskId(@Param("taskId") String taskId);

}
