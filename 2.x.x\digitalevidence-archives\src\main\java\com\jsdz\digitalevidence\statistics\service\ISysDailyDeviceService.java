package com.jsdz.digitalevidence.statistics.service;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.bean.param.SysDailyDeviceParam;
import com.jsdz.digitalevidence.statistics.model.SysAbnormalStatisticsModel;
import com.jsdz.digitalevidence.statistics.model.SysDailyDeviceModel;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface ISysDailyDeviceService extends IService<SysDailyDeviceModel> {

    Page<SysDailyDeviceModel> getDatas(SysDailyDeviceParam param, PerLvlBean permLvl);

    void startDailyDevice();

    void addCaseFlow(Long id, String caseFlow);
}
