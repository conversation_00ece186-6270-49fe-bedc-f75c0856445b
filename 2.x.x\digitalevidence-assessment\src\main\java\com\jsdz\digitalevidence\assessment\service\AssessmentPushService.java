/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.service;

import java.util.List;

import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.bean.AssessmentPushDocBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentPushDocQueryBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentOfPushDocBean;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessment;

/**
 * @类名: AssessmentPushService
 * @说明: 拍摄考核推送应用服务
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午6:04:13
 * 修改记录：
 *
 * @see 	 
 */
public interface AssessmentPushService {
	
	// (考核员)查找推送视频
	public Page<AssessmentPushDocBean> searchPushDoc(Page<AssessmentPushDocBean> page, 
						AssessmentPushDocQueryBean qb) throws Exception;
	
	/**
	 * @说明：推送视频
	 *
	 * <AUTHOR>
	 * @return
	 * 
	 */
	public void pushDoc();
	
	// 考核推送视频
	public ShootingAssessmentOfPushDocBean openPushDocAssessment(Long pushingDocId, Operator operator, String ip);
	public void submitPushDocAssessment(Long pushingDocId, ShootingAssessment assessment);

	
	// 考核工作查询
	// 。。。 
	
}
