package com.jsdz.digitalevidence.task.dao.mapper;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.digitalevidence.task.model.TTask;
import com.jsdz.digitalevidence.task.model.TaskRecorderBean;
import com.jsdz.digitalevidence.task.bean.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 任务管理表(TTask)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-25 16:47:07
 */
public interface TTaskMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TTask queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param tTask 查询条件
     * @param page         分页对象
     * @return 对象列表
     */
    List<TTask> queryAllByLimit(TTask tTask, @Param("page") Pageable page);

    /**
     * 统计总行数
     *
     * @param tTask 查询条件
     * @return 总行数
     */
    long count(TTask tTask);

    /**
     * 新增数据
     *
     * @param tTask 实例对象
     * @return 影响行数
     */
    int insert(TTask tTask);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TTask> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TTask> entities);

    /**
     * 修改数据
     *
     * @param tTask 实例对象
     * @return 影响行数
     */
    int update(TTask tTask);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 分页查询任务列表
     * @param pageBean
     * @return
     */
    List<TTask> pageQuery(TaskPageBean pageBean);

    /**
     * 用于查询任务分页总数
     * @param pageBean
     * @return
     */
    int totalCount(TaskPageBean pageBean);

    /**
     * 查询执法仪
     * @param devIds
     * @return
     *//*
    List<TaskRecorderBean> queryRecorders(@Param("taskId") Long taskId, @Param("devIds") Long[] devIds, @Param("perLvlBean") PerLvlBean perLvlBean);*/

    /**
     * 查询执法仪
     * @param devIds 执法仪id
     * @return
     */
    List<TaskRecorderBean> queryRecorders(@Param("devIds") Long[] devIds, @Param("perLvlBean") PerLvlBean perLvlBean);


    /**
     * 根据id查找任务
     * @param taskId
     * @return
     */
    TTask findById(Long taskId);

    /**
     * 根据存储编号查询存储id
     * @param storageNo
     * @return
     */
    Long getStorageIdByCode(String storageNo);

    /**
     * 执法仪获取任务列表
     * @param dto
     * @return
     */
    List<TaskApiVo> apiPage(TaskApiDto dto);

    /**
     * 执法仪任务列表总数查询
     * @param dto
     * @return
     */
    int apiPageCount(TaskApiDto dto);

    /**
     * 根据文件名查询文档id
     * @param fileNames
     * @return
     */
    List<Long> getDocByName(@Param("fileNames") List<String> fileNames);

    /**
     * 根据文件名查询文档id
     * @param fileName
     * @return
     */
    Long getDocIdByName(@Param("fileName") String fileName);

    /**
     * 任务分发列表分页查询
     * @param pageBean
     * @return
     */
    List<TaskProcessVo> processPage(TaskProcessPageBean pageBean);

    /**
     * 任务分发列表总数查询
     * @param pageBean
     * @return
     */
    int processPageCount(TaskProcessPageBean pageBean);

    /**
     * 根据id列表查询任务
     * @param ids
     * @return
     */
    List<TTask> queryByIds(@Param("ids") Long[] ids);

    /**
     * 查询任务结果
     * @param pageBean
     * @return
     */
    List<TaskDocVo> taskDocPage(TaskPageBean pageBean);

    /**
     * 查询任务结果数量
     * @param pageBean
     * @return
     */
    int taskDocPageCount(TaskPageBean pageBean);

    /**
     * 通过id删除
     * @param ids
     */
    void deleteByIds(@Param("ids") Long[] ids);

    /**
     * 待办已办任务分页查询
     * @param pageBean
     * @return
     */
    List<AffairVo> affairList(TaskPageBean pageBean);

    /**
     * 待办已办任务总数
     * @param pageBean
     * @return
     */
    int affairCount(TaskPageBean pageBean);

    /**
     * 查询事件详情
     * @param id 事件id
     * @param type 事件类型
     * @param permLvl 用户权限
     * @return
     */
    AffairVo affairInfo(@Param("id") Long id, @Param("type") Integer type, @Param("permLvl") PerLvlBean permLvl);
}

