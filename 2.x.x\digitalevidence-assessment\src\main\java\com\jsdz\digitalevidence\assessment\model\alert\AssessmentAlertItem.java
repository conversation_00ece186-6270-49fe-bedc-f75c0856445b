/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.model.alert;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: AssessmentAlertItem
 * @说明: 考核预警项
 *        
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午6:15:56
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentAlertItem<V> extends AbstractDTO {
	
	/** Id*/
	private Long alertItemId;
	/** 项目*/
	private String item;
	/** 值*/
	private V v;
	/** 参考值*/
	private V ref;
	
	public String getItem() {
		return item;
	}
	public void setItem(String item) {
		this.item = item;
	}
	public V getV() {
		return v;
	}
	public void setV(V v) {
		this.v = v;
	}
	public V getRef() {
		return ref;
	}
	public void setRef(V ref) {
		this.ref = ref;
	}
	public Long getAlertItemId() {
		return alertItemId;
	}
	public void setAlertItemId(Long alertItemId) {
		this.alertItemId = alertItemId;
	}
	
	
}
