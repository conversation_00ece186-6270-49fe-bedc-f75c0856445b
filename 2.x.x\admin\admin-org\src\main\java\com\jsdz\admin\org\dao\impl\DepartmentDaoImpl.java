package com.jsdz.admin.org.dao.impl;

/**
 *
 * @类名: DepartmentDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-10 08:34:34
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.org.dao.DepartmentDao;
import com.jsdz.admin.org.model.Department;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class DepartmentDaoImpl extends GenericEntityDaoHibernateImpl<Department,Long> implements DepartmentDao{

	//新增
	public void addDepartment(Department department) {
		this.saveOrUpdate(department);
	}

	//删除
	public void deleteDepartment(Department department) {
		this.delete(department);
	}

	//修改
	public void updateDepartment(Department department) {
		this.saveOrUpdate(department);
	}

	//按id查询
	public Department locateDepartmentById(Long id){

		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery("from Department o where o.id=:id");
		query.setParameter("id", id);
		List<Department> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//单个查询
	public Department findDepartmentByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		};
		List<Department> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//查询全部
	public List<Department> findAllDepartments(){
		return this.find("from Department department ");
	}

	//列表查询
	public List<Department> findDepartmentsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<Department> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<Department> findDepartmentsOnPage(Page<Department> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<Department>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
