package com.jsdz.digitalevidence.alarm.alarm110.dao.impl;

/**
 *
 * @类名: AlarmFeedbackDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2018-08-28 11:11:14
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmFeedbackDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmFeedback;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class AlarmFeedbackDaoImpl extends GenericEntityDaoHibernateImpl<AlarmFeedback,Long> implements AlarmFeedbackDao{

	//新增
	public void addAlarmFeedback(AlarmFeedback alarmFeedback) {
		this.saveOrUpdate(alarmFeedback);
	}

	//删除
	public void deleteAlarmFeedback(AlarmFeedback alarmFeedback) {
		this.delete(alarmFeedback);
	}

	//修改
	public void updateAlarmFeedback(AlarmFeedback alarmFeedback) {
		this.merge(alarmFeedback);
	}

	//按id查询(游离状态)
	public AlarmFeedback findAlarmFeedbackById(Long id){

		final String  hql = "from AlarmFeedback a where a.id = :id";
		final Long oid = id;
		AlarmFeedback data = getHibernateTemplate().execute(new HibernateCallback<AlarmFeedback>() {
			public AlarmFeedback doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmFeedback> list = query.list();
				AlarmFeedback rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public AlarmFeedback locateAlarmFeedbackById(Long id){

		final String  hql = "from AlarmFeedback a where a.id = :id";
		final Long oid = id;
		AlarmFeedback data = getHibernateTemplate().execute(new HibernateCallback<AlarmFeedback>() {
			public AlarmFeedback doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmFeedback> list = query.list();
				AlarmFeedback rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public AlarmFeedback findAlarmFeedbackByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		AlarmFeedback data = getHibernateTemplate().execute(new HibernateCallback<AlarmFeedback>() {
		public AlarmFeedback doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<AlarmFeedback> list = query.list();
			AlarmFeedback rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<AlarmFeedback> findAllAlarmFeedbacks(){
		return this.find("from AlarmFeedback alarmFeedback ");
	}

	//列表查询
	public List<AlarmFeedback> findAlarmFeedbacksByCondition(String queryStr,String[] paramNames,Object[] values){
		List<AlarmFeedback> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<AlarmFeedback> findAlarmFeedbacksOnPage(Page<AlarmFeedback> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<AlarmFeedback>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//执行自定义的hql
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
}
