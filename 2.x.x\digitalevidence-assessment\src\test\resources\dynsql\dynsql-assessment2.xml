<?xml version="1.0" encoding="utf-8"?>
<!-- 考核监督动态sql -->
<sqlTemplates>
  <entry>
  	<!-- 分页，查询警员考核简要报告 -->
    <string>queryAssessmentReportBrief</string>
    <sqlTemplate>
      <!-- hql -->
      <sqlPattern>
          select 
      	  new com.jsdz.digitalevidence.assessment.bean.AssessmentReportBean(
      	    r.reportId,
      	    r.sumOfShootingAssessment.totalAssessingDoc,
      	    r.sumOfShootingAssessment.totalDoc,
      	    r.startDate,
      	    r.endDate,
      	    r.submitDate,
      	    r.type
      	  )
	      from ShootingAssessmentReport r
	      where 1=1 {0} <!-- policeId -->
	      {1} {2} <!-- 开始结束日期 -->
	      {3} <!-- 报表类型 -->
	      order by r.submitDate desc
	      
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentReportBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeId</paramName>
                <t>and r.police.id = :policeId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, startDate -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and r.startDate &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endDate, exclude -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and r.endDate &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, type -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>type</paramName>
                <t>and r.type = :type</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 查询警员简要报告，总页数 -->
    <string>queryAssessmentReportBriefTotalPage</string>
    <sqlTemplate>
      <!-- sql -->
      <sqlPattern>
	      SELECT count(*) as total
	      from ShootingAssessmentReport r
	      where 1=1 {0} <!-- policeId -->
	      {1} {2} <!-- 开始结束日期 -->
	      {3}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentReportBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeId</paramName>
                <t>and r.police.id = :policeId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, startDate -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and r.startDate &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endDate, exclude -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and r.endDate &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, type -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>type</paramName>
                <t>and r.type = :type</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 获取考核报告bean -->
    <string>findAssessmentReportBean</string>
    <sqlTemplate>
      <!-- hql -->
      <sqlPattern>
         from ShootingAssessmentReport r 
            left join fetch r.sumOfShooting s1
            left join fetch r.sumOfShootingAssessment s2
            left join fetch r.police police 
	        left join fetch police.department d
	        left join fetch police.organization o
	        left join fetch o.region r1
	      where 1=1 {0}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>reportId</paramName>
                <t>and r.reportId = :reportId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 获取完整考核报告bean -->
    <string>getCompleteShootingAccessmentReport</string>
    <sqlTemplate>
      <!-- sql -->
      <sqlPattern>
      	select
		        d.report_id as reportId ,
				b._workNumber as policeCode,
				b._name as policeName,
				c._position as position,
				b._male as sex,
				b._age as age,
				o._orgName as orgName,
				d1._name as deptName,
				d.submit_date as createTime,
				d.comments as comments,
				d.start_date as startDate,
				d.end_date as endDate,
				sumOfShooting.*,
				sumOfShootingAssessment.*
				from T_ASSESSMENT_REPORT d
				left join admin_employees b on b._id = d.police_id
				left join admin_position c on b._positionId = c._id
				left join admin_organization o on o._id = b._organizationId
				left join admin_region r on o._regionId = r._id
				left join admin_department d1 on d1._id = b._departmentId
				left outer join t_analysis_shooting_summary sumOfShooting on d.shooting_sum_id = sumOfShooting.id
				left outer join t_assessment_shooting_summary sumOfShootingAssessment on d.SHOOTING_ASSESSMENT_SUM_ID = sumOfShootingAssessment.id				
				where 1=1 {0}
         
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReport</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>reportId</paramName>
                <t>and d.report_id = :reportId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 获取完整考核报告bean -->
    <string>getCompleteShootingAccessmentReportTotalPage</string>
    <sqlTemplate>
      <!-- sql -->
      <sqlPattern>
         select 1 total from T_ASSESSMENT_REPORT d
         where 1=1 {0}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReportBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>reportId</paramName>
                <t>and d.report_id = :reportId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
</sqlTemplates>