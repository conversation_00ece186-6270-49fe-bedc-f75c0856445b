/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.assessment.model.AssessmentCate;

/**
 * @类名: ShootingAssessmentBean
 * @说明: 拍摄考核Bean
 *        
 *      
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingAssessmentBean extends AbstractDTO {

	/********警员资料********/
	/** 警号*/
	private String policeCode;
	/** 职位*/
	private String position;
	/** 姓名*/
	private String name;
	/** 性别*/
	private String sex;
	/** 年龄*/
	private Integer age;
	/** 考核目标资料*/
	private Long docId;
	/** 考核类型*/
	private AssessmentCate cate;
	
	/******警情信息******/
	private Long alarmId;    // 警情Id
    private String alarmCode;    //警情号
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date alarmTime;    //报警时间
    private String alarmName;    //报警人姓名
    private String alarmTel;    //报警电话
    private String alarmContext;    //报警内容
    private Integer isRelation=0; //是否已关联(0未关联，1已关联)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime; //创建时间
	
	/** 文档url*/
	private String docUrl;
	/** */
	private String contentType;

	public String getPoliceCode() {
		return policeCode;
	}

	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}

	public String getPosition() {
		return position==null ? "-" : position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}

	public String getDocUrl() {
		return docUrl;
	}

	public void setDocUrl(String docUrl) {
		this.docUrl = docUrl;
	}

	public AssessmentCate getCate() {
		return cate;
	}

	public void setCate(AssessmentCate cate) {
		this.cate = cate;
	}
	
	public String getAlarmCode() {
		return alarmCode;
	}

	public void setAlarmCode(String alarmCode) {
		this.alarmCode = alarmCode;
	}

	public Date getAlarmTime() {
		return alarmTime;
	}

	public void setAlarmTime(Date alarmTime) {
		this.alarmTime = alarmTime;
	}

	public String getAlarmName() {
		return alarmName;
	}

	public void setAlarmName(String alarmName) {
		this.alarmName = alarmName;
	}

	public String getAlarmTel() {
		return alarmTel;
	}

	public void setAlarmTel(String alarmTel) {
		this.alarmTel = alarmTel;
	}

	public String getAlarmContext() {
		return alarmContext;
	}

	public void setAlarmContext(String alarmContext) {
		this.alarmContext = alarmContext;
	}

	public Integer getIsRelation() {
		return isRelation;
	}

	public void setIsRelation(Integer isRelation) {
		this.isRelation = isRelation;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getAlarmId() {
		return alarmId;
	}

	public void setAlarmId(Long alarmId) {
		this.alarmId = alarmId;
	}

	public String getContentType() {
		return contentType;
	}

	public void setContentType(String contentType) {
		this.contentType = contentType;
	}
	
}
