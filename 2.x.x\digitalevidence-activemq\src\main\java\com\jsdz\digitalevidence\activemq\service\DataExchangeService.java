package com.jsdz.digitalevidence.activemq.service;

/**
 * 
 * @类名: DataExchangeService
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-03-11 21:58:37
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.activemq.model.DataExchange;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface DataExchangeService {

	//新增
	public AjaxResult addDataExchange(DataExchange dataExchange);

	//修改
	public AjaxResult updateDataExchange(DataExchange dataExchange);

	//删除
	public AjaxResult deleteDataExchange(DataExchange dataExchange);

	//按id查询,结果是游离状态的数据
	public DataExchange findDataExchangeById(Long id);

	//按id查询
	public DataExchange locateDataExchangeById(Long id);

	//单个查询
	public DataExchange findDataExchangeByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DataExchange> findAllDataExchanges();

	//列表查询
	public List<DataExchange> findDataExchangesByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DataExchange> findDataExchangesOnPage(Page<DataExchange> page, String queryStr,String[] paramNames,Object[] values);

	//根据系统参数设置定时删除时间过得太久的交换数据
	public Integer deleteDataExchangeSetAnyDays();
	
}

