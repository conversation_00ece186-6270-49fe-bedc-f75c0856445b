package com.jsdz.digitalevidence.statistics.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VEquipmentOnlineStatisticsModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private Long id;

    @TableField("ORGCODE")
    private String orgcode;

    @TableField("ORGNAME")
    private String orgname;

    @TableField("PATH")
    private String path;

    @TableField("PARENTID")
    private Long parentid;

    @TableField("SITEONESELF")
    private Long siteoneself;

    @TableField("SITEALL")
    private Long siteall;

    @TableField("STORAGEONESELF")
    private Long storageoneself;

    @TableField("STORAGEALL")
    private Long storageall;

    @TableField("EMPLOYEESONESELF")
    private Long employeesoneself;

    @TableField("EMPLOYEESALL")
    private Long employeesall;

    @TableField("RECORDERONESELF")
    private Long recorderoneself;

    @TableField("RECORDERALL")
    private Long recorderall;

    @TableField("RECORDERNORMALONESELF")
    private Long recordernormaloneself;

    @TableField("RECORDERNORMALALL")
    private Long recordernormalall;

    @TableField("DOCONESELF")
    private Long doconeself;

    @TableField("DOCALL")
    private Long docall;

    @TableField("ZONGDOCDURATIONONESELF")
    private String zongdocdurationoneself;

    @TableField("ZONGDOCDURATIONALL")
    private String zongdocdurationall;

    @TableField("ONLINEQUANTITYONESELF")
    private Double onlinequantityoneself;

    @TableField("ONLINEQUANTITYALL")
    private Double onlinequantityall;

    @TableField("DOCUNMONESELF")
    private String docunmoneself;

    @TableField("DOCUNM")
    private String docunm;

    @TableField("DOCDURATIONONESELF")
    private String docdurationoneself;

    @TableField("DOCDURATIONALL")
    private String docdurationall;

    @TableField("DOCSIZEONESELF")
    private String docsizeoneself;

    @TableField("DOCSIZEALL")
    private String docsizeall;


}
