/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.model.push.strategy.impl;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAssessorSumBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPushDocDao;
import com.jsdz.digitalevidence.assessment.model.push.AssessmentPushDoc;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocAssessorStrategy;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocDocStrategy;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocPoliceStrategy;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocStrategy;
import com.jsdz.digitalevidence.document.model.Document;
import com.sun.xml.bind.v2.TODO;

/**
 * @类名: PushDocStrategyImpl
 * @说明: 推送考核文档策略实现
 *
 * <AUTHOR>
 * @Date	 2017年10月19日 下午2:40:36
 * 修改记录：
 *
 * @see 	 
 * 
 * TODO 
	  *. 支持并行推送
	          考核员分布式存储
 */
@Component
public class PushDocStrategyImpl implements PushDocStrategy {
	
	private int topNPolice =100; 
	private int topNDoc =10; 
	private int topNAssessor =50; 
	@Value("${assessment.assessRate}")
	private Float assessRate;
	@Autowired
	private PushDocPoliceStrategy pushDocPoliceStrategy;
	@Autowired
	private PushDocDocStrategy pushDocDocStrategy;
	@Autowired
	private PushDocAssessorStrategy pushDocAssessorStrategy;
	@Autowired
	private AssessmentPushDocDao pushDocDao;
	@Autowired
	private EmployeesService empService;
	

	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocStrategy#push()
	 */
	@Override
	@Transactional(readOnly=true)
	public void push(Cycle cycle) {
		// 查找考核推送目标警员，未达考核率
		List<ShootingAssessmentSumBean> assessmentSums = pushDocPoliceStrategy.
							findPushingPolice(cycle.getStart(), cycle.getEnd(), assessRate, topNPolice);
		// 查找考核员
		List<AssessmentAssessorSumBean> assessors = pushDocAssessorStrategy.
							findAssessor(cycle.getStart(), cycle.getEnd(), topNAssessor);
		for(ShootingAssessmentSumBean police : assessmentSums) {
			((PushDocStrategy) AopContext.currentProxy()).doPush(cycle, police, assessors);
			
		}
	}
	
	// 打开新事务，分批提交
	@Override
	@Transactional(propagation=Propagation.REQUIRES_NEW, rollbackFor=Exception.class)
	public void doPush(Cycle cycle, ShootingAssessmentSumBean assessmentSum, List<AssessmentAssessorSumBean> assessors) {
		// 查找推送文档
		List<Document> docs = pushDocDocStrategy.
								findPushDoc(assessmentSum.getPoliceId(), cycle.getStart(), cycle.getEnd(), topNDoc);
		// 考核员考核量排序
		sortAssessors(assessors);
		int c = 0;
		do {
			// 计算推送文档数量，推送后不大于考核率，若大于，推送一个文档
			c = Math.round((assessRate-assessmentSum.getAssessRate())*assessmentSum.getUploadDocs());
			if(c==0)
				c=1;
			// 计算推送到考核员文档数量，推送后不大于当前最大考核量，但至少推送一个文档
			AssessmentAssessorSumBean min = assessors.get(0);
			AssessmentAssessorSumBean max = assessors.get(assessors.size()-1);
			int c1 = max.getAssessDocs()-min.getAssessDocs();
			if(c>c1&&c1!=0) {
				min.addPushDoc(c1);
				c = c1;
			} else {
				min.addPushDoc(c);
			}
			//
			assessmentSum.addAssessDoc(c);
			//
			List<Document> pushingDocs = docs.subList(0, c); 
			// 写入推送文档
			addPushDoc(assessmentSum.getPoliceId(), min.getPoliceId(), pushingDocs);
			//
			docs.removeAll(pushingDocs);
			// 重新排序考核员
			sortAssessors(assessors);
		} while(assessRate>assessmentSum.getAssessRate());
	}
	
	// 排序考核员，按考核量
	public void sortAssessors(List<AssessmentAssessorSumBean> assessors) {
		Collections.sort(assessors, new Comparator<AssessmentAssessorSumBean>() {  			  
            @Override  
            public int compare(AssessmentAssessorSumBean o1, AssessmentAssessorSumBean o2) {  
               return o1.getAssessDocs()-o2.getAssessDocs();
            }  
        });  
	}
	
	// 排序考核员考核量
	public void addPushDoc(Long policeId, Long assessorId, List<Document> pushingDocs) {
		for(Document doc : pushingDocs) {
			AssessmentPushDoc pushDoc = new AssessmentPushDoc();
			Employees police = empService.locateEmployeesById(policeId);
			pushDoc.setPolice(police);
			Employees assessor = empService.locateEmployeesById(assessorId);
			pushDoc.setAssessor(assessor);
			pushDoc.setDoc(doc);
			pushDoc.setCreateTime(new Date());
			//
			pushDocDao.saveOrUpdate(pushDoc);
		}
	}
	
	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocStrategy#pushByOrg()
	 */
	@Override
	public void pushByOrg(Cycle cycle) {

	}

}
