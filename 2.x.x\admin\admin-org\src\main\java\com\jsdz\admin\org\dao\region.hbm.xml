<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<!-- 区域类 -->	
	<class name="com.jsdz.admin.org.model.Region" table="T_admin_region">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="regionName" column="regionName"></property>

		<many-to-one name="parentRegion" class="com.jsdz.admin.org.model.Region" fetch="select">
            <column name="parentId"><comment>上级区域编号</comment></column>
        </many-to-one>
         
		<many-to-one name="regionType" cascade="none" class="com.jsdz.admin.org.model.RegionType" fetch="select">
            <column name="regionTypeId"><comment>地区类别编号</comment></column>
        </many-to-one>
        <set name="subRegion" fetch="join" inverse="true" lazy="false">
            <key>
                <column name="parentId">
                <comment>下级区域编号</comment>
                </column>
                <!-- "_parentId" 是指明了在T_region表里有一个_parentId的列名，是指向T_region表的外键   -->
            </key>
            <one-to-many class="com.jsdz.admin.org.model.Region" />
        </set>
        
		<property name="createTime" column="createTime"></property>
	</class>

	<!-- 地区类别 -->
	<class name="com.jsdz.admin.org.model.RegionType" table="T_admin_regiontype">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="name" column="name"></property>
		<property name="level" column="level"></property>
		<property name="description" column="description"></property>
	</class>
	
	<!-- 部门 -->
	<class name="com.jsdz.admin.org.model.Department" table="T_admin_department">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="name" column="name"></property>
		<property name="description" column="description"></property>
		<property name="createTime" column="createTime"></property>
	</class>
	<!-- 职位 -->
	<class name="com.jsdz.admin.org.model.Position" table="T_admin_position">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="position" column="position"></property>
		<property name="description" column="description"></property>
		<property name="createTime" column="createTime"></property>
	</class>

	<!--组织机构类 -->	
	<class name="com.jsdz.admin.org.model.Organization" table="T_admin_organization">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="orgName" column="orgName"></property>
		<property name="orgType" column="orgType"></property>
		<property name="path" column="path"></property>
		<property name="orgCode" column="orgCode"></property>
		<property name="createTime" column="createTime"></property>
		
		<!--区域 -->
		<many-to-one name="region" cascade="none" fetch="select" 
		    class="com.jsdz.admin.org.model.Region" column="regionId">
        </many-to-one>
        <!-- 上级机构 -->
		<many-to-one name="parentOrg" cascade="none" fetch="select" 
            class="com.jsdz.admin.org.model.Organization" column ="parentId">
        </many-to-one>
        <!-- 下级机构 -->
        <set name="subOrg" fetch="join" inverse="true" lazy="false">
            <key>
                <column name="parentId"><comment>下级区域编号</comment></column>
            </key>
            <one-to-many class="com.jsdz.admin.org.model.Organization" />
        </set>
 	</class>
 	
</hibernate-mapping>