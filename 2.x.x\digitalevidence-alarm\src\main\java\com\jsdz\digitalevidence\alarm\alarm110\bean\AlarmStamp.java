package com.jsdz.digitalevidence.alarm.alarm110.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用来保存每次查找的条件
 * <AUTHOR>
 *
 */
public class AlarmStamp {
	
	private Long id ;
	
	// 查找案件的条件
	private Long casId ;
	
	// 查找警情的条件
	private Long alaId ;
	
	// 查找视频的条件
	private Long docId ;
	
	// 查找案件与警情关联保存的条件
	private Long polId;
	
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date stampTime ;
	
	
	
	public AlarmStamp() {
		super();
	}
	
	
	public AlarmStamp(Long id, Long casId, Long alaId, Long docId, Date stampTime) {
		super();
		this.id = id;
		this.casId = casId;
		this.alaId = alaId;
		this.docId = docId;
		this.stampTime = stampTime;
	}


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	public Long getCasId() {
		return casId;
	}

	public void setCasId(Long casId) {
		this.casId = casId;
	}

	public Long getAlaId() {
		return alaId;
	}

	public void setAlaId(Long alaId) {
		this.alaId = alaId;
	}

	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}

	public Date getStampTime() {
		return stampTime;
	}

	public void setStampTime(Date stampTime) {
		this.stampTime = stampTime;
	}


	public Long getPolId() {
		return polId;
	}


	public void setPolId(Long polId) {
		this.polId = polId;
	}
	
}
