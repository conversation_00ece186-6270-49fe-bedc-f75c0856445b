package com.jsdz.digitalevidence.analysis.openapi.dao.impl;

/**
 *
 * @类名: StationLogDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-06-09 16:04:15
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.analysis.openapi.dao.StationLogDao;
import com.jsdz.digitalevidence.analysis.openapi.model.StationLog;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class StationLogDaoImpl extends GenericEntityDaoHibernateImpl<StationLog,Long> implements StationLogDao{

	//新增
	public void addStationLog(StationLog stationLog) {
		this.saveOrUpdate(stationLog);
	}

	//删除
	public void deleteStationLog(StationLog stationLog) {
		this.delete(stationLog);
	}

	//修改
	public void updateStationLog(StationLog stationLog) {
		this.merge(stationLog);
	}

	//按id查询(游离状态)
	public StationLog findStationLogById(Long id){

		final String  hql = "from StationLog s where s.id = :id";
		final Long oid = id;
		StationLog data = getHibernateTemplate().execute(new HibernateCallback<StationLog>() {
			public StationLog doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<StationLog> list = query.list();
				StationLog rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public StationLog locateStationLogById(Long id){

		final String  hql = "from StationLog s where s.id = :id";
		final Long oid = id;
		StationLog data = getHibernateTemplate().execute(new HibernateCallback<StationLog>() {
			public StationLog doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<StationLog> list = query.list();
				StationLog rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public StationLog findStationLogByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		StationLog data = getHibernateTemplate().execute(new HibernateCallback<StationLog>() {
		public StationLog doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<StationLog> list = query.list();
			StationLog rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<StationLog> findAllStationLogs(){
		return this.find("from StationLog stationLog ");
	}

	//列表查询
	public List<StationLog> findStationLogsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<StationLog> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<StationLog> findStationLogsOnPage(Page<StationLog> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<StationLog>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
