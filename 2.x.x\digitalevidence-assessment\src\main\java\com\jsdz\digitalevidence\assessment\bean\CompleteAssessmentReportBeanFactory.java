package com.jsdz.digitalevidence.assessment.bean;

import java.util.ArrayList;
import java.util.Date;

/**\
 * 
 * @类名: CompleteAssessmentReportBeanFactory
 * @说明: 完整报告数据源工厂
 *
 * <AUTHOR>
 * @Date	 2017年11月13日 下午6:03:02
 * 修改记录：
 *
 * @see
 */
public class CompleteAssessmentReportBeanFactory {
	public static ArrayList<CompleteAssessmentScore> getBeanconection(){
		ArrayList<CompleteAssessmentScore> datalist=new ArrayList<>();
		CompleteAssessmentScore com=new CompleteAssessmentScore();
		com.setAge(1);
		com.setComments("tt");
		com.setCountOfAudio(2);
		com.setCountOfAYQPS(4);
		com.setCountOfLog(6);
		com.setCountOfPic(6);
		com.setCountOfShootingAngleA(3);
		com.setCountOfShootingAngleE(4);
		com.setCountOfShootingAngleG(6);
		com.setCountOfShootingContentA(6);
		com.setCountOfShootingContentE(6);
		com.setCountOfShootingContentG(6);
		com.setCountOfShootingResolutionG(7);
		com.setCountOfShootingResolutionH(6);
		com.setCountOfShootingResolutionL(8);
		com.setCountOfVedio(8);
		com.setCountOfWMGF(5);
		com.setCreateTime(new Date());
		com.setDeptName("fff");
		com.setEndDate(new Date());
		com.setLengthOfAudio(6L);
		com.setLengthOfVedio(7L);
		com.setOrgName("gg");
		com.setPoliceCode("565");
		com.setPoliceName("ff");
		com.setPosition("gjhgj");
		com.setSex("男");
		com.setSizeOfAudio(7L);
		com.setSizeOfLog(20L);
		com.setSizeOfPic(30L);
		com.setSizeOfVedio(50L);
		com.setStartDate(new Date());
		com.setSubmitDate(new Date());
		datalist.add(com);
		return datalist;
	}
}
