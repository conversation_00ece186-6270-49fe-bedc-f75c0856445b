<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.jsdz</groupId>
        <artifactId>digitalevidence</artifactId>
        <version>2.4.1</version>
    </parent>
    
	<modules>
        <module>admin-org</module>
        <module>admin-security</module>
    </modules>
	
	<name>admin</name>
	<artifactId>admin</artifactId>
    <packaging>pom</packaging>

    <url>http://maven.apache.org</url>
    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
