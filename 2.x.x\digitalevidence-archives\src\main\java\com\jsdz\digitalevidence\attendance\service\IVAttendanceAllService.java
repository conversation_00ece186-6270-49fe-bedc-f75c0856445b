package com.jsdz.digitalevidence.attendance.service;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.archives.bean.param.DocEnforParam;
import com.jsdz.digitalevidence.archives.model.DocEnfor;
import com.jsdz.digitalevidence.attendance.bean.param.AttendanceAllParam;
import com.jsdz.digitalevidence.attendance.model.AttendanceMonthModel;
import com.jsdz.digitalevidence.attendance.model.VAttendanceAllModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * VIEW 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface IVAttendanceAllService extends IService<VAttendanceAllModel> {

    Page<VAttendanceAllModel> getAttendanceDatas(AttendanceAllParam param, PerLvlBean permLvl);

    Page<AttendanceMonthModel> getAttendanceCountsOfMonth(AttendanceAllParam param, PerLvlBean permLvl);

    Page<DocEnfor> getDocEnfor(DocEnforParam param, PerLvlBean permLvl);

    Map<String, Object> deviceInfoById(Long deviceId);

    Map<String, Object> taskInfoById(Long taskId);

    List<Map> getOperatorByEid(String id);

    void saveAlarmOrg(Long id, Long orgId);

    AjaxResult updateCase(long id, Integer isRelation);
}
