================================================================================
                    档案与文档关联功能开发任务提示词
================================================================================

# 角色定义
你是一个专业的Java后端开发工程师，精通Spring Boot、MyBatis、数据库设计和前端开发。
你需要根据以下需求开发档案与文档关联功能。

# 任务背景
在数字证据管理系统中，需要实现档案(Archives)与文档(Document)之间的多对多关联关系管理。
系统要求采用独立关联表的方式实现，支持完整的CRUD操作和批量处理能力。

================================================================================
# 核心需求
================================================================================

## 1. 数据库设计要求

### 1.1 关联表设计 (t_archives_relation)
```sql
CREATE TABLE t_archives_relation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    archivesId BIGINT NOT NULL COMMENT '档案ID',
    docId BIGINT NOT NULL COMMENT '文档ID', 
    createTime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    relationUserId BIGINT COMMENT '关联操作用户ID',
    relationUserName VARCHAR(100) COMMENT '关联操作用户姓名',
    INDEX idx_archives_id (archivesId),
    INDEX idx_doc_id (docId),
    UNIQUE KEY uk_archives_doc (archivesId, docId)
);
```

### 1.2 查询视图设计 (v_archives_doc)
创建视图整合档案信息、文档信息和关联关系，包含以下字段：
- 档案信息：archivesId, archivesCode, archivesTitle, archivesDescribe
- 文档信息：docId, docName, docSize, contentType, duration, uploadTime
- 关联信息：relationUserId, relationUserName, createTime
- 组织信息：orgCode, orgName

## 2. 后端开发要求

### 2.1 实体类设计
```java
// 关联实体类
public class ArchivesRelation {
    private Long id;
    private Long archivesId;
    private Long docId;
    private Date createTime;
    private Long relationUserId;
    private String relationUserName;
    // getter/setter方法
}

// 查询视图实体类  
public class ArchivesDoc {
    private Long archivesId;
    private String archivesCode;
    private String archivesTitle;
    private Long docId;
    private String docName;
    private String docSize;
    private String duration;
    // 其他字段和getter/setter方法
}
```

### 2.2 数据访问层 (Mapper)
```java
@Mapper
public interface ArchivesRelationMapper {
    
    @Insert("INSERT INTO t_archives_relation (archivesId,docId,relationUserId,relationUserName) VALUES (#{archivesId}, #{docId}, #{relationUserId}, #{relationUserName})")
    void insertRelation(@Param("archivesId") Long archivesId, 
                       @Param("docId") Long docId,
                       @Param("relationUserId") Long relationUserId, 
                       @Param("relationUserName") String relationUserName);
    
    @Delete("DELETE FROM t_archives_relation WHERE id = #{rid}")
    void removeByRid(Long rid);
    
    @Select("SELECT * FROM t_archives_relation WHERE archivesId = #{archivesId} AND docId = #{docId}")
    List<ArchivesRelation> findArchivesDoc(@Param("archivesId") Long archivesId, @Param("docId") String docId);
}

@Mapper  
public interface ArchivesDocMapper {
    @Select("SELECT * FROM v_archives_doc WHERE archivesId = #{archivesId}")
    List<ArchivesDoc> findDocByArchivesId(Long archivesId);
}
```

### 2.3 服务层设计
```java
public interface IArchivesRelationService {
    boolean removeByRid(Long rid);
    List<ArchivesRelation> findArchivesDoc(Long archivesId, String docId);
    boolean saveArchivesRelationsBatch(List<ArchivesRelation> archivesRelations);
}

public interface IArchivesDocService {
    List<ArchivesDoc> findDocByArchivesId(Long archivesId);
}
```

### 2.4 控制器设计
```java
@RestController
@RequestMapping("/archivesDoc")
public class ArchivesDocController {
    
    // 批量添加关联
    @PostMapping("/saveArchivesDocs")
    public AjaxResult saveArchivesDocs(@RequestParam Long archivesId, 
                                      @RequestParam String docIds, 
                                      HttpServletRequest req);
    
    // 删除关联
    @PostMapping("/deleteArchivesDoc") 
    public AjaxResult deleteArchivesDoc(@RequestParam Long rid);
    
    // 查询关联文档
    @GetMapping("/findDocByArchivesId")
    public List<ArchivesDoc> findDocByArchivesId(@RequestParam Long archivesId);
}
```

## 3. 前端开发要求

### 3.1 批量关联功能
```javascript
// 一键归档功能
function releaseArchivesVideo() {
    return new Promise(resolve => {
        $.ajax({
            url: context + "/archivesDoc/saveArchivesDocs",
            type: "post",
            data: {
                "archivesId": archivesId,
                "docIds": docIds  // 逗号分隔的文档ID列表
            },
            success: function(data) {
                if (data.success) {
                    layer.msg("关联成功");
                } else {
                    layer.msg(data.msg);
                }
                resolve(data);
            }
        });
    });
}
```

### 3.2 查询展示功能
```javascript
// 查询关联文档列表
function getdoctable(archivesId) {
    $("#doctable").bootstrapTable({
        url: context + '/archivesDoc/findDocByArchivesId',
        method: 'get',
        queryParams: function(params) {
            return { archivesId: archivesId }
        },
        columns: [
            {field: 'docName', title: '文档名称'},
            {field: 'docSize', title: '文件大小'},
            {field: 'duration', title: '时长'},
            {field: 'uploadTime', title: '上传时间'},
            {field: 'operate', title: '操作', formatter: operateFormatter}
        ]
    });
}
```

### 3.3 删除关联功能
```javascript
// 取消关联
function removerelation(relationId, archivesId) {
    layer.confirm('确定要取消此视频的关联?', {
        icon: 3,
        title: '提示'
    }, function(index) {
        $.ajax({
            url: context + "/archivesDoc/deleteArchivesDoc",
            type: "post",
            data: { "rid": relationId },
            success: function(data) {
                if (data.success) {
                    layer.msg("取消关联成功");
                    getdoctable(archivesId); // 刷新列表
                } else {
                    layer.msg(data.msg);
                }
            }
        });
        layer.close(index);
    });
}
```

================================================================================
# 开发要求与约束
================================================================================

## 技术要求
1. **后端框架**：Spring Boot + MyBatis
2. **数据库**：MySQL 5.7+
3. **前端技术**：jQuery + Bootstrap Table + Layer
4. **返回格式**：统一使用AjaxResult包装返回结果

## 功能要求
1. **批量操作**：支持一次关联多个文档到档案
2. **重复检查**：添加关联前检查是否已存在相同关联
3. **事务处理**：批量操作需要事务保证数据一致性
4. **权限控制**：记录操作用户信息
5. **错误处理**：完善的异常处理和用户提示

## 性能要求
1. **查询优化**：通过视图预聚合数据，减少关联查询
2. **索引设计**：在archivesId和docId字段上建立索引
3. **批量处理**：单次批量操作建议不超过1000条记录

## 数据完整性
1. **外键约束**：确保archivesId和docId的有效性
2. **唯一约束**：同一档案和文档只能有一条关联记录
3. **级联处理**：档案或文档删除时需要处理关联记录

================================================================================
# 测试要求
================================================================================

## 单元测试
1. 测试批量添加关联的各种场景（正常、重复、异常）
2. 测试删除关联功能
3. 测试查询功能的数据完整性

## 集成测试  
1. 测试前后端接口的完整调用链路
2. 测试并发操作的数据一致性
3. 测试大批量数据的处理性能

## 用户验收测试
1. 验证用户界面的易用性
2. 验证业务流程的完整性
3. 验证错误提示的友好性

================================================================================
# 交付物清单
================================================================================

## 后端代码
- [ ] ArchivesRelation.java (实体类)
- [ ] ArchivesDoc.java (视图实体类)
- [ ] ArchivesRelationMapper.java (数据访问层)
- [ ] ArchivesDocMapper.java (查询映射层)
- [ ] IArchivesRelationService.java (服务接口)
- [ ] ArchivesRelationServiceImpl.java (服务实现)
- [ ] IArchivesDocService.java (查询服务接口)
- [ ] ArchivesDocServiceImpl.java (查询服务实现)
- [ ] ArchivesDocController.java (控制器)

## 前端代码
- [ ] 批量关联JavaScript函数
- [ ] 查询展示JavaScript函数  
- [ ] 删除关联JavaScript函数
- [ ] 相关JSP页面修改

## 数据库脚本
- [ ] 关联表创建脚本
- [ ] 查询视图创建脚本
- [ ] 索引创建脚本

## 测试代码
- [ ] 单元测试类
- [ ] 集成测试用例

================================================================================
# 开发优先级
================================================================================

## 第一阶段（核心功能）
1. 数据库表和视图设计
2. 后端基础CRUD功能实现
3. 基本的前端调用功能

## 第二阶段（完善功能）
1. 批量操作优化
2. 错误处理完善
3. 用户界面优化

## 第三阶段（性能优化）
1. 查询性能优化
2. 大数据量处理优化
3. 并发处理优化

================================================================================
# 注意事项
================================================================================

1. **代码规范**：遵循项目现有的代码规范和命名约定
2. **异常处理**：所有数据库操作都要有完善的异常处理
3. **日志记录**：关键操作需要记录详细的操作日志
4. **向后兼容**：确保新功能不影响现有功能
5. **文档更新**：及时更新相关的技术文档和用户手册

开始开发前，请确认你理解了所有需求，如有疑问请及时沟通。
开发过程中遇到技术难点，可以参考现有代码的实现方式。

================================================================================
