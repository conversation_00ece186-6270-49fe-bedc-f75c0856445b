================================================================================
                    档案与媒体关联功能开发任务提示词
================================================================================

# 角色定义
你是一个专业的Golang后端开发工程师，精通Gin框架、GORM、数据库设计和RESTful API开发。
你需要根据以下需求开发档案与媒体关联功能。

# 任务背景
在数字证据管理系统中，需要实现档案(Archives)与媒体(Media)之间的多对多关联关系管理。
系统要求采用独立关联表的方式实现，支持完整的CRUD操作和批量处理能力。

================================================================================
# 核心需求
================================================================================

## 1. 数据库设计要求

### 1.1 关联表设计 (t_archives_media_relation)
```sql
CREATE TABLE t_archives_media_relation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    archives_id BIGINT NOT NULL COMMENT '档案ID',
    media_id BIGINT NOT NULL COMMENT '媒体ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    relation_user_id BIGINT COMMENT '关联操作用户ID',
    relation_user_name VARCHAR(100) COMMENT '关联操作用户姓名',
    INDEX idx_archives_id (archives_id),
    INDEX idx_media_id (media_id),
    UNIQUE KEY uk_archives_media (archives_id, media_id)
);
```

### 1.2 查询视图设计 (v_archives_media)
创建视图整合档案信息、媒体信息和关联关系，包含以下字段：
- 档案信息：archives_id, archives_code, archives_title, archives_describe
- 媒体信息：media_id, media_name, media_size, content_type, duration, upload_time
- 关联信息：relation_user_id, relation_user_name, create_time
- 组织信息：org_code, org_name

## 2. 后端开发要求

### 2.1 结构体设计
```go
// 关联实体结构体
type ArchivesMediaRelation struct {
    ID               uint      `gorm:"primaryKey;autoIncrement" json:"id"`
    ArchivesID       uint      `gorm:"column:archives_id;not null" json:"archives_id"`
    MediaID          uint      `gorm:"column:media_id;not null" json:"media_id"`
    CreateTime       time.Time `gorm:"column:create_time;autoCreateTime" json:"create_time"`
    RelationUserID   uint      `gorm:"column:relation_user_id" json:"relation_user_id"`
    RelationUserName string    `gorm:"column:relation_user_name;size:100" json:"relation_user_name"`
}

func (ArchivesMediaRelation) TableName() string {
    return "t_archives_media_relation"
}

// 查询视图结构体
type ArchivesMedia struct {
    ID                 uint      `json:"id"`
    ArchivesID         uint      `json:"archives_id"`
    ArchivesCode       string    `json:"archives_code"`
    ArchivesTitle      string    `json:"archives_title"`
    ArchivesDescribe   string    `json:"archives_describe"`
    MediaID            uint      `json:"media_id"`
    MediaName          string    `json:"media_name"`
    MediaSize          string    `json:"media_size"`
    ContentType        string    `json:"content_type"`
    Duration           string    `json:"duration"`
    UploadTime         time.Time `json:"upload_time"`
    RelationUserID     uint      `json:"relation_user_id"`
    RelationUserName   string    `json:"relation_user_name"`
    CreateTime         time.Time `json:"create_time"`
    OrgCode            string    `json:"org_code"`
    OrgName            string    `json:"org_name"`
}

// 请求结构体
type BatchRelationRequest struct {
    ArchivesID uint   `json:"archives_id" binding:"required"`
    MediaIDs   []uint `json:"media_ids" binding:"required"`
}

type DeleteRelationRequest struct {
    RelationID uint `json:"relation_id" binding:"required"`
}
```

### 2.2 数据访问层 (Repository)
```go
type ArchivesMediaRepository interface {
    CreateRelation(relation *ArchivesMediaRelation) error
    CreateRelationsBatch(relations []ArchivesMediaRelation) error
    DeleteRelationByID(id uint) error
    FindRelationByArchivesAndMedia(archivesID, mediaID uint) (*ArchivesMediaRelation, error)
    FindMediaByArchivesID(archivesID uint) ([]ArchivesMedia, error)
    CheckRelationExists(archivesID, mediaID uint) bool
}

type archivesMediaRepository struct {
    db *gorm.DB
}

func NewArchivesMediaRepository(db *gorm.DB) ArchivesMediaRepository {
    return &archivesMediaRepository{db: db}
}

func (r *archivesMediaRepository) CreateRelation(relation *ArchivesMediaRelation) error {
    return r.db.Create(relation).Error
}

func (r *archivesMediaRepository) CreateRelationsBatch(relations []ArchivesMediaRelation) error {
    return r.db.CreateInBatches(relations, 100).Error
}

func (r *archivesMediaRepository) DeleteRelationByID(id uint) error {
    return r.db.Delete(&ArchivesMediaRelation{}, id).Error
}

func (r *archivesMediaRepository) FindRelationByArchivesAndMedia(archivesID, mediaID uint) (*ArchivesMediaRelation, error) {
    var relation ArchivesMediaRelation
    err := r.db.Where("archives_id = ? AND media_id = ?", archivesID, mediaID).First(&relation).Error
    if err != nil {
        return nil, err
    }
    return &relation, nil
}

func (r *archivesMediaRepository) FindMediaByArchivesID(archivesID uint) ([]ArchivesMedia, error) {
    var medias []ArchivesMedia
    err := r.db.Table("v_archives_media").Where("archives_id = ?", archivesID).Find(&medias).Error
    return medias, err
}

func (r *archivesMediaRepository) CheckRelationExists(archivesID, mediaID uint) bool {
    var count int64
    r.db.Model(&ArchivesMediaRelation{}).Where("archives_id = ? AND media_id = ?", archivesID, mediaID).Count(&count)
    return count > 0
}
```

### 2.3 服务层设计
```go
type ArchivesMediaService interface {
    CreateRelationsBatch(req *BatchRelationRequest, userID uint, userName string) error
    DeleteRelation(relationID uint) error
    GetMediaByArchivesID(archivesID uint) ([]ArchivesMedia, error)
}

type archivesMediaService struct {
    repo ArchivesMediaRepository
}

func NewArchivesMediaService(repo ArchivesMediaRepository) ArchivesMediaService {
    return &archivesMediaService{repo: repo}
}

func (s *archivesMediaService) CreateRelationsBatch(req *BatchRelationRequest, userID uint, userName string) error {
    var relations []ArchivesMediaRelation

    for _, mediaID := range req.MediaIDs {
        // 检查是否已存在关联
        if s.repo.CheckRelationExists(req.ArchivesID, mediaID) {
            continue // 跳过已存在的关联
        }

        relation := ArchivesMediaRelation{
            ArchivesID:       req.ArchivesID,
            MediaID:          mediaID,
            RelationUserID:   userID,
            RelationUserName: userName,
        }
        relations = append(relations, relation)
    }

    if len(relations) == 0 {
        return errors.New("所有媒体文件已关联，无需重复关联")
    }

    return s.repo.CreateRelationsBatch(relations)
}

func (s *archivesMediaService) DeleteRelation(relationID uint) error {
    return s.repo.DeleteRelationByID(relationID)
}

func (s *archivesMediaService) GetMediaByArchivesID(archivesID uint) ([]ArchivesMedia, error) {
    return s.repo.FindMediaByArchivesID(archivesID)
}
```

### 2.4 控制器设计
```go
type ArchivesMediaController struct {
    service ArchivesMediaService
}

func NewArchivesMediaController(service ArchivesMediaService) *ArchivesMediaController {
    return &ArchivesMediaController{service: service}
}

// 批量添加关联
func (c *ArchivesMediaController) CreateRelationsBatch(ctx *gin.Context) {
    var req BatchRelationRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        ctx.JSON(http.StatusBadRequest, gin.H{
            "success": false,
            "message": "请求参数错误: " + err.Error(),
        })
        return
    }

    // 从上下文获取用户信息
    userID := ctx.GetUint("user_id")
    userName := ctx.GetString("user_name")

    if err := c.service.CreateRelationsBatch(&req, userID, userName); err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{
            "success": false,
            "message": err.Error(),
        })
        return
    }

    ctx.JSON(http.StatusOK, gin.H{
        "success": true,
        "message": "关联成功",
    })
}

// 删除关联
func (c *ArchivesMediaController) DeleteRelation(ctx *gin.Context) {
    var req DeleteRelationRequest
    if err := ctx.ShouldBindJSON(&req); err != nil {
        ctx.JSON(http.StatusBadRequest, gin.H{
            "success": false,
            "message": "请求参数错误: " + err.Error(),
        })
        return
    }

    if err := c.service.DeleteRelation(req.RelationID); err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{
            "success": false,
            "message": "删除关联失败: " + err.Error(),
        })
        return
    }

    ctx.JSON(http.StatusOK, gin.H{
        "success": true,
        "message": "删除关联成功",
    })
}

// 查询关联媒体
func (c *ArchivesMediaController) GetMediaByArchivesID(ctx *gin.Context) {
    archivesIDStr := ctx.Query("archives_id")
    archivesID, err := strconv.ParseUint(archivesIDStr, 10, 32)
    if err != nil {
        ctx.JSON(http.StatusBadRequest, gin.H{
            "success": false,
            "message": "档案ID参数错误",
        })
        return
    }

    medias, err := c.service.GetMediaByArchivesID(uint(archivesID))
    if err != nil {
        ctx.JSON(http.StatusInternalServerError, gin.H{
            "success": false,
            "message": "查询失败: " + err.Error(),
        })
        return
    }

    ctx.JSON(http.StatusOK, gin.H{
        "success": true,
        "data":    medias,
    })
}

// 路由注册
func (c *ArchivesMediaController) RegisterRoutes(router *gin.RouterGroup) {
    archivesMedia := router.Group("/archives-media")
    {
        archivesMedia.POST("/batch", c.CreateRelationsBatch)
        archivesMedia.DELETE("/relation", c.DeleteRelation)
        archivesMedia.GET("/list", c.GetMediaByArchivesID)
    }
}
```

## 3. 前端开发要求

### 3.1 批量关联功能
```javascript
// 一键归档功能
function releaseArchivesMedia() {
    return new Promise(resolve => {
        const requestData = {
            archives_id: parseInt(archivesId),
            media_ids: mediaIds  // 媒体ID数组
        };

        $.ajax({
            url: context + "/api/v1/archives-media/batch",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(requestData),
            success: function(data) {
                if (data.success) {
                    layer.msg("关联成功");
                } else {
                    layer.msg(data.message);
                }
                resolve(data);
            },
            error: function(xhr, status, error) {
                layer.msg("请求失败: " + error);
                resolve({success: false});
            }
        });
    });
}
```

### 3.2 查询展示功能
```javascript
// 查询关联媒体列表
function getMediaTable(archivesId) {
    $("#mediaTable").bootstrapTable({
        url: context + '/api/v1/archives-media/list',
        method: 'get',
        queryParams: function(params) {
            return { archives_id: archivesId }
        },
        responseHandler: function(res) {
            if (res.success) {
                return {
                    total: res.data.length,
                    rows: res.data
                };
            } else {
                layer.msg(res.message);
                return {total: 0, rows: []};
            }
        },
        columns: [
            {field: 'media_name', title: '媒体名称'},
            {field: 'media_size', title: '文件大小'},
            {field: 'duration', title: '时长'},
            {field: 'upload_time', title: '上传时间', formatter: dateFormatter},
            {field: 'content_type', title: '媒体类型'},
            {field: 'operate', title: '操作', formatter: operateFormatter}
        ]
    });
}

// 日期格式化
function dateFormatter(value, row, index) {
    if (value) {
        return new Date(value).toLocaleString();
    }
    return '-';
}
```

### 3.3 删除关联功能
```javascript
// 取消关联
function removeRelation(relationId, archivesId) {
    layer.confirm('确定要取消此媒体的关联?', {
        icon: 3,
        title: '提示'
    }, function(index) {
        $.ajax({
            url: context + "/api/v1/archives-media/relation",
            type: "DELETE",
            contentType: "application/json",
            data: JSON.stringify({
                relation_id: parseInt(relationId)
            }),
            success: function(data) {
                if (data.success) {
                    layer.msg("取消关联成功");
                    getMediaTable(archivesId); // 刷新列表
                } else {
                    layer.msg(data.message);
                }
            },
            error: function(xhr, status, error) {
                layer.msg("删除失败: " + error);
            }
        });
        layer.close(index);
    });
}
```

================================================================================
# 开发要求与约束
================================================================================

## 技术要求
1. **后端框架**：Gin + GORM
2. **数据库**：MySQL 8.0+
3. **前端技术**：jQuery + Bootstrap Table + Layer
4. **返回格式**：统一使用JSON格式，包含success、message、data字段
5. **Go版本**：Go 1.19+
6. **依赖管理**：使用Go Modules

## 功能要求
1. **批量操作**：支持一次关联多个媒体到档案
2. **重复检查**：添加关联前检查是否已存在相同关联
3. **事务处理**：批量操作需要事务保证数据一致性
4. **权限控制**：记录操作用户信息
5. **错误处理**：完善的异常处理和用户提示
6. **参数验证**：使用binding标签进行请求参数验证

## 性能要求
1. **查询优化**：通过视图预聚合数据，减少关联查询
2. **索引设计**：在archives_id和media_id字段上建立索引
3. **批量处理**：单次批量操作建议不超过1000条记录，使用GORM的CreateInBatches
4. **连接池**：合理配置数据库连接池参数

## 数据完整性
1. **外键约束**：确保archives_id和media_id的有效性
2. **唯一约束**：同一档案和媒体只能有一条关联记录
3. **级联处理**：档案或媒体删除时需要处理关联记录
4. **软删除**：考虑使用GORM的软删除功能

================================================================================
# 测试要求
================================================================================

## 单元测试
1. 使用testify框架编写单元测试
2. 测试批量添加关联的各种场景（正常、重复、异常）
3. 测试删除关联功能
4. 测试查询功能的数据完整性
5. 使用mock进行数据库操作测试

## 集成测试
1. 测试前后端接口的完整调用链路
2. 测试并发操作的数据一致性
3. 测试大批量数据的处理性能
4. 使用Docker容器进行集成测试

## 用户验收测试
1. 验证用户界面的易用性
2. 验证业务流程的完整性
3. 验证错误提示的友好性

================================================================================
# 交付物清单
================================================================================

## 后端代码
- [ ] models/archives_media_relation.go (关联实体结构体)
- [ ] models/archives_media.go (查询视图结构体)
- [ ] repositories/archives_media_repository.go (数据访问层)
- [ ] services/archives_media_service.go (服务层)
- [ ] controllers/archives_media_controller.go (控制器)
- [ ] routes/archives_media_routes.go (路由注册)
- [ ] dto/archives_media_dto.go (请求响应结构体)

## 前端代码
- [ ] 批量关联JavaScript函数
- [ ] 查询展示JavaScript函数
- [ ] 删除关联JavaScript函数
- [ ] 相关HTML页面修改

## 数据库脚本
- [ ] 关联表创建脚本
- [ ] 查询视图创建脚本
- [ ] 索引创建脚本

## 测试代码
- [ ] repositories/archives_media_repository_test.go
- [ ] services/archives_media_service_test.go
- [ ] controllers/archives_media_controller_test.go

## 配置文件
- [ ] go.mod (依赖管理)
- [ ] config/database.go (数据库配置)
- [ ] middleware/auth.go (认证中间件)

================================================================================
# 开发优先级
================================================================================

## 第一阶段（核心功能）
1. 数据库表和视图设计
2. Go项目结构搭建和依赖管理
3. 后端基础CRUD功能实现
4. 基本的前端调用功能

## 第二阶段（完善功能）
1. 批量操作优化
2. 错误处理完善
3. 用户界面优化
4. 中间件集成（认证、日志等）

## 第三阶段（性能优化）
1. 查询性能优化
2. 大数据量处理优化
3. 并发处理优化
4. 缓存机制集成

================================================================================
# Go项目结构建议
================================================================================

```
project/
├── cmd/
│   └── server/
│       └── main.go
├── internal/
│   ├── controllers/
│   ├── services/
│   ├── repositories/
│   ├── models/
│   ├── dto/
│   └── middleware/
├── pkg/
│   ├── database/
│   ├── logger/
│   └── utils/
├── configs/
├── migrations/
├── tests/
├── go.mod
├── go.sum
└── README.md
```

================================================================================
# 注意事项
================================================================================

1. **代码规范**：遵循Go官方代码规范，使用gofmt和golint
2. **异常处理**：所有数据库操作都要有完善的错误处理
3. **日志记录**：使用结构化日志记录关键操作
4. **向后兼容**：确保新功能不影响现有功能
5. **文档更新**：及时更新相关的技术文档和用户手册
6. **依赖管理**：合理管理Go模块依赖，避免版本冲突
7. **接口设计**：遵循RESTful API设计原则
8. **数据验证**：使用validator包进行请求参数验证
9. **安全考虑**：实现适当的认证和授权机制
10. **性能监控**：集成性能监控和指标收集

## Go特定注意事项
1. **错误处理**：遵循Go的错误处理惯例，不要忽略错误
2. **并发安全**：注意goroutine安全，合理使用mutex
3. **内存管理**：避免内存泄漏，合理使用指针
4. **接口设计**：优先使用小接口，遵循接口隔离原则

开始开发前，请确认你理解了所有需求，如有疑问请及时沟通。
开发过程中遇到技术难点，可以参考Go社区的最佳实践。

================================================================================
