package com.jsdz.admin.security.dao;

/**
 *
 * @类名: SysConfigDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-16 20:48:56
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.SysConfig;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface SysConfigDao extends GenericORMEntityDAO<SysConfig,Long> {

	//新增
	public void addSysConfig(SysConfig sysConfig);

	//修改
	public void updateSysConfig(SysConfig sysConfig);

	//删除
	public void deleteSysConfig(SysConfig sysConfig);

	//按id查询,结果是游离状态的数据
	public SysConfig findSysConfigById(Long id);

	//按id查询
	public SysConfig locateSysConfigById(Long id);

	//单个查询
	public SysConfig findSysConfigByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<SysConfig> findAllSysConfigs();

	//列表查询
	public List<SysConfig> findSysConfigsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<SysConfig> findSysConfigsOnPage(Page<SysConfig> page,String queryStr,String[] paramNames,Object[] values);

}

