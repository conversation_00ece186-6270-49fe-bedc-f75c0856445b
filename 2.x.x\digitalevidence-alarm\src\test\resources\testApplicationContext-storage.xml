<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
        http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
	<!-- 存储服务类型映射器 -->
	<bean id="fileLoadType"
		class="com.jsdz.digitalevidence.storage.handler.FileStorageHandlerRegister">
		<property name="handlers">
			<map>
				<entry key="smb">
					<ref bean="smbFileUploadHandler" />
				</entry>
				<entry key="http">
					<ref bean="httpFileUploadHandler" />
				</entry>

				<entry key="ftp">
					<ref bean="ftpFileUploadHandler" />
				</entry>
				<entry key="file">
					<ref bean="nativeFileUploadHandler" />
				</entry>
			</map>
		</property>
	</bean>
</beans>
