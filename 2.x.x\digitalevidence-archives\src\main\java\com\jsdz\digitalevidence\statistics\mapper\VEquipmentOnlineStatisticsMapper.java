package com.jsdz.digitalevidence.statistics.mapper;

import com.jsdz.digitalevidence.statistics.bean.param.VEquipmentOnlineStatisticsParam;
import com.jsdz.digitalevidence.statistics.model.VEquipmentOnlineStatisticsModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * VIEW Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface VEquipmentOnlineStatisticsMapper extends BaseMapper<VEquipmentOnlineStatisticsModel> {

    List<VEquipmentOnlineStatisticsModel> selectModels(@Param("param")VEquipmentOnlineStatisticsParam param);

    Integer selectModelCount(@Param("param")VEquipmentOnlineStatisticsParam param);
}
