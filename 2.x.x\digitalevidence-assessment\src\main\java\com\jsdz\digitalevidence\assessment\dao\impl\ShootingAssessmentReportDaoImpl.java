/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.dao.impl;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReportBean;
import com.jsdz.digitalevidence.assessment.dao.ShootingAssessmentReportDao;
import com.jsdz.digitalevidence.assessment.model.ReportType;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.ShootingSummary;

/**
 * @类名: ShootingAssessmentDaoImpl
 * @说明: 拍摄考核dao实现
 *
 * <AUTHOR>
 * @Date	 2017年8月14日 下午4:07:05
 * 修改记录：
 *
 * @see 	 
 */
@Repository
public class ShootingAssessmentReportDaoImpl extends GenericEntityDaoHibernateImpl<ShootingAssessmentReport,Long> 
					implements ShootingAssessmentReportDao {

	@Override
	public boolean isReportFrozen(Long reportId) {
		// TODO Auto-generated method stub
		return false;
	}
	
	@Override
	public ShootingAssessmentReport getShootingAssessmentReport(
			Long policeId,
			Date cycleDate) {
		String hql = "from ShootingAssessmentReport r "
				+ "where r.police.id = ? "
			    + "and r.startDate <= ? "
				+ "and r.endDate >= ? "
				+ "and r.type = ?";
		//
		List<ShootingAssessmentReport> reports = this.findByParam(hql, 
				new Object[]{policeId, cycleDate, cycleDate, ReportType.Monthly});
		if(reports!=null&&reports.size()>0)
			return reports.get(0);
		return null;
		
	}

	@Override
	public Page<ShootingAssessmentReport> queryShootingAssessmentReport(
			Page<ShootingAssessmentReport> page, Long policeId,
			Date startDate, Date endDate) {
		String hql = "from ShootingAssessmentReport r "
				+ "where r.police.id = :policeId "
			    + "and r.startDate >= :startDate "
				+ "and r.endDate < :endDate "
			    + "order by r.submitDate";
		// 总页数
		String totalPageHql = " select count(*) from ShootingAssessmentReport r "
				+ "where r.police.id = :policeId "
			    + "and r.startDate >= :startDate "
				+ "and r.endDate < :endDate "
			    + "order by r.submitDate";
		page.setTotalQueryString(totalPageHql);
		
		return this.pageQueryHQL(page, hql, new String[]{"policeId","startDate","endDate"}, 
				new Object[]{policeId, startDate, endDate});
		
	}

	@Override
	public CompleteAssessmentReportBean getShootingAssessmentReport(Long reportId) {
		//
		return getHibernateTemplate().execute(new HibernateCallback<CompleteAssessmentReportBean>() {
			@Override
			public CompleteAssessmentReportBean doInHibernate(Session session) throws HibernateException, SQLException {
				// 获取查询对象
				SQLQuery queryObject = (SQLQuery)session.getNamedQuery("getCompleteShootingAccessmentReport");
					
				queryObject.addEntity("a", CompleteAssessmentReportBean.class);
				/*queryObject.addJoin("a1", "a.sumOfShooting");
				queryObject.addJoin("a2", "a.sumOfShootingAssessment");*/
				queryObject.addEntity("a1", "shootingSumBean");
				queryObject.addEntity("a2", "shootingAssessmentSumBean");
				// 置入参数
				applyNamedParameterToQuery(queryObject, "reportId", reportId);
				//
				Object obj = queryObject.uniqueResult();
				Object[] objs = (Object[])obj;
				CompleteAssessmentReportBean reportBean = (CompleteAssessmentReportBean) objs[0];
				ShootingSummary sumOfShooting = (ShootingSummary) objs[1];
				ShootingAssessmentSummary sumOfShootingAssessment = (ShootingAssessmentSummary) objs[2];
				reportBean.setSumOfShooting(sumOfShooting);
				reportBean.setSumOfShootingAssessment(sumOfShootingAssessment);
				return reportBean;
			}
		});
	} 

}
