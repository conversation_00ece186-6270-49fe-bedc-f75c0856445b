package com.jsdz.admin.security.utils;

import java.util.List;
import java.util.Map;

import com.jsdz.admin.security.model.Action;
import com.jsdz.digitalevidence.cache.utils.Constant;
import com.jsdz.digitalevidence.cache.utils.SysSource;

/**
 * 
 * @类名: PermissionHelper
 * @说明: 权限判断类, 根据Tag找到index
 *
 * @author: kenny
 * @Date	2017年4月28日上午8:45:05
 * 修改记录：
 *
 * @see
 */

public class PermissionHelper {
	private static int getPermissionIndex(String permissionTag){
		int index = 0;
		@SuppressWarnings("unchecked")
		Map<Integer,Object> map = (Map<Integer,Object>)SysSource.getInstance().get(Constant.VAR_ACTION);
		Integer[] indexList = map.keySet().toArray(new Integer[0]);
		for (Integer idx : indexList){
			Action action = (Action)map.get(idx);
			if (null != action && action.getTag().equals(permissionTag)){
				index = idx;
				break;
			}
		}
		return index;
	}
	
	private static boolean comparePermission(int index, String rc){
			if (index==0){
				return false;
			}
			index --;
			//System.out.println("*** index:" + index + "/rc:" + rc);
			char value = rc.charAt(index);
			//System.out.println("*** value:" + value);
			if(value == '1'){
				return true;
			}
			return false;
	}
	
		/**
		 * @说明：判断操作员是否有权限
		 *
		 * <AUTHOR>
		 * @param tag
		 * @param rc
		 * @return
		 *
		 */
	public static boolean checkPermission(String tag, String rc){
		if(rc==null || "".equals(rc)){
			return false;
		}
		if(tag==null || "".equals(tag)){
			return false;
		}
		return comparePermission(getPermissionIndex(tag),rc);
		
	}
	

		/**
		 * @说明：创建权限字符串
		 *
		 * <AUTHOR>
		 * @param indexs 有权限的项,比如 1,3,6,11,20
		 * @return 权限字符串, 比如0101001001000000000
		 * 2017年4月28日 下午3:12:59
		 */
	public static String makePermissionContent(String indexs){
		StringBuilder sb = new StringBuilder(Constant.PERMISSION_RAW);
		String[] indexArray = indexs.split(",");
		for(String index: indexArray){
			if(null == index || "".equals(index)){
				continue;
			}
			int ak = Integer.parseInt(index);
			if (ak > 0) ak --;
			sb.setCharAt(ak, '1');
		}
		
		return sb.toString();
	}	
}
