package com.jsdz.admin.security.dao;

/**
 *
 * @类名: RsaKeyDao
 * @说明:
 * @author: kenny
 * @Date 2017-06-02 11:38:34
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.RsaKey;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface RsaKeyDao extends GenericORMEntityDAO<RsaKey,Long> {

	//新增
	public void addRsaKey(RsaKey rsaKey);

	//修改
	public void updateRsaKey(RsaKey rsaKey);

	//删除
	public void deleteRsaKey(RsaKey rsaKey);

	//按id查询,结果是游离状态的数据
	public RsaKey findRsaKeyById(Long id);

	//按id查询
	public RsaKey locateRsaKeyById(Long id);

	//单个查询
	public RsaKey findRsaKeyByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<RsaKey> findAllRsaKeys();

	//列表查询
	public List<RsaKey> findRsaKeysByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RsaKey> findRsaKeysOnPage(Page<RsaKey> page,String queryStr,String[] paramNames,Object[] values);

}

