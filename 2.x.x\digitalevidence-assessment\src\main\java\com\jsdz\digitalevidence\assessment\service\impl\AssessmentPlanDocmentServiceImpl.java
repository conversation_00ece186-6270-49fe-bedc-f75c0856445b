package com.jsdz.digitalevidence.assessment.service.impl;

import java.util.Date;

/**
 * 
 * @类名: AssessmentPlanDocmentServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-06-19 16:05:25
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;

import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanDao;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanDocmentDao;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlan;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanDocment;
import com.jsdz.digitalevidence.assessment.service.AssessmentPlanDocmentService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

@Service("AssessmentPlanDocmentServiceImpl")
public class AssessmentPlanDocmentServiceImpl implements AssessmentPlanDocmentService {

	@Autowired
	private AssessmentPlanDocmentDao assessmentPlanDocmentDao;
	@Autowired
	private AssessmentPlanDao assessmentPlanDao;

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			String queryStr = " from AssessmentPlanDocment d "
					+ " where d.planId = :planId and d.doc.id = :docId ";
			
			List<AssessmentPlanDocment> list = 
			assessmentPlanDocmentDao.findAssessmentPlanDocmentsByCondition(queryStr,
					new String[]{"planId","docId"},
					new Object[]{assessmentPlanDocment.getId(),assessmentPlanDocment.getDoc().getId()});
			
			if (list != null && list.size() > 0){
				result.setMsg("重复的数据，不能保存");
				return result;
			}
			if (assessmentPlanDocment.getAlloted() == null){
				assessmentPlanDocment.setAlloted(0);
			}
			assessmentPlanDocmentDao.addAssessmentPlanDocment(assessmentPlanDocment);
			
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("新增保存成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updateAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			assessmentPlanDocmentDao.updateAssessmentPlanDocment(assessmentPlanDocment);
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("修改成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deleteAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			assessmentPlanDocment = assessmentPlanDocmentDao.locateAssessmentPlanDocmentById(assessmentPlanDocment.getId());
			if (assessmentPlanDocment == null){
				result.setMsg("不存在数据，不能删除");
				return result;
			}
			
			AssessmentPlan assessmentPlan = assessmentPlanDao.findAssessmentPlanById(assessmentPlanDocment.getPlanId());
			if (assessmentPlan.getStatus() != 0){
				result.setMsg("方案不是初始状态，不能删除");
				return result;
				
			}
			
			assessmentPlanDocmentDao.deleteAssessmentPlanDocment(assessmentPlanDocment);
			result.setSuccess(true);
			result.setCode(200);
			result.setMsg("删除成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 按id查询(游离数据)
	 */ 
 	public AssessmentPlanDocment findAssessmentPlanDocmentById(Long id){

		return assessmentPlanDocmentDao.findAssessmentPlanDocmentById(id);
	}

	/** 
 	 * 按 id 查询
	 */ 
 	public AssessmentPlanDocment locateAssessmentPlanDocmentById(Long id) {
		return assessmentPlanDocmentDao.locateAssessmentPlanDocmentById(id);
	}

	/** 
 	 * 单个查询
	 */ 
 	public AssessmentPlanDocment findAssessmentPlanDocmentByParam(String queryStr, String[] paramNames, Object[] values) {
		return assessmentPlanDocmentDao.findAssessmentPlanDocmentByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanDocment> findAllAssessmentPlanDocments() {
		return assessmentPlanDocmentDao.findAllAssessmentPlanDocments();
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanDocment> findAssessmentPlanDocmentsByParam(String queryStr, String[] paramNames, Object[] values) {
		return assessmentPlanDocmentDao.findAssessmentPlanDocmentsByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanDocment> findAssessmentPlanDocmentsOnPage(Page<AssessmentPlanDocment> page, String queryStr, String[] paramNames, Object[] values) {
		Page<AssessmentPlanDocment> pos = assessmentPlanDocmentDao.findAssessmentPlanDocmentsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

 	public List<Object> getRadomDoc(Date time1,Date time2,Integer num){
 		return assessmentPlanDocmentDao.getRadomDoc(time1,time2,num);
 	}
}
