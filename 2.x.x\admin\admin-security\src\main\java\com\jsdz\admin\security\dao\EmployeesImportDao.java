package com.jsdz.admin.security.dao;

/**
 *
 * @类名: EmployeesImportDao
 * @说明:
 * @author: kenny
 * @Date 2017-11-04 18:25:26
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.EmployeesImport;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface EmployeesImportDao extends GenericORMEntityDAO<EmployeesImport,Long> {

	//新增
	public void addEmployeesImport(EmployeesImport employeesImport);

	//修改
	public void updateEmployeesImport(EmployeesImport employeesImport);

	//删除
	public void deleteEmployeesImport(EmployeesImport employeesImport);

	//按id查询,结果是游离状态的数据
	public EmployeesImport findEmployeesImportById(Long id);

	//按id查询
	public EmployeesImport locateEmployeesImportById(Long id);

	//单个查询
	public EmployeesImport findEmployeesImportByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<EmployeesImport> findAllEmployeesImports();

	//列表查询
	public List<EmployeesImport> findEmployeesImportsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<EmployeesImport> findEmployeesImportsOnPage(Page<EmployeesImport> page,String queryStr,String[] paramNames,Object[] values);

	//执行简单的Hql
	public int execSimpleHql(String execHql,String[] paramNames, Object[] values);
}

