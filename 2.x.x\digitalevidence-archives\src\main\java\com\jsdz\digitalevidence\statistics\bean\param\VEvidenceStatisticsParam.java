package com.jsdz.digitalevidence.statistics.bean.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.model.SysAbnormalStatisticsModel;
import com.jsdz.digitalevidence.statistics.model.VEvidenceStatisticsModel;
import com.jsdz.utils.DateTimeUtils;

import java.util.Date;

public class VEvidenceStatisticsParam {

    Page<VEvidenceStatisticsModel> page;

    private String orgPath;

    private String sortName;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date uploadTimeStart;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date uploadTimeEnd;

    private String sortOrder;
    //是否包含下级 （0不，1包含）
    private String includeSub;



    public VEvidenceStatisticsParam(){
        page = new Page<VEvidenceStatisticsModel>();
        page.setOffset(0);
        page.setPageSize(20);
    }

    public Date getUploadTimeStart() {
        return uploadTimeStart;
    }

    public void setUploadTimeStart(Date uploadTimeStart) {
        this.uploadTimeStart = uploadTimeStart;
    }

    public Date getUploadTimeEnd() {
        return uploadTimeEnd;
    }

    public void setUploadTimeEnd(Date uploadTimeEnd) {
        this.uploadTimeEnd = uploadTimeEnd;
    }

    public Page<VEvidenceStatisticsModel> getPage() {
        return page;
    }

    public void setPage(Page<VEvidenceStatisticsModel> page) {
        this.page = page;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public String getSortName() {
        return sortName;
    }

    public void setSortName(String sortName) {
        this.sortName = sortName;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(String includeSub) {
        this.includeSub = includeSub;
    }
}
