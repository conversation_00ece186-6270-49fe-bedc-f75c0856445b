package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmStamp;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmInfoDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmPoliceDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmRelationDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmStampDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmPolice;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;
import com.jsdz.digitalevidence.alarm.alarm110.service.AlarmRelevanceService;
import com.jsdz.digitalevidence.cache.utils.SysSource;
import com.jsdz.digitalevidence.document.dao.DocumentDao;
import com.jsdz.digitalevidence.document.model.Document;

/**
 * 警情和视频进行关联
 * 
 * <AUTHOR>
 *
 */
@Service("AlarmRelevanceServiceImpl")
public class AlarmRelevanceServiceImpl implements AlarmRelevanceService {

	private final Logger logger = Logger.getLogger(this.getClass());

	@Autowired
	private AlarmInfoDao alarmInfoDao;

	@Autowired
	private DocumentDao documentDao;

	@Autowired
	private AlarmRelationDao alarmRelationDao;

	@Autowired
	private AlarmStampDao alarmStampDao;

	@Autowired
	private AlarmPoliceDao alarmPoliceDao;


	// 警情和视频自动关联
	public void alarmBouVid() {
		// 计算开始时间
		System.out.println("*** 开始：警情与视频自动关联==========================");

		// 保存的条件
		String stamp = " from AlarmStamp  ala order by ala.stampTime asc";
		AlarmStamp alarmStamp01 = alarmStampDao.findAlarmStamp(stamp);

		if (alarmStamp01 == null || alarmStamp01.getAlaId() == null || alarmStamp01.getDocId() == null) {
			alarmStamp01 = new AlarmStamp(null, 1l, 1l, 1l, null);
		}

		// 查询带条件的视频信息
		String queryStr01 = "from Document doc left join fetch doc.police emp "
				+ "where doc.id > :docId and doc.cate <> 3 ";
		String[] paramNames01 = new String[] { "docId" };
		Object[] values01 = new Object[] { alarmStamp01.getDocId() };
		List<Document> documents = documentDao.findDocumentsByCondition(queryStr01, paramNames01, values01);

		// 查询带条件的警情信息
		String queryStr02 = "from AlarmPolice ap left join fetch ap.alarmInfo ai "
				+ "left join fetch ap.police where  ai.id > :alaId order by ai.alarmTime asc ";
		String[] paramNames02 = new String[] { "alaId" };
		Object[] values02 = new Object[] { alarmStamp01.getAlaId() };
		List<AlarmPolice> alarmPolices = alarmPoliceDao.findAlarmPolicesByCondition(queryStr02, paramNames02, values02);

		List<AlarmRelation> list = new ArrayList<>();
		AlarmRelation alarmRelation = null;
		AlarmInfo alarmInfo = null;
		for (Document document : documents) {
			for (AlarmPolice alarmPolice : alarmPolices) {
				if (document.getPolice() != null && alarmPolice.getPolice() != null) {
					// 判断视频信息中的工号和警情信息中的工号是否相等
					if ((document.getPolice().getWorkNumber()).equals(alarmPolice.getPolice().getWorkNumber())) {
						if (alarmPolice.getAlarmInfo().getAlarmTime() != null) {
							// 用来判断警情创建时间比视频创建时间早
							int time1 = (document.getCreateTime()).compareTo(alarmPolice.getAlarmInfo().getAlarmTime());
							if (time1 > 0) {
								alarmInfo = alarmPolice.getAlarmInfo();
							} else {
								break;
							}
						}
					}
				}
			}
			// 用来保存AlarmRelation 信息
			if (alarmInfo != null) {
				alarmRelation = new AlarmRelation();
				alarmRelation.setMediaInfo(document);
				alarmRelation.setAlarmInfo(alarmInfo);
				alarmRelation.setRelationTime(new Date());

				// 设置警情信息中的已关联
				alarmInfo.setIsRelation(1);
				list.add(alarmRelation);
				alarmInfo = null;

			}
		}
		// 插入关联数据
		for (AlarmRelation ar : list) {
			// 更新警情信息中已关联
			alarmInfoDao.updateAlarmInfo(ar.getAlarmInfo());
			AlarmRelation mutipleData = alarmRelationDao.findMutipleAlarmRelation(ar);
			// 数据表t_alarm_relation中有重复数据
			if (mutipleData != null) {
				// 删除重复数据
				alarmRelationDao.delete(mutipleData);
			}
			// 向数据表中t_alarm_relation增加数据
			alarmRelationDao.addAlarmRelation(ar);
		}

		// 得到警情信息中id的的最大值
		String queryStr03 = " select MAX(id) from AlarmInfo ";
		Long maxAlaId = alarmInfoDao.findMaxId(queryStr03);

		// 得到视频信息中id的最大值
		String queryStr04 = " select MAX(id) from Document ";
		Long maxDouId = documentDao.fiadMaxId(queryStr04);

		// 插入新的时间戳
		AlarmStamp alarmStamp02 = new AlarmStamp(null, null, maxAlaId, maxDouId, new Date());
		alarmStampDao.insertStamp(alarmStamp02);
		list.clear();

		// 计算结束时间
		// long endTime = System.currentTimeMillis();
		// System.out.println("程序运行时间："+(endTime-startTime)+"ms");
	}

	// 获取到锁执行目标方法,
	public void seckill() {

		try {
			String identifier = UUID.randomUUID().toString();
			// 获取锁值
			Object attribute01 = SysSource.getInstance().get("AlarmRelevance_alarmBouVid_resource");
			if (attribute01 == null) {
				// 设置锁值
				SysSource.getInstance().set("AlarmRelevance_alarmBouVid_resource", identifier, 3l);
			}

			Object attribute02 = SysSource.getInstance().get("AlarmRelevance_alarmBouVid_resource");
			if (identifier.equals(attribute02)) {
				// 贵阳警视频自动关联
				alarmBouVid();
				// 删除锁
				// SysSource.getInstance().removeAttribute("AlarmRelevance_alarmBouVid_resource");
			}
		} catch (Exception e) {
			// 删除锁
			SysSource.getInstance().remove("AlarmRelevance_alarmBouVid_resource");
			logger.error("*** 错误：" + e);

		} finally {
			SysSource.getInstance().remove("AlarmRelevance_alarmBouVid_resource");
		}

	}
}
