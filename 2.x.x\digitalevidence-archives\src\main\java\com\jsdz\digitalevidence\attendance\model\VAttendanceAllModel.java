package com.jsdz.digitalevidence.attendance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_attendance_all")
public class VAttendanceAllModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private Long id;

    /**
     * 经度
     */
    @TableField("LONGITUDE")
    private String longitude;

    /**
     * 维度
     */
    @TableField("LATITUDE")
    private String latitude;

    /**
     * 考勤时间
     */
    @TableField("ATTENDANCETIME")
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date attendancetime;

    /**
     * 是否自动打卡（0自动，1手动）
     */
    @TableField("ISAUTOMATIC")
    private Integer isautomatic;

    /**
     * 考勤任务名称
     */
    @TableField("TASKID")
    private Long taskId;
    /**
     * 考勤任务名称
     */
    @TableField("ATTENDANCENAME")
    private String attendancename;

    /**
     * 考勤时间
     */
    @TableField("TATTENDANCETIME")
    private String tattendancetime;

    /**
     * 可提前多少分钟打卡
     */
    @TableField("ADVANCEBYMINUTES")
    private Integer advancebyminutes;

    @TableField("ATTENDANCEDESCRIBE")
    private String attendancedescribe;

    @TableField("ORGID")
    private Long orgid;

    @TableField("ORGNAME")
    private String orgname;

    @TableField("ORGCODE")
    private String orgcode;

    @TableField("PATH")
    private String path;

    @TableField("USERNAME")
    private String username;

    @TableField("WORKNUMBER")
    private String worknumber;

    @TableField("DEVICECODE")
    private String devicecode;

    @TableField("DEVICEID")
    private Long deviceid;


}
