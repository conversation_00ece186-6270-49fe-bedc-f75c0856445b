package com.jsdz.digitalevidence.analysis.openapi.service.impl;

import java.util.Date;

/**
 * 
 * @类名: DsjInfoServiceImpl
 * @说明: 非过检版的的实现类
 *
 * @author: kenny
 * @Date 2017-06-09 15:15:00
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.digitalevidence.analysis.openapi.dao.DsjInfoDao;
import com.jsdz.digitalevidence.analysis.openapi.model.DsjInfo;
import com.jsdz.digitalevidence.analysis.openapi.service.DsjInfoService;
import com.jsdz.digitalevidence.document.distributed.ContentTypeDefineMapper;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.model.ImportantLevel;
import com.jsdz.digitalevidence.document.service.DocumentService;
import com.jsdz.digitalevidence.site.model.Recorder;
import com.jsdz.digitalevidence.site.model.Site;
import com.jsdz.digitalevidence.site.service.RecorderService;
import com.jsdz.digitalevidence.site.service.SiteService;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.service.OrganizationService;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Env;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("DsjInfoServiceImpl")
public class DsjInfoServiceImpl implements DsjInfoService {

	@Autowired
	private DsjInfoDao dsjInfoDao;
	
	@Autowired
	private RecorderService recorderService;
	
	@Autowired
	private OrganizationService organizationService;
	@Autowired
	private  EmployeesService employeesService;
	
	@Autowired
	private  SiteService siteService;
	
	@Autowired
	private DocumentService documentService;
	@Autowired
	private ContentTypeDefineMapper contentTypeDefineMapper;

	//新增
	public AjaxResult addDsjInfo(String siteCode,DsjInfo dsjInfo) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		result.setSuccess(false);
		
		result = this.checkDsjInfo(dsjInfo);
		if (!result.isSuccess()){
			return result;
		}

		//处理单位
		result = this.getOrganization(dsjInfo);
		if (!result.isSuccess()){
			result.setSuccess(false);
			return result;
		}
		Organization org = (Organization)result.getData();
		
		//处理警员
		result = this.getPolice(dsjInfo, org);
		if (!result.isSuccess()){
			result.setSuccess(false);
			return result;
		}
		Employees police = (Employees)result.getData();
		
		//处理采集站
		Site site = null;
		if (!Env.getProperty("is.check.version").equals("1")){
			site = this.getSite(siteCode,dsjInfo, org);
			if (site == null){
				result.setSuccess(false);
				result.setMsg("站点(gzz_xh="+dsjInfo.getGzz_xh()+")没有注册");
				return result;
			}
		}
		//执法仪
//		Recorder recorder = this.getRecorder(dsjInfo,police,site);

		Document doc = new Document();
		doc.setName(dsjInfo.getWjbh());//文件编号
		doc.setComments(dsjInfo.getBzlx());//文件别名
		doc.setCreateTime(dsjInfo.getPssj());//拍摄时间(YYYY-MM-DD HH:mm:ss)
		//是否过检版本
		if (Env.getProperty("is.check.version").equals("1")){
			doc.setCate(DocumentCate.VEDIO);
		}else{
			doc.setCate(getCate(dsjInfo.getWjlx())); //文件类型
			result = this.getDocNames(dsjInfo.getWjbh());
			if(!result.isSuccess()){
				result.setSuccess(false);
				return result;
			}
		}
			
		
		Long size = 0L;
		try{
			size = Long.valueOf(dsjInfo.getWjdx());//文件大小
		}catch (Exception e){
			size = 0L;
		}
		
		
		doc.setPolice(police);
		doc.setUri(dsjInfo.getBfwz());//播放位置
		doc.setSize(size);
		doc.setSite(site);
		doc.setUploadTime(dsjInfo.getScsj());//上传时间
		doc.setComments(dsjInfo.getBzlx());
		doc.setImpLevel(ImportantLevel.GENERAL);
		doc.setImpLevelRec(ImportantLevel.GENERAL);
		doc.setStorageType(2);
		String contentType = this.getContentType(dsjInfo.getWjbh());
		doc.setOrganization(org);
		doc.setContentType(contentType);
		documentService.addDocument(doc);
		
		//处理执法仪
		
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 	
		
	}

	

	//修改
	public AjaxResult updateDsjInfo(DsjInfo dsjInfo) {
		AjaxResult result = new AjaxResult();
		dsjInfoDao.updateDsjInfo(dsjInfo);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteDsjInfo(DsjInfo dsjInfo) {
		AjaxResult result = new AjaxResult();
		dsjInfoDao.deleteDsjInfo(dsjInfo);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public DsjInfo findDsjInfoById(Long id){

		return dsjInfoDao.findDsjInfoById(id);
	}

	//按 id 查询
	public DsjInfo locateDsjInfoById(Long id) {
		return dsjInfoDao.locateDsjInfoById(id);
	}

	//单个查询
	public DsjInfo findDsjInfoByParam(String queryStr, String[] paramNames, Object[] values) {
		return dsjInfoDao.findDsjInfoByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<DsjInfo> findAllDsjInfos() {
		return dsjInfoDao.findAllDsjInfos();
	}

	//列表查询
	public List<DsjInfo> findDsjInfosByParam(String queryStr, String[] paramNames, Object[] values) {
		return dsjInfoDao.findDsjInfosByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<DsjInfo> findDsjInfosOnPage(Page<DsjInfo> page, String queryStr, String[] paramNames, Object[] values) {
		Page<DsjInfo> pos = dsjInfoDao.findDsjInfosOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
	/**
	 * 校验导入的数据 
	 * @param dsjInfo
	 * @return
	 */
	private AjaxResult checkDsjInfo(DsjInfo dsjInfo) {
		//拍摄日期
		//if (this.getDateFromStr(dsjInfo.getPssj())==null)
		AjaxResult result = new AjaxResult(300,false,"",null);
		if (dsjInfo.getWjbh()==null || dsjInfo.getWjbh().equals("")){
			
			result.setMsg("参数文件编号(wjbh)必须 提供");
			return result;
		}
		
		if (dsjInfo.getPssj()==null ){
			result.setMsg("参数拍摄时间(pssj)必须 提供");
			return result;
		}

		if (dsjInfo.getScsj()==null ){
			result.setMsg("参数上传时间(scsj)必须 提供");
			return result;
		}
		
		if (dsjInfo.getWjlx()==null || dsjInfo.getWjlx().equals("")){
			result.setMsg("参数文件类型(wjlx)必须 提供");
			return result;
		}
		//文件类型
		System.out.println("wjlx=" + dsjInfo.getWjlx());
		String wjlx = dsjInfo.getWjlx();
		if (!Env.getProperty("is.check.version").equals("1")){
			DocumentCate cate = getCate(wjlx);
			if (cate == null){
				result.setMsg("文件类型必须 是（图片/音频/视频/日志）之一");
				return result;
			}
		}
		
		if (dsjInfo.getJybh()==null || dsjInfo.getJybh().equals("")){
			result.setMsg("参数警号(jybh)必须 提供");
			return result;
		}
		
		if (dsjInfo.getJgdm()==null || dsjInfo.getJgdm().equals("")){
			result.setMsg("参数单位编号(jgdm)必须 提供");
			return result;
		}
		
		if (dsjInfo.getBfwz()==null || dsjInfo.getBfwz().equals("")){
			result.setMsg("参数播放位置(bfwz)必须 提供");
			return result;
		}
		
		if (dsjInfo.getGzz_xh()==null || dsjInfo.getGzz_xh().equals("")){
			result.setMsg("参数采集编号(gzz_xh)必须 提供");
			return result;
		}
		
		result.setSuccess(true);
		return result;
	}
	
	/**
	 * 判断文件名是否重复
	 * @param wjbh
	 * @return
	 */
	private AjaxResult getDocNames(String wjbh) {
		
		AjaxResult result = new AjaxResult(300,false,"",null);
		String queryStr = "from Document docs where docs.name = :name";
		String[] paramNames = new String[]{"name"};
		Object[] values = new Object[]{wjbh};//文档编号
		List<Document> listDocs = documentService.findDocumentsByCondition(queryStr, paramNames, values);
		if(listDocs.size() > 0){
			result.setSuccess(false);
			result.setMsg("文件编号(wjbh="+wjbh+")重复");
		}else{
			result.setSuccess(true);
		}
		return result;
	}
	/**
	 * 根据名称最媒体类型
	 * @param mediaType
	 * @return
	 */
	private DocumentCate getCate(String mediaType) {
		
		if (mediaType.equals("video")){
			mediaType = "视频";
		}
		else if (mediaType.equals("audio")){
			mediaType = "音频";
		}
		else if (mediaType.equals("image")){
			mediaType = "图片";
		}
		else if (mediaType.equals("log")){
			mediaType = "日志";
		}
		
		DocumentCate cate = null;
		for (DocumentCate c : DocumentCate.values()) {  
		    if (c.getName().equals(mediaType)){
		    	cate = c;
		    	break;
		    }
		}
		return cate;
	}	
	/**
	 * 取得单位对象，如不存在则新增
	 * @param dsjInfo
	 * @return
	 */
	private AjaxResult getOrganization(DsjInfo dsjInfo){
		
		AjaxResult result = new AjaxResult(300,false,"",null);
		
		String queryStr = "from Organization o where o.orgCode = :orgCode";
		String[] paramNames = new String[]{"orgCode"};
		Object[] values = new Object[]{dsjInfo.getJgdm()};
		Organization org = organizationService.findOrganizationByParam(queryStr,paramNames,values);
		
		if(org == null){
			result.setSuccess(false);
			result.setMsg("单位代码(jgdm="+dsjInfo.getJgdm()+")没有注册");
		}else{
			result.setSuccess(true);
			result.setData(org);
		}
		
		/*if (org == null){
			org = new Organization();
			org.setOrgCode(dsjInfo.getJgdm());//单位编号
			org.setOrgName(dsjInfo.getDwmc()==null || "".equals(dsjInfo.getDwmc())?dsjInfo.getJgdm():dsjInfo.getDwmc());//单位名称
			org.setCreateTime(new Date());
			result = organizationService.addOrganization(org);
			if (!result.isSuccess())
				org = null;
		}else{
			result.setSuccess(true);
		}
		result.setData(org);*/
		return result;
	}
	
	/**
	 * 取得警员对象，如不存在则新增
	 * @param dsjInfo
	 * @param org
	 * @return
	 */
	private AjaxResult getPolice(DsjInfo dsjInfo,Organization org){
		AjaxResult result = new AjaxResult(300,false,"",null);
		String queryStr = "from Employees e where e.isDeleted = false and e.workNumber = :workNumber";
		String[] paramNames = new String[]{"workNumber"};
		Object[] values = new Object[]{dsjInfo.getJybh()};//警号
		
		Employees police = employeesService.findEmployeesByParam(queryStr, paramNames, values);
		
		if(police == null){
			result.setSuccess(false);
			result.setMsg("队员编号(jybh="+dsjInfo.getJybh()+")没有注册");
		}else {
			result.setSuccess(true);
			result.setData(police);
		}
		
	/*	if (police == null){
			police = new Employees(); 
			police.setName(dsjInfo.getJy_xm()==null||"".equals(dsjInfo.getJy_xm())?dsjInfo.getJybh():dsjInfo.getJy_xm());
			police.setWorkNumber(dsjInfo.getJybh());
			police.setCreateTime(new Date());
			police.setOrganization(org);
			police.setIsPolice(1);
			result = employeesService.addEmployees(police);
			if (!result.isSuccess())
				police = null;
		}else{
			result.setSuccess(true);
		    
		}
		result.setData(police);*/
		return result;
	}

	/**
	 * 取得采集站对象，如不存在则新增
	 * @param dsjInfo
	 * @param org
	 * @return
	 */
	private Site getSite(String siteCode,DsjInfo dsjInfo,Organization org){

		String queryStr = "from Site s where s.siteNo = :siteNo";
		String[] paramNames = new String[]{"siteNo"};
		Object[] values = new Object[]{siteCode + "-" + dsjInfo.getGzz_xh()};//IP
		
		Site site = siteService.findSiteByParam(queryStr, paramNames, values);
		
		if (site == null)
			return null;
		
		if (org != null){
			if (site.getOrganization() == null || !site.getOrganization().getId().equals(org.getId())){
				site.setOrganization(org);
				siteService.updateSite(site);
			}
		}
		
/*		if (site == null){
			site = new Site();
			site.setSiteType(SiteType.COLLECT_SITE);
			site.setSiteNo(siteIP);
			site.setSiteName(siteIP);
			site.setSiteIP(siteIP);
			site.setHttp("http://" + siteNo + "/");
			site.setOrganization(org);
			site.setCreateTime(new Date());
			site.setStatus(1);// 1、正常，2、报修，3、遗失，4、报废   5 未配备/未启用
			site.setState(0);//0 在线，1离线
			site.setOpen("1");//1 启用，0未启用
			AjaxResult result = siteService.addSite(site);
			if (!result.isSuccess())
				site = null;
		}*/
		return site;
	}
	
	/**
	 * 取得执法仪对象，如不存在则新增
	 * @param dsjInfo
	 * @param org
	 * @return
	 */
	private Recorder getRecorder(DsjInfo dsjInfo,Employees police,Site site){
		String queryStr = "from Recorder r where r.isDeleted=false and r.code = :code";
		String[] paramNames = new String[]{"code"};
		Object[] values = new Object[]{dsjInfo};//执法仪编码
		
		Recorder recorder = recorderService.findRecorderByParam(queryStr, paramNames, values);
		if (recorder == null){
			recorder = new Recorder();
			recorder.setCode(dsjInfo.getGzz_xh());
			recorder.setCreateTime(new Date());
			recorder.setState(1);
			recorder.setSite(site);
			recorder.setEmployees(police);
			AjaxResult result=null;
			try {
				result = recorderService.addRecorder(recorder);
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			if (!result.isSuccess())
				recorder = null;
		}
		return recorder;
	}
	
	/**
	 * 根据文件后辍名取得contextType
	 * @param fileName
	 * @return
	 */
	private String getContentType(String fileName){
		String key = "";
		int index = fileName.lastIndexOf(".");
		if (index > -1){
			key = fileName.substring(index+1).toLowerCase();
		}else{
			return null;
		}

		return contentTypeDefineMapper.getValue(key);
		
	}
	
}
