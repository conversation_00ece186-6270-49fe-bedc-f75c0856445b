package tools;

import java.text.SimpleDateFormat;
import java.util.Date;

import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.test.context.transaction.TransactionConfiguration;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;

/**
 * 
 * @类名: CodeGenerator.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年4月28日下午8:44:56
 * 修改记录：
 *
 * @see
 */
public class CodeGenerator {
	private static String packageName; //包名
	private static String className; //
	private static String shortClassName; //
	private static String objectName;//对象名，首字母小写
	private static String aliasName;//别名
	private static String path = System.getProperty("user.dir") + "\\src\\main\\java";
	private static boolean toFile = false;
	
	private static SimpleDateFormat formatter =   new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	
	public enum FileType {
		xml,java;
	}
	
	public static void main(String[] args) throws Exception {
		packageName = "com.jsdz.digitalevidence.graphensemble.upload";
		className = packageName + ".model.UpgradeHist";
		shortClassName = "UpgradeHist";
		aliasName = "uh"; //别名
		String ss = shortClassName;
		objectName = ss.replaceFirst(ss.substring(0, 1),ss.substring(0, 1).toLowerCase());
		//是否写入到文件中
		toFile=true;
		
		//path="D:\\work\\workspace\\工程\\site\\src\\main\\java";
		//Site
		//path="D:\\work\\workspace\\工程\\site\\src\\main\\java"; //路径
		//FtpServer
		//path="D:\\work\\2.4\\workspace20170927\\工程\\digitalevidence_activemq\\src\\main\\java"; 
		//path="D:\\work\\2.4\\workspace20170927\\工程\\digitalevidence-alarm\\src\\main\\java";
		//path="D:\\work\\2.4\\workspace20170927\\工程\\admin\\admin-security\\src\\main\\java";
		//path="D:\\work\\2.4\\贵阳\\工程\\digitalevidence-alarm\\src\\main\\java";
		//path="F:\\4G\\workspace\\digitalevidence\\digitalevidence-graphensemble\\src\\main\\java";
		path="F:\\4G\\source\\2.x.x\\digitalevidence-graphensemble\\src\\main\\java";
		
		
		//路径
		//path="D:\\work\\2.4\\workspace20170801\\工程\\digitalevidence-alarm\\src\\main\\java"; //路径
		//alarm/common
		//path="D:\\work\\2.4\\workspace20170927\\工程\\digitalevidence-alarm\\src\\main\\java";
		
		//path="D:\\work\\2.4\\workspace20170927\\工程\\digitalevidence-site\\src\\main\\java";
		//path="D:\\work\\2.4\\workspace20170927\\工程\\admin\\admin-security\\src\\main\\java";
				
		//Security
		//path="D:\\work\\2.4\\workspace20170927\\工程\\admin\\admin-security\\src\\main\\java";
		
		
		//D:\work\workspace\工程\admin\security\src\main\java\com\jsdz\admin\security\dao\EmployeesDao.java
		String s1 = createSerivceContent();
		String s2 = createSerivceImplContent();
		String s3 = createDaoContent();
		String s4 = createDaoImplContent();
	}

	public static String getPackageName() {
		return packageName;
	}

	public static void setPackageName(String packageName) {
		CodeGenerator.packageName = packageName;
	}

	public static String getClassName() {
		return className;
	}

	public static void setClassName(String className) {
		CodeGenerator.className = className;
	}

	public static String getShortClassName() {
		return shortClassName;
	}

	public static void setShortClassName(String shortClassName) {
		CodeGenerator.shortClassName = shortClassName;
	}

	public static String getObjectName() {
		return objectName;
	}

	public static void setObjectName(String objectName) {
		CodeGenerator.objectName = objectName;
	}

	public static String getAliasName() {
		return aliasName;
	}

	public static void setAliasName(String aliasName) {
		CodeGenerator.aliasName = aliasName;
	}

	public static String getPath() {
		return path;
	}

	public static void setPath(String path) {
		CodeGenerator.path = path;
	}

	public static SimpleDateFormat getFormatter() {
		return formatter;
	}

	public static void setFormatter(SimpleDateFormat formatter) {
		CodeGenerator.formatter = formatter;
	}

		/**
		 * @说明：取得类的属性
		 *
		 * <AUTHOR>
		 * @param className
		 * @return
		 * 2017年4月28日 下午8:51:20
		 */
	public static void getField(String className){
		try {
		   Class clazz = Class.forName(className);//根据类名获得其对应的Class对象 写上你想要的类名就是了 注意是全名 如果有包的话要加上 比如java.Lang.String
		   Field[] fields = clazz.getDeclaredFields();//根据Class对象获得属性 私有的也可以获得
		   for(Field f : fields) {
			   System.out.println(f.getType().getName());//打印每个属性的类型名字
			   
		   }
		} catch(Exception e) {
		   e.printStackTrace();
		}
	}
	

		/**
		 * @说明：产生Service文件内容
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年4月28日 下午9:12:51
		 */
	public static String createSerivceContent() {
		StringBuffer sb = new StringBuffer("");
		sb.append("package " + packageName + ".service;\r\n\n");
		sb.append("/**\r\n");
		sb.append(" * \r\n");
		sb.append(" * @类名: "+shortClassName+"Service\r\n");
     	sb.append(" * @说明: \r\n");
     	sb.append(" *\r\n");
     	sb.append(" * @author: kenny\r\n");
     	Date d = new Date();
     	sb.append(" * @Date "+ formatter.format(d) +"\r\n");
     	sb.append(" * 修改记录：\r\n");
     	sb.append(" *\r\n");
     	sb.append(" * @see\r\n");
     	sb.append("*/\r\n\n");
     	sb.append("import java.util.List;\r\n");
     	sb.append("import "+className+";\r\n");
     	sb.append("import com.jsdz.core.AjaxResult;\r\n");
     	sb.append("import com.jsdz.core.Page;\r\n\n");
     	sb.append("public interface "+shortClassName+"Service {\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 新增\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public AjaxResult add"+shortClassName+"("+shortClassName+" "+objectName+");\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 修改\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public AjaxResult update"+shortClassName+"("+shortClassName+" "+objectName+");\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 删除\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public AjaxResult delete"+shortClassName+"("+shortClassName+" "+objectName+");\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 按id查询,结果是游离状态的数据\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public " + shortClassName+" find"+shortClassName+"ById(Long id);\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 按id查询\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public " + shortClassName+" locate"+shortClassName+"ById(Long id);\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 单个查询\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public " + shortClassName+" find"+shortClassName+"ByParam(String queryStr,String[] paramNames,Object[] values);\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 查询全部\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public List<"+shortClassName+"> findAll"+shortClassName+"s();\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 列表查询\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public List<"+shortClassName+"> find"+shortClassName+"sByParam(String queryStr,String[] paramNames,Object[] values);\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 分页查询\r\n");
     	sb.append("	 */ \r\n ");

     	sb.append("	public Page<"+shortClassName+"> find"+shortClassName+"sOnPage(Page<"+shortClassName+"> page, String queryStr,String[] paramNames,Object[] values);\r\n\n");
     	sb.append("}\r\n\n");
     	sb.append("");
  	
     	createFile("service","Service",FileType.java,sb.toString());
     	return sb.toString();
	}
	
	
		/**
		 * @说明：createSerivceImplContent
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年5月2日 下午9:00:04
		 */
	public static String createSerivceImplContent() {
		StringBuffer sb = new StringBuffer("");
		sb.append("package " + packageName + ".service.impl;\r\n\n");
		sb.append("/**\r\n");
		sb.append(" * \r\n");
		sb.append(" * @类名: "+shortClassName+"ServiceImpl\r\n");
	 	sb.append(" * @说明: \r\n");
	 	sb.append(" *\r\n");
	 	sb.append(" * @author: kenny\r\n");
	 	Date d = new Date();
	 	sb.append(" * @Date "+ formatter.format(d) +"\r\n");
	 	sb.append(" * 修改记录：\r\n");
	 	sb.append(" *\r\n");
	 	sb.append(" * @see\r\n");
	 	sb.append("*/\r\n\n");
	 	sb.append("import java.util.List;\r\n");
	 	sb.append("import org.springframework.beans.factory.annotation.Autowired;\r\n");
	 	sb.append("import org.springframework.stereotype.Service;\r\n");
	 	sb.append("import org.springframework.test.context.transaction.TransactionConfiguration;\r\n");
	 	sb.append("import "+packageName+".dao."+shortClassName+"Dao;\r\n");
	 	sb.append("import "+packageName+".model."+shortClassName+";\r\n");
	 	sb.append("import "+packageName+".service."+shortClassName+"Service;\r\n");
	 	//sb.append("import "+packageName+".utils.MD5;\r\n");
	 	sb.append("import com.jsdz.core.AjaxResult;\r\n");
	 	sb.append("import com.jsdz.core.Page;\r\n\n");
	 	
	 	//sb.append("@TransactionConfiguration(transactionManager = \"transactionManager\", defaultRollback = false)\r\n");
	 	sb.append("@Service(\""+shortClassName+"ServiceImpl\")\r\n");
	 	sb.append("public class "+shortClassName+"ServiceImpl implements "+shortClassName+"Service {\r\n\n");
	 	sb.append("	@Autowired\r\n");
	 	sb.append("	private "+shortClassName+"Dao "+objectName+"Dao;\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 新增\r\n");
     	sb.append("	 */ \r\n ");
	 	sb.append("	public AjaxResult add"+shortClassName+"("+shortClassName+" "+objectName+") {\r\n");
	 	sb.append("		AjaxResult result = new AjaxResult(300,false,\"\",null);\r\n");
	 	sb.append("		try{\r\n");
	 	sb.append("			"+objectName+"Dao.add"+shortClassName+"("+objectName+");\r\n");
	 	sb.append("			result.setCode(200);\r\n");
	 	sb.append("			result.setSuccess(true);\r\n");
	 	sb.append("			result.setMsg(\"新增保存成功。\");\r\n");
	 	sb.append("		}catch(Exception e){\r\n");
	 	sb.append("			result.setMsg(e.toString());\r\n");	
	 	sb.append("		}\r\n");
	 	sb.append("		return result; \r\n");
	 	sb.append("	}\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 修改\r\n");
     	sb.append("	 */ \r\n ");
	 	sb.append("	public AjaxResult update"+shortClassName+"("+shortClassName+" "+objectName+") {\r\n");
	 	sb.append("		AjaxResult result = new AjaxResult(300,false,\"\",null);\r\n");
	 	sb.append("		try{\r\n");
	 	sb.append("			"+objectName+"Dao.update"+shortClassName+"("+objectName+");\r\n");
	 	sb.append("			result.setCode(200);\r\n");
	 	sb.append("			result.setSuccess(true);\r\n");
	 	sb.append("			result.setMsg(\"修改成功。\");\r\n");
	 	sb.append("		}catch(Exception e){\r\n");
	 	sb.append("			result.setMsg(e.toString());\r\n");	
	 	sb.append("		}\r\n");
	 	sb.append("		return result; \r\n");
	 	sb.append("	}\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 删除\r\n");
     	sb.append("	 */ \r\n ");
	 	sb.append("	public AjaxResult delete"+shortClassName+"("+shortClassName+" "+objectName+") {\r\n");
	 	sb.append("		AjaxResult result = new AjaxResult(300,false,\"\",null);\r\n");
	 	sb.append("		try{\r\n");
	 	sb.append("			"+objectName+"Dao.delete"+shortClassName+"("+objectName+");\r\n");
	 	sb.append("			result.setSuccess(true);\r\n");
	 	sb.append("			result.setCode(200);\r\n");
	 	sb.append("			result.setMsg(\"删除成功。\");\r\n");
	 	sb.append("		}catch(Exception e){\r\n");
	 	sb.append("			result.setMsg(e.toString());\r\n");	
	 	sb.append("		}\r\n");
	 	sb.append("		return result; \r\n");
	 	sb.append("	}\r\n\n");
	 	
     	sb.append("	/** \r\n ");
     	sb.append("	 * 按id查询(游离数据)\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public " + shortClassName+" find"+shortClassName+"ById(Long id){\r\n\n");
	 	sb.append("		return "+objectName+"Dao.find"+shortClassName+"ById(id);\r\n");
	 	sb.append("	}\r\n\n");
     	
     	sb.append("	/** \r\n ");
     	sb.append("	 * 按 id 查询\r\n");
     	sb.append("	 */ \r\n ");
	 	sb.append("	public "+shortClassName+" locate"+shortClassName+"ById(Long id) {\r\n");
	 	sb.append("		return "+objectName+"Dao.locate"+shortClassName+"ById(id);\r\n");
	 	sb.append("	}\r\n\n");
	 	
     	sb.append("	/** \r\n ");
     	sb.append("	 * 单个查询\r\n");
     	sb.append("	 */ \r\n ");
	 	sb.append("	public "+shortClassName+" find"+shortClassName+"ByParam(String queryStr, String[] paramNames, Object[] values) {\r\n");
	 	sb.append("		return "+objectName+"Dao.find"+shortClassName+"ByCondition(queryStr,paramNames,values);\r\n");
	 	sb.append("	}\r\n\n");
	
     	sb.append("	/** \r\n ");
     	sb.append("	 * 查询全部\r\n");
     	sb.append("	 */ \r\n ");
	 	sb.append("	public List<"+shortClassName+"> findAll"+shortClassName+"s() {\r\n");
	 	sb.append("		return "+objectName+"Dao.findAll"+shortClassName+"s();\r\n");
	 	sb.append("	}\r\n\n");

     	sb.append("	/** \r\n ");
     	sb.append("	 * 列表查询\r\n");
     	sb.append("	 */ \r\n ");
	 	sb.append("	public List<"+shortClassName+"> find"+shortClassName+"sByParam(String queryStr, String[] paramNames, Object[] values) {\r\n");
	 	sb.append("		return "+objectName+"Dao.find"+shortClassName+"sByCondition(queryStr,paramNames,values);\r\n");
	 	sb.append("	}\r\n\n");
	 	

     	sb.append("	/** \r\n ");
     	sb.append("	 * 分页查询\r\n");
     	sb.append("	 */ \r\n ");
	 	sb.append("	public Page<"+shortClassName+"> find"+shortClassName+"sOnPage(Page<"+shortClassName+"> page, String queryStr, String[] paramNames, Object[] values) {\r\n");
	 	sb.append("		Page<"+shortClassName+"> pos = "+objectName+"Dao.find"+shortClassName+"sOnPage(page, queryStr, paramNames, values);\r\n");
	 	sb.append("		return pos;\r\n");
	 	sb.append("	}\r\n\n");
	 	sb.append("}\r\n");
	 	
	 	System.out.println(sb.toString());
	 	createFile("service.impl","ServiceImpl",FileType.java,sb.toString());
	 	return sb.toString();
	}

	/**
	 * @说明：产生Dao接口文件内容
	 *
	 * <AUTHOR>
	 * @param 
	 * @return
	 * 2017年5月2日 下午8:25:00
	 */
	public static String createDaoContent() {
		StringBuffer sb = new StringBuffer("");
		sb.append("package " +packageName + ".dao;\r\n\n");
		sb.append("/**\r\n");
		sb.append(" *\r\n");
		sb.append(" * @类名: "+shortClassName+"Dao\r\n");
		sb.append(" * @说明:\r\n");
		sb.append(" * @author: kenny\r\n");
		Date d = new Date();
	 	sb.append(" * @Date "+ formatter.format(d) +"\r\n");	
		sb.append(" * 修改记录：\r\n");
		sb.append(" * @see\r\n");
		sb.append(" * @see\r\n");
		sb.append(" */\r\n\n");
		
		sb.append("import java.util.List;\r\n");
		sb.append("import "+packageName+".model."+shortClassName+";\r\n");
		sb.append("import com.jsdz.core.Page;\r\n");
		sb.append("import com.jsdz.core.dao.GenericORMEntityDAO;\r\n\n");
		
		sb.append("public interface "+shortClassName+"Dao extends GenericORMEntityDAO<"+shortClassName+",Long> {\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 新增\r\n");
     	sb.append("	 */ \r\n ");

		sb.append("	public void add"+shortClassName+"("+shortClassName+" "+objectName+");\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 修改\r\n");
     	sb.append("	 */ \r\n ");

		sb.append("	public void update"+shortClassName+"("+shortClassName+" "+objectName+");\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 删除\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public void delete"+shortClassName+"("+shortClassName+" "+objectName+");\r\n\n");

      	sb.append("	/** \r\n ");
     	sb.append("	 * 按id查询,结果是游离状态的数据\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public " + shortClassName+" find"+shortClassName+"ById(Long id);\r\n\n");
     	
      	sb.append("	/** \r\n ");
     	sb.append("	 * 按id查询\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public "+shortClassName+" locate"+shortClassName+"ById(Long id);\r\n\n");
      	sb.append("	/* \r\n ");
     	sb.append("	 * 单个查询\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public " + shortClassName+" find"+shortClassName+"ByCondition(String queryStr,String[] paramNames,Object[] values);\r\n\n");

      	sb.append("	/** \r\n ");
     	sb.append("	 * 查询全部\r\n");
     	sb.append("	 */ \r\n ");
		sb.append("	public List<"+shortClassName+"> findAll"+shortClassName+"s();\r\n\n");

     	sb.append("	/** \r\n ");
     	sb.append("	 * 列表查询\r\n");
     	sb.append("	 */ \r\n ");

		sb.append("	public List<"+shortClassName+"> find"+shortClassName+"sByCondition(String queryStr,String[] paramNames,Object[] values);\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 分页查询\r\n");
     	sb.append("	 */ \r\n ");

		sb.append("	public Page<"+shortClassName+"> find"+shortClassName+"sOnPage(Page<"+shortClassName+"> page,String queryStr,String[] paramNames,Object[] values);\r\n\n");
     	sb.append("	/** \r\n ");
     	sb.append("	 * 执行指定的HQL文件\r\n");
     	sb.append("	 */ \r\n ");

		sb.append("	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);\r\n\n");
		
		sb.append("}\r\n");
		sb.append("\r\n");
		//System.out.println(sb.toString());
		createFile("dao","Dao",FileType.java,sb.toString());
	 	return sb.toString();
	}
	
		/**
		 * @说明：createDaoImplContent
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年5月2日 下午8:42:53
		 */
	public static String createDaoImplContent() {
		StringBuffer sb = new StringBuffer("");
		sb.append("package " +packageName + ".dao.impl;\r\n\n");
		sb.append("/**\r\n");
		sb.append(" *\r\n");
		sb.append(" * @类名: "+shortClassName+"DaoImpl\r\n");
		sb.append(" * @说明:\r\n");
		sb.append(" * @author: kenny\r\n");
		Date d = new Date();
	 	sb.append(" * @Date "+ formatter.format(d) +"\r\n");	
		sb.append(" * 修改记录：\r\n");
		sb.append(" * @see\r\n");
		sb.append(" * @see\r\n");
		sb.append(" */\r\n\n");
		
		sb.append("import java.util.List;\r\n");
		sb.append("import org.hibernate.Query;\r\n");
		sb.append("import org.hibernate.Session;\r\n");
		sb.append("import org.hibernate.SessionFactory;\r\n");
		sb.append("import org.springframework.stereotype.Repository;\r\n");
		sb.append("import "+packageName + ".dao."+shortClassName+"Dao;\r\n");
		sb.append("import "+packageName + ".model."+shortClassName+";\r\n");
		sb.append("import com.jsdz.core.Page;\r\n");
		sb.append("import org.springframework.orm.hibernate3.HibernateCallback;\r\n");
		sb.append("import org.hibernate.HibernateException;\r\n");
		sb.append("import java.sql.SQLException;\r\n");
		
		sb.append("import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;\r\n\n");
		
		sb.append("@Repository\r\n");
		
		sb.append("public class "+shortClassName+"DaoImpl extends GenericEntityDaoHibernateImpl<"+shortClassName+",Long> implements "+shortClassName+"Dao{\r\n\n");
		
     	sb.append("	/** \r\n ");
     	sb.append("	 * 新增\r\n");
     	sb.append("	 */ \r\n ");
		sb.append("	public void add"+shortClassName+"("+shortClassName+" "+objectName+") {\r\n");
		sb.append("		this.saveOrUpdate("+objectName+");\r\n");
		sb.append("	}\r\n\n");
		
     	sb.append("	/** \r\n ");
     	sb.append("	 * 删除\r\n");
     	sb.append("	 */ \r\n ");
		sb.append("	public void delete"+shortClassName+"("+shortClassName+" "+objectName+") {\r\n");
		sb.append("		this.delete("+objectName+");\r\n");
		sb.append("	}\r\n\n");
		
     	sb.append("	/** \r\n ");
     	sb.append("	 * 修改\r\n");
     	sb.append("	 */ \r\n ");
		sb.append("	public void update"+shortClassName+"("+shortClassName+" "+objectName+") {\r\n");
		sb.append("		this.merge("+objectName+");\r\n");
		sb.append("	}\r\n\n");

     	sb.append("	/** \r\n ");
     	sb.append("	 * 按id查询(游离状态)\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public " + shortClassName+" find"+shortClassName+"ById(Long id){\r\n\n");
     	sb.append("		final String  hql = \"from "+shortClassName+" "+aliasName+" where "+aliasName+".id = :id\";\r\n");
     	sb.append("		final Long oid = id;\r\n");
     	sb.append("		"+shortClassName+" data = getHibernateTemplate().execute(new HibernateCallback<"+shortClassName+">() {\r\n");
     	sb.append("			public "+shortClassName+" doInHibernate(Session session)throws HibernateException, SQLException { \r\n");
     	sb.append("				Query query = session.createQuery(hql);\r\n");
     	sb.append("				query.setParameter(\"id\", oid);\r\n");
     	sb.append("				List<"+shortClassName+"> list = query.list();\r\n");
     	sb.append("				"+shortClassName+" rsult = list == null || list.size() <= 0?null:list.get(0);\r\n");
     	sb.append("				if (rsult != null){\r\n");	
     	sb.append("					session.evict(rsult);\r\n");	
     	sb.append("				}\r\n");	
     	sb.append("				return rsult;\r\n");	
     	sb.append("			}\r\n");	
     	sb.append("		});\r\n");	
     	sb.append("		return data;\r\n");	
     	sb.append("	}\r\n\n");	
		
     	sb.append("	/** \r\n ");
     	sb.append("	 * 按id查询\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public " + shortClassName+" locate"+shortClassName+"ById(Long id){\r\n\n");
     	sb.append("		final String  hql = \"from "+shortClassName+" "+aliasName+" where "+aliasName+".id = :id\";\r\n");
     	sb.append("		final Long oid = id;\r\n");
     	sb.append("		"+shortClassName+" data = getHibernateTemplate().execute(new HibernateCallback<"+shortClassName+">() {\r\n");
     	sb.append("			public "+shortClassName+" doInHibernate(Session session)throws HibernateException, SQLException { \r\n");
     	sb.append("				Query query = session.createQuery(hql);\r\n");
     	sb.append("				query.setParameter(\"id\", oid);\r\n");
     	sb.append("				List<"+shortClassName+"> list = query.list();\r\n");
     	sb.append("				"+shortClassName+" rsult = list == null || list.size() <= 0?null:list.get(0);\r\n");
     	sb.append("				return rsult;\r\n");	
     	sb.append("			}\r\n");	
     	sb.append("		});\r\n");	
     	sb.append("		return data;\r\n");	
     	sb.append("	}\r\n\n");		

      	sb.append("	/** \r\n ");
     	sb.append("	 * 单个查询(根据其它字段查询)\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public "+shortClassName+" find"+shortClassName+"ByCondition(final String queryStr,final String[] paramNames,final Object[] values){\r\n");
     	sb.append("		"+shortClassName+" data = getHibernateTemplate().execute(new HibernateCallback<"+shortClassName+">() {\r\n");
     	sb.append("		public "+shortClassName+" doInHibernate(Session session)throws HibernateException, SQLException { \r\n");
     	sb.append("			Query query = session.createQuery(queryStr);\r\n");
     	sb.append("			for(int i=0;i<values.length;i++){\r\n");
     	sb.append("				query.setParameter(paramNames[i], values[i]);\r\n");
     	sb.append("			}\r\n");
     	sb.append("			List<"+shortClassName+"> list = query.list();\r\n");
     	sb.append("			"+shortClassName+" rsult = list == null || list.size() <= 0?null:list.get(0);\r\n");
     	sb.append("			if (rsult != null){\r\n");
     	sb.append("				session.evict(rsult);\r\n");
     	sb.append("			}\r\n");
     	sb.append("			return rsult;\r\n");
     	sb.append("			}\r\n");	
     	sb.append("		});\r\n");	
     	sb.append("		return data;\r\n");	
     	sb.append("	}\r\n\n");		
     	

     	sb.append("	/** \r\n ");
     	sb.append("	 * 查询全部\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public List<"+shortClassName+"> findAll"+shortClassName+"s(){\r\n");
     	sb.append("		return this.find(\"from "+shortClassName+" "+objectName+" \");\r\n");	    
		sb.append("	}\r\n\n");
		
     	sb.append("	/** \r\n ");
     	sb.append("	 * 列表查询\r\n");
     	sb.append("	 */ \r\n ");
     	sb.append("	public List<"+shortClassName+"> find"+shortClassName+"sByCondition(String queryStr,String[] paramNames,Object[] values){\r\n");
     	sb.append("		List<"+shortClassName+"> lists = null;\r\n");	    
     	sb.append("		if (values == null || values.length <= 0){\r\n");
     	sb.append("			lists = this.find(queryStr);\r\n");
     	sb.append("		}else{\r\n");
     	sb.append("			lists = this.findByNamedParam(queryStr,paramNames,values);\r\n");
     	sb.append("		}\r\n");
     	sb.append("		return lists;\r\n");
		sb.append("	}\r\n\n");
		
     	sb.append("	/** \r\n ");
     	sb.append("	 * 分页查询\r\n");
     	sb.append("	 */ \r\n ");
		sb.append("	public Page<"+shortClassName+"> find"+shortClassName+"sOnPage(Page<"+shortClassName+"> page, String queryStr, String[] paramNames, Object[] values) {\r\n");
		sb.append("		return (Page<"+shortClassName+">) this.pageQueryHQL(page, queryStr, paramNames,values);\r\n");
		sb.append("	}\r\n\n");
		
     	sb.append("	/** \r\n ");
     	sb.append("	 * 执行自定义的hql\r\n");
     	sb.append("	 */ \r\n ");
		sb.append("	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {\r\n");
		sb.append("		SessionFactory sessionFactory = this.getSessionFactory();\r\n");
		sb.append("		Session session = sessionFactory.openSession();\r\n");
		sb.append("		String hqlStr = queryStr;\r\n");
		sb.append("		Query query = session.createQuery(queryStr);\r\n");
		sb.append("		for (int i=0;i<values.length;i++){\r\n");
		sb.append("			query.setParameter(paramNames[i], values[i]);\r\n");
		sb.append("		}\r\n");
		sb.append("		query.executeUpdate();\r\n");
		sb.append("		session.close();\r\n");
		sb.append("	}\r\n");

		sb.append("}\r\n");
		//System.out.println(sb.toString());
		createFile("dao.impl","DaoImpl",FileType.java,sb.toString());
		return sb.toString();
	}
	
		/**
		 * @说明：
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年5月3日 上午10:18:39
		 */
	public static void createFile(String subPackNmae,String suffix,FileType fileType,String fileContent){
		System.out.println(fileContent.toString());
		if (toFile == false){
			return;
		}
		StringBuilder filePath = new StringBuilder();
		filePath.append(path);
		filePath.append("\\");
		filePath.append(packageName.replace(".", "\\"));
		filePath.append("\\");
		filePath.append(subPackNmae.replace(".", "\\").toLowerCase());
		//String filePath = path + "\" + packageName.replace(".", "\") + "\"+subPackNmae.replace(".", "\").toLowerCase();// 首行引入包名
		System.out.println(filePath.toString());
		//pagekagePathStr = pagekagePathStr + "/" + suffix;// 创建接口的路径
		String fileName = filePath.toString() + "\\" + shortClassName + suffix;
		System.out.println(fileName);
		
		try{
			;
			
			File fileDir = new File(filePath.toString());// 创建文件夹
			if (!fileDir.exists()) {
				fileDir.mkdirs();
			}
			
			File file = new File("");
			if (fileType == FileType.xml) {
				file = new File(fileName + ".xml");
			} else {
				file = new File(fileName + ".java");;// 创建文件
			}
			if (!file.exists()) {
				file.createNewFile();
			}
			byte bytes[] = new byte[10000];
			bytes = fileContent.toString().getBytes();
			int b = fileContent.length();
			try {
				FileOutputStream fos = new FileOutputStream(file);
				//fos.write(bytes, 0, b);
				fos.write(bytes);
				fos.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		}catch(IOException e){
			System.out.println("【错误】:" + e);
		}
	}
	

}
