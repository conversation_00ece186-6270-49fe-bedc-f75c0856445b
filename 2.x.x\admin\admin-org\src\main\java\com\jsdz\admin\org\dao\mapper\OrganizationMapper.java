package com.jsdz.admin.org.dao.mapper;

import java.util.List;
import java.util.Map;
import com.jsdz.admin.org.bean.OrganizationBean;
import com.jsdz.admin.org.model.Organization;
import org.apache.ibatis.annotations.Param;

public interface OrganizationMapper<findById> {
	List<OrganizationBean> findOrgByTopLevel(Map<String,Object>param);
	Integer findOrgByTopLevelCount(Map<String,Object>param);
	
	List<OrganizationBean> findOrgByParent(Map<String,Object>param);
		
	Organization findById(Long id);
	
	Integer updateIndex(@Param("organization")Organization organization);
	
	List<Organization> findByParentId(Long parentId);
	
	
	List<OrganizationBean> findByLikeName(Map<String,Object>param);
	Integer findByLikeNameCount(Map<String,Object>param);
	
	List<Organization> findTopOrganization();
	
}
