package com.jsdz.digitalevidence.alarm.alarm110.dao;

/**
 *
 * @类名: AlarmPoliceDao
 * @说明:
 * @author: kenny
 * @Date 2018-03-25 13:43:48
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmPolice;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface AlarmPoliceDao extends GenericORMEntityDAO<AlarmPolice,Long> {

	//新增
	public void addAlarmPolice(AlarmPolice alarmPolice);

	//修改
	public void updateAlarmPolice(AlarmPolice alarmPolice);

	//删除
	public void deleteAlarmPolice(AlarmPolice alarmPolice);

	//按id查询,结果是游离状态的数据
	public AlarmPolice findAlarmPoliceById(Long id);

	//按id查询
	public AlarmPolice locateAlarmPoliceById(Long id);

	//单个查询
	public AlarmPolice findAlarmPoliceByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmPolice> findAllAlarmPolices();

	//列表查询
	public List<AlarmPolice> findAlarmPolicesByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmPolice> findAlarmPolicesOnPage(Page<AlarmPolice> page,String queryStr,String[] paramNames,Object[] values);

	//执行指定的HQL文件
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);

}

