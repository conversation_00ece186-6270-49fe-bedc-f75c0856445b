package com.jsdz.digitalevidence.analysis.openapi.service;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import com.jsdz.digitalevidence.analysis.model.Version;
/**
 * 动态取得接口实现类
 * 不用版本可以使用不同的实现类
 * <AUTHOR>
 * 2019年11月18日下午2:19:58
 *
 */

@Service
public class GetDsjInfoService implements InitializingBean, ApplicationContextAware{

	private Map<String, DsjInfoService> getDsjInfoServiceImplMap = new HashMap<>();
	private ApplicationContext applicationContext;
	
	public DsjInfoService createService(Version version){
        switch (version){
            case CHECK:return getDsjInfoServiceImplMap.get("DsjInfoCheckServiceImpl");
            case OFFICIAL:return getDsjInfoServiceImplMap.get("DsjInfoServiceImpl");
            default:return getDsjInfoServiceImplMap.get("DsjInfoServiceImpl");
        }
    }

	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext=applicationContext;
	}

	@Override
	public void afterPropertiesSet() throws Exception {
		Map<String, DsjInfoService> beanMap = applicationContext.getBeansOfType(DsjInfoService.class);
        //遍历该接口的所有实现，将其放入map中
        for (DsjInfoService serviceImpl : beanMap.values()) {
        	getDsjInfoServiceImplMap.put(serviceImpl.getClass().getSimpleName(), serviceImpl);
        }
	}

	
}
