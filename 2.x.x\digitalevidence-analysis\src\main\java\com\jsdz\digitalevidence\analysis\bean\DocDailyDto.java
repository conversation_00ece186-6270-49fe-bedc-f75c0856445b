package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: DocDailyDto
 * @说明: 日结表DTO
 *
 * @author: kenny
 * @Date	2017年5月31日下午8:30:44
 * 修改记录：
 *
 * @see
 */
public class DocDailyDto {
	private Long id;
	private Long objectId; //项目ID
	private Long fileSize; // 文件长度;
	private Long fileCount;
	private int cate;//分类
	private int impLevel;//等级
    private Long vedioLen;//视频时长
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getObjectId() {
		return objectId;
	}
	public void setObjectId(Long objectId) {
		this.objectId = objectId;
	}
	public Long getFileSize() {
		return fileSize;
	}
	public void setFileSize(Long fileSize) {
		this.fileSize = fileSize;
	}
	public Long getFileCount() {
		return fileCount;
	}
	public void setFileCount(Long fileCount) {
		this.fileCount = fileCount;
	}
	public int getCate() {
		return cate;
	}
	public void setCate(int cate) {
		this.cate = cate;
	}
	public int getImpLevel() {
		return impLevel;
	}
	public void setImpLevel(int impLevel) {
		this.impLevel = impLevel;
	}
	public Long getVedioLen() {
		return vedioLen;
	}
	public void setVedioLen(Long vedioLen) {
		this.vedioLen = vedioLen;
	}

    
}
