package com.jsdz.admin.security.service.impl;

/**
 * 
 * @类名: SysConfigServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-16 20:48:56
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.admin.security.dao.SysConfigDao;
import com.jsdz.admin.security.model.SysConfig;
import com.jsdz.admin.security.service.SysConfigService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.cache.utils.Constant;
import com.jsdz.digitalevidence.cache.utils.SysSource;
@Service("SysConfigServiceImpl")
public class SysConfigServiceImpl implements SysConfigService {

	@Autowired
	private SysConfigDao sysConfigDao;

	//新增
	public AjaxResult addSysConfig(SysConfig sysConfig) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		
		if (sysConfig == null){
			result.setMsg("没有数据可保存。");
			return result;
		}
		
		if (sysConfig.getSysConfigKey() == null){
			result.setMsg("Key必须输入。");
			return result;
		}

		if (null!=findSysConfigByParam(
					"from SysConfig s where s.sysConfigKey = :sysConfigKey",
					new String[]{"sysConfigKey"},
					new Object[]{sysConfig.getSysConfigKey()})){
				result.setMsg("Key已存在。");
				return result;
			}
		sysConfig.setId(null);
		sysConfigDao.addSysConfig(sysConfig);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		result.setData(sysConfig);
		
		SysSource.getInstance().hset(Constant.VAR_SYSCONFIG, 
				sysConfig.getSysConfigKey(), sysConfig.getSysConfigValue(),0L);
		return result; 
	}

	//修改
	public AjaxResult updateSysConfig(SysConfig sysConfig) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);

		if (sysConfig == null){
			result.setMsg("没有数据可修改。");
			return result;
		}
		
		SysConfig sysConfig1 = sysConfigDao.locateSysConfigById(sysConfig.getId());
		if (sysConfig1 == sysConfigDao.locateSysConfigById(sysConfig.getId())){
			result.setMsg("不存在的参数资料");
			return result;
		}
		
		sysConfig1.setSysConfigValue(sysConfig.getSysConfigValue());
		
		sysConfigDao.updateSysConfig(sysConfig1);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		
		SysSource.getInstance().hset(Constant.VAR_SYSCONFIG, 
				sysConfig1.getSysConfigKey(), sysConfig1.getSysConfigValue(),0L);
		return result; 
	}

	//删除
	public AjaxResult deleteSysConfig(SysConfig sysConfig) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		result.setMsg("暂不提供删除操作。");
		return result;
		
/*		sysConfigDao.deleteSysConfig(sysConfig);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; */
	}

	//按id查询(游离数据)
	public SysConfig findSysConfigById(Long id){

		return sysConfigDao.findSysConfigById(id);
	}

	//按 id 查询
	public SysConfig locateSysConfigById(Long id) {
		return sysConfigDao.locateSysConfigById(id);
	}

	//单个查询
	public SysConfig findSysConfigByParam(String queryStr, String[] paramNames, Object[] values) {
		return sysConfigDao.findSysConfigByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<SysConfig> findAllSysConfigs() {
		return sysConfigDao.findAllSysConfigs();
	}

	//列表查询
	public List<SysConfig> findSysConfigsByParam(String queryStr, String[] paramNames, Object[] values) {
		return sysConfigDao.findSysConfigsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<SysConfig> findSysConfigsOnPage(Page<SysConfig> page, String queryStr, String[] paramNames, Object[] values) {
		Page<SysConfig> pos = sysConfigDao.findSysConfigsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

	@Override
	public SysConfig findSysConfigByKey(String key) {
		
		return sysConfigDao.findSysConfigByCondition(
				"from SysConfig s where s.sysConfigKey=:sysConfigKey",
				new String[]{"sysConfigKey"},
				new Object[]{key});
	}

}
