package com.jsdz.admin.security.dao.impl;
import java.sql.SQLException;
import java.util.Date;
/**
 * 
 * @类名: OperatorDaoImpl
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年4月26日下午9:09:51
 * 修改记录：
 *
 * @see
 */
import java.util.List;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.model.Region;
import com.jsdz.admin.security.dao.OperatorDao;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class OperatorDaoImpl extends GenericEntityDaoHibernateImpl<Operator,Long> implements OperatorDao{
	public Operator findOperatorById(Long id){
		
		final String  hql = "from Operator o where o.isDeleted = false and o.id = :id";
		final Long oid = id;
		Operator data = getHibernateTemplate().execute(new HibernateCallback<Operator>() {      	 
			//@Override  
    	    public Operator doInHibernate(Session session)throws HibernateException, SQLException {  
    	    	// 获取查询对象
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<Operator> list = query.list();
				//session.evict(list);
				Operator rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
    	    }  
    	});
		return data;
		

	}
	
	//按警员id 找操作员
	@Override
	public Operator findOperatorByWorkNum(String workNum){
		
		final String  hql = "from Operator o where o.isDeleted = false  and o.loginName = :workNumber";
		Operator data = getHibernateTemplate().execute(new HibernateCallback<Operator>() {      	 
			//@Override  
    	    public Operator doInHibernate(Session session)throws HibernateException, SQLException {  
    	    	// 获取查询对象
				Query query = session.createQuery(hql);
				query.setParameter("workNumber", workNum);
				List<Operator> list = query.list();
				Operator rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
    	    }  
    	});
		return data;
	}
	
	public List<Operator> findByCondition(String queryStr,String[] paramNames,Object[] values){
		List<Operator> ops = null;	    
		if (values == null || values.length <= 0){
			ops = this.find(queryStr);
	    }else{
			ops = this.findByNamedParam(queryStr,paramNames,values);
	    }
		return ops;
	}
	
	//按id查询
	public Operator locateOperatorById(Long id){
	
		List<Operator> ops = this.findByNamedParam(
				"from Operator  o where o.isDeleted = false  and o.id=:id",
				new String[]{"id"},
				new Object[]{id});
		if (CollectionUtils.isEmpty(ops)){
			return null;
		}
		return ops.get(0);
		
	}

	public void updateOperator(Operator operator) {
		this.saveOrUpdate(operator);
	}
	public void deleteOperator(Operator operator) {
		//删除角色权限表中相关记录
		
		final String  hqlDelete = "delete OperatorRole o where o.operator.id = :id";
		final Long id = operator.getId();
		Long data = getHibernateTemplate().execute(new HibernateCallback<Long>() {      	 
			//@Override  
    	    public Long doInHibernate(Session session)throws HibernateException, SQLException {  
    	    	// 获取查询对象
				Query queryObject = session.createQuery(hqlDelete);
				queryObject.setParameter("id", id);
				queryObject.executeUpdate();
				return null;
    	    }  
    	});
		this.delete(operator);
	}
	
	public void addOperator(Operator operator) {
		this.saveOrUpdate(operator);
		
	}
	public Page<Operator> findOperatorOnPage(Page<Operator> page, String queryStr, String[] paramNames, Object[] values) {
		Page<Operator> lists = null;	    
    	lists = this.pageQueryHQL(page, queryStr, paramNames,values);
    	return lists;
	}

	public Operator getOperatorAndOrg(final Long id) {
		//
		final String hql = "from Operator op left join fetch op.organization org " 
								+ "where op.isDeleted = false  and op.id = :id";
		return getHibernateTemplate().execute(new HibernateCallback<Operator>() {
			public Operator doInHibernate(Session session) throws HibernateException, SQLException {
				// 获取查询对象
				Query queryObject = session.createQuery(hql);
				// 置入参数
				applyNamedParameterToQuery(queryObject, "id", id);
				return (Operator)queryObject.uniqueResult();
			}
		});
	}
	
	public Operator setDeleteFlag(Operator operator){
		Date now = new Date();
		Operator operator1 = this.get(operator.getId());
		operator1.setIsDeleted(true);
		operator1.setUpdateTime(now);
		operator1.setDeleteTime(now);
		operator1.setDeleteBy(operator.getDeleteBy());
		operator1.setDeleteCode(operator1.getLoginName());
		operator1.setLoginName("DELETE_"+ operator1.getLoginName());
		this.merge(operator1);
		return operator1;
	}

}
