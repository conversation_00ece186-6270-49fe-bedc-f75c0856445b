package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: SiteDocDailyDto
 * @说明: 站点拍摄时间查询
 *
 * @author: kenny
 * @Date	2017年6月1日上午8:49:08
 * 修改记录：
 *
 * @see
 */
import java.util.Date;


public class SiteDocDailyDto {
	private Long id;
	private Date dateFrom; //时间段从
	private Date dateTo;//时间到
	private Long objectId;//站点ID
	private String code;//站点编号
	private String name;//站点名称
	private Long fileSize;//文件长度
	private Long vedioLen;//总时长
	private Long fileCount;//文件数量
	
	private Long vedeoSize;//视频时长
	private Long audioSize;//音频时长
	private Long picCount;//照片数量
	private Long txtCount;//日志数量
	private Long importanceCount;//重要文件 数量
	private Long generalCount;//一般文件 数量
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Date getDateFrom() {
		return dateFrom;
	}
	public void setDateFrom(Date dateFrom) {
		this.dateFrom = dateFrom;
	}
	public Date getDateTo() {
		return dateTo;
	}
	public void setDateTo(Date dateTo) {
		this.dateTo = dateTo;
	}
	public Long getObjectId() {
		return objectId;
	}
	public void setObjectId(Long objectId) {
		this.objectId = objectId;
	}

	public Long getFileSize() {
		return fileSize;
	}
	public void setFileSize(Long fileSize) {
		this.fileSize = fileSize;
	}
	public Long getVedioLen() {
		return vedioLen;
	}
	public void setVedioLen(Long vedioLen) {
		this.vedioLen = vedioLen;
	}
	public Long getFileCount() {
		return fileCount;
	}
	public void setFileCount(Long fileCount) {
		this.fileCount = fileCount;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Long getPicCount() {
		return picCount;
	}
	public void setPicCount(Long picCount) {
		this.picCount = picCount;
	}
	public Long getTxtCount() {
		return txtCount;
	}
	public void setTxtCount(Long txtCount) {
		this.txtCount = txtCount;
	}
	public Long getImportanceCount() {
		return importanceCount;
	}
	public void setImportanceCount(Long importanceCount) {
		this.importanceCount = importanceCount;
	}
	public Long getGeneralCount() {
		return generalCount;
	}
	public void setGeneralCount(Long generalCount) {
		this.generalCount = generalCount;
	}
	public Long getVedeoSize() {
		return vedeoSize;
	}
	public void setVedeoSize(Long vedeoSize) {
		this.vedeoSize = vedeoSize;
	}
	public Long getAudioSize() {
		return audioSize;
	}
	public void setAudioSize(Long audioSize) {
		this.audioSize = audioSize;
	}
	
	
}
