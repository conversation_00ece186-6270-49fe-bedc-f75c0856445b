# Default Properties file for use by StdSchedulerFactory
# to create a Quartz Scheduler Instance, if a different
# properties file is not explicitly specified.
#

org.quartz.scheduler.instanceId = AUTO
org.quartz.scheduler.instanceName = DefaultQuartzScheduler
org.quartz.scheduler.rmi.export = false
org.quartz.scheduler.rmi.proxy = false
org.quartz.scheduler.wrapJobExecutionInUserTransaction = false

org.quartz.threadPool.class = org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount = 10
org.quartz.threadPool.threadPriority = 5
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread = true

# for test
org.quartz.jobStore.misfireThreshold = 60000

# ram jobstore
# org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore

# jdbc jobstore
org.quartz.jobStore.class = org.quartz.impl.jdbcjobstore.JobStoreTX
org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.StdJDBCDelegate
org.quartz.jobStore.useProperties = false  
org.quartz.jobStore.dataSource = mysql 
org.quartz.jobStore.tablePrefix = qrtz_     
org.quartz.jobStore.isClustered = true 
org.quartz.jobStore.clusterCheckinInterval = 20000 

#============================================================================       
# Configure Datasources         
#============================================================================       
org.quartz.dataSource.mysql.driver = com.mysql.jdbc.Driver
org.quartz.dataSource.mysql.URL = ******************************************************************************************************************************************************************
org.quartz.dataSource.mysql.user = root
org.quartz.dataSource.mysql.password = jsdz1234
org.quartz.dataSource.mysql.maxConnections = 30
  