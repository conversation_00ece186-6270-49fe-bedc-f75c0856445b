package com.jsdz.digitalevidence.activemq.utils;
import net.sf.json.JSON;
import net.sf.json.JSONArray;
/**
 * 
 * @类名: JsonParse
 * @说明: 遍历JSON对象数据
 *
 * @author: kenny
 * @Date	2018年2月1日下午11:16:35
 * 修改记录：
 *
 * @see
 */
import net.sf.json.JSONObject;
import java.util.Iterator;

import com.google.gson.Gson;

public class JsonParse {
	/**
	 * 深度遍历
	 * 复合的Json结构数据，JSON对象里面可以嵌套多层对象(数组或对象)
	 * @return 
	 */
	
	
	private static boolean isJsonStr(Object value){
		try {
	        JSONObject jsonStr= JSONObject.fromObject(value);
	        return  true;
	   } catch (Exception e) {
	        return false;
	  }
	}
	
	public static Object getValue(Object jsonObject,String keyStr){
		Object result = null;
		if(jsonObject == null || jsonObject.toString().equals("")){  
			return null;  
	    }

		JSONObject jsonStr=null;
		JSON json=null;
		try{
			jsonStr = JSONObject.fromObject(jsonObject);
	        json= (JSON)jsonStr;
		}catch(Exception e){
			System.out.println(e.toString());
			return null;
		}
		System.out.println("jsonObject:" + jsonObject.toString());
		if (!json.isArray()){//json 是一个map
			Iterator it = jsonStr.keys();  
	        while(it.hasNext()){
	        	   //获取map的key  
		           String key = (String) it.next(); 
		           Object value = jsonStr.get(key); 
		           if (key.equalsIgnoreCase(keyStr)){
		        	   result = value;
		        	   break;
		           }else{
			           //递归遍历  
			           result =  getValue(value,keyStr);  
		           }
		           if (result != null)
		        	   break;
	        }
		}
		return result; 
	}
	/**
	 * 深层查找指定的key,是为排除多层对象有相同的key
	 * parentKey为""
	 * @param json
	 * @param parentKey
	 * @param keyStr
	 * @return
	 */
	public static Object getValue(Object jsonObject,String parentKey,String keyStr){
		Object result = null;
		if(jsonObject == null || jsonObject.toString().equals("")){  
			return null;  
	    }

		JSONObject jsonStr=null;
		JSON json=null;
		try{
			jsonStr = JSONObject.fromObject(jsonObject);
	        json= (JSON)jsonStr;
		}catch(Exception e){
			System.out.println(e.toString());
			return null;
		}
		System.out.println("jsonObject:" + jsonObject.toString());
		if (!json.isArray()){//json 是一个map
			Iterator it = jsonStr.keys();  
	        while(it.hasNext()){
	        	   //获取map的key  
		           String key = (String) it.next(); 
		           String fullKey = parentKey==null || parentKey.equals("")?key:parentKey + "." + key;
		           Object value = jsonStr.get(key); 
		           if (fullKey.equalsIgnoreCase(keyStr)){
		        	   result = value;
		        	   break;
		           }else{
			           //递归遍历  
			           result =  getValue(value,fullKey,keyStr);  
		           }
		           if (result != null)
		        	   break;
	        }
		}
		return result;
	}
	
	
	//遍历数组json
	private static Object traverseArrayJson(Object jsonObject){
		JSONArray jsonAry = new JSONArray();  
        JSONArray jsonStr = (JSONArray) jsonObject;  
        //获取Array 的长度  
        int length = jsonStr.size();//length();  
        for (int i = 0; i <length; i++) {  
            jsonAry.add(traverseArrayJson(jsonStr.get(i)));  
        }  
        return jsonAry; 
	}
	

/*		Gson gson=new Gson();
	result=gson.fromJson(json, JSONObject.class);
*/		

}

