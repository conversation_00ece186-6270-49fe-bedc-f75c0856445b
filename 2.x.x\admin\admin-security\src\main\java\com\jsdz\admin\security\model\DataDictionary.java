package com.jsdz.admin.security.model;

import java.util.Date;

/**
 * 
 * @类名: DataDictionary
 * @说明: 数据字典
 *
 * @author: kenny
 * @Date	2017年10月31日上午11:10:32
 * 修改记录：
 *
 * @see
 */
public class DataDictionary {
	private Long id;
	private String code; //编号
	private String name; //名称
	private String note; //描述,备注
	private Integer order; //显示顺序
	private Date createTime;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public Integer getOrder() {
		return order;
	}
	public void setOrder(Integer order) {
		this.order = order;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	
	
}
