package com.jsdz.digitalevidence.archives.service.impl;


import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.archives.bean.ArchivesDataBean;
import com.jsdz.digitalevidence.archives.bean.param.ArchivesParam;
import com.jsdz.digitalevidence.archives.dao.ArchivesDataDao;
import com.jsdz.digitalevidence.archives.service.ArchivesDataService;
import com.jsdz.digitalevidence.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Service("ArchivesDataServiceImpl")
public class ArchivesDataServiceImpl  implements ArchivesDataService {

    @Autowired
    private ArchivesDataDao archivesDataDao;


    @Override
    public ArchivesDataBean archivesById(Long archivesDataBean) {
        if (StringUtils.isNoBlank(archivesDataBean))
            return archivesDataDao.archivesById(archivesDataBean);
        else
            return null;

    }

    @Override
    public boolean getArchivesCode(String archivesCode) {
        if (StringUtils.isNoBlank(archivesCode))
            return archivesDataDao.getArchivesCode(archivesCode);
        else
            return false;
    }

    @Override
    public List<ArchivesDataBean> getArchivesByCode(String archivesCode) {
        return archivesDataDao.getArchivesByCode(archivesCode);
    }

    @Override
    public boolean update(ArchivesDataBean archivesDataBean) {
        try {
            archivesDataDao.update(archivesDataBean);
            return true;
        }catch (Exception e){
            return false;
        }
    }
    @Override
    public boolean save(ArchivesDataBean archivesDataBean) {
        try {
            archivesDataDao.insert(archivesDataBean);
            return true;
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }

    }

    @Override
    public Page<ArchivesDataBean> findArchivesByPage(ArchivesParam archivesParam, PerLvlBean p) {
        return null;// archivesDataDao.findArchivesByPage(archivesParam,p);
    }

    @Override
    public Page<ArchivesDataBean> findArchivesOnPage(Page<ArchivesDataBean> page, String queryStr, String[] paramNames, Object[] values) {
        return archivesDataDao.pageQueryHQL(page, queryStr, paramNames, values);
    }



}
