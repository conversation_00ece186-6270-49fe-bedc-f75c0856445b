/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.dao;

import java.util.List;

import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.approve.model.ApproveBusiness;

/**
 * @类名: ApproveBusinessDao
 * @说明: 审批业务dao
 *
 * <AUTHOR>
 * @Date	 2017年4月17日 上午11:32:38
 * 修改记录：
 *
 * @see 	 
 */
public interface ApproveBusinessDao extends GenericORMEntityDAO<ApproveBusiness, Long> {

	public List<ApproveBusiness> getAllApproveBusiness();
	
}
