/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.model;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: AproveBusiness
 * @说明: 审批业务
 *
 * <AUTHOR>
 * @Date	 2017年4月17日 上午10:59:23
 * 修改记录：
 *
 * @see 	 
 */
public class ApproveBusiness extends AbstractDTO {
	
	/** ID*/
	private Long id;
	/** 业务编码*/
	private String code;
	/** */
	private String uri;
	/** 描述*/
	private String comment;
	
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getComment() {
		return comment;
	}
	public void setComment(String comment) {
		this.comment = comment;
	}
	public String getUri() {
		return uri;
	}
	public void setUri(String uri) {
		this.uri = uri;
	}
	
}
