package com.jsdz.digitalevidence.statistics.model;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SYS_ABNORMAL_STATISTICS")
public class SysAbnormalStatisticsModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    @TableField("TYPECODE")
    private Integer typecode;

    @TableField("TYPENAME")
    private String typename;

    @TableField("ABNORMALDOC")
    private String abnormaldoc;

    @TableField("ORGNAME")
    private String orgname;

    @TableField("ORGCODE")
    private String orgcode;

    @TableField("ORGPATH")
    private String orgpath;

    @TableField("DESCRIBEDETAILS")
    private String describedetails;

    @TableField("RECORDERCODE")
    private String recordercode;

    @TableField("SITECODE")
    private String sitecode;

    @TableField("SITENAME")
    private String sitename;

    @TableField("USERCODE")
    private String usercode;

    @TableField("USERNAME")
    private String username;

    @TableField(value = "CREATETIME", fill = FieldFill.INSERT)
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createtime;


}
