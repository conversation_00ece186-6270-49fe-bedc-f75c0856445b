package com.jsdz.digitalevidence.assessment.service.impl;

/**
 * 
 * @类名: AssessmentPlanPoliceServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-06-21 16:28:56
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;

import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanDao;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanPoliceDao;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlan;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanDocment;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPolice;
import com.jsdz.digitalevidence.assessment.service.AssessmentPlanPoliceService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

@Service("AssessmentPlanPoliceServiceImpl")
public class AssessmentPlanPoliceServiceImpl implements AssessmentPlanPoliceService {

	@Autowired
	private AssessmentPlanPoliceDao assessmentPlanPoliceDao;
	@Autowired
	private AssessmentPlanDao assessmentPlanDao;

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addAssessmentPlanPolice(AssessmentPlanPolice assessmentPlanPolice) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			String queryStr = " from AssessmentPlanPolice d "
					+ " where d.planId = :planId and d.police.id = :policeId ";
			
			List<AssessmentPlanPolice> list = 
			assessmentPlanPoliceDao.findAssessmentPlanPolicesByCondition(queryStr,
					new String[]{"planId","policeId"},
					new Object[]{assessmentPlanPolice.getId(),assessmentPlanPolice.getPolice().getId()});
			
			if (list != null && list.size() > 0){
				result.setMsg("重复的数据，不能保存");
				return result;
			}

			assessmentPlanPoliceDao.addAssessmentPlanPolice(assessmentPlanPolice);
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("新增保存成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updateAssessmentPlanPolice(AssessmentPlanPolice assessmentPlanPolice) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			assessmentPlanPoliceDao.updateAssessmentPlanPolice(assessmentPlanPolice);
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("修改成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deleteAssessmentPlanPolice(AssessmentPlanPolice assessmentPlanPolice) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			assessmentPlanPolice = assessmentPlanPoliceDao.locateAssessmentPlanPoliceById(assessmentPlanPolice.getId());
			if (assessmentPlanPolice == null){
				result.setMsg("不存在的数据，不能删除");
				return result;
			}
			
			AssessmentPlan assessmentPlan = assessmentPlanDao.findAssessmentPlanById(assessmentPlanPolice.getPlanId());
			if (assessmentPlan.getStatus() != 0){
				result.setMsg("方案不是初始状态，不能删除");
				return result;
				
			}
			
			assessmentPlanPoliceDao.deleteAssessmentPlanPolice(assessmentPlanPolice);
			result.setSuccess(true);
			result.setCode(200);
			result.setMsg("删除成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 按id查询(游离数据)
	 */ 
 	public AssessmentPlanPolice findAssessmentPlanPoliceById(Long id){

		return assessmentPlanPoliceDao.findAssessmentPlanPoliceById(id);
	}

	/** 
 	 * 按 id 查询
	 */ 
 	public AssessmentPlanPolice locateAssessmentPlanPoliceById(Long id) {
		return assessmentPlanPoliceDao.locateAssessmentPlanPoliceById(id);
	}

	/** 
 	 * 单个查询
	 */ 
 	public AssessmentPlanPolice findAssessmentPlanPoliceByParam(String queryStr, String[] paramNames, Object[] values) {
		return assessmentPlanPoliceDao.findAssessmentPlanPoliceByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanPolice> findAllAssessmentPlanPolices() {
		return assessmentPlanPoliceDao.findAllAssessmentPlanPolices();
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanPolice> findAssessmentPlanPolicesByParam(String queryStr, String[] paramNames, Object[] values) {
		return assessmentPlanPoliceDao.findAssessmentPlanPolicesByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanPolice> findAssessmentPlanPolicesOnPage(Page<AssessmentPlanPolice> page, String queryStr, String[] paramNames, Object[] values) {
		Page<AssessmentPlanPolice> pos = assessmentPlanPoliceDao.findAssessmentPlanPolicesOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
