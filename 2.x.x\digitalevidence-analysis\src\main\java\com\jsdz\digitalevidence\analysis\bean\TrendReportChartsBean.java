package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: TrendReportChartsBean
 * @说明: 系统简报图表封装类
 *
 * @author: kenny
 * @Date	2018年3月28日下午3:18:00
 * 修改记录：
 *
 * @see
 */

import java.util.ArrayList;
import java.util.List;

public class TrendReportChartsBean {
	
	List<String> codes; //项目，单位或日期
	List<Integer> fileCounts;//文件数量
	List<Integer> vedioCounts;//视频数量
	List<Integer> vedioQualifys;//视频质量
	
	List<Double> sumDurations ; //视频时长(分)
	List<Double> sumHdDurations;//高清时长(分)
	List<Double> alarmRates;//警情关联率
	List<Double> caseRates;//案件关联率
	
	public TrendReportChartsBean(){
		codes = new ArrayList<String>();
		fileCounts = new ArrayList<Integer>();
		vedioCounts = new ArrayList<Integer>();
		vedioQualifys = new ArrayList<Integer>();
		
		sumDurations = new ArrayList<Double>();
		sumHdDurations = new ArrayList<Double>();
		alarmRates = new ArrayList<Double>();
		caseRates = new ArrayList<Double>();
	}
	public void assign(TrendReport src){
		this.getCodes().add(src.getObjCode());
		this.getFileCounts().add(src.getFileCount()==null?0:src.getFileCount().intValue());
		this.getVedioCounts().add(src.getVedioCount()==null?0:src.getVedioCount().intValue());
		this.getVedioQualifys().add(src.getVedioQualify()==null?0:src.getVedioQualify().intValue());
		
		double douValue = 0;
		douValue = src.getSumDuration()==null?0:this.secondToMin(src.getSumDuration());  
		this.getSumDurations().add(douValue);
		douValue = src.getSumHdDuration()==null?0:this.secondToMin(src.getSumHdDuration()); 
		this.getSumHdDurations().add(douValue);
		douValue = src.getAlarmRate()==null?0:this.rounded(src.getAlarmRate(), 2); 
		this.getAlarmRates().add(douValue);
		douValue = src.getCaseRate()==null?0:this.rounded(src.getCaseRate(), 2); 
		this.getCaseRates().add(douValue);
	}
	
	//秒转为分
	private double secondToMin(Double value){
		double result = 0;
		if (value != null){
			result = this.rounded(value/60.0, 0);
		}
		return result;
	}
	
	/*
	 * 四舍五入
	 * bitNum  小数位
	 */
	private Double rounded(Double value,int bitNum){
		double result = 0;
		double stepval = 1.0;
		for (int i=0;i<bitNum;i++){
			stepval = stepval * 10;
		}
		if (value != null){
			result = Math.round(value*stepval)/stepval;
		}
		return result;
	}
	
	public List<String> getCodes() {
		return codes;
	}
	public void setCodes(List<String> codes) {
		this.codes = codes;
	}
	public List<Integer> getFileCounts() {
		return fileCounts;
	}
	public void setFileCounts(List<Integer> fileCounts) {
		this.fileCounts = fileCounts;
	}
	public List<Integer> getVedioCounts() {
		return vedioCounts;
	}
	public void setVedioCounts(List<Integer> vedioCounts) {
		this.vedioCounts = vedioCounts;
	}
	public List<Integer> getVedioQualifys() {
		return vedioQualifys;
	}
	public void setVedioQualifys(List<Integer> vedioQualifys) {
		this.vedioQualifys = vedioQualifys;
	}
	public List<Double> getSumDurations() {
		return sumDurations;
	}
	public void setSumDurations(List<Double> sumDurations) {
		this.sumDurations = sumDurations;
	}
	public List<Double> getSumHdDurations() {
		return sumHdDurations;
	}
	public void setSumHdDurations(List<Double> sumHdDurations) {
		this.sumHdDurations = sumHdDurations;
	}
	public List<Double> getAlarmRates() {
		return alarmRates;
	}
	public void setAlarmRates(List<Double> alarmRates) {
		this.alarmRates = alarmRates;
	}
	public List<Double> getCaseRates() {
		return caseRates;
	}
	public void setCaseRates(List<Double> caseRates) {
		this.caseRates = caseRates;
	}
	
	




}
