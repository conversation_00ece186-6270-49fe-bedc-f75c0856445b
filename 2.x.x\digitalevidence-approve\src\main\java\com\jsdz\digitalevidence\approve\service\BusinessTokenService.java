/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.service;

import java.util.List;
import java.util.Map;

import com.jsdz.core.AjaxResult;
import com.jsdz.digitalevidence.approve.model.BusinessToken;

/**
 * @类名: BusinessTokenService
 * @说明: 业务令牌服务接口
 *
 * <AUTHOR>
 * @Date	 2017年11月17日 上午11:16:03
 * 修改记录：
 *
 * @see 	 
 */
public interface BusinessTokenService {
	
	/**
	 * @说明：检查令牌是否适用
	 *
	 * <AUTHOR>
	 * @param token
	 * @return
	 * 
	 */
	public AjaxResult checkToken(Map<String, Object> param, Long tokenId);
	
	/**
	 * @说明：获取使用令牌
	 *
	 * <AUTHOR>
	 * @param param
	 * @return
	 * 
	 */
	public List<BusinessToken> getValidToken(Map<String, Object> param);
	
}
