package com.jsdz.digitalevidence.assessment.test;

import javax.annotation.Resource;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.cycle.CycleConfiguration;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: CycleTest
 * @说明: 周期单元测试
 *
 * <AUTHOR>
 * @Date	 2017年4月24日 下午2:00:03
 * 修改记录：
 *
 * @see 	 
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations={
		"/testApplicationContext-common.xml",
		"/testApplicationContext-cycle.xml",
		"/testApplicationContext-dynsql.xml",
		"/testApplicationContext-scheduler.xml",
		"/testApplicationContext-reportengine.xml",
		"/testApplicationContext-documenttype.xml"})
public class CycleTest {

	@Resource(name="assessmentCycleConfiguration")
	private CycleConfiguration cc;
	
	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
	}
	
	// 获取当前日期所在周期
	@Test
	@Transactional
	public void testGetCurCycle() {
		Cycle cycle = cc.getCurrentCycle();
		printCycle(cycle);
	}
	
	// 获取当前日期所在周期
	@Test
	@Transactional
	public void testNextCycle() {
		Cycle cycle = cc.getCurrentCycle();
		Cycle next = cc.nextCycle(cycle);
		printCycle(next);
	}
		
	private void printCycle(Cycle cycle) {
		System.out.println("start:" + DateTimeUtils.DateToString(cycle.getStart()));
		System.out.println("end:" + DateTimeUtils.DateToString(cycle.getEnd()));
	}
}
