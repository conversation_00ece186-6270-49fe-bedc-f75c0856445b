package com.jsdz.digitalevidence.statistics.service.impl;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.bean.param.SysDailyDeviceParam;
import com.jsdz.digitalevidence.statistics.bean.param.VEquipmentOnlineStatisticsParam;
import com.jsdz.digitalevidence.statistics.mapper.SysDailyDeviceMapper;
import com.jsdz.digitalevidence.statistics.model.SysDailyDeviceModel;
import com.jsdz.digitalevidence.statistics.model.VEquipmentOnlineStatisticsModel;
import com.jsdz.digitalevidence.statistics.mapper.VEquipmentOnlineStatisticsMapper;
import com.jsdz.digitalevidence.statistics.service.IVEquipmentOnlineStatisticsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsdz.digitalevidence.utils.StringUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * VIEW 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Service
public class VEquipmentOnlineStatisticsServiceImpl extends ServiceImpl<VEquipmentOnlineStatisticsMapper, VEquipmentOnlineStatisticsModel> implements IVEquipmentOnlineStatisticsService {

    @Resource
    private VEquipmentOnlineStatisticsMapper equipmentOnlineStatisticsMapper;

//    @Override
//    public Page<VEquipmentOnlineStatisticsModel> getDatas(VEquipmentOnlineStatisticsParam param, PerLvlBean permLvl) {
//        Page<VEquipmentOnlineStatisticsModel> page = param.getPage();
//
//        String orgPath = param.getOrgPath();
//        if(StringUtils.isEmpty(orgPath)) {
//            param.setOrgPath(permLvl.getOrgPath());
//            Integer permissionLevel = permLvl.getPermissionLevel();
//            param.setIncludeSub((permissionLevel<3?0:1)+"");
//        }
//        List<VEquipmentOnlineStatisticsModel> vAttendanceAllModels =equipmentOnlineStatisticsMapper.selectModels(param);
//
//        vAttendanceAllModels.forEach((T)-> {
//                    T.setZongdocdurationall(StringUtils.getTimeString(T.getZongdocdurationall()));
//                    T.setDocdurationall(StringUtils.getTimeString(T.getDocdurationall()));
//                });
//            Integer total =equipmentOnlineStatisticsMapper.selectModelCount(param);
//        page.setTotal(total);
//        page.setRows(vAttendanceAllModels);
//        return page;
//    }


    public Page<VEquipmentOnlineStatisticsModel> getDatas(VEquipmentOnlineStatisticsParam param, PerLvlBean permLvl) {
        Page<VEquipmentOnlineStatisticsModel> page = param.getPage();

        String orgPath = param.getOrgPath();
        if (StringUtils.isEmpty(orgPath)) {
            param.setOrgPath(permLvl.getOrgPath());
            Integer permissionLevel = permLvl.getPermissionLevel();
            param.setIncludeSub((permissionLevel < 3 ? 0 : 1) + "");
        }
        // 使用CompletableFuture并行执行两个查询
        CompletableFuture<List<VEquipmentOnlineStatisticsModel>> modelsFuture = CompletableFuture.supplyAsync(() -> {
            return equipmentOnlineStatisticsMapper.selectModels(param);
        });
        CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> {
            return equipmentOnlineStatisticsMapper.selectModelCount(param);
        });
        // 等待两个操作完成，并获取结果
        try {
            List<VEquipmentOnlineStatisticsModel> vAttendanceAllModels = modelsFuture.get();
            Integer total = countFuture.get();
            // 处理模型数据
            vAttendanceAllModels.forEach(model -> {
                model.setZongdocdurationall(StringUtils.getTimeString(model.getZongdocdurationall()));
                model.setDocdurationall(StringUtils.getTimeString(model.getDocdurationall()));
            });

            // 更新Page对象
            page.setTotal(total); // 假设Page使用setTotalElements而不是setTotal
            page.setRows(vAttendanceAllModels); // 假设Page使用setContent而不是setRows

            return page;
        } catch (InterruptedException | ExecutionException e) {
            // 处理异常
            throw new RuntimeException("Error occurred while fetching data asynchronously", e);
        }
    }


}
