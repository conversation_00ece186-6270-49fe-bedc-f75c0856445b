package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: SysConfigDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-16 20:48:56
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.SysConfigDao;
import com.jsdz.admin.security.model.SysConfig;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class SysConfigDaoImpl extends GenericEntityDaoHibernateImpl<SysConfig,Long> implements SysConfigDao{

	//新增
	public void addSysConfig(SysConfig sysConfig) {
		this.saveOrUpdate(sysConfig);
	}

	//删除
	public void deleteSysConfig(SysConfig sysConfig) {
		this.delete(sysConfig);
	}

	//修改
	public void updateSysConfig(SysConfig sysConfig) {
		this.merge(sysConfig);
	}

	//按id查询(游离状态)
	public SysConfig findSysConfigById(Long id){
		final String  hql = "from SysConfig s where s.id = :id";
		final Long oid = id;
		SysConfig data = getHibernateTemplate().execute(new HibernateCallback<SysConfig>() {
			public SysConfig doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<SysConfig> list = query.list();
				SysConfig rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public SysConfig locateSysConfigById(Long id){

		final String  hql = "from SysConfig s where s.id = :id";
		final Long oid = id;
		SysConfig data = getHibernateTemplate().execute(new HibernateCallback<SysConfig>() {
			public SysConfig doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<SysConfig> list = query.list();
				SysConfig rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public SysConfig findSysConfigByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		SysConfig data = getHibernateTemplate().execute(new HibernateCallback<SysConfig>() {
		public SysConfig doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<SysConfig> list = query.list();
			SysConfig rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<SysConfig> findAllSysConfigs(){
		return this.find("from SysConfig sysConfig ");
	}

	//列表查询
	public List<SysConfig> findSysConfigsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<SysConfig> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<SysConfig> findSysConfigsOnPage(Page<SysConfig> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<SysConfig>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
