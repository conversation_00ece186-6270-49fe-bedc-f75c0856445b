package com.jsdz.admin.security.bean;

import java.util.List;

/**
 * 
 * @类名: MenuMainTreeBean
 * @说明: 树型菜单封装类，为文便前端设置多页模式,类似EasyUI
 *
 * @author: kenny
 * @Date	2018年4月16日下午7:34:03
 * 修改记录：
 *
 * @see
 */

public class MenuMainTreeBean {
	
	private String id;
	private String text;
	private String icon;
	private String url;
	//应前端要求加两个字段
/*	private Boolean selected = false;
	private Boolean spread = false;
*/	
	private List<MenuMainTreeBean> menus;
	
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}
	public String getIcon() {
		return icon;
	}
	public void setIcon(String icon) {
		this.icon = icon;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public List<MenuMainTreeBean> getMenus() {
		return menus;
	}
	public void setMenus(List<MenuMainTreeBean> menus) {
		this.menus = menus;
	}



}
