package com.jsdz.digitalevidence.archives.service.impl;

import com.jsdz.digitalevidence.archives.model.ArchivesRelation;
import com.jsdz.digitalevidence.archives.mapper.ArchivesRelationMapper;
import com.jsdz.digitalevidence.archives.service.IArchivesRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-11
 */
@Service
public class ArchivesRelationServiceImpl implements IArchivesRelationService {

    @Autowired
    private  ArchivesRelationMapper archivesRelationMapper;

    @Override
    public boolean removeByRid(Long rid) {
        try {
            archivesRelationMapper.removeByRid(rid);
            return true ;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    @Override
    public List<ArchivesRelation> findArchivesDoc(Long archivesId, String docId) {
        return archivesRelationMapper.findArchivesDoc( archivesId, docId);
    }

    @Override
    public boolean saveArchivesRelationsBatch(List<ArchivesRelation> archivesRelations) {
        try {
            for (ArchivesRelation relation:
                    archivesRelations) {
                archivesRelationMapper.insertRelation(relation.getArchivesId(),relation.getDocId(),relation.getRelationUserId(),relation.getRelationUserName());
            }
            return true;
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }

    }
}
