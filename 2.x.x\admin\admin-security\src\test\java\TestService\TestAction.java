package TestService;
/**
 * 
 * @类名: TestAction.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月19日上午9:30:46
 * 修改记录：
 *
 * @see
 */

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Action;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleAction;
import com.jsdz.admin.security.service.ActionService;
import com.jsdz.admin.security.service.RoleActionService;
import com.jsdz.core.AjaxResult;

@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { "classpath:/testApplicationContext-common.xml" })

public class TestAction {
	@Autowired
	private ActionService actionService;
	@Autowired
	private RoleActionService roleActionService;
	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}

	//@Test
	@Transactional
	public void testAddAction()
	{
		//Action action1 = new Action("操作员管理","MENU_OPERATOR_MANAGE",1);
		Action action2 = new Action("员工管理","MENU_EMPLOYEES_MANAGE",2);
		Action action3 = new Action("角色管理","MENU_ROLE_MANAGE",2);
		Action action4 = new Action("操作日志","MENU_OPERATELOG_MANAGE",2);
		Action action5 = new Action("区域管理","MENU_REGION_MANAGE",2);
		Action action6 = new Action("单位管理","MENU_ORGANIZATION_MANAGE",2);
		Action action7 = new Action("部门管理","MENU_DEPARTMENT_MANAGE",2);
		Action action8 = new Action("职位管理","MENU_POSITION_MANAGE",2);
		
		//AjaxResult result = actionService.addAction(action1);
		//System.out.println("*** " + result.isSuccess() + "/" + result.getMsg());
		AjaxResult result = actionService.addAction(action2);
		System.out.println("*** " + result.isSuccess() + "/" + result.getMsg());
		result = actionService.addAction(action3);
		System.out.println("*** " + result.isSuccess() + "/" + result.getMsg());
		result = actionService.addAction(action4);
		System.out.println("*** " + result.isSuccess() + "/" + result.getMsg());
		result = actionService.addAction(action5);
		System.out.println("*** " + result.isSuccess() + "/" + result.getMsg());
		result = actionService.addAction(action6);
		System.out.println("*** " + result.isSuccess() + "/" + result.getMsg());
		result = actionService.addAction(action7);
		System.out.println("*** " + result.isSuccess() + "/" + result.getMsg());
		result = actionService.addAction(action8);
	}
	@Test
	@Transactional
	public void testAddRoleAction(){
		Action action = new Action();
		action.setId(9L);
		Role role = new Role();
		role.setId(1L);
		
		RoleAction roleAction = new RoleAction();
		roleAction.setAction(action);
		roleAction.setRole(role);
		roleActionService.addRoleAction(roleAction);
		
	}
	
}
