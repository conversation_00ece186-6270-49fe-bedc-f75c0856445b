package com.jsdz.digitalevidence.archives.service;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.archives.bean.ArchivesDataBean;

import com.jsdz.digitalevidence.archives.bean.param.ArchivesParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */

public interface ArchivesDataService {


    ArchivesDataBean archivesById(Long archivesDataBean);

    boolean update(ArchivesDataBean archivesDataBean);

    boolean save(ArchivesDataBean archivesDataBean);

    Page<ArchivesDataBean> findArchivesByPage(ArchivesParam archivesParam, PerLvlBean currentOperator);

    Page<ArchivesDataBean> findArchivesOnPage(Page<ArchivesDataBean> page, String queryStr, String[] paramNames, Object[] values);

    boolean getArchivesCode(String archivesCode);

    List<ArchivesDataBean> getArchivesByCode(String archivesCode);
}
