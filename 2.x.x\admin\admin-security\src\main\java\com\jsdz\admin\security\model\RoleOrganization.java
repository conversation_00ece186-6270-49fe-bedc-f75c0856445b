package com.jsdz.admin.security.model;

import java.io.Serializable;

import com.jsdz.admin.org.model.Organization;

/**
 * 
 * @类名: RoleOrganization
 * @说明: 角色单位，一个角色可以拥有多个单位的数据操作权限，
 * 权限级别1，只查看本人的资料，2只查看本单位的资料，3本单位级下级单位的资料
 *
 * @author: kenny
 * @Date	2018年5月15日下午4:17:47
 * 修改记录：
 *
 * @see
 */
public class RoleOrganization implements Serializable{

	
	/**
	 * 
	 */
	private static final long serialVersionUID = -4918669531248394811L;
	
	private Long id;
	private Role role;
	private Organization organization;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Role getRole() {
		return role;
	}
	public void setRole(Role role) {
		this.role = role;
	}
	public Organization getOrganization() {
		return organization;
	}
	public void setOrganization(Organization organization) {
		this.organization = organization;
	}
	


}
