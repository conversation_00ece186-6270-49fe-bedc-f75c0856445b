package com.jsdz.digitalevidence.archives.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RelationCount implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long parentId;

    private String orgname;

    private String orgcode;

    private String path;
    private Integer sumdoc;
    private Integer docnoarchives;
    private Integer docarchives;
    private Integer alarmrelation;
    private Integer caserelation;



}
