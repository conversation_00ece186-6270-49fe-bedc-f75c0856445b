package com.jsdz.digitalevidence.analysis.dao.impl;

/**
 *
 * @类名: DailyLogDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-31 15:33:22
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;

import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.analysis.dao.DailyLogDao;
import com.jsdz.digitalevidence.analysis.model.DailyLog;

@Repository
public class DailyLogDaoImpl extends GenericEntityDaoHibernateImpl<DailyLog,Long> implements DailyLogDao{

	//新增
	public void addDailyLog(DailyLog dailyLog) {
		this.saveOrUpdate(dailyLog);
	}

	//删除
	public void deleteDailyLog(DailyLog dailyLog) {
		this.delete(dailyLog);
	}

	//修改
	public void updateDailyLog(DailyLog dailyLog) {
		this.merge(dailyLog);
	}

	//按id查询(游离状态)
	public DailyLog findDailyLogById(Long id){

		final String  hql = "from DailyLog d where d.id = :id";
		final Long oid = id;
		DailyLog data = getHibernateTemplate().execute(new HibernateCallback<DailyLog>() {
			public DailyLog doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DailyLog> list = query.list();
				DailyLog rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public DailyLog locateDailyLogById(Long id){

		final String  hql = "from DailyLog d where d.id = :id";
		final Long oid = id;
		DailyLog data = getHibernateTemplate().execute(new HibernateCallback<DailyLog>() {
			public DailyLog doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DailyLog> list = query.list();
				DailyLog rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public DailyLog findDailyLogByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		DailyLog data = getHibernateTemplate().execute(new HibernateCallback<DailyLog>() {
		public DailyLog doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<DailyLog> list = query.list();
			DailyLog rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<DailyLog> findAllDailyLogs(){
		return this.find("from DailyLog dailyLog ");
	}

	//列表查询
	public List<DailyLog> findDailyLogsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<DailyLog> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<DailyLog> findDailyLogsOnPage(Page<DailyLog> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<DailyLog>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
