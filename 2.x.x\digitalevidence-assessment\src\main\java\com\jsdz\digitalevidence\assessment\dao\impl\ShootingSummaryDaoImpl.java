/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.dao.impl;

import java.sql.SQLException;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.assessment.dao.ShootingSummaryDao;
import com.jsdz.digitalevidence.assessment.model.ShootingItem;
import com.jsdz.digitalevidence.assessment.model.ShootingSummary;

/**
 * @类名: ShootingAssessmentDaoImpl
 * @说明: 拍摄考核dao实现
 *
 * <AUTHOR>
 * @Date	 2017年8月14日 下午4:07:05
 * 修改记录：
 *
 * @see 	 
 */
@Repository
public class ShootingSummaryDaoImpl extends GenericEntityDaoHibernateImpl<ShootingSummary, Long> 
					implements ShootingSummaryDao {

	public List<ShootingItem> getShootingItems(final Long policeId) {
		final String hql = 
				"select new com.jsdz.digitalevidence.assessment.bean.ShootingItem("
			   + "d.cate,"
			   + "count(d.cate),"
			   + "sum(d.size),"
			   + "sum(d.duration)) "
			   +"from Document d "
			   +"where d.police.id = :policeId "
			   +"and d.uploadTime >= :startTime and d.uploadTime < :endTime " 
			   +"group by d.cate";
		//
		List<ShootingItem> data = getHibernateTemplate().execute(new HibernateCallback<List<ShootingItem>>() {
			@SuppressWarnings("unchecked")
			@Override
			public List<ShootingItem> doInHibernate(Session session) throws HibernateException, SQLException {
				// 获取查询对象
				Query queryObject = session.createQuery(hql);
				// 置入参数
				applyNamedParameterToQuery(queryObject, "policeId", policeId);
				return queryObject.list();
			}
		});
		return data;
	}

}
