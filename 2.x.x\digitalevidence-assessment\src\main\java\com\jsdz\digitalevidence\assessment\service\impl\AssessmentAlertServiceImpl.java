/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.admin.security.utils.SessionUtils;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.AssessmentConsts;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertQueryBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertSumBean;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.cycle.CycleConfiguration;
import com.jsdz.digitalevidence.assessment.cycle.CycleSelector;
import com.jsdz.digitalevidence.assessment.dao.AssessmentAlertDao;
import com.jsdz.digitalevidence.assessment.model.ReportType;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.digitalevidence.assessment.service.AssessmentAlertService;
import com.jsdz.digitalevidence.assessment.service.AssessmentQueryService;
import com.jsdz.digitalevidence.assessment.service.AssessmentService;
import com.jsdz.reportquery.ChainFilter;
import com.jsdz.reportquery.NullFilter;
import com.jsdz.reportquery.ReportQueryDao;
import com.jsdz.reportquery.ReportQueryUtils;
import com.jsdz.reportquery.dynsql.fork.support.tree.TreeField;
import com.jsdz.ruleengine.MapWrapper;
import com.jsdz.ruleengine.NameValueReferableMap;
import com.jsdz.ruleengine.Result;
import com.jsdz.ruleengine.model.RuleBook;
import com.jsdz.ruleengine.service.RuleService;
import com.jsdz.utils.DTOUtils.Filter;

/**
 * @类名: AssessmentAlertServiceImpl
 * @说明: 考核预警服务实现
 *
 * <AUTHOR>
 * @Date	 2017年9月26日 上午11:16:00
 * 修改记录：
 *
 * @see 	 
 */
@Service
public class AssessmentAlertServiceImpl implements AssessmentAlertService {
	
	@Autowired
	private CycleConfiguration cycleConf;
	@Autowired
	private RuleService ruleService;
	@Autowired
	private AssessmentAlertDao alertDao;
	@Autowired
	private AssessmentQueryService assessmentQueryService;
	@Autowired
	private AssessmentService assessmentService;
	@Autowired
	private EmployeesService empService;
	@Autowired
	private ReportQueryDao rqDao;
	
	// 考核周期设置
	@Resource(name=AssessmentConsts.Assessment_Cycle_Configuration_Key)
	private CycleConfiguration cc;

	@Override
	public AssessmentAlert getAssessmentAlertOfPolice(Long policeId, Date cycleDate) {
		return alertDao.getAssessmentAlert(policeId, cycleDate);
	}

	@SuppressWarnings("unchecked")
	@Override
	public Page<AssessmentAlert> queryAssessmentAlert(Page<AssessmentAlert> page, AssessmentAlertQueryBean qb) 
			throws Exception {
		// 转换参数
		Map<String, Object> kvs = ReportQueryUtils.queryBean3Map(qb,
				new ChainFilter(
						// 过滤Null参数
						new NullFilter()));
		// 增加日期参数
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		return (Page<AssessmentAlert>) rqDao.pageQueryNamedHQL(page, AssessmentConsts.SQL_KEY_Query_Assessment_Alert,
				paramNames,
				values);
	}

	@Override
	@Transactional(readOnly=true)
	public AssessmentAlertBean openAssessmentAlert(Long alertId) {		
		return alertDao.getAssessmentAlertBean(alertId);
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public Page<AssessmentAlertSumBean> searchAssessmentAlert(Page<AssessmentAlertSumBean> page,
			AssessmentAlertQueryBean qb) throws Exception {
		// 转换参数
		Map<String, Object> kvs = ReportQueryUtils.queryBean3Map(qb,
				new ChainFilter(
						// 过滤Null参数
						new NullFilter(),
						// 过滤TreeField参数
						new Filter() {
							public boolean accept(String propName, Object value) {
								if(value instanceof TreeField) {
									TreeField tr = (TreeField)value;
									if(tr.getPath()==null)
										return false;
									if(tr.getIncludeSub()==null)
										tr.setIncludeSub(false);
								}
								return true;
							}},
						// 过滤周期参数
						new Filter() {
							@Override
							public boolean accept(String propName, Object value) {
								if ("cycleDate".equals(propName) || "cycleSelector".equals(propName))
									return false;
								return true;
							}
						}));
		// 周期参数
		if(qb!=null&&qb.getCycleDate()!=null) {
			Cycle cycle = getQueryCycle(qb.getCycleDate(), qb.getCycleSelector());
			kvs.put("startDate", cycle.getStart());
			kvs.put("endDate", cycle.getEnd());
		}
		// 参数
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		return (Page<AssessmentAlertSumBean>)rqDao.pageQueryNamedSQL(page, 
					AssessmentConsts.SQL_KEY_Search_Assessment_Alert, paramNames, values);
	}
	
	@Override
	public Page<AssessmentAlertSumBean> searchMyAssessmentAlert(Page<AssessmentAlertSumBean> page, AssessmentAlertQueryBean qb) 
			throws Exception {
		Employees emp = SessionUtils.getCurrentOperator().getEmployees();
		qb.setPoliceCode(emp.getWorkNumber());
		return searchAssessmentAlert(page, qb);
	}

	@Override
	@Transactional
	public AssessmentAlert genAssessmentAlertOfPolice(Long policeId, Date startDate, Date endDate) throws Exception {
		return doGenAssessmentAlertOfPolice(policeId, startDate, endDate, ReportType.Monthly, AssessmentConsts.RULE_Assessment_ALert);
	}

	@Override
	public AssessmentAlert feedback(Long alertId, String feedback) {
		AssessmentAlert alert = alertDao.get(alertId);
		alert.setFeedBack(feedback);
		alertDao.saveOrUpdate(alert);
		return alert;
	}

	@Override
	@Transactional
	public AssessmentAlert genAssessmentAlertOfPoliceIfNotExist(Long policeId, Date startDate, Date endDate) throws Exception {
		// 报告是否已产生
		AssessmentAlert alert = alertDao.getAssessmentAlert(policeId, endDate);
		if(alert==null)
			return doGenAssessmentAlertOfPolice(policeId, startDate, endDate,  ReportType.Monthly, AssessmentConsts.RULE_Assessment_ALert);
		return alert;
	}
	
	
	@Override
	@Transactional
	public AssessmentAlert genAssessmentWeeklyAlertOfPolice(Long policeId, Date reportDate) throws Exception {
		Cycle c = cc.fromDate(reportDate);
		//
		return doGenAssessmentAlertOfPolice(policeId, c.getStart(), reportDate, 
				 ReportType.Weekly, AssessmentConsts.RULE_Assessment_WeeklyALert);
	}
	
	/**
	 * @说明：生成警员预警报告
	 *
	 * <AUTHOR>
	 * @param policeId 警员Id
	 * @param cycleDate 周期
	 * @param ruleTopology  适用的规则
	 * @return
	 * @throws Exception
	 * 
	 */
	private AssessmentAlert doGenAssessmentAlertOfPolice(Long policeId, Date startDate, Date reportDate, 
								ReportType type, String ruleTopology) 
								throws Exception {
		// 获取事实, 即警员报告
		ShootingAssessmentReport report = assessmentService.generateAccessmentReport(policeId, 
												startDate, reportDate, ReportType.Monthly);
		// 未有报告
		if(report==null)
			return null;
		NameValueReferableMap<ShootingAssessmentSummary> facts = new MapWrapper<ShootingAssessmentSummary>();
		ShootingAssessmentSummary sum = report.getSumOfShootingAssessment();
		facts.setValue("sum", sum);
		// 使用规则生成预警
		RuleBook ruleBook = ruleService.run(ruleTopology, facts);
		//
		Result r = (Result)ruleBook.getResult().get();
		//
		List<AssessmentAlertItem> alertItems = (List<AssessmentAlertItem>)r.getValue();
		//
		AssessmentAlert alert = new AssessmentAlert();
		//
		alert.setCreateDate(new Date());
		//
		Cycle cycle = cycleConf.fromDate(reportDate);
		alert.setStartDate(cycle.getStart());
		alert.setEndDate(cycle.getEnd());
		//
		alert.setItems(alertItems);
		alert.setType(type);
		//
		Employees police = empService.locateEmployeesById(policeId);
		alert.setPolice(police);
		//
		alertDao.saveOrUpdate(alert);
		return alert;
	}
	
	private Cycle getQueryCycle(Date cycleDate, CycleSelector selector) {
		Cycle cycle = cc.fromDate(cycleDate);
		if(selector==null)
			return cycle;
		if(selector.equals(CycleSelector.PREV))
			cycle = cc.prevCycle(cycle);
		else if(selector.equals(CycleSelector.NEXT))
			cycle = cc.nextCycle(cycle);
		return cycle;
	}
	
}
