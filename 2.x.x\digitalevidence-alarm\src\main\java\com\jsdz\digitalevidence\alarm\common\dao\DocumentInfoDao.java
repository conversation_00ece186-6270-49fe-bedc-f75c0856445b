package com.jsdz.digitalevidence.alarm.common.dao;

/**
 *
 * @类名: DocumentInfoDao
 * @说明:
 * @author: kenny
 * @Date 2017-11-06 16:23:56
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.document.model.DocumentInfo;

public interface DocumentInfoDao extends GenericORMEntityDAO<DocumentInfo,Long> {

	//新增
	public void addDocumentInfo(DocumentInfo documentInfo);

	//修改
	public void updateDocumentInfo(DocumentInfo documentInfo);

	//删除
	public void deleteDocumentInfo(DocumentInfo documentInfo);

	//按id查询,结果是游离状态的数据
	public DocumentInfo findDocumentInfoById(Long id);

	//按id查询
	public DocumentInfo locateDocumentInfoById(Long id);

	//单个查询
	public DocumentInfo findDocumentInfoByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DocumentInfo> findAllDocumentInfos();

	//列表查询
	public List<DocumentInfo> findDocumentInfosByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DocumentInfo> findDocumentInfosOnPage(Page<DocumentInfo> page,String queryStr,String[] paramNames,Object[] values);

}

