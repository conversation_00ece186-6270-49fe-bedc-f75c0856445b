package com.jsdz.digitalevidence.assessment.service;


/**
 * 
 * @类名: OrgAssPeriodService
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-01-11 17:27:52
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.assessment.model.org.OrgAssPeriod;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface OrgAssPeriodService {

	//新增
	public AjaxResult addOrgAssPeriod(OrgAssPeriod orgAssPeriod);

	//修改
	public AjaxResult updateOrgAssPeriod(OrgAssPeriod orgAssPeriod);

	//删除
	public AjaxResult deleteOrgAssPeriod(OrgAssPeriod orgAssPeriod);

	//按id查询,结果是游离状态的数据
	public OrgAssPeriod findOrgAssPeriodById(Long id);

	//按id查询
	public OrgAssPeriod locateOrgAssPeriodById(Long id);

	//单个查询
	public OrgAssPeriod findOrgAssPeriodByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<OrgAssPeriod> findAllOrgAssPeriods();

	//列表查询
	public List<OrgAssPeriod> findOrgAssPeriodsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<OrgAssPeriod> findOrgAssPeriodsOnPage(Page<OrgAssPeriod> page, String queryStr,String[] paramNames,Object[] values);

}
