package com.jsdz.digitalevidence.attendance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 考勤任务（后台定制考勤任务，考勤范围等信息）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_attendance_task")
public class AttendanceTaskModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 单位编号
     */
    private Long attendanceOrgId;

    /**
     * 考勤任务名称
     */
    private String attendanceName;

    /**
     * 考勤时间 当天的多少分钟
     */
    private String attendanceTime;

    /**
     * 可提前多少分钟打卡
     */
    private Integer advanceByMinutes;

    /**
     * 是否允许自动打卡（0允许，1不允许）
     */
    private Integer isAutomatic;

    /**
     * 离考勤范围多少米可打卡
     */
    private Double attendanceScopeRice;



    private String attendanceDescribe;


    private int isDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date updateTime;



}
