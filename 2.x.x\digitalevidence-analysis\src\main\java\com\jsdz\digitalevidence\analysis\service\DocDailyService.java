package com.jsdz.digitalevidence.analysis.service;

import java.util.Date;

/**
 * 
 * @类名: DocDailyService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-31 15:31:04
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.analysis.bean.SiteDocDailyDto;
import com.jsdz.digitalevidence.analysis.model.DailyObject;
import com.jsdz.digitalevidence.analysis.model.DailyType;
import com.jsdz.digitalevidence.analysis.model.DocDaily;

public interface DocDailyService {

	//新增
	public AjaxResult addDocDaily(DocDaily docDaily);

	//修改
	public AjaxResult updateDocDaily(DocDaily docDaily);

	//删除
	public AjaxResult deleteDocDaily(DocDaily docDaily);

	//按id查询,结果是游离状态的数据
	public DocDaily findDocDailyById(Long id);

	//按id查询
	public DocDaily locateDocDailyById(Long id);

	//单个查询
	public DocDaily findDocDailyByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DocDaily> findAllDocDailys();

	//列表查询
	public List<DocDaily> findDocDailysByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DocDaily> findDocDailysOnPage(Page<DocDaily> page, String queryStr,String[] paramNames,Object[] values);

	//日结统计
	public void dailyExecute(DailyType dailyType,DailyObject dailyObject, String dateBegin,String dateEnd);
	
	//按站点查询统计
	public Page<SiteDocDailyDto> findSiteAnalysis(Page page,String[] paramNames, Object[] values,
			String[] totalParamNames, Object[] totalValues);	
	
	//按执法仪查询统计
	//public Page<SiteDocDailyDto> findRecorderAnalysis(Page page,String[] paramNames, Object[] values);
	public Page<SiteDocDailyDto> findRecorderAnalysis(Page page,String[] paramNames, Object[] values,
			   String[] totalParamNames, Object[] totalValues);
	//按警员查询统计
	public Page<SiteDocDailyDto> findPoliceAnalysis(Page page,String[] paramNames, Object[] values,
			String[] totalParamNames, Object[] totalValues);
	
	//按类型查询统计
	public Page<SiteDocDailyDto> findCateAnalysis(Page page,String[] paramNames, Object[] values);	
	
}

