[INFO ] 2024-08-08 17:24:20,384 method:org.springframework.test.context.support.AbstractTestContextBootstrapper.getDefaultTestExecutionListenerClassNames(AbstractTestContextBootstrapper.java:259)
Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener]
[INFO ] 2024-08-08 17:24:20,398 method:org.springframework.test.context.support.AbstractTestContextBootstrapper.getTestExecutionListeners(AbstractTestContextBootstrapper.java:185)
Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@4ba2ca36, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@3444d69d, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1372ed45, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6a79c292, org.springframework.test.context.transaction.TransactionalTestExecutionListener@37574691, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@25359ed8]
[INFO ] 2024-08-08 17:24:20,454 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-common.xml]
[INFO ] 2024-08-08 17:24:20,777 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-documenttype.xml]
[INFO ] 2024-08-08 17:24:20,791 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-dynsql.xml]
[INFO ] 2024-08-08 17:24:20,803 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-reportengine.xml]
[INFO ] 2024-08-08 17:24:20,812 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-scheduler.xml]
[INFO ] 2024-08-08 17:24:20,820 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-storage.xml]
[INFO ] 2024-08-08 17:24:20,828 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-redis.xml]
[INFO ] 2024-08-08 17:24:20,840 method:org.springframework.context.support.AbstractApplicationContext.prepareRefresh(AbstractApplicationContext.java:578)
Refreshing org.springframework.context.support.GenericApplicationContext@29ba4338: startup date [Thu Aug 08 17:24:20 CST 2024]; root of context hierarchy
[INFO ] 2024-08-08 17:24:21,110 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-config.properties]
[INFO ] 2024-08-08 17:24:21,112 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-redis.properties]
[INFO ] 2024-08-08 17:24:21,175 method:com.mchange.v2.log.MLog.<clinit>(MLog.java:80)
MLog clients using log4j logging.
[INFO ] 2024-08-08 17:24:21,333 method:com.mchange.v2.c3p0.C3P0Registry.banner(C3P0Registry.java:204)
Initializing c3p0-0.9.1.1 [built 15-March-2007 01:32:31; debug? true; trace: 10]
[INFO ] 2024-08-08 17:24:21,438 method:org.hibernate.annotations.common.Version.<clinit>(Version.java:37)
Hibernate Commons Annotations 3.2.0.Final
[INFO ] 2024-08-08 17:24:21,441 method:org.hibernate.cfg.Environment.<clinit>(Environment.java:603)
Hibernate 3.6.10.Final
[INFO ] 2024-08-08 17:24:21,443 method:org.hibernate.cfg.Environment.<clinit>(Environment.java:636)
hibernate.properties not found
[INFO ] 2024-08-08 17:24:21,445 method:org.hibernate.cfg.Environment.buildBytecodeProvider(Environment.java:814)
Bytecode provider name : javassist
[INFO ] 2024-08-08 17:24:21,454 method:org.hibernate.cfg.Environment.<clinit>(Environment.java:695)
using JDK 1.4 java.sql.Timestamp handling
[INFO ] 2024-08-08 17:24:21,482 method:org.hibernate.cfg.Configuration.configure(Configuration.java:2192)
configuring from url: file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/digitalevidence-alarm/target/test-classes/test-hibernate.cfg.xml
[WARN ] 2024-08-08 17:24:21,526 method:org.hibernate.util.DTDEntityResolver.resolveEntity(DTDEntityResolver.java:73)
recognized obsolete hibernate namespace http://hibernate.sourceforge.net/. Use namespace http://www.hibernate.org/dtd/ instead. Refer to Hibernate 3.6 Migration Guide!
[INFO ] 2024-08-08 17:24:21,537 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Document.hbm.xml
[INFO ] 2024-08-08 17:24:21,553 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Case.hbm.xml
[INFO ] 2024-08-08 17:24:21,562 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/site/dao/site.hbm.xml
[INFO ] 2024-08-08 17:24:21,578 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/admin/security/dao/security.hbm.xml
[INFO ] 2024-08-08 17:24:21,589 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml
[WARN ] 2024-08-08 17:24:21,591 method:org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
[ERROR] 2024-08-08 17:24:21,597 method:org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:231)
Caught exception while allowing TestExecutionListener [org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1372ed45] to prepare test instance [com.jsdz.www.quartz.AlarmRelevanceServiceImplTest@221af3c0]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:83)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:117)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:230)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:15)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:249)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:89)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:231)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:60)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:229)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:50)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:222)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:300)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:193)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:316)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:240)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:214)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:155)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:772)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:125)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:60)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.delegateLoading(AbstractDelegatingSmartContextLoader.java:109)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.loadContext(AbstractDelegatingSmartContextLoader.java:261)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:98)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:116)
	... 27 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1578)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:545)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByName(AbstractAutowireCapableBeanFactory.java:1244)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1194)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:543)
	... 41 more
Caused by: org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.hibernate.cfg.Configuration.addResource(Configuration.java:799)
	at org.hibernate.cfg.Configuration.parseMappingElement(Configuration.java:2344)
	at org.hibernate.cfg.Configuration.parseSessionFactory(Configuration.java:2310)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2290)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2243)
	at org.hibernate.cfg.Configuration.configure(Configuration.java:2194)
	at org.springframework.orm.hibernate3.LocalSessionFactoryBean.buildSessionFactory(LocalSessionFactoryBean.java:588)
	at org.springframework.orm.hibernate3.AbstractSessionFactoryBean.afterPropertiesSet(AbstractSessionFactoryBean.java:189)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1637)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1574)
	... 50 more
[INFO ] 2024-08-08 17:24:21,608 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-common.xml]
[INFO ] 2024-08-08 17:24:21,760 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-documenttype.xml]
[INFO ] 2024-08-08 17:24:21,765 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-dynsql.xml]
[INFO ] 2024-08-08 17:24:21,772 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-reportengine.xml]
[INFO ] 2024-08-08 17:24:21,777 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-scheduler.xml]
[INFO ] 2024-08-08 17:24:21,783 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-storage.xml]
[INFO ] 2024-08-08 17:24:21,788 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-redis.xml]
[INFO ] 2024-08-08 17:24:21,793 method:org.springframework.context.support.AbstractApplicationContext.prepareRefresh(AbstractApplicationContext.java:578)
Refreshing org.springframework.context.support.GenericApplicationContext@26dcd8c0: startup date [Thu Aug 08 17:24:21 CST 2024]; root of context hierarchy
[INFO ] 2024-08-08 17:24:21,909 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-config.properties]
[INFO ] 2024-08-08 17:24:21,909 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-redis.properties]
[INFO ] 2024-08-08 17:24:21,938 method:org.hibernate.cfg.Configuration.configure(Configuration.java:2192)
configuring from url: file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/digitalevidence-alarm/target/test-classes/test-hibernate.cfg.xml
[WARN ] 2024-08-08 17:24:21,940 method:org.hibernate.util.DTDEntityResolver.resolveEntity(DTDEntityResolver.java:73)
recognized obsolete hibernate namespace http://hibernate.sourceforge.net/. Use namespace http://www.hibernate.org/dtd/ instead. Refer to Hibernate 3.6 Migration Guide!
[INFO ] 2024-08-08 17:24:21,942 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Document.hbm.xml
[INFO ] 2024-08-08 17:24:21,947 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Case.hbm.xml
[INFO ] 2024-08-08 17:24:21,952 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/site/dao/site.hbm.xml
[INFO ] 2024-08-08 17:24:21,959 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/admin/security/dao/security.hbm.xml
[INFO ] 2024-08-08 17:24:21,965 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml
[WARN ] 2024-08-08 17:24:21,966 method:org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
[ERROR] 2024-08-08 17:24:21,967 method:org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:231)
Caught exception while allowing TestExecutionListener [org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1372ed45] to prepare test instance [com.jsdz.www.quartz.AlarmRelevanceServiceImplTest@73a00e09]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:83)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:117)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:230)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:15)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:249)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:89)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:231)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:60)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:229)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:50)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:222)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:300)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:193)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:316)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:240)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:214)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:155)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:772)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:125)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:60)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.delegateLoading(AbstractDelegatingSmartContextLoader.java:109)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.loadContext(AbstractDelegatingSmartContextLoader.java:261)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:98)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:116)
	... 27 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1578)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:545)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByName(AbstractAutowireCapableBeanFactory.java:1244)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1194)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:543)
	... 41 more
Caused by: org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.hibernate.cfg.Configuration.addResource(Configuration.java:799)
	at org.hibernate.cfg.Configuration.parseMappingElement(Configuration.java:2344)
	at org.hibernate.cfg.Configuration.parseSessionFactory(Configuration.java:2310)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2290)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2243)
	at org.hibernate.cfg.Configuration.configure(Configuration.java:2194)
	at org.springframework.orm.hibernate3.LocalSessionFactoryBean.buildSessionFactory(LocalSessionFactoryBean.java:588)
	at org.springframework.orm.hibernate3.AbstractSessionFactoryBean.afterPropertiesSet(AbstractSessionFactoryBean.java:189)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1637)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1574)
	... 50 more
[INFO ] 2024-08-08 17:24:21,970 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-common.xml]
[INFO ] 2024-08-08 17:24:22,110 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-documenttype.xml]
[INFO ] 2024-08-08 17:24:22,115 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-dynsql.xml]
[INFO ] 2024-08-08 17:24:22,120 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-reportengine.xml]
[INFO ] 2024-08-08 17:24:22,125 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-scheduler.xml]
[INFO ] 2024-08-08 17:24:22,130 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-storage.xml]
[INFO ] 2024-08-08 17:24:22,134 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-redis.xml]
[INFO ] 2024-08-08 17:24:22,139 method:org.springframework.context.support.AbstractApplicationContext.prepareRefresh(AbstractApplicationContext.java:578)
Refreshing org.springframework.context.support.GenericApplicationContext@1b1c538d: startup date [Thu Aug 08 17:24:22 CST 2024]; root of context hierarchy
[INFO ] 2024-08-08 17:24:22,240 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-config.properties]
[INFO ] 2024-08-08 17:24:22,241 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-redis.properties]
[INFO ] 2024-08-08 17:24:22,261 method:org.hibernate.cfg.Configuration.configure(Configuration.java:2192)
configuring from url: file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/digitalevidence-alarm/target/test-classes/test-hibernate.cfg.xml
[WARN ] 2024-08-08 17:24:22,263 method:org.hibernate.util.DTDEntityResolver.resolveEntity(DTDEntityResolver.java:73)
recognized obsolete hibernate namespace http://hibernate.sourceforge.net/. Use namespace http://www.hibernate.org/dtd/ instead. Refer to Hibernate 3.6 Migration Guide!
[INFO ] 2024-08-08 17:24:22,264 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Document.hbm.xml
[INFO ] 2024-08-08 17:24:22,268 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Case.hbm.xml
[INFO ] 2024-08-08 17:24:22,272 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/site/dao/site.hbm.xml
[INFO ] 2024-08-08 17:24:22,277 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/admin/security/dao/security.hbm.xml
[INFO ] 2024-08-08 17:24:22,283 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml
[WARN ] 2024-08-08 17:24:22,283 method:org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
[ERROR] 2024-08-08 17:24:22,284 method:org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:231)
Caught exception while allowing TestExecutionListener [org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1372ed45] to prepare test instance [com.jsdz.www.quartz.AlarmRelevanceServiceImplTest@2eeb0f9b]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:83)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:117)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:230)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:15)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:249)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:89)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:231)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:60)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:229)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:50)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:222)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:300)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:193)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:316)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:240)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:214)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:155)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:772)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:125)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:60)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.delegateLoading(AbstractDelegatingSmartContextLoader.java:109)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.loadContext(AbstractDelegatingSmartContextLoader.java:261)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:98)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:116)
	... 27 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1578)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:545)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByName(AbstractAutowireCapableBeanFactory.java:1244)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1194)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:543)
	... 41 more
Caused by: org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.hibernate.cfg.Configuration.addResource(Configuration.java:799)
	at org.hibernate.cfg.Configuration.parseMappingElement(Configuration.java:2344)
	at org.hibernate.cfg.Configuration.parseSessionFactory(Configuration.java:2310)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2290)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2243)
	at org.hibernate.cfg.Configuration.configure(Configuration.java:2194)
	at org.springframework.orm.hibernate3.LocalSessionFactoryBean.buildSessionFactory(LocalSessionFactoryBean.java:588)
	at org.springframework.orm.hibernate3.AbstractSessionFactoryBean.afterPropertiesSet(AbstractSessionFactoryBean.java:189)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1637)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1574)
	... 50 more
[INFO ] 2024-08-08 17:24:22,287 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-common.xml]
[INFO ] 2024-08-08 17:24:22,410 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-documenttype.xml]
[INFO ] 2024-08-08 17:24:22,415 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-dynsql.xml]
[INFO ] 2024-08-08 17:24:22,419 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-reportengine.xml]
[INFO ] 2024-08-08 17:24:22,424 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-scheduler.xml]
[INFO ] 2024-08-08 17:24:22,428 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-storage.xml]
[INFO ] 2024-08-08 17:24:22,432 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-redis.xml]
[INFO ] 2024-08-08 17:24:22,437 method:org.springframework.context.support.AbstractApplicationContext.prepareRefresh(AbstractApplicationContext.java:578)
Refreshing org.springframework.context.support.GenericApplicationContext@1c61eda5: startup date [Thu Aug 08 17:24:22 CST 2024]; root of context hierarchy
[INFO ] 2024-08-08 17:24:22,545 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-config.properties]
[INFO ] 2024-08-08 17:24:22,545 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-redis.properties]
[INFO ] 2024-08-08 17:24:22,565 method:org.hibernate.cfg.Configuration.configure(Configuration.java:2192)
configuring from url: file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/digitalevidence-alarm/target/test-classes/test-hibernate.cfg.xml
[WARN ] 2024-08-08 17:24:22,567 method:org.hibernate.util.DTDEntityResolver.resolveEntity(DTDEntityResolver.java:73)
recognized obsolete hibernate namespace http://hibernate.sourceforge.net/. Use namespace http://www.hibernate.org/dtd/ instead. Refer to Hibernate 3.6 Migration Guide!
[INFO ] 2024-08-08 17:24:22,568 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Document.hbm.xml
[INFO ] 2024-08-08 17:24:22,572 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Case.hbm.xml
[INFO ] 2024-08-08 17:24:22,575 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/site/dao/site.hbm.xml
[INFO ] 2024-08-08 17:24:22,580 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/admin/security/dao/security.hbm.xml
[INFO ] 2024-08-08 17:24:22,585 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml
[WARN ] 2024-08-08 17:24:22,586 method:org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
[ERROR] 2024-08-08 17:24:22,586 method:org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:231)
Caught exception while allowing TestExecutionListener [org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1372ed45] to prepare test instance [com.jsdz.www.quartz.AlarmRelevanceServiceImplTest@e9474f]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:83)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:117)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:230)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:15)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:249)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:89)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:231)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:60)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:229)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:50)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:222)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:300)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:193)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:316)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:240)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:214)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:155)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:772)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:125)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:60)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.delegateLoading(AbstractDelegatingSmartContextLoader.java:109)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.loadContext(AbstractDelegatingSmartContextLoader.java:261)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:98)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:116)
	... 27 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1578)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:545)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByName(AbstractAutowireCapableBeanFactory.java:1244)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1194)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:543)
	... 41 more
Caused by: org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.hibernate.cfg.Configuration.addResource(Configuration.java:799)
	at org.hibernate.cfg.Configuration.parseMappingElement(Configuration.java:2344)
	at org.hibernate.cfg.Configuration.parseSessionFactory(Configuration.java:2310)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2290)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2243)
	at org.hibernate.cfg.Configuration.configure(Configuration.java:2194)
	at org.springframework.orm.hibernate3.LocalSessionFactoryBean.buildSessionFactory(LocalSessionFactoryBean.java:588)
	at org.springframework.orm.hibernate3.AbstractSessionFactoryBean.afterPropertiesSet(AbstractSessionFactoryBean.java:189)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1637)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1574)
	... 50 more
[INFO ] 2024-08-08 17:24:22,589 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-common.xml]
[INFO ] 2024-08-08 17:24:22,701 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-documenttype.xml]
[INFO ] 2024-08-08 17:24:22,704 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-dynsql.xml]
[INFO ] 2024-08-08 17:24:22,708 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-reportengine.xml]
[INFO ] 2024-08-08 17:24:22,712 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-scheduler.xml]
[INFO ] 2024-08-08 17:24:22,716 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-storage.xml]
[INFO ] 2024-08-08 17:24:22,719 method:org.springframework.beans.factory.xml.XmlBeanDefinitionReader.loadBeanDefinitions(XmlBeanDefinitionReader.java:317)
Loading XML bean definitions from class path resource [testApplicationContext-redis.xml]
[INFO ] 2024-08-08 17:24:22,723 method:org.springframework.context.support.AbstractApplicationContext.prepareRefresh(AbstractApplicationContext.java:578)
Refreshing org.springframework.context.support.GenericApplicationContext@677cb96e: startup date [Thu Aug 08 17:24:22 CST 2024]; root of context hierarchy
[INFO ] 2024-08-08 17:24:22,814 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-config.properties]
[INFO ] 2024-08-08 17:24:22,815 method:org.springframework.core.io.support.PropertiesLoaderSupport.loadProperties(PropertiesLoaderSupport.java:172)
Loading properties file from class path resource [test-redis.properties]
[INFO ] 2024-08-08 17:24:22,832 method:org.hibernate.cfg.Configuration.configure(Configuration.java:2192)
configuring from url: file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/digitalevidence-alarm/target/test-classes/test-hibernate.cfg.xml
[WARN ] 2024-08-08 17:24:22,834 method:org.hibernate.util.DTDEntityResolver.resolveEntity(DTDEntityResolver.java:73)
recognized obsolete hibernate namespace http://hibernate.sourceforge.net/. Use namespace http://www.hibernate.org/dtd/ instead. Refer to Hibernate 3.6 Migration Guide!
[INFO ] 2024-08-08 17:24:22,834 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Document.hbm.xml
[INFO ] 2024-08-08 17:24:22,838 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/document/dao/Case.hbm.xml
[INFO ] 2024-08-08 17:24:22,842 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/digitalevidence/site/dao/site.hbm.xml
[INFO ] 2024-08-08 17:24:22,847 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/admin/security/dao/security.hbm.xml
[INFO ] 2024-08-08 17:24:22,852 method:org.hibernate.cfg.Configuration.addResource(Configuration.java:789)
Reading mappings from resource : com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml
[WARN ] 2024-08-08 17:24:22,852 method:org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:546)
Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
[ERROR] 2024-08-08 17:24:22,853 method:org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:231)
Caught exception while allowing TestExecutionListener [org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1372ed45] to prepare test instance [com.jsdz.www.quartz.AlarmRelevanceServiceImplTest@41a16eb3]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:83)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:117)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:228)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:230)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:15)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:249)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:89)
	at org.junit.runners.ParentRunner$3.run(ParentRunner.java:231)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:60)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:229)
	at org.junit.runners.ParentRunner.access$000(ParentRunner.java:50)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:222)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:300)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:193)
	at org.apache.maven.surefire.junit4.JUnit4Provider.execute(JUnit4Provider.java:316)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeWithRerun(JUnit4Provider.java:240)
	at org.apache.maven.surefire.junit4.JUnit4Provider.executeTestSet(JUnit4Provider.java:214)
	at org.apache.maven.surefire.junit4.JUnit4Provider.invoke(JUnit4Provider.java:155)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'actionDaoImpl' defined in URL [jar:file:/E:/minorbin/workspaces/changsha/4gweb-platform/2.x.x/admin/admin-security/target/admin-security-2.4.1.jar!/com/jsdz/admin/security/dao/impl/ActionDaoImpl.class]: Initialization of bean failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.RERopository] for bean with name 'reRepo' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.RERopository
Related cause: org.springframework.beans.factory.CannotLoadBeanClassException: Cannot find class [com.jsdz.reportengine.ContentTypeMapper] for bean with name 'contentTypeMapper' defined in class path resource [testApplicationContext-reportengine.xml]; nested exception is java.lang.ClassNotFoundException: com.jsdz.reportengine.ContentTypeMapper
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:553)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:772)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:125)
	at org.springframework.test.context.support.AbstractGenericContextLoader.loadContext(AbstractGenericContextLoader.java:60)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.delegateLoading(AbstractDelegatingSmartContextLoader.java:109)
	at org.springframework.test.context.support.AbstractDelegatingSmartContextLoader.loadContext(AbstractDelegatingSmartContextLoader.java:261)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:98)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:116)
	... 27 more
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sessionFactory' defined in class path resource [testApplicationContext-common.xml]: Invocation of init method failed; nested exception is org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1578)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:545)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByName(AbstractAutowireCapableBeanFactory.java:1244)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1194)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:543)
	... 41 more
Caused by: org.hibernate.MappingNotFoundException: resource: com/jsdz/scheduler/repo/dao/hibernate/hbm/Job.hbm.xml not found
	at org.hibernate.cfg.Configuration.addResource(Configuration.java:799)
	at org.hibernate.cfg.Configuration.parseMappingElement(Configuration.java:2344)
	at org.hibernate.cfg.Configuration.parseSessionFactory(Configuration.java:2310)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2290)
	at org.hibernate.cfg.Configuration.doConfigure(Configuration.java:2243)
	at org.hibernate.cfg.Configuration.configure(Configuration.java:2194)
	at org.springframework.orm.hibernate3.LocalSessionFactoryBean.buildSessionFactory(LocalSessionFactoryBean.java:588)
	at org.springframework.orm.hibernate3.AbstractSessionFactoryBean.afterPropertiesSet(AbstractSessionFactoryBean.java:189)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1637)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1574)
	... 50 more
