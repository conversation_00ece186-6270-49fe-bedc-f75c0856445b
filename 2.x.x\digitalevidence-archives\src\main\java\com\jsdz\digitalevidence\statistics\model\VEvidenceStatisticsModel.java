package com.jsdz.digitalevidence.statistics.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class VEvidenceStatisticsModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("ID")
    private Long id;

    @TableField("ORGCODE")
    private String orgcode;

    @TableField("ORGNAME")
    private String orgname;

    @TableField("PATH")
    private String path;

    @TableField("PARENTID")
    private Long parentid;

    @TableField("DOCONESELF")
    private Long doconeself;

    @TableField("DOCALL")
    private Long docall;

    @TableField("DOCMP4ONESELF")
    private Long docmp4oneself;

    @TableField("DOCMP4ALL")
    private Long docmp4all;

    @TableField("DOCAUDIOONESELF")
    private Long docaudiooneself;

    @TableField("DOCAUDIOALL")
    private Long docaudioall;

    @TableField("DOCPICTUREONESELF")
    private Long docpictureoneself;

    @TableField("DOCPICTUREALL")
    private Long docpictureall;

    @TableField("ARCHIVESYES")
    private Long archivesyes;

    @TableField("ARCHIVESNO")
    private Long archivesno;

    @TableField("RELATIONYES")
    private Long relationyes;

    @TableField("RELATIONNO")
    private Long relationno;

    @TableField("OVERTIME")
    private Long overtime;

    private Long allAlarms;

    private Long relationAlarms;

    private Long voidAlarms;

}
