package com.jsdz.digitalevidence.archives.bean.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.utils.DateTimeUtils;

import java.io.Serializable;
import java.util.Date;


public class SearchCheckDocParam implements Serializable {

    /** 页数, 以0开始 */
    private int page;
    /** 每页显示多少 */
    private int pageSize;

    private String checkCode;

    private String orgPath; //单位path

    private int isBelow; //是否包含下级 0 不包含 1 包含

    private String type; //类型

    private PerLvlBean perLvlBean;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTimeStart;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTimeEnd;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date uploadTimeStart;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date uploadTimeEnd;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        if(pageSize ==0){
            this.pageSize=10;
        }
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public int getIsBelow() {
        return isBelow;
    }

    public void setIsBelow(int isBelow) {
        this.isBelow = isBelow;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(Date createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public Date getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(Date createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public Date getUploadTimeStart() {
        return uploadTimeStart;
    }

    public void setUploadTimeStart(Date uploadTimeStart) {
        this.uploadTimeStart = uploadTimeStart;
    }

    public Date getUploadTimeEnd() {
        return uploadTimeEnd;
    }

    public void setUploadTimeEnd(Date uploadTimeEnd) {
        this.uploadTimeEnd = uploadTimeEnd;
    }

    public PerLvlBean getPerLvlBean() {
        return perLvlBean;
    }

    public void setPerLvlBean(PerLvlBean perLvlBean) {
        this.perLvlBean = perLvlBean;
    }
}
