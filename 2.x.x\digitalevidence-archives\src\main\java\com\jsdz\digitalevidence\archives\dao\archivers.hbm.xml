<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
    
<hibernate-mapping>
	<!-- 平台 -->
	<class name="com.jsdz.digitalevidence.archives.bean.ArchivesDataBean" table="t_archives_data">
		<id name="archivesId" column="archivesId" type="long">
			<generator class="native" />
		</id>
		<property name="archivesCode"></property>
		<property name="archivesTitle"></property>
		<property name="archivesType"></property>
		<property name="archivesDescribe"></property>
		<!-- 所属单位  -->
		<many-to-one name="organization" cascade="none"
					 class="com.jsdz.admin.org.model.Organization" column="departmentId">
		</many-to-one>

		<property name="inputUserId"></property>
		<property name="inputUserName"></property>
		<property name="inputTime"></property>
		<property name="updateUserId"></property>
		<property name="updateUserName"></property>
		<property name="updateTime"></property>
		<property name="status"></property>
		<property name="remarks"></property>
		<property name="saveMonth"></property>
		<property name="expirationTime"></property>
		<property name="alarmId"></property>
		<property name="documents"></property>

	</class>


</hibernate-mapping>