package com.jsdz.digitalevidence.task.model;

import com.jsdz.digitalevidence.task.constant.TaskProcessStatus;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * 任务分发表(TTaskProcess)实体类
 *
 * <AUTHOR>
 * @since 2023-09-27 11:29:37
 */
public class TTaskProcess implements Serializable {
    private static final long serialVersionUID = 353289431028712669L;

    public TTaskProcess(TTask task, TaskRecorderBean r, Long operatorId) {
        this.taskId = task.getId();
        this.deviceId = r.getId();
        this.cate = task.getCate();
        this.setPoliceId(r.getPoliceId());
        this.setCreateBy(operatorId);
        this.setUpdateBy(operatorId);
        this.setStartTime(task.getStartTime());
        this.setEndTime(task.getEndTime());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if(this.getStartTime() != null)
        this.setStartTimeStr(dateFormat.format(this.getStartTime()));
        if(this.getEndTime() != null)
        this.setEndTimeStr(dateFormat.format(this.getEndTime()));
        this.setStatus(TaskProcessStatus.WAIT);
    }

    public TTaskProcess() {

    }

    public TTaskProcess(Long id, Integer status) {
        this.id = id;
        this.status = status;
    }

    private Long id;
/**
     * 任务id
     */
    private Long taskId;
    /**
     * 类别
     */
    private Integer cate;
/**
     * 执法仪id
     */
    private Long deviceId;

    /**
     * 执法仪编号
     */
    private String code;

    /**
     * 警员姓名
     */
    private String employeesName;

/**
     * 警员id
     */
    private Long policeId;
/**
     * 任务开始时间
     */
    private Date startTime;
/**
     * 任务结束时间
     */
    private Date endTime;
/**
     * 任务开始时间
     */
    private String startTimeStr;
/**
     * 任务结束时间
     */
    private String endTimeStr;
/**
     * 任务结束时间
     */
    private String delayTimeStr;
/**
     * 任务延期时间
     */
    private Date delayTime;
/**
     * 是否延期 0、未延期 1、已延期
     */
    private Byte isDelay;
/**
     * 任务反馈
     */
    private String result;
/**
     * 任务状态：0、未接收 1、已接收 2、待审核 3、已完成 4、审核不通过
     */
    private Integer status;

    /**
     * 审核意见
     */
    private String advice;

/**
     * 审核人
     */
    private Long examineBy;

    /**
     * 警员姓名
     */
    private String examineName;

/**
     * 创建者
     */
    private Long createBy;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 更新者
     */
    private Long updateBy;
/**
     * 更新时间
     */
    private Date updateTime;
/**
     * 是否删除 0、未删除 1、已删除
     */
    private Byte isDeleted;

    private List<Long> docIds;

    private List<String> urlList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getCate() {
        return cate;
    }

    public void setCate(Integer cate) {
        this.cate = cate;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getPoliceId() {
        return policeId;
    }

    public void setPoliceId(Long policeId) {
        this.policeId = policeId;
    }

    public String getEmployeesName() {
        return employeesName;
    }

    public void setEmployeesName(String employeesName) {
        this.employeesName = employeesName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(Date delayTime) {
        this.delayTime = delayTime;
    }

    public Byte getIsDelay() {
        return isDelay;
    }

    public void setIsDelay(Byte isDelay) {
        this.isDelay = isDelay;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getExamineBy() {
        return examineBy;
    }

    public void setExamineBy(Long examineBy) {
        this.examineBy = examineBy;
    }

    public String getExamineName() {
        return examineName;
    }

    public void setExamineName(String examineName) {
        this.examineName = examineName;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Byte isDeleted) {
        this.isDeleted = isDeleted;
    }

    public String getAdvice() {
        return advice;
    }

    public void setAdvice(String advice) {
        this.advice = advice;
    }

    public List<Long> getDocIds() {
        return docIds;
    }

    public void setDocIds(List<Long> docIds) {
        this.docIds = docIds;
    }

    public List<String> getUrlList() {
        return urlList;
    }

    public void setUrlList(List<String> urlList) {
        this.urlList = urlList;
    }

    public String getStartTimeStr() {
        return startTimeStr;
    }

    public void setStartTimeStr(String startTimeStr) {
        this.startTimeStr = startTimeStr;
    }

    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public String getDelayTimeStr() {
        return delayTimeStr;
    }

    public void setDelayTimeStr(String delayTimeStr) {
        this.delayTimeStr = delayTimeStr;
    }
}

