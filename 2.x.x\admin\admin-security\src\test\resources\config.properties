#
version=2.4.0
#jdbc.database=Oracle


jdbc.database=Mysql
jdbc.driver=com.mysql.cj.jdbc.Driver
jdbc.url=*************************************************************************************************************************************************************************************************************************
#jdbc.url=****************************************************************************************************************************************************************************************************************************
jdbc.username=root
jdbc.password=jsdz1234
validationQuery=SELECT 1

#jdbc.url=**************************************************************************************************************************************************************
#jdbc.username=root
#jdbc.password=htrsdb

c3p0.acquireIncrement=3
c3p0.acquireRetryAttempts=60
c3p0.acquireRetryDelay=1000
c3p0.initialPoolSize=10
c3p0.maxPoolSize=30
c3p0.minPoolSize=10
c3p0.maxIdleTime=60
c3p0.numHelperThreads=3
c3p0.maxStatementsPerConnection=100

#document##############
document.storage.rootpath=d:/ftp/
document.storage.rootpath_log=d:/ftp/log
fileserver.rootpath=http://58.250.27.16:8081/media/

document.alarm.path=d:/ftp/
document.alarm.rootpath=http://127.0.0.1:8081/media/
document.alarm.uploadMsg=http://50.1.235.253:8085/uploadData/addFaces

document.alarm.plateLib=D:/ftp/plates

#
document.expireddays=180
#dynsql template
document.dynsql.template=../dynsql_${jdbc.database}/
#1024*1024*10
document.extract.writelimit=10485760
#if document expireyed then n days' warning
document.expiry.waring.days=7
# 
document.pathstrategy.id.segs=3
document.pathstrategy.id.seglen=3
#
report.template.path=report
#
document.delete.batchsize = 10
#
document.jobgroup = Document
document.clear.jobname = DocumentClearJob
document.clear.jobtype = documentClearJob
document.clear.jobdesc = \u8FC7\u671F\u6E05\u7406\u4F5C\u4E1A

######geo########
# jedis cluster
redis.maxIdle=100  
jedis.maxActive=300  
jedis.timeout=100000  
jedis.hosts=localhost:7001,localhost:7002
# for test
jedis.host=************:7001
#\u6700\u5927\u5EFA\u7ACB\u8FDE\u63A5\u7B49\u5F85\u65F6\u95F4  
# Object Pool config
jedis.maxWait=1000  
redis.maxTotal=1000  
redis.minIdle=8  
jedis.maxAttemps=5  
jedis.testOnBorrow=true 
redis.dbIndex=0
#
lbs.geoKey=GEO_KEY

#red5
red5.host=************
red5.port=1935
red5.app=live
#
# Assessment
assessment.start.day=5
assessment.frozen.day=10
assessment.rule.gfyq.threshold=0.2
assessment.rule.quality.threshold=0.2
assessment.assessRate=0.33334
#
# assessment job
assessment.jobgroup = Assessment
#
assessment.dailyreportjob.name = AssessmentReportDailyJob
assessment.dailyreportjob.type = assessmentReportDailyJob
assessment.dailyreportjob.desc = \u65E5\u8003\u6838\u62A5\u544A\u4F5C\u4E1A
#
assessment.pushjob.name = AssessmentPushJob
assessment.pushjob.type = assessmentPushJob
assessment.pushjob.desc = \u8003\u6838\u89C6\u9891\u63A8\u9001\u4F5C\u4E1A
#
assessment.weeklyalertjob.name = AssessmentWeeklyAlertJob
assessment.weeklyalertjob.type = assessmentWeeklyAlertJob
assessment.weeklyalertjob.desc = \u6708\u8003\u6838\u9884\u8B66\u4F5C\u4E1A
#
assessment.monthlyreportjob.name = AssessmentMonthlyReportJob
assessment.monthlyreportjob.type = assessmentMonthlyReportJob
assessment.monthlyreportjob.desc = \u6708\u8003\u6838\u62A5\u544A\u4F5C\u4E1A
#
assessment.alertjob.name = AssessmentAlertJob
assessment.alertjob.type = assessmentAlertJob
assessment.alertjob.desc = \u8003\u6838\u9884\u8B66\u4F5C\u4E1A
#
assessment.freezejob.name = AssessmentFreezeJob
assessment.freezejob.type = assessmentFreezeJob
assessment.freezejob.desc = \u8003\u6838\u62A5\u544A\u51BB\u7ED3\u4F5C\u4E1A

#gueiyang
#gueiyang.api.url=http://*************:9000/
gueiyang.api.url=http://localhost:8080/web-site/simulate/
gueiyang.api.un=jingshengUser
gueiyang.api.pwd=20dfcf8ba380ba5b164193424a1b329d

#activeMQ
activemq.active=0
activemq.brokerurl=tcp://localhost:61616
#save to exchange database
exchange.active=0
#save alarm relation to exchange database
exchange.alarm.relation.active=0


#redis for spring session
redis.host=*************
redis.port=7001
redis.pwd=
redis.timeout=100000
redis.maxIdle=100
redis.maxActive=300
redis.maxWait=1000
redis.timeout=100000
redis.maxTotal=1000
redis.minIdle=8
redis.testOnBorrow=true
spring.redis.cluster.nodes=*************\:7001,*************\:7002,*************\:7003,*************\:7004,*************\:7005,*************\:7006
spring.redis.cluster.max-redirects=3

#portal
notify.notice.topN = 10
notify.notice.ast= 100000
notify.notice.open.resource=/right_page/portal/default.jsp

#download center
dc.storage.rootpath=d://download/
dc.fileserver.rootpath=http://***********:8080/media/
#notify
notify.day=-8

#Graph ensemble url
active.socket.connect=0
#graph.ensemble.url=http://************:8082
#graph.ensemble.url=http://**********:8082
graph.ensemble.url=http://************:8082
socket.server.ip=************
socket.server.port=8083

#default.longitude=112.736252
#default.latitude=37.723385
default.paths=*************:7011
default.longitude=114.02328674850753
default.latitude=22.738345783842362

graph.4g.userKey=4d7e23807e2d89af1fde73cfa563258a
#graph.4g.url=http://127.0.0.1:8081/gldata/alarm/
#graph.4g.url=http://*************:8081/gldata/alarm/
graph.4g.url=http://************:8081/gldata/alarm

delete.original.file=0
upload.device.isempty=1
upload.police.isempty=1

# 0 not include 1 inlude
Document.include.logfile=0
# is check version
is.check.version=0

# user defined rtsp play url: 0 get url from wujian system, 1 user defined url;
rtsp.user.defined=1
# the %s is paramers for device channel no
rtsp.user.defined.play.url=rtsp://************:7011/d=%s&t=0&c=0&l=0


# default device heartbeat interval, unit: second
default.device.heartbeat.interval=600

# must check auth code when register device 
device.register.check.authcode=false

# web socket server ip for push message
ws.server.ip=http://************:9213/