/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.model.alert;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.assessment.model.ReportType;

/**
 * @类名: AssessmentAlert
 * @说明: 考核预警
 *        
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午6:15:56
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentAlert extends AbstractDTO {
	
	private Long id;
	/** 所属警员*/
	private Employees police;
	/** 预警项*/
	@SuppressWarnings("rawtypes")
	private List<AssessmentAlertItem> items;
	/** 考核时段，开始、结束*/
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date startDate;
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date endDate;
	/** 报告生成日期*/
	private Date createDate;
	/** 预警反馈*/
	private String feedBack;
	/** 报告类型*/
	private ReportType type;
	
	public Long getId() {
		return id;
	}         
	public void setId(Long id) {
		this.id = id;
	}
	public List<AssessmentAlertItem> getItems() {
		return items;
	}                                                                           
	public void setItems(List<AssessmentAlertItem> items) {
		this.items = items;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public String getFeedBack() {
		return feedBack;
	}
	public void setFeedBack(String feedBack) {
		this.feedBack = feedBack;
	}
	public Employees getPolice() {
		return police;
	}
	public void setPolice(Employees police) {
		this.police = police;
	}
	public ReportType getType() {
		return type;
	}
	public void setType(ReportType type) {
		this.type = type;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
}
