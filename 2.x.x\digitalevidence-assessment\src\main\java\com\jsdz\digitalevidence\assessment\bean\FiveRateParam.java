package com.jsdz.digitalevidence.assessment.bean;
/**
 * 
 * @类名: FiveRateParam
 * @说明: 
 *
 * @author: kenny
 * @Date	2018年1月18日下午8:50:26
 * 修改记录：
 *
 * @see
 */
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.jsdz.admin.security.bean.ParamGenerateHql;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.admin.security.utils.JSDateFormatUtils;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.model.org.PeriodType;

public class FiveRateParam extends ParamGenerateHql{
	
	private Page<FiveRateBean> page;
	private Map<String, Object> kvs = new HashMap<String, Object>();
	private List<String> parLst = new ArrayList<String>();
	private Long orgId;
	private String orgPath;//orgPath 不为空表示包含下级单位
	private String currDate;//日期
	private Integer cycleNum;//周期
	private Integer cycleUnit;//周期单位 0=分钟,1=小时, 2=天, 3=周,4=月,5=年
	private String post;//岗位
	private String querySql;
	private String totalSql;

	public String generateParam(PerLvlBean p) throws ParseException{
		//只能查本单位及下级单位的数据
		if (orgId==null){
			//{0}
			//p.getPermissionLevel()=9 是admin用户
			if  (p.getPermissionLevel() > 0 && p.getPermissionLevel() < 9 ){
				kvs.put("orgPath1", p.getOrgPath() + "%");
			}
		}else if (this.getOrgPath() != null){
			//{1}
			kvs.put("orgPath2", this.getOrgPath() + "%");
		}else if (orgId != null){
			//{2}
			kvs.put("orgId", this.getOrgId());
		}
		
		if (this.getCurrDate() != null && !"".equals(this.getCurrDate())){
			Date dateTo = JSDateFormatUtils.parseDateHour(getCurrDate());
			Date dateFrom = this.calculateFromDate(dateTo, this.getCycleNum(),this.getCycleUnit());
			//{3}
			kvs.put("dateFrom", JSDateFormatUtils.formatDateTime(dateFrom));
			//{4}
			kvs.put("dateTo", JSDateFormatUtils.formatDateTime(dateTo));
		}

		//岗位
		if (this.getPost() != null && !"".equals(this.getPost())){
			//{3} or {5}
			kvs.put("post", this.getPost());
		}
		
		return querySql;
	}
	
	/**
	 * 说明：根据当时间向前计算开始日期
	 * @param d   当前时间
	 * @param num  数量
	 * @param integer 单位 
	 * @return
	 */
	private Date calculateFromDate(Date d,Integer num,Integer integer ){
		Date result = null;
/*		if (integer == PeriodType.MIN.ordinal() ){
			return new Date(d.getTime() - num * 60 * 1000); 
		}
*/
		if (integer == PeriodType.HOUR.ordinal() ){
			return new Date(d.getTime() - num * 60 * 60 * 1000); 
		}
		
		GregorianCalendar gc=new GregorianCalendar();
		gc.setTime(d);
		
		//年
		if (integer == PeriodType.YEAR.ordinal()){
			gc.add(1,-1 * num);
			result = gc.getTime();
		}else if (integer == PeriodType.MONTH.ordinal()){
			//月
			gc.add(2,-1 * num);
			result = gc.getTime();
		}else if (integer == PeriodType.WEEK.ordinal()){
			//周
			gc.add(3,-1 * num);
			result = gc.getTime();
		}else if (integer == PeriodType.DAY.ordinal()){
			//天
			gc.add(5,-1 * num);
			result = gc.getTime();
		}
		return result;
	}
	
	public Page<FiveRateBean> getPage() {
		return page;
	}
	public void setPage(Page<FiveRateBean> page) {
		this.page = page;
	}
	public Map<String, Object> getKvs() {
		return kvs;
	}
	public void setKvs(Map<String, Object> kvs) {
		this.kvs = kvs;
	}
	public String getCurrDate() {
		return currDate;
	}
	public void setCurrDate(String currDate) {
		this.currDate = currDate;
	}
	public String getPost() {
		return post;
	}
	public void setPost(String post) {
		this.post = post;
	}
	public String getQuerySql() {
		return querySql;
	}
	public void setQuerySql(String querySql) {
		this.querySql = querySql;
	}
	public String getTotalSql() {
		return totalSql;
	}
	public void setTotalSql(String totalSql) {
		this.totalSql = totalSql;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOrgPath() {
		return orgPath;
	}

	public void setOrgPath(String orgPath) {
		this.orgPath = orgPath;
	}


	public Integer getCycleNum() {
		return cycleNum;
	}
	public void setCycleNum(Integer cycleNum) {
		this.cycleNum = cycleNum;
	}

	public Integer getCycleUnit() {
		return cycleUnit;
	}

	public void setCycleUnit(Integer cycleUnit) {
		this.cycleUnit = cycleUnit;
	}

}
