package com.jsdz.digitalevidence.alarm.alarm110.model;
/**
 * @类名: AlarmPolice
 * @说明: 处警人
 *
 * <AUTHOR>
 * @Date	 2018年3月25日 上午19:11:05
 * 修改记录：
 *
 * @see 	 
 */
import java.io.Serializable;

import com.jsdz.admin.security.model.Employees;

public class AlarmPolice implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -82548393589271607L;
	
	private Long id;
	private AlarmInfo alarmInfo; //警情
	private AlarmFeedback alarmFeedback; //警情

	private Employees police; //警员
	private String createTime;
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}

	public Employees getPolice() {
		return police;
	}
	public void setPolice(Employees police) {
		this.police = police;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public AlarmFeedback getAlarmFeedback() {
		return alarmFeedback;
	}
	public void setAlarmFeedback(AlarmFeedback alarmFeedback) {
		this.alarmFeedback = alarmFeedback;
	}
	public AlarmInfo getAlarmInfo() {
		return alarmInfo;
	}
	public void setAlarmInfo(AlarmInfo alarmInfo) {
		this.alarmInfo = alarmInfo;
	}   
	

	
}
