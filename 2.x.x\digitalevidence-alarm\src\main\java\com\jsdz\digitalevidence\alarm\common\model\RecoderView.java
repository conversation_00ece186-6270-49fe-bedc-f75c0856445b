package com.jsdz.digitalevidence.alarm.common.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 
 * @类名: RecoderView
 * @说明: 视频监督回放
 *
 * @author: kenny
 * @Date	2017年10月26日上午10:36:56
 * 修改记录：
 *
 * @see
 */
public class RecoderView {
	private Long id;
	private Long doId;//记录的id，docid;
	private String glbm;//管理部门编码，观看者管理部门编码
	private String gjbh;//干警编号，观看警员编号 
	private String xm;//干警姓名，观看警员姓名
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date gxsj;//更新时间，监督回放时间
	private Integer start;//开始时长，视频开始时长，单位秒
	private Integer end;//结束时长，视频结束时长，单位秒
	private String spwt;//审批问题；多选用（;）分割
	/*无问题
	 *不文明使用执法记录仪(抽烟、玩游戏等)
	 *使用不规范(静止画面、摄录与执法无关)
	 *未告知正在使用执法记录仪
	 *超短视频
	 *无声音视频
	 *拍摄画面无效(无当事人正脸、询问信息等)
	 */
	private String spyj;//审批意见
	private String reserved1;//可为空
	private String reserved2;//可为空
	private String docName;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDoId() {
		return doId;
	}
	public void setDoId(Long doId) {
		this.doId = doId;
	}
	public String getGlbm() {
		return glbm;
	}
	public void setGlbm(String glbm) {
		this.glbm = glbm;
	}
	public String getGjbh() {
		return gjbh;
	}
	public void setGjbh(String gjbh) {
		this.gjbh = gjbh;
	}
	public String getXm() {
		return xm;
	}
	public void setXm(String xm) {
		this.xm = xm;
	}
	public Date getGxsj() {
		return gxsj;
	}
	public void setGxsj(Date gxsj) {
		this.gxsj = gxsj;
	}
	public Integer getStart() {
		return start;
	}
	public void setStart(Integer start) {
		this.start = start;
	}
	public Integer getEnd() {
		return end;
	}
	public void setEnd(Integer end) {
		this.end = end;
	}
	public String getSpwt() {
		return spwt;
	}
	public void setSpwt(String spwt) {
		this.spwt = spwt;
	}
	public String getSpyj() {
		return spyj;
	}
	public void setSpyj(String spyj) {
		this.spyj = spyj;
	}
	public String getReserved1() {
		return reserved1;
	}
	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}
	public String getReserved2() {
		return reserved2;
	}
	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}
	public String getDocName() {
		return docName;
	}
	public void setDocName(String docName) {
		this.docName = docName;
	}

}
