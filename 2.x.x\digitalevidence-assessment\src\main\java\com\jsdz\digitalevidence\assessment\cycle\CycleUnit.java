package com.jsdz.digitalevidence.assessment.cycle;

import java.util.Date;

/**
 * 
 * @类名: CycleUnit
 * @说明: 周期单位
 *
 * <AUTHOR>
 * @Date	 2017年4月13日 下午5:46:34
 * 修改记录：
 *
 * @see
 */
public abstract class CycleUnit {
	
	protected int day;
	
	/**
	 * @说明：获取某日所在周期
	 *
	 * <AUTHOR>
	 * @param date
	 * @return
	 * 
	 */
	public abstract Cycle fromDate(Date date);
	
	/**
	 * @说明：验证周期是否属于本配置
	 *
	 * <AUTHOR>
	 * @param cycle
	 * @return
	 * 
	 */
	public boolean validate(Cycle cycle) {
		return false;
	}
	
	// 下一周期
	public Cycle nextCycle(Cycle cycle) {
		return null;
	}
	
	// 上一周期
	public Cycle prevCycle(Cycle cycle) {
		return null;
	}

	public int getDay() {
		return day;
	}

	public void setDay(int day) {
		this.day = day;
	}
	
}
