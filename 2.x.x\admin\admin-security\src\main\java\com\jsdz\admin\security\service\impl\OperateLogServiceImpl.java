package com.jsdz.admin.security.service.impl;

import java.util.Calendar;
import java.util.Date;

/**
 * 
 * @类名: OperateLogServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-11 08:56:21
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.admin.security.dao.OperateLogDao;
import com.jsdz.admin.security.model.OperateLog;
import com.jsdz.admin.security.service.OperateLogService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.cache.utils.SysSource;

@Service("OperateLogServiceImpl")
public class OperateLogServiceImpl implements OperateLogService {

	@Autowired
	private OperateLogDao operateLogDao;

	//新增
	public AjaxResult addOperateLog(OperateLog operateLog) {
		AjaxResult result = new AjaxResult();
		operateLogDao.addOperateLog(operateLog);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateOperateLog(OperateLog operateLog) {
		AjaxResult result = new AjaxResult();
		operateLogDao.updateOperateLog(operateLog);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteOperateLog(OperateLog operateLog) {
		AjaxResult result = new AjaxResult();
		operateLogDao.deleteOperateLog(operateLog);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}
	
	//根据系统参数设置定时删除时间过得太久的日志
	public Integer deleteLogBySetting(){
		String s = (String)SysSource.getInstance().getSysConfigParam("LOG_SAVE_DADS");
		
		Integer saveDays;
		try{
			saveDays = Integer.valueOf(s);
		}catch(Exception e){
			saveDays = 60;//默认60天
		}
		
		saveDays = saveDays==null?60:saveDays;

		//当前日期减去saveDays天
		Date beginDate = new Date();
		Calendar date = Calendar.getInstance();
		date.setTime(beginDate);
		date.set(Calendar.DATE, date.get(Calendar.DATE) - saveDays);
		Date logCreateTime = date.getTime();
		
		String hql = "delete from OperateLog where logCreateTime <=:logCreateTime ";
		String[] params = new String[]{"logCreateTime"};
		Object[] values = new Object[]{logCreateTime};
		Integer result = this.execHqlByCondition(hql,params,values);
		return result;
	}

	//按 id 查询
	public OperateLog locateOperateLogById(Long id) {
		return operateLogDao.locateOperateLogById(id);
	}

	//单个查询
	public OperateLog findOperateLogByParam(String queryStr, String[] paramNames, Object[] values) {
		return operateLogDao.findOperateLogByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<OperateLog> findAllOperateLogs() {
		return operateLogDao.findAllOperateLogs();
	}

	//列表查询
	public List<OperateLog> findOperateLogsByParam(String queryStr, String[] paramNames, Object[] values) {
		return operateLogDao.findOperateLogsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<OperateLog> findOperateLogsOnPage(Page<OperateLog> page, String queryStr, String[] paramNames, Object[] values) {
		Page<OperateLog> pos = operateLogDao.findOperateLogsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

	//按条件执行HQL（非查询）
	public Integer execHqlByCondition(final String hql,final String[] params,final Object[] values ){
		return operateLogDao.execHqlByCondition(hql, params, values);
	}
}
