package com.jsdz.digitalevidence.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.param.AttendanceInfoParam;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceInfoBean;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceInfoTaskBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceInfoModel;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface IAttendanceInfoService extends IService<AttendanceInfoModel> {

    Page<AttendanceInfoBean> getDatas(AttendanceInfoParam param, PerLvlBean permLvl);

    void saveData(AttendanceInfoModel model, PerLvlBean permLvl);
    
    void updateDataById(AttendanceInfoModel model, PerLvlBean permLvl);

    AttendanceInfoModel getById(AttendanceInfoModel model, PerLvlBean permLvl);

    void deleteById(AttendanceInfoModel model, PerLvlBean permLvl);

//    List<AttendanceInfoModel> getInfos(Long id);
    List<AttendanceInfoModel> getInfos(String Code);

    Integer findisSave(Long taskId, Long deviceId);

    List<AttendanceInfoTaskBean> getInfoTaskAll(String code);

    List<AttendanceInfoModel> getInfosByCode(String deviceCode);
}
