package com.jsdz.admin.org.dao;

/**
 *
 * @类名: DepartmentDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-10 08:34:34
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.org.model.Department;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface DepartmentDao extends GenericORMEntityDAO<Department,Long> {

	//新增
	public void addDepartment(Department department);

	//修改
	public void updateDepartment(Department department);

	//删除
	public void deleteDepartment(Department department);

	//按id查询
	public Department locateDepartmentById(Long id);

	//单个查询
	public Department findDepartmentByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<Department> findAllDepartments();

	//列表查询
	public List<Department> findDepartmentsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Department> findDepartmentsOnPage(Page<Department> page,String queryStr,String[] paramNames,Object[] values);

}

