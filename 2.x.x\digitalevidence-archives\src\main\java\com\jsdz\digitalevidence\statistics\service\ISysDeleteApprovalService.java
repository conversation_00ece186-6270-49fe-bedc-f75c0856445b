package com.jsdz.digitalevidence.statistics.service;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.bean.param.ApprovedDatasParam;
import com.jsdz.digitalevidence.statistics.model.SysDeleteApprovalModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
public interface ISysDeleteApprovalService extends IService<SysDeleteApprovalModel> {

    Page<SysDeleteApprovalModel> approvedDatas(Long empId, ApprovedDatasParam taskParam);

    List<Employees> findAllEmployeess(Long operatorId);

    void deletedocmentbyId(long docid);
}
