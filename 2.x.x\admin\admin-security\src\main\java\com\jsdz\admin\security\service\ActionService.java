package com.jsdz.admin.security.service;

/**
 * 
 * @类名: ActionService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-03 19:35:10
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.Action;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface ActionService {

	//新增
	public AjaxResult addAction(Action action);

	//修改
	public AjaxResult updateAction(Action action);

	//删除
	public AjaxResult deleteAction(Action action);

	//单个查询
	public Action findActionByParam(String queryStr,String[] paramNames,Object[] values);

	//查询所有
	public List<Action> findALlActions(String queryStr);
	
	//列表查询
	public List<Action> findActionsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Action> findActionsOnPage(Page<Action> page, String queryStr,String[] paramNames,Object[] values);

	public List<Action> findAll();
	
}

