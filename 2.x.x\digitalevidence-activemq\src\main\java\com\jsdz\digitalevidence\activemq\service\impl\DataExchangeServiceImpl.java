package com.jsdz.digitalevidence.activemq.service.impl;

import java.util.Calendar;
import java.util.Date;

/**
 * 
 * @类名: DataExchangeServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-03-11 21:58:37
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.digitalevidence.activemq.dao.DataExchangeDao;
import com.jsdz.digitalevidence.activemq.model.DataExchange;
import com.jsdz.digitalevidence.activemq.service.DataExchangeService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("DataExchangeServiceImpl")
public class DataExchangeServiceImpl implements DataExchangeService {

	@Autowired
	private DataExchangeDao dataExchangeDao;

	//新增
	public AjaxResult addDataExchange(DataExchange dataExchange) {
		AjaxResult result = new AjaxResult();
		dataExchangeDao.addDataExchange(dataExchange);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateDataExchange(DataExchange dataExchange) {
		AjaxResult result = new AjaxResult();
		dataExchangeDao.updateDataExchange(dataExchange);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteDataExchange(DataExchange dataExchange) {
		AjaxResult result = new AjaxResult();
		dataExchangeDao.deleteDataExchange(dataExchange);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public DataExchange findDataExchangeById(Long id){

		return dataExchangeDao.findDataExchangeById(id);
	}

	//按 id 查询
	public DataExchange locateDataExchangeById(Long id) {
		return dataExchangeDao.locateDataExchangeById(id);
	}

	//单个查询
	public DataExchange findDataExchangeByParam(String queryStr, String[] paramNames, Object[] values) {
		return dataExchangeDao.findDataExchangeByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<DataExchange> findAllDataExchanges() {
		return dataExchangeDao.findAllDataExchanges();
	}

	//列表查询
	public List<DataExchange> findDataExchangesByParam(String queryStr, String[] paramNames, Object[] values) {
		return dataExchangeDao.findDataExchangesByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<DataExchange> findDataExchangesOnPage(Page<DataExchange> page, String queryStr, String[] paramNames, Object[] values) {
		Page<DataExchange> pos = dataExchangeDao.findDataExchangesOnPage(page, queryStr, paramNames, values);
		return pos;
	}

	//根据系统参数设置定时删除时间过得太久的交换数据
	@Override
	public Integer deleteDataExchangeSetAnyDays(){
		//系统日志保存一个月就可以了
		Integer saveDays=10;
		//当前日期减去saveDays天
		Date beginDate = new Date();
		Calendar date = Calendar.getInstance();
		date.setTime(beginDate);
		date.set(Calendar.DATE, date.get(Calendar.DATE) - saveDays);
		Date createTime = date.getTime();
		
		String hql = "delete from DataExchange d where d.createTime <=:createTime ";
		String[] params = new String[]{"createTime"};
		Object[] values = new Object[]{createTime};
		return dataExchangeDao.exeCommHql(hql, params, values);
	}
}
