package com.jsdz.digitalevidence.assessment.service.impl;

/**
 * 
 * @类名: OrgAssPeriodServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-01-11 17:27:52
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.digitalevidence.assessment.dao.OrgAssPeriodDao;
import com.jsdz.digitalevidence.assessment.model.org.OrgAssPeriod;
import com.jsdz.digitalevidence.assessment.service.OrgAssPeriodService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("OrgAssPeriodServiceImpl")
public class OrgAssPeriodServiceImpl implements OrgAssPeriodService {

	@Autowired
	private OrgAssPeriodDao orgAssPeriodDao;

	//新增
	public AjaxResult addOrgAssPeriod(OrgAssPeriod orgAssPeriod) {
		AjaxResult result = new AjaxResult();
		orgAssPeriodDao.addOrgAssPeriod(orgAssPeriod);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateOrgAssPeriod(OrgAssPeriod orgAssPeriod) {
		AjaxResult result = new AjaxResult();
		orgAssPeriodDao.updateOrgAssPeriod(orgAssPeriod);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteOrgAssPeriod(OrgAssPeriod orgAssPeriod) {
		AjaxResult result = new AjaxResult();
		orgAssPeriodDao.deleteOrgAssPeriod(orgAssPeriod);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public OrgAssPeriod findOrgAssPeriodById(Long id){

		return orgAssPeriodDao.findOrgAssPeriodById(id);
	}

	//按 id 查询
	public OrgAssPeriod locateOrgAssPeriodById(Long id) {
		return orgAssPeriodDao.locateOrgAssPeriodById(id);
	}

	//单个查询
	public OrgAssPeriod findOrgAssPeriodByParam(String queryStr, String[] paramNames, Object[] values) {
		return orgAssPeriodDao.findOrgAssPeriodByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<OrgAssPeriod> findAllOrgAssPeriods() {
		return orgAssPeriodDao.findAllOrgAssPeriods();
	}

	//列表查询
	public List<OrgAssPeriod> findOrgAssPeriodsByParam(String queryStr, String[] paramNames, Object[] values) {
		return orgAssPeriodDao.findOrgAssPeriodsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<OrgAssPeriod> findOrgAssPeriodsOnPage(Page<OrgAssPeriod> page, String queryStr, String[] paramNames, Object[] values) {
		Page<OrgAssPeriod> pos = orgAssPeriodDao.findOrgAssPeriodsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
