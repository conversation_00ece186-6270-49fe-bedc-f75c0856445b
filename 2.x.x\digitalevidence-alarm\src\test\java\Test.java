	/**
	 * 
	 * @类名: Test
	 * @说明: 
	 *
	 * @author: kenny
	 * @Date	2017年10月30日下午5:11:05
	 * 修改记录：
	 *
	 * @see
	 */

public class Test {
	public static void main(String[] args) throws Exception {
	String s = "25:1";
	Integer secNum = 0;Integer result = 0;
	String[] s1 = s.split(":");
	for (int i=s1.length;i>0;i--){
		System.out.println(s1[i-1]);
		
		if (i==s1.length)
			secNum = 1;
		else
			secNum = secNum * 60;
		result = result + Integer.valueOf(s1[i-1]) * secNum;
	}
	System.out.println("total:" + result);


		
/*		String s = "http://192.168.1.99:8900/media/";
		System.out.println(s.lastIndexOf("/"));
		System.out.println(s.length()-1);
*/
		
	}
}
