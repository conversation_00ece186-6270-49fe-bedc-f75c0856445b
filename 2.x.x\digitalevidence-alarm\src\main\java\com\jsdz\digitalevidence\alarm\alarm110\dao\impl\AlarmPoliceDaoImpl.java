package com.jsdz.digitalevidence.alarm.alarm110.dao.impl;

/**
 *
 * @类名: AlarmPoliceDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2018-03-25 13:43:48
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmPoliceDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmPolice;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class AlarmPoliceDaoImpl extends GenericEntityDaoHibernateImpl<AlarmPolice,Long> implements AlarmPoliceDao{

	//新增
	public void addAlarmPolice(AlarmPolice alarmPolice) {
		this.saveOrUpdate(alarmPolice);
	}

	//删除
	public void deleteAlarmPolice(AlarmPolice alarmPolice) {
		this.delete(alarmPolice);
	}

	//修改
	public void updateAlarmPolice(AlarmPolice alarmPolice) {
		this.merge(alarmPolice);
	}

	//按id查询(游离状态)
	public AlarmPolice findAlarmPoliceById(Long id){

		final String  hql = "from AlarmPolice a where a.id = :id";
		final Long oid = id;
		AlarmPolice data = getHibernateTemplate().execute(new HibernateCallback<AlarmPolice>() {
			public AlarmPolice doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmPolice> list = query.list();
				AlarmPolice rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public AlarmPolice locateAlarmPoliceById(Long id){

		final String  hql = "from AlarmPolice a where a.id = :id";
		final Long oid = id;
		AlarmPolice data = getHibernateTemplate().execute(new HibernateCallback<AlarmPolice>() {
			public AlarmPolice doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmPolice> list = query.list();
				AlarmPolice rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public AlarmPolice findAlarmPoliceByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		AlarmPolice data = getHibernateTemplate().execute(new HibernateCallback<AlarmPolice>() {
		public AlarmPolice doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<AlarmPolice> list = query.list();
			AlarmPolice rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<AlarmPolice> findAllAlarmPolices(){
		return this.find("from AlarmPolice alarmPolice ");
	}

	//列表查询
	public List<AlarmPolice> findAlarmPolicesByCondition(String queryStr,String[] paramNames,Object[] values){
		List<AlarmPolice> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<AlarmPolice> findAlarmPolicesOnPage(Page<AlarmPolice> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<AlarmPolice>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//执行自定义的hql
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
}
