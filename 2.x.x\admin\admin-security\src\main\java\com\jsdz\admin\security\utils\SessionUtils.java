/**
 * %%
 * %%
 */
package com.jsdz.admin.security.utils;
import java.util.List;

import javax.servlet.http.HttpSession;
import com.jsdz.admin.security.model.Operator;

/**
 * @类名: SessionUtils
 * @说明: 会话工具类
 *
 * <AUTHOR>
 * @Date	 2017年4月12日 上午9:35:28
 * 修改记录：
 *
 * TODO
 *   目前用于测试，权限管理完成后再重构
 *   
 * @see 	 
 */
public class SessionUtils {
	
	
	public static final ThreadLocal<List<String>> roleSession = new ThreadLocal<List<String>>();
	public static final ThreadLocal<Operator> operatorSession = new ThreadLocal<Operator>();

	
	// 获取当前RoleNmae
	public static List<String> getCurrentRoles() {
		List<String> list = roleSession.get();
		return list==null || list.size() == 0 ? null : list;
	}
	
	// 设置当前RoleNmae
	public static void setCurrentRoles(List<String> value) {
		roleSession.set(value);
	}
	
	// 获取当前操作员
	public static Operator getCurrentOperator() {
		return operatorSession.get();
	}
	// 保存当前操作员
	public static void setCurrentOperator(Operator value) {
		operatorSession.set(value);
	}
	
}
