<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

	<!-- *********考核监督DTO********* -->
	<!-- 考核资料bean -->
	<class name="com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentBean" 
								table="TMP_ASSESSMENT_DOCUMENT">  
		<id name="id" >
            <generator class="assigned"/>
        </id>
        <property name="orgName" type="string"/>
        <property name="orgPath" type="string"/>
        <property name="policeCode" type="string"/>
        <property name="policeName" type="string"/>
        <property name="siteCode" type="string"/>
        <property name="type" type="string"/>
        <property name="impLevelRec">
           	<type name="org.hibernate.type.EnumType">
           		<param name="enumClass">com.jsdz.digitalevidence.document.model.ImportantLevel</param>
           	</type>
        </property>
        <property name="impLevel">
           	<type name="org.hibernate.type.EnumType">
           		<param name="enumClass">com.jsdz.digitalevidence.document.model.ImportantLevel</param>
           	</type>
        </property>
        <property name="cate">
            	<type name="org.hibernate.type.EnumType">
            		<param name="enumClass">com.jsdz.digitalevidence.document.model.DocumentCate</param>
            	</type>
            </property>
        <property name="docName" type="string" />
        <property name="uploadTime" type="date" />
        <property name="createTime" type="date" />
        	<property name="clarity">
            	<type name="org.hibernate.type.EnumType">
            		<param name="enumClass">com.jsdz.digitalevidence.document.model.VideoClarity</param>
            	</type>
            </property>
        <property name="duration"/>
        <property name="equimentCode" />
        <property name="fileM"/>
        <property name="siteAddr"/>
        <property name="comments"/>
        <property name="enforceTypeId"/>
        <property name="enforceTypeName"/>
        <!--  -->
    	<property name="isAssessed"/>
    	<!-- 拍摄分辨率-->
		<property name="assessmentCate">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.assessment.model.AssessmentCate
            		</param>
            	</type>
		</property>
    	<property name="assessDate"/>
    </class>  
    
    <!-- 考核推送文档bean -->
	<class name="com.jsdz.digitalevidence.assessment.bean.AssessmentPushDocBean" 
								table="TMP_ASSESSMENT_PUSHING_DOC">  
		<id name="pushDocId" >
            <generator class="assigned"/>
        </id>
        <property name="policeCode" />
        <property name="policeName" />
        <property name="orgName" />
        <property name="deptName" />
		<property name="position" />
		<property name="docId"/>
		<property name="docName"/>
		<property name="assessorPoliceCode"/>
		<property name="assessorName"/>
    	<property name="hasAssessed"/>
    	<property name="createTime" type="timestamp"/>
    </class> 
    
    <!-- 考核预警概要bean -->
	<class name="com.jsdz.digitalevidence.assessment.bean.AssessmentAlertSumBean" 
								table="TMP_ASSESSMENT_ALERT_SUM">  
		<id name="alertId" >
            <generator class="assigned"/>
        </id>
        <property name="policeCode" type="string"/>
        <property name="policeName" type="string"/>
        <property name="sex"/>
        <property name="age"/>
        <property name="orgName" type="string"/>
        <property name="deptName" type="string"/>
		<property name="position" type="string"/>
		<property name="createTime"/>
    	<property name="hasFeedback"/>
    	<!-- 报告类型 -->
		<property name="type">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.assessment.model.ReportType
            		</param>
            	</type>
		</property>
    </class>  
    
    <!-- 考核预警报告bean -->
	<class name="com.jsdz.digitalevidence.assessment.bean.AssessmentAlertBean" 
								table="TMP_ASSESSMENT_ALERT_BEAN">  
		<id name="alertId" >
            <generator class="assigned"/>
        </id>
        <property name="policeCode" type="string"/>
        <property name="policeName" type="string"/>
        <property name="sex"/>
        <property name="age"/>
        <property name="orgName" type="string"/>
        <property name="deptName" type="string"/>
		<property name="position" type="string"/>
		<property name="createTime"/>
		<property name="startDate" type="timestamp"/>
		<property name="endDate" type="timestamp"/>
    	<property name="feedback"/>
    	<!-- 预警项 -->
    	<list name="items" lazy="false" cascade="none">  
            <key column="alert_id"></key>  
            <index column="_index"></index>
            <one-to-many entity-name="alertItemBean"/>  
        </list>  
    </class>
    
    <!-- 考核预警项Bean -->
	<class name="com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem"
								table="TMP_ASSESSMENT_ALERT_ITEM" entity-name="alertItemBean">  
		<id name="alertItemId" column="alert_item_id" >
            <generator class="native"/>
        </id>
        <property name="item" column="ALTER_ITEM"></property>  
	    <property name="v" column="V" type="com.jsdz.core.PersistentObject"></property>  
	    <property name="ref" column="REF" type="com.jsdz.core.PersistentObject"></property>
    </class>
    
    <!-- 完整考核报告bean -->
	<class name="com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReportBean" 
								table="TMP_COMPLETE_ASSESSMENT_REPORT_BEAN">  
		<id name="reportId" >
            <generator class="assigned"/>
        </id>
        <property name="policeCode" />
        <property name="policeName" />
        <property name="sex"/>
        <property name="age"/>
        <property name="orgName" />
        <property name="deptName" />
		<property name="position" />
		<property name="createTime" type="timestamp"/>
		<property name="startDate" type="timestamp"/>
		<property name="endDate" type="timestamp"/>
		<property name="comments"/>
		<property name="frozen" column="IS_FROZEN"></property>
    	<!-- 拍摄统计 column="SHOOTING_SUM_ID" -->
    	<!-- <many-to-one name="sumOfShooting" entity-name="shootingSumBean" cascade="none" lazy="false" 
    					column="SHOOTING_SUM_ID">
		</many-to-one> --> 		
		<!-- 拍摄考核统计 column="SHOOTING_ASSESSMENT_SUM_ID" -->
		<!-- <many-to-one name="sumOfShootingAssessment" entity-name="shootingAssessmentSumBean" cascade="none" lazy="false" 
						column="SHOOTING_ASSESSMENT_SUM_ID">
		</many-to-one> -->  
    </class>
    
    <!-- 拍摄统计 -->
	<class 
		name="com.jsdz.digitalevidence.assessment.model.ShootingSummary" 
					table="TMP_ANALYSIS_SHOOTING_SUMMARY"
					entity-name="shootingSumBean">
		<id name="id" column="ID" type="long">
			<generator class="native"/>
		</id>
		
		<!-- 视频数 -->
		<property name="countOfVedio" column="COUNT_OF_VEDIO"></property>
		<!-- 视频时长 -->
		<property name="lengthOfVedio" column="LENGTH_OF_VEDIO"></property>
		<!-- 视频大小 -->
		<property name="sizeOfVedio" column="SIZE_OF_VEDIO"></property>
		
		<!-- 音频数 -->
		<property name="countOfAudio" column="COUNT_OF_AUDIO"></property>
		<!-- 音频时长 -->
		<property name="lengthOfAudio" column="LENGTH_OF_AUDIO"></property>
		<!-- 音频大小 -->
		<property name="sizeOfAudio" column="SIZE_OF_AUDIO"></property>
		
		<!-- 图片数 -->
		<property name="countOfPic" column="COUNT_OF_PIC"></property>
		<!-- 图片大小 -->
		<property name="sizeOfPic" column="SIZE_OF_PIC"></property>
		
		<!-- 日志数 -->
		<property name="countOfLog" column="COUNT_OF_LOG"></property>
		<!-- 日志大小 -->
		<property name="sizeOfLog" column="SIZE_OF_LOG"></property>
		
	</class>
	
	<!-- 拍摄考核统计 -->
	<class 
		name="com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary" 
			table="TMP_ASSESSMENT_SHOOTING_SUMMARY"
			entity-name="shootingAssessmentSumBean">
		<id name="id" column="ID">
		    <generator class="native"/>
		</id>
		
		<!-- 文明规范数 -->
		<property name="countOfWMGF"></property>
		<!-- 不存在违法违纪 -->
		<property name="countOfNoWFWJ" ></property>
		
		<!-- 按要求拍摄数 -->
		<property name="countOfAYQPS" ></property>
		<!-- 按要求运用语言 -->
		<property name="countOfYYYY" ></property>
		
		<!-- 拍摄角度准确数 -->
		<property name="countOfShootingAngleA" ></property>
		<!-- 拍摄角度一般数 -->
		<property name="countOfShootingAngleG" ></property>
        <!-- 拍摄角度错误数 -->
		<property name="countOfShootingAngleE" ></property>
		
		<!-- 拍摄内容准确数 -->
		<property name="countOfShootingContentA" ></property>
		<!-- 拍摄内容一般数 -->
		<property name="countOfShootingContentG" ></property>
        <!-- 拍摄内容错误数 -->
		<property name="countOfShootingContentE" ></property>
		
		<!-- 拍摄分辨率高数 -->
		<property name="countOfShootingResolutionH" ></property>
		<!-- 拍摄分辨率一般数 -->
		<property name="countOfShootingResolutionG" ></property>
        <!-- 拍摄分辨率低数 -->
		<property name="countOfShootingResolutionL" ></property>
		
		<!-- 期间，考核视频数 -->
		<property name="totalAssessingDoc" ></property>
        <!-- 期间，上传视频总数 -->
		<property name="totalDoc" ></property>
		
	</class>
	
	<!-- 完整考核报告bean 报表 -->
	<class name="com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReport" 
								table="TMP_COMPLETE_ASSESSMENT_REPORT">  
		<id name="reportId" >
            <generator class="assigned"/>
        </id>
        
        <property name="policeCode" />
        <property name="policeName" />
        <property name="sex"/>
        <property name="age"/>
        <property name="orgName" />
        <property name="deptName" />
		<property name="position" />
		<property name="createTime" type="timestamp"/>
		<property name="startDate" type="timestamp"/>
		<property name="endDate" type="timestamp"/>
		<property name="comments"/>
		
		<!-- 视频数 -->
		<property name="countOfVedio" column="COUNT_OF_VEDIO"></property>
		<!-- 视频时长 -->
		<property name="lengthOfVedio" column="LENGTH_OF_VEDIO"></property>
		<!-- 视频大小 -->
		<property name="sizeOfVedio" column="SIZE_OF_VEDIO"></property>
		
		<!-- 音频数 -->
		<property name="countOfAudio" column="COUNT_OF_AUDIO"></property>
		<!-- 音频时长 -->
		<property name="lengthOfAudio" column="LENGTH_OF_AUDIO"></property>
		<!-- 音频大小 -->
		<property name="sizeOfAudio" column="SIZE_OF_AUDIO"></property>
		
		<!-- 图片数 -->
		<property name="countOfPic" column="COUNT_OF_PIC"></property>
		<!-- 图片大小 -->
		<property name="sizeOfPic" column="SIZE_OF_PIC"></property>
		
		<!-- 日志数 -->
		<property name="countOfLog" column="COUNT_OF_LOG"></property>
		<!-- 日志大小 -->
		<property name="sizeOfLog" column="SIZE_OF_LOG"></property>
		
		<!-- 文明规范数 -->
		<property name="countOfWMGF"></property>
		<!-- 不存在违法违纪 -->
		<property name="countOfNoWFWJ" ></property>
		
		<!-- 按要求拍摄数 -->
		<property name="countOfAYQPS" ></property>
		<!-- 按要求运用语言 -->
		<property name="countOfYYYY" ></property>
		
		<!-- 拍摄角度准确数 -->
		<property name="countOfShootingAngleA" ></property>
		<!-- 拍摄角度一般数 -->
		<property name="countOfShootingAngleG" ></property>
        <!-- 拍摄角度错误数 -->
		<property name="countOfShootingAngleE" ></property>
		
		<!-- 拍摄内容准确数 -->
		<property name="countOfShootingContentA" ></property>
		<!-- 拍摄内容一般数 -->
		<property name="countOfShootingContentG" ></property>
        <!-- 拍摄内容错误数 -->
		<property name="countOfShootingContentE" ></property>
		
		<!-- 拍摄分辨率高数 -->
		<property name="countOfShootingResolutionH" ></property>
		<!-- 拍摄分辨率一般数 -->
		<property name="countOfShootingResolutionG" ></property>
        <!-- 拍摄分辨率低数 -->
		<property name="countOfShootingResolutionL" ></property>
		
		<!-- 期间，考核视频数 -->
		<property name="totalAssessingDoc" ></property>
        <!-- 期间，上传视频总数 -->
		<property name="totalDoc" ></property>
    	
    </class>
    
    <!-- 考核预警报告-报表 -->
	<class name="com.jsdz.digitalevidence.assessment.bean.AssessmentAlertReport" 
		table="TMP_ASSESSMENT_ALERT_REPORT">  
		<id name="alertId" >
            <generator class="assigned"/>
        </id>
        <property name="policeCode" type="string"/>
        <property name="policeName" type="string"/>
        <property name="sex"/>
        <property name="age"/>
        <property name="orgName" type="string"/>
        <property name="deptName" type="string"/>
		<property name="position" type="string"/>
		<property name="createTime"/>
		<property name="startDate" type="timestamp"/>
		<property name="endDate" type="timestamp"/>
    	<property name="feedback"/>
    	<property name="item"></property>
	    <property name="v" type="com.jsdz.core.PersistentObject"></property>  
	    <property name="ref" type="com.jsdz.core.PersistentObject"></property>
    </class>
    <!-- 考核工作统计 -->
    <class name="com.jsdz.digitalevidence.assessment.bean.AssessmentWorkStatisticsBean" 
		table="TMP_ASSESSMENT_Work_Statistics">  
		<id name="id" >
            <generator class="assigned"/>
        </id>
        <property name="name" type="string"/>
        <property name="countOfChecking" type="integer"/>
        <property name="countOfTotalAssessing" type="integer"/>
        <property name="countOfPushAssessing" type="integer"/>
        <property name="countOfPushed" type="integer"/>
		<!-- <property name="startDate" type="timestamp"/>
		<property name="endDate" type="timestamp"/> -->
	 </class>
	 
	     <!--按警员统计上传，抽查，考评数据  -->
	<class 	name="com.jsdz.digitalevidence.assessment.bean.plan.AssessmentStatisticsBean">
		<!--警员  -->
		<id name="id"><generator class="assigned"/></id>	
		<property name="policeCode"></property>
		<property name="policeName"></property>
		<!--上传文件数里  -->
		<property name="uploadDocs"></property>
		<!--时间段  -->
		<property name="startDate"></property>
		<property name="endDate"></property>
		<!--抽查数，抽查率   -->
		<property name="samplingDocs"></property>
		<property name="samplingRate"></property>
		<!--考评数，考评率  -->
		<property name="assessDocs"></property>
		<property name="accessRate"></property>
	 </class>
	 
	<!--拍摄考核  -->
	<class 	name="com.jsdz.digitalevidence.assessment.bean.plan.ShootingAssessmentBean">
		<!--警员  -->
		<id name="id"><generator class="assigned"/></id>
		<!-- 是否文明规范 -->	
		<property name="isWMGF"></property>
		<!--  不存在违法违纪 -->
		<property name="isNoWFWJ"></property>
		<!--按要求拍摄  -->
		<property name="isAYQPS"></property>
		<!--按要求运用语言  -->
		<property name="isYYYY"></property>
		<!-- 拍摄角度 -->
		<property name="shootingAngle"></property>
		<!--拍摄内容要素   -->
		<property name="shootingContent"></property>
		<!-- 拍摄分辨率 -->
		<property name="shootingResolution"></property>
		<!--考核时间  -->
		<property name="assessDate"></property>
		<!-- 考核文档 -->
		<property name="docId"></property>
		<property name="docName"></property>
		<property name="uploadTime"></property>
		<!-- 上传人 -->
		<property name="policeId"></property>
		<property name="policeCode"></property>
		<property name="policeName"></property>
		<!-- 考核员 -->
		<property name="inspectorId"></property>
		<property name="inspectorCode"></property>
		<property name="inspectorName"></property>
	 </class>	 
	 
    <!-- 打开考核预警报告，mysql -->
    <sql-query name="getAssessmentAlertBean">  
        <![CDATA[
        select
		        d.ID as {a.alertId} ,
				b.workNumber as {a.policeCode},
				b.name as {a.policeName} ,
				c.position as {a.position},
				b.male as {a.sex},
				b.age as {a.age},
				o.orgName as {a.orgName},
				d1.name as {a.deptName},
				d.create_date as {a.createTime}, 
				d.fee_back as {a.feedback},
				d.start_date as {a.startDate},
				d.end_date as {a.endDate},
				{items.*}
				from T_ASSESSMENT_ALERT d
				left join admin_employees b on b.id = d.police_id
				left join admin_position c on b.positionId = c.id
				left join admin_organization o on o.id = b.organizationId
				left join admin_region r on o.regionId = r.id
				left join admin_department d1 on d1.id = b.departmentId
				left outer join T_ASSESSMENT_ALERT_ITEM as items on d.ID = items.alert_id
				where 1=1 and d.ID = :alertId
        ]]>
    </sql-query>
    
    <!-- 完整考核报告，mysql -->
    <sql-query name="getCompleteShootingAccessmentReport">  
        <![CDATA[
         select
		        d.report_id as {a.reportId} ,
				b.workNumber as {a.policeCode},
				b.name as {a.policeName} ,
				c.position as {a.position},
				b.male as {a.sex},
				b.age as {a.age},
				o.orgName as {a.orgName},
				d1.name as {a.deptName},
				d.submit_date as {a.createTime},
				d.comments as {a.comments},
				d.start_date as {a.startDate},
				d.end_date as {a.endDate},
				d.IS_FROZEN as {a.frozen},
				{a1.*},
				{a2.*}
				from T_ASSESSMENT_REPORT d
				left join admin_employees b on b.id = d.police_id
				left join admin_position c on b.positionId = c.id
				left join admin_organization o on o.id = b.organizationId
				left join admin_region r on o.regionId = r.id
				left join admin_department d1 on d1.id = b.departmentId
				left join t_analysis_shooting_summary a1 on d.shooting_sum_id = a1.id
				left join t_assessment_shooting_summary a2 on d.SHOOTING_ASSESSMENT_SUM_ID = a2.id				
				where 1=1 and d.report_id = :reportId	
        ]]>
    </sql-query>
    


        
	
</hibernate-mapping>