/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.cycle;

import java.io.IOException;
import java.util.Date;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: DateConvertor
 * @说明: 日期转换器
 *
 * <AUTHOR>
 * @Date	 2017年8月29日 下午7:05:54
 * 修改记录：
 *
 * @see 	 
 */
public class JsonDateSerializer extends JsonSerializer<Date> {
	
	@Override    
    public void serialize(Date date, JsonGenerator gen, SerializerProvider provider)    
            throws IOException, JsonProcessingException {    
        String value = DateTimeUtils.DateToString(date, DateTimeUtils.defaultDatePatten2);
        gen.writeString(value);    
    } 

}
