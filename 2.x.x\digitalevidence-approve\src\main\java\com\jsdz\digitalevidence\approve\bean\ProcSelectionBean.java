/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.bean;

import org.activiti.engine.repository.ProcessDefinition;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: ProcSelectionBean
 * @说明: 流程定义选择DTO
 *
 * <AUTHOR>
 * @Date	 2017年4月14日 上午9:50:43
 * 修改记录：
 *
 * @see 	 
 */
public class ProcSelectionBean extends AbstractDTO {
	
	/** */
	private String procId;
	/** 流程名称*/
	private String procName;
	/** 描述*/
	private String procDesc;
	/** 版本*/
	private int version;
	/** 是否选上*/
	private boolean selected;
	
	/**
	 * @说明：赋值
	 *
	 * <AUTHOR>
	 * @param src
	 * 
	 */
	public void assign(ProcessDefinition src) {
		this.setProcId(src.getId());
		this.setProcName(src.getName());
		this.setProcDesc(src.getDescription());
		this.setVersion(src.getVersion());
		this.selected = selected;
		
	}

	public int getVersion() {
		return version;
	}

	public void setVersion(int version) {
		this.version = version;
	}
	
	public String getProcId() {
		return procId;
	}
	public void setProcId(String procId) {
		this.procId = procId;
	}
	public String getProcName() {
		return procName;
	}
	public void setProcName(String procName) {
		this.procName = procName;
	}
	public String getProcDesc() {
		return procDesc;
	}
	public void setProcDesc(String procDesc) {
		this.procDesc = procDesc;
	}

	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}
	
	
}
