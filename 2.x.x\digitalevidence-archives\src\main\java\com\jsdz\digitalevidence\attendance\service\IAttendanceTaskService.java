package com.jsdz.digitalevidence.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceTaskBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceScopeModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceTaskModel;
import com.jsdz.digitalevidence.attendance.bean.param.AttendanceTaskParam;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface IAttendanceTaskService extends IService<AttendanceTaskModel> {

    Page<AttendanceTaskBean> getDatas(AttendanceTaskParam param, PerLvlBean permLvl);

    void saveData(AttendanceTaskModel model, PerLvlBean permLvl);

    void updateDataById(AttendanceTaskModel model, PerLvlBean permLvl);

    AttendanceTaskModel getById(AttendanceTaskModel model, PerLvlBean permLvl);

    void deleteById(AttendanceTaskModel model, PerLvlBean permLvl);
}
