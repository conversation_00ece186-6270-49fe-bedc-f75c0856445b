package com.jsdz.admin.security.dao;

/**
 *
 * @类名: PostDao
 * @说明:
 * @author: kenny
 * @Date 2017-12-30 11:53:22
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.Post;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface PostDao extends GenericORMEntityDAO<Post,Long> {

	//新增
	public void addPost(Post post);

	//修改
	public void updatePost(Post post);

	//删除
	public void deletePost(Post post);

	//按id查询,结果是游离状态的数据
	public Post findPostById(Long id);

	//按id查询
	public Post locatePostById(Long id);

	//单个查询
	public Post findPostByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<Post> findAllPosts();

	//列表查询
	public List<Post> findPostsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Post> findPostsOnPage(Page<Post> page,String queryStr,String[] paramNames,Object[] values);
	
	//更新下级的path
	public void updatePostPath(String oldPath,String newPath);

}

