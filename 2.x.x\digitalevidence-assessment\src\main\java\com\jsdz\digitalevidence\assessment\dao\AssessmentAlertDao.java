/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.dao;

import java.util.Date;

import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertBean;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert;

/**
 * @类名: AssessmentAlertDao
 * @说明: 考核预警Dao
 *
 * <AUTHOR>
 * @Date	 2017年8月7日 下午5:51:34
 * 修改记录：
 *
 * @see 	 
 */
public interface AssessmentAlertDao extends GenericORMEntityDAO<AssessmentAlert, Long> {
	
	/**
	 * @说明：获取考核预警Bean
	 *
	 * <AUTHOR>
	 * @param policeId
	 * @return
	 * 
	 */
	public AssessmentAlertBean getAssessmentAlertBean(Long id);
	
	/**
	 * @说明：获取月度考核预警
	 *
	 * <AUTHOR>
	 * @param policeId
	 * @return
	 * 
	 */
	public AssessmentAlert getAssessmentAlert(Long policeId, Date cycleDate);
	
	/**
	 * @说明：获取周考核预警
	 *
	 * <AUTHOR>
	 * @param policeId
	 * @return
	 * 
	 */
	public AssessmentAlert getWeeklyAssessmentAlert(Long policeId, Date reportDate);
	
}
