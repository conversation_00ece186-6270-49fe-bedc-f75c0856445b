<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping >
	<!-- 系统日志 -->
	<class name="com.jsdz.digitalevidence.activemq.model.SysLog" table="admin_syslog">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="javaClass" column="java_class"></property>
		<property name="logType" column="log_type"></property>
		<property name="topic" column="topic"></property>
		<property name="keyWord" column="key_word"></property>
		<property name="context" column="context"></property>
		<property name="createTime" column="create_time"></property>
	</class>	
	
	<!-- 数据交换数据库 -->
	<class name="com.jsdz.digitalevidence.activemq.model.DataExchange" table="admin_exchange_data">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="topic" column="topic"></property>
		<property name="option" column="option_name"></property>
		<property name="context" column="context"></property>
		<property name="note" column="note"></property>
		<property name="createTime" column="create_time"></property>
		<property name="proccessFlag" column="proccess_flag"></property>
		<property name="proccessCount" column="proccess_count"></property>
		<property name="proccessResult" column="proccess_result"></property>
	</class>
	
	<!-- 数据交换主题表 -->
	<class name="com.jsdz.digitalevidence.activemq.model.SysLogTopic" table="admin_syslog_topic">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="topic" column="topic"></property>
	</class>	
		

</hibernate-mapping>