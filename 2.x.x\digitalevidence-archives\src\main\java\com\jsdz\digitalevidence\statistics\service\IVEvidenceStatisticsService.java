package com.jsdz.digitalevidence.statistics.service;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.model.VEvidenceStatisticsModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jsdz.digitalevidence.statistics.bean.param.VEvidenceStatisticsParam;

/**
 * <p>
 * VIEW 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface IVEvidenceStatisticsService extends IService<VEvidenceStatisticsModel> {

    Page<VEvidenceStatisticsModel> getDatas(VEvidenceStatisticsParam param, PerLvlBean permLvl);
}
