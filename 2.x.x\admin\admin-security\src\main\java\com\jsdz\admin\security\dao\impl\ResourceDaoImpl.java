package com.jsdz.admin.security.dao.impl;
/**
 * 
 * @类名: ResourceDaoImpl
 * @说明: 资源接口实现类
 *
 * @author: kenny
 * @Date	2017年4月27日上午9:49:12
 * 修改记录：
 *
 * @see
 */
import java.util.List;

import org.springframework.stereotype.Service;

import com.jsdz.admin.security.dao.ResourceDao;
import com.jsdz.admin.security.model.Resource;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
@Service("ResourceDaoImpl")
public class ResourceDaoImpl extends GenericEntityDaoHibernateImpl<Resource, Long> implements ResourceDao{
	
	public List<Resource> findAll(){
		String hql = "from Resourrce";
		return this.find(hql);
	}

}
