================================================================================
                        档案文档关联表功能说明文档
================================================================================

项目名称：数字证据管理系统
功能模块：档案文档关联管理（关联表方式）
创建时间：2025-01-19
文档版本：v1.0

================================================================================
一、功能概述
================================================================================

档案文档关联表功能是数字证据管理系统中用于管理档案(Archives)与文档(Document)之间
多对多关系的核心功能模块。该功能采用独立关联表(t_archives_relation)的方式实现，
支持批量关联、精确删除、丰富查询等完整的CRUD操作。

主要特点：
- 支持一个档案关联多个文档
- 支持一个文档关联多个档案  
- 记录关联操作的详细信息（操作人、操作时间等）
- 提供批量操作能力
- 通过数据库视图提供丰富的查询功能

================================================================================
二、数据库设计
================================================================================

2.1 关联表结构 (t_archives_relation)
------------------------------------
字段名称          类型        说明
id               BIGINT      主键，自增
archivesId       BIGINT      档案ID，外键关联t_archives_data.archivesId
docId            BIGINT      文档ID，外键关联t_doc.doc_id
createTime       DATETIME    关联创建时间
relationUserId   BIGINT      关联操作用户ID
relationUserName VARCHAR     关联操作用户姓名

2.2 查询视图 (v_archives_doc)
-----------------------------
该视图整合了档案信息、文档信息和关联关系信息，提供丰富的查询数据：
- 档案基本信息（编号、标题、描述等）
- 文档详细信息（名称、大小、类型、时长等）
- 关联操作信息（操作人、操作时间等）
- 组织机构信息

================================================================================
三、后端实现架构
================================================================================

3.1 核心类结构
--------------
ArchivesRelation.java          - 关联实体类
ArchivesRelationMapper.java    - MyBatis映射接口
IArchivesRelationService.java  - 服务接口
ArchivesRelationServiceImpl.java - 服务实现类
ArchivesDocController.java     - 控制器类

ArchivesDoc.java               - 查询视图实体类
ArchivesDocMapper.java         - 查询视图映射接口
IArchivesDocService.java       - 查询视图服务接口
ArchivesDocServiceImpl.java    - 查询视图服务实现

3.2 接口设计
------------
POST /archivesDoc/saveArchivesDocs    - 批量添加关联
POST /archivesDoc/deleteArchivesDoc   - 删除关联
GET  /archivesDoc/findDocByArchivesId - 查询档案关联的文档

================================================================================
四、功能详细说明
================================================================================

4.1 添加关联功能
----------------
功能描述：支持将多个文档批量关联到指定档案

后端实现：
- 接口：POST /archivesDoc/saveArchivesDocs
- 参数：archivesId(档案ID), docIds(文档ID列表，逗号分隔)
- 核心逻辑：
  1. 解析文档ID列表
  2. 检查重复关联
  3. 批量插入关联记录
  4. 记录操作人信息

前端调用：
- 文件：datamanage.js, datamanage_js/datamanage.js
- 场景：一键归档功能
- 实现：releaseArchivesVideo()函数

代码示例：
```javascript
$.ajax({
    url: context + "/archivesDoc/saveArchivesDocs",
    type: "post",
    data: {
        "archivesId": archivesId,
        "docIds": "1,2,3,4"
    }
});
```

4.2 删除关联功能
----------------
功能描述：精确删除指定的档案文档关联关系

后端实现：
- 接口：POST /archivesDoc/deleteArchivesDoc
- 参数：rid(关联记录ID)
- 核心逻辑：
  1. 根据关联ID删除记录
  2. 物理删除，不可恢复

前端调用：
- 文件：systemmanagement_js/filemanage.js
- 场景：文件管理界面取消关联
- 实现：removerelation()函数

代码示例：
```javascript
$.ajax({
    url: context + "/archivesDoc/deleteArchivesDoc",
    type: "post",
    data: { "rid": relationId }
});
```

4.3 查询关联功能
----------------
功能描述：查询指定档案关联的所有文档信息

后端实现：
- 接口：GET /archivesDoc/findDocByArchivesId
- 参数：archivesId(档案ID)
- 核心逻辑：
  1. 通过v_archives_doc视图查询
  2. 返回完整的文档和关联信息
  3. 格式化文件大小和时长显示

前端调用：
- 文件：systemmanagement_js/filemanage.js
- 场景：文件管理界面展示关联文档
- 实现：getdoctable()函数

代码示例：
```javascript
$("#doctable").bootstrapTable({
    url: context + '/archivesDoc/findDocByArchivesId',
    queryParams: function(params) {
        return { archivesId: archivesId }
    }
});
```

================================================================================
五、前端界面说明
================================================================================

5.1 主要使用页面
----------------
1. 数据管理页面 (datamanage.jsp)
   - 功能：一键归档
   - 特点：支持批量选择文档进行归档

2. 文件管理页面 (filemanage.jsp)
   - 功能：查看和管理档案关联的文档
   - 特点：表格展示，支持取消关联

5.2 用户操作流程
----------------
归档操作流程：
1. 在数据管理页面选择要归档的文档
2. 点击"一键归档"按钮
3. 选择现有档案或创建新档案
4. 系统自动建立关联关系

取消关联流程：
1. 在文件管理页面选择档案
2. 查看该档案关联的文档列表
3. 点击"取消关联"按钮
4. 确认后删除关联关系

================================================================================
六、技术特点与优势
================================================================================

6.1 技术特点
------------
- 采用MyBatis注解方式实现数据访问
- 使用数据库视图优化查询性能
- 支持事务处理保证数据一致性
- 前端采用Bootstrap Table组件展示数据

6.2 功能优势
------------
1. 完整的CRUD操作支持
2. 批量操作提高工作效率
3. 详细的操作记录便于审计
4. 灵活的多对多关系支持
5. 丰富的查询视图数据

6.3 性能优势
------------
- 通过视图预聚合数据，减少关联查询
- 索引优化提高查询效率
- 批量操作减少数据库交互次数

================================================================================
七、注意事项与限制
================================================================================

7.1 使用注意事项
----------------
1. 删除关联操作不可恢复，需谨慎操作
2. 批量关联时会自动跳过已存在的关联关系
3. 关联操作需要相应的权限验证

7.2 系统限制
------------
1. 单次批量关联的文档数量建议不超过1000个
2. 关联表不支持直接修改，只能删除后重新创建
3. 查询视图的数据实时性依赖于基础表的更新

================================================================================
八、维护与扩展
================================================================================

8.1 日常维护
------------
- 定期清理无效的关联记录
- 监控关联表的数据增长情况
- 检查视图的查询性能

8.2 功能扩展建议
----------------
- 增加关联关系的修改功能
- 支持关联关系的批量导入导出
- 增加关联操作的审计日志
- 提供关联关系的统计分析功能

================================================================================
文档结束
================================================================================
