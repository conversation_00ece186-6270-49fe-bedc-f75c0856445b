package com.jsdz.digitalevidence.activemq.dao.impl;

/**
 *
 * @类名: DataExchangeDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2018-03-11 21:58:37
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.activemq.dao.DataExchangeDao;
import com.jsdz.digitalevidence.activemq.model.DataExchange;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class DataExchangeDaoImpl extends GenericEntityDaoHibernateImpl<DataExchange,Long> implements DataExchangeDao{

	//新增
	public void addDataExchange(DataExchange dataExchange) {
		this.saveOrUpdate(dataExchange);
	}

	//删除
	public void deleteDataExchange(DataExchange dataExchange) {
		this.delete(dataExchange);
	}

	//修改
	public void updateDataExchange(DataExchange dataExchange) {
		this.merge(dataExchange);
	}

	//按id查询(游离状态)
	public DataExchange findDataExchangeById(Long id){

		final String  hql = "from DataExchange d where d.id = :id";
		final Long oid = id;
		DataExchange data = getHibernateTemplate().execute(new HibernateCallback<DataExchange>() {
			public DataExchange doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DataExchange> list = query.list();
				DataExchange rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public DataExchange locateDataExchangeById(Long id){

		final String  hql = "from DataExchange d where d.id = :id";
		final Long oid = id;
		DataExchange data = getHibernateTemplate().execute(new HibernateCallback<DataExchange>() {
			public DataExchange doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DataExchange> list = query.list();
				DataExchange rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public DataExchange findDataExchangeByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		DataExchange data = getHibernateTemplate().execute(new HibernateCallback<DataExchange>() {
		public DataExchange doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<DataExchange> list = query.list();
			DataExchange rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<DataExchange> findAllDataExchanges(){
		return this.find("from DataExchange dataExchange ");
	}

	//列表查询
	public List<DataExchange> findDataExchangesByCondition(String queryStr,String[] paramNames,Object[] values){
		List<DataExchange> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<DataExchange> findDataExchangesOnPage(Page<DataExchange> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<DataExchange>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//执行自定义的hql
	public Integer exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		Integer result = null;
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		result = query.executeUpdate();
		session.close();
		return result;
	}
	

}
