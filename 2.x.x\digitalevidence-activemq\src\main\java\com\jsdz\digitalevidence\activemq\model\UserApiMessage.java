package com.jsdz.digitalevidence.activemq.model;
/**
 * 
 * @类名: UserApiMessage
 * @说明: API对接消息结构类
 *
 * @author: kenny
 * @Date	2017年11月8日下午7:20:54
 * 修改记录：
 *
 * @see
 */
import java.io.Serializable;


public class UserApiMessage implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 3393092959873694024L;
	private String option; //操作类型INSERT,UPDATE,DELETE(新增，修改，删除)
	private String msg;//消息体
	private Object data;//对象
	
	public UserApiMessage(){
		super();
	}
	public UserApiMessage(String option,String msg,Object data){
		this.setOption(option);
		this.setMsg(msg);
		this.setData(data);
	}
	
	
	
	public String getOption() {
		return option;
	}
	public void setOption(String option) {
		this.option = option;
	}
	public String getMsg() {
		return msg;
	}
	public void setMsg(String msg) {
		this.msg = msg;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	
	public String toString(){
		return "option=" + this.getOption() + "  msg=" + this.getMsg();
	}

}
