package com.jsdz.admin.org.service;

/**
 * 
 * @类名: OrganizationService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-06 19:18:40
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import java.util.Map;

import com.jsdz.admin.org.bean.OrganizationBean;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.core.TreeBean;

public interface OrganizationService {

	//新增
	public AjaxResult addOrganization(Organization organization);

	//修改
	public AjaxResult updateOrganization(Organization organization);

	//删除
	public AjaxResult deleteOrganization(Organization organization);

	//按id查询
	public Organization locateOrganizationById(Long id);

	//单个查询
	public Organization findOrganizationByParam(String queryStr,String[] paramNames,Object[] values);

	//列表查询
	public List<Organization> findOrganizationsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Organization> findOrganizationsOnPage(Page<Organization> page, String queryStr,String[] paramNames,Object[] values);

	//查询所有的单位并生成tree
	public List<TreeBean> findOrganizationOnTree();
	
	//分页查询最上级数据给前端easyUi treegrid使用
	public Page<OrganizationBean> findOrgByTopLevel(Page<OrganizationBean> page, String[] paramNames, Object[] values);

	/**
	 * 分页查询最上级数据给前端easyUi datagrid, 使用mybits查询
	 * @param page
	 * @param kvs
	 * @return
	 */
	public Page<OrganizationBean> findOrgByTopLevelMybits(Page<OrganizationBean> page,Map<String, Object> kvs);
	
	//按id查询(游离状态)
	//public Organization findOrganizationById(Long id);
	
	//按上级ID查询
	public List<OrganizationBean> findOrgByParentId(Long parentId);
	
	//

	public Page<OrganizationBean> findOrgOnPageForSy(Page<OrganizationBean> page, String[] paramNames, Object[] values);
	
	
	/**
	 * 查询单位列表给前端 的easyUi
	 * @param sql
	 * @param paramNames
	 * @param values
	 * @return
	 */
	public List<Object> getOrgListForTree(String sql,String[] paramNames, Object[] values);

}

