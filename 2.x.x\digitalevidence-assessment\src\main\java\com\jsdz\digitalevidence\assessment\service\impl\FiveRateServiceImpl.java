package com.jsdz.digitalevidence.assessment.service.impl;
/**
 * 
 * @类名: FiveRateServiceImpl
 * @说明: 五率考核
 *
 * @author: kenny
 * @Date	2018年1月20日下午4:18:25
 * 修改记录：
 *
 * @see
 */

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.bean.FiveRateBean;
import com.jsdz.digitalevidence.assessment.service.FiveRateService;
import com.jsdz.reportquery.ReportQueryDao;
@Service
public class FiveRateServiceImpl implements FiveRateService{
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<FiveRateBean> reportQueryDao;
	
	//配备率
	public Page<FiveRateBean> findAllocationRateFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values){
		Page<FiveRateBean> p1 = reportQueryDao.pageQueryNamedSQL(page,"findRecorderAllot",paramNames,values);		
		return p1;
	}
	
	//使用率
	public Page<FiveRateBean> findUsageRateFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values){
		Page<FiveRateBean> p1 = reportQueryDao.pageQueryNamedSQL(page,"findUsageRate",paramNames,values);		
		return p1;
	}
	
	//3.维护率
	public Page<FiveRateBean> findApprovedRateFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values){
		Page<FiveRateBean> p1 = reportQueryDao.pageQueryNamedSQL(page,"findApprovedRate",paramNames,values);		
		return p1;
	}
	//4.在线率
	public Page<FiveRateBean> findOnlineRateFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values){
		Page<FiveRateBean> p1 = reportQueryDao.pageQueryNamedSQL(page,"findOnlineRate",paramNames,values);		
		return p1;
	}

	//5.平均时长
	public Page<FiveRateBean> findAvgDurationFromSql(Page<FiveRateBean> page,String[] paramNames, Object[] values){
		Page<FiveRateBean> p1 = reportQueryDao.pageQueryNamedSQL(page,"findAvgDuration",paramNames,values);		
		return p1;
	}

}
