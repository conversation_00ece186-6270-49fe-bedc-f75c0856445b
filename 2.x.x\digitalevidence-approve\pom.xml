<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
		<groupId>com.jsdz</groupId>
		<artifactId>digitalevidence</artifactId>   
		<version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>digitalevidence-approve</artifactId>
    <packaging>jar</packaging>
    <name>digitalevidence-approve</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
	
    <build>
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
			<!-- 加入以下内容使得测试代码出错不影响项目的编译 -->
            <plugin>
	            <groupId>org.apache.maven.plugins</groupId>
	            <artifactId>maven-surefire-plugin</artifactId>
	            <configuration>
	                <testFailureIgnore>true</testFailureIgnore>            
	            </configuration>
	        </plugin>
			
		</plugins>
		
	</build>

    <dependencies>
        <dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-core</artifactId>
				<exclusions>
					<exclusion>
						<groupId>commons-logging</groupId>
						<artifactId>commons-logging</artifactId>
					</exclusion>
				</exclusions>
		</dependency>
		<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-context-support</artifactId>
		</dependency>	
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-tx</artifactId>
		</dependency>	
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-orm</artifactId>
		</dependency>		
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- SPRING basic end -->	
		<!-- AOP -->
		<dependency>
		  <groupId>org.aspectj</groupId>
		  <artifactId>aspectjrt</artifactId>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib</artifactId>
		</dependency>		
		<dependency>
		  <groupId>asm</groupId>
		  <artifactId>asm</artifactId>
		</dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
		</dependency>
		
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>
		<!-- db driver -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
		    <groupId>junit</groupId>
		    <artifactId>junit</artifactId>
		    <version>4.10</version>
		    <scope>test</scope>
		</dependency>
		    
		<!-- db pool -->
		<!--<dependency>
			<groupId>com.mchange</groupId>
			<artifactId>c3p0</artifactId>
		</dependency>-->

		<dependency>
			<groupId>com.jsdz</groupId>
			<artifactId>core</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.jsdz</groupId>
			<artifactId>process-processengine</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
    </dependencies>
</project>
