package com.jsdz.admin.org.dao;

/**
 * 
 * @类名: RegionTypeDao
 * @说明: 地区类别Dao
 *
 * @author: kenny
 * @Date	2017年4月25日下午5:9:31
 * 修改记录：
 *
 * @see
 */
import java.util.List;

import org.springframework.stereotype.Repository;

import com.jsdz.admin.org.model.RegionType;
import com.jsdz.core.dao.GenericORMEntityDAO;

@Repository
public interface RegionTypeDao extends GenericORMEntityDAO<RegionType, Long>{
	
	public List<RegionType> All();
	
}
