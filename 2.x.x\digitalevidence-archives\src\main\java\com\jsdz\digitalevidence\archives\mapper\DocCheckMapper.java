package com.jsdz.digitalevidence.archives.mapper;

import com.jsdz.digitalevidence.archives.bean.DocCheck;
import com.jsdz.digitalevidence.archives.bean.param.SearchCheckDocParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
public interface DocCheckMapper {

    void saveDocCheck(@Param("docCheck") DocCheck docCheck);

    DocCheck SearchCheckDocInitByDocId(@Param("docId") Long docId);

    DocCheck SearchCheckDocInitByDocIdCode(@Param("docId")Long docId,@Param("checkCode") String checkCode);

    void updateDocCheck(@Param("docCheck") DocCheck docCheck);

    List<DocCheck> SearchCheckDoc(@Param("param") SearchCheckDocParam param);

    int SearchCheckDocCount(@Param("param") SearchCheckDocParam param);
}
