package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

/**
 * 
 * @类名: AlarmInfoServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-08-29 19:34:11
 * 修改记录：
 *
 * @see
*/

import java.io.IOException;
import java.util.List;
import java.util.UUID;

import com.alibaba.fastjson.JSON;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.digitalevidence.activemq.model.SysLog;
import com.jsdz.digitalevidence.activemq.service.SysLogService;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmApiVo;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmInfoVo;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmToQZHBean;
import com.jsdz.digitalevidence.alarm.alarm110.dao.mapper.AlarmMapper;
import com.jsdz.digitalevidence.graphensemble.nw4g.http.HttpUtils;
import com.jsdz.digitalevidence.alarm.alarm110.bean.DeviceAlarmDto;
import com.jsdz.digitalevidence.site.model.Recorder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmInfoDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.digitalevidence.alarm.alarm110.service.AlarmInfoService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Slf4j
@Service("AlarmInfoServiceImpl")
public class AlarmInfoServiceImpl implements AlarmInfoService {

	@Autowired
	private AlarmInfoDao alarmInfoDao;

	@Autowired
	private AlarmMapper alarmMapper;

	@Autowired
	private SysLogService sysLogService;

	@Value("${qzx.alarmUrl:}")
	private String QZHAlarmUrl;

	//新增
	public AjaxResult addAlarmInfo(AlarmInfo alarmInfo) {
		AjaxResult result = new AjaxResult();
		alarmInfoDao.addAlarmInfo(alarmInfo);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateAlarmInfo(AlarmInfo alarmInfo) {
		AjaxResult result = new AjaxResult();
		alarmInfoDao.updateAlarmInfo(alarmInfo);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteAlarmInfo(AlarmInfo alarmInfo) {
		AjaxResult result = new AjaxResult();
		alarmInfoDao.deleteAlarmInfo(alarmInfo);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public AlarmInfo findAlarmInfoById(Long id){
		return alarmInfoDao.findAlarmInfoById(id);
	}

	//按 id 查询
	public AlarmInfo locateAlarmInfoById(Long id) {
		return alarmInfoDao.locateAlarmInfoById(id);
	}

	//单个查询
	public AlarmInfo findAlarmInfoByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmInfoDao.findAlarmInfoByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<AlarmInfo> findAllAlarmInfos() {
		return alarmInfoDao.findAllAlarmInfos();
	}

	//列表查询
	public List<AlarmInfo> findAlarmInfosByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmInfoDao.findAlarmInfosByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<AlarmInfo> findAlarmInfosOnPage(Page<AlarmInfo> page, String queryStr, String[] paramNames, Object[] values) {
		Page<AlarmInfo> pos = alarmInfoDao.findAlarmInfosOnPage(page, queryStr, paramNames, values);
		return pos;
	}

    @Override
	@Transactional
    public void addSendRecord(AlarmInfo alarmInfo, List<Recorder> devices, Integer status) {
		for(Recorder recorder : devices) {
			// 修改处警人
			Employees employees = recorder.getEmployees();
			if(employees != null) {
				String dealPolice = employees.getName()+ "(" +employees.getWorkNumber()+ ")" ;
				String alarmPolices = alarmInfo.getAlarmPolices();
				if(!StringUtils.hasText(alarmPolices)) {
					alarmPolices = dealPolice;
				} else {
					if(!alarmPolices.contains(dealPolice)) alarmPolices = alarmPolices + "," + dealPolice;
				}
				alarmInfo.setAlarmPolices(alarmPolices);
			}
			alarmInfoDao.updateAlarmInfo(alarmInfo);
			// 状态： 0 未出警 1、已出警 2、结束出警
			alarmMapper.insertAlamSendRecord(alarmInfo.getId(), recorder.getId(), status);
		}
    }

    @Override
    public AlarmInfo findAlarmInfoByCode(String alarmCode) {
		String hql = " from AlarmInfo where alarmCode = :alarmCode";
        return this.findAlarmInfoByParam(hql, new String[]{"alarmCode"}, new Object[]{alarmCode});
    }

	@Override
	public List<AlarmInfoVo> getAlarmListByRecorderCode(String code) {
		return alarmMapper.getAlarmListByRecorderCode(code);
	}

	@Override
	public AlarmInfoVo findRecorderAlarm(String code, String alarmCode) {
		return alarmMapper.findRecorderAlarm(code, alarmCode);
	}

    @Override
    public AlarmApiVo getAlarmInfo(Long id) {
		// 查询警情信息
		AlarmInfo alarmInfo = findAlarmInfoById(id);
		if(alarmInfo == null) {
			return null;
		}
		return new AlarmApiVo().sign(alarmInfo);
    }

	@Override
	public Integer alarmStatus(DeviceAlarmDto dto) {
		Integer status = alarmMapper.alarmStatus(dto);
		return status == null ? 0 : status;
	}

	/**
	 * 给情指行发送接警信息
	 * @param dto
	 */
	@Override
	public AjaxResult sendReciveAlarmToQZH(DeviceAlarmDto dto) {
		String jsonString = JSON.toJSONString(new AlarmToQZHBean(dto));
		try {
			log.debug("=================发送执法仪接警给情指行-参数："+ jsonString +"=================");
			if(!StringUtils.hasText(QZHAlarmUrl)) return null;
			AjaxResult result = HttpUtils.sendWithJson(QZHAlarmUrl, jsonString, AjaxResult.class);
			if(result.getCode() != 1) {
				SysLog sysLog = new SysLog(this.getClass(), "给情指行推送接警信息", "给情指行推送接警信息异常");
				sysLogService.setErrorLog(sysLog, result.getMsg());
				throw new RuntimeException("给情指行推送接警信息异常："+result.getMsg());
			}
			log.debug("=================发送执法仪接警给情指行-结果："+ JSON.toJSONString(result) +"=================");
			return result;
		} catch (IOException e) {
			SysLog sysLog = new SysLog(this.getClass(), "给情指行推送接警信息", "给情指行推送接警信息异常");
			sysLogService.setErrorLog(sysLog, jsonString);
			throw new RuntimeException("给情指行推送接警信息异常："+jsonString);
		}
	}

	/**
	 * 生成随机key
	 * @return
	 */
	public static String genKey() {
		return UUID.randomUUID().toString().replaceAll("-", "");
	}


}
