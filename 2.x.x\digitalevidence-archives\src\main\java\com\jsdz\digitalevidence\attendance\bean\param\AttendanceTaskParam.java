package com.jsdz.digitalevidence.attendance.bean.param;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceTaskBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceTaskModel;

public class AttendanceTaskParam {

    Page<AttendanceTaskBean> page;

    private String orgPath;

    private String sortName;

    private String sortOrder;
    //是否包含下级 （0不，1包含）
    private String includeSub;



    public AttendanceTaskParam(){
        page = new Page<AttendanceTaskBean>();
        page.setOffset(0);
        page.setPageSize(20);
    }

    public Page<AttendanceTaskBean> getPage() {
        return page;
    }

    public void setPage(Page<AttendanceTaskBean> page) {
        this.page = page;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public String getSortName() {
        return sortName;
    }

    public void setSortName(String sortName) {
        this.sortName = sortName;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(String includeSub) {
        this.includeSub = includeSub;
    }
}
