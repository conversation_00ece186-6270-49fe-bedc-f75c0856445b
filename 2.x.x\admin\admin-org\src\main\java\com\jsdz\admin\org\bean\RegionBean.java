package com.jsdz.admin.org.bean;
/**
 * 
 * @类名: RegionBean
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月8日下午5:11:55
 * 修改记录：
 *
 * @see
 */

import java.util.Date;
import com.jsdz.admin.org.model.Region;
import com.jsdz.admin.org.model.RegionType;
import com.jsdz.core.AbstractDTO;

public class RegionBean extends AbstractDTO{
	private Long id;
	private String regionName;//行政区域名称
	private Long regionTypeId;//类型(省(直辖市)/市/县(区)/镇(乡));
	private String regionTypeName;//类型(省(直辖市)/市/县(区)/镇(乡));
	private Long parentId; //上级区域
	private String parentname; //上级区域
	private Date createTime; //创建日期
	
	public void assign(Region src) {
		try {
			//BeanUtils.copyProperties(this, src);
			this.id = src.getId();
			this.regionName = src.getRegionName();
			this.createTime = src.getCreateTime();
			this.parentId = src.getParentRegion()==null?null:src.getParentRegion().getId();
			this.parentname=src.getParentRegion()==null?null:src.getParentRegion().getRegionName();
			
			this.regionTypeId = src.getRegionType() == null?null:src.getRegionType().getId();
			this.regionTypeName = src.getRegionType() == null?null:src.getRegionType().getName();
			

		} catch (Exception e) {
			throw new RuntimeException("Assign Error.", e);
		}
	}
	
	public void assignTo(Region dest) {
		try {
			//BeanUtils.copyProperties(dest, this);
			dest.setId(this.id);
			dest.setRegionName(this.regionName);
			dest.setCreateTime(this.createTime);
			if (this.parentId != null){
				Region region = new Region();
				region.setId(this.parentId);
				dest.setParentRegion(region);
			}
			if (this.regionTypeId != null){
				RegionType regionType = new RegionType();
				regionType.setId(this.regionTypeId);
				dest.setRegionType(regionType);
			}
			
		} catch (Exception e) {
			throw new RuntimeException("AssignTo Error.", e);
		}
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getRegionName() {
		return regionName;
	}

	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}

	public Long getRegionTypeId() {
		return regionTypeId;
	}

	public void setRegionTypeId(Long regionTypeId) {
		this.regionTypeId = regionTypeId;
	}

	public String getRegionTypeName() {
		return regionTypeName;
	}

	public void setRegionTypeName(String regionTypeName) {
		this.regionTypeName = regionTypeName;
	}

	public Long getParentId() {
		return parentId;
	}

	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}

	public String getParentname() {
		return parentname;
	}

	public void setParentname(String parentname) {
		this.parentname = parentname;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	
}
