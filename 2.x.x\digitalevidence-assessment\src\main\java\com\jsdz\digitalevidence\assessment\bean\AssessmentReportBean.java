/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.assessment.model.ReportType;

/**
 * @类名: AssessmentReportBean
 * @说明: 考核报告Bean, 显示报告简要信息
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentReportBean extends AbstractDTO {
	
	/** 报告Id*/
	private Long reportId;
	/** 视频考核视频数*/
	private Integer assessDocs;
	/** 考核期间视频上传数*/
	private Integer uploadDocs;
	/** 考核时段，开始、结束*/
	@JsonFormat(pattern="yyyy-MM")
	private Date startDate;
	@JsonFormat(pattern="yyyy-MM")
	private Date endDate;
	/** 报告生成日期*/
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date submitDate;
	/** 报表类型*/
	private ReportType type;
	
	public AssessmentReportBean(Long reportId, Integer assessDocs, Integer uploadDocs, Date startDate, Date endDate,
			Date submitDate, ReportType type) {
		super();
		this.reportId = reportId;
		this.assessDocs = assessDocs;
		this.uploadDocs = uploadDocs;
		this.startDate = startDate;
		this.endDate = endDate;
		this.submitDate = submitDate;
		this.type = type;
	}

	public Integer getAssessDocs() {
		return assessDocs;
	}
	public void setAssessDocs(Integer assessDocs) {
		this.assessDocs = assessDocs;
	}
	public Integer getUploadDocs() {
		return uploadDocs;
	}
	public void setUploadDocs(Integer uploadDocs) {
		this.uploadDocs = uploadDocs;
	}
	public float getAssessRate() {
		if(assessDocs==null||uploadDocs==null||uploadDocs==0)
			return 0;
		return (float)assessDocs/uploadDocs;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Date getSubmitDate() {
		return submitDate;
	}
	public void setSubmitDate(Date submitDate) {
		this.submitDate = submitDate;
	}

	public Long getReportId() {
		return reportId;
	}

	public void setReportId(Long reportId) {
		this.reportId = reportId;
	}

	public ReportType getType() {
		return type;
	}

	public void setType(ReportType type) {
		this.type = type;
	}
	
}
