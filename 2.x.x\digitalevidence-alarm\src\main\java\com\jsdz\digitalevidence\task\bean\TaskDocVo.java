package com.jsdz.digitalevidence.task.bean;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class TaskDocVo {

    /**
     * 文件名
     */
    private String name;

    /**
     * 播放地址
     */
    private String url;

    /**
     * 播放地址
     */
    private String sceneMark;

    /**
     * 警员名
     */
    private String policeName;

    /**
     * 设备号
     */
    private String code;

    /**
     * 设备号
     */
    private String longitude;

    /**
     * 设备号
     */
    private String latitude;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    /**
     * 抓拍时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date captureTime;

    private String libIdNum;

    private String libPersonName;

    private String libLabel;

    private String libPersonSex;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getSceneMark() {
        return sceneMark;
    }

    public void setSceneMark(String sceneMark) {
        this.sceneMark = sceneMark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getPoliceName() {
        return policeName;
    }

    public void setPoliceName(String policeName) {
        this.policeName = policeName;
    }

    public Date getCaptureTime() {
        return captureTime;
    }

    public void setCaptureTime(Date captureTime) {
        this.captureTime = captureTime;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getLibIdNum() {
        return libIdNum;
    }

    public void setLibIdNum(String libIdNum) {
        this.libIdNum = libIdNum;
    }

    public String getLibPersonName() {
        return libPersonName;
    }

    public void setLibPersonName(String libPersonName) {
        this.libPersonName = libPersonName;
    }

    public String getLibLabel() {
        return libLabel;
    }

    public void setLibLabel(String libLabel) {
        this.libLabel = libLabel;
    }

    public String getLibPersonSex() {
        return libPersonSex;
    }

    public void setLibPersonSex(String libPersonSex) {
        this.libPersonSex = libPersonSex;
    }
}
