package com.jsdz.digitalevidence.attendance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceTaskBean;
import com.jsdz.digitalevidence.attendance.mapper.AttendanceInfoMapper;
import com.jsdz.digitalevidence.attendance.mapper.AttendanceScopeMapper;
import com.jsdz.digitalevidence.attendance.mapper.AttendanceTaskMapper;
import com.jsdz.digitalevidence.attendance.model.AttendanceInfoModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceTaskModel;
import com.jsdz.digitalevidence.attendance.bean.param.AttendanceTaskParam;
import com.jsdz.digitalevidence.attendance.service.IAttendanceInfoService;
import com.jsdz.digitalevidence.attendance.service.IAttendanceTaskService;
import com.jsdz.digitalevidence.utils.DateUtil;
import com.jsdz.digitalevidence.utils.SqlUtils;
import com.jsdz.digitalevidence.utils.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Service
public class AttendanceTaskServiceImpl  extends ServiceImpl<AttendanceTaskMapper, AttendanceTaskModel> implements IAttendanceTaskService {
    private final Logger logger = Logger.getLogger(this.getClass());
    @Resource
    private AttendanceTaskMapper attendanceTaskMapper;
    //设置表名
    private String tableName = "sys_attendance_task";
    SqlUtils sqlUtils = new SqlUtils<AttendanceTaskModel>();
    @Override
    public Page<AttendanceTaskBean> getDatas(AttendanceTaskParam param, PerLvlBean permLvl) {
        Page<AttendanceTaskBean> page = param.getPage();
        String countSql = " select count(1) countSum ";
        String dataSql  = " select t.*,o.orgName,o.path as orgPath,o.orgCode,o.Id as orgId ";
        String whereSql = " from "+tableName+" t LEFT JOIN admin_organization o on t.attendanceOrgId = o.id where t.isDelete = 0 ";
        //分页
        String limitSql = " limit "+param.getPage().getOffset()+" ,"+param.getPage().getPageSize();
        //排序条件，暂时无需求
        String orderSql = " order by t.createTime desc";
        //页面没指定单位权限，需使用用户权限
        if(StringUtils.isBlank(param.getOrgPath())){
            whereSql = sqlUtils.userLevel( whereSql,permLvl);
        }else if(StringUtils.isNoBlank(param.getOrgPath()) & StringUtils.isNoBlank(param.getIncludeSub())){
            String includeSub = param.getIncludeSub();
            whereSql+=" and o.path like '"+param.getOrgPath() +""+ ("1".equals(includeSub)?"%":"")  +"'";
        }
        //其他调教查询补充
        logger.info(dataSql + whereSql + orderSql + limitSql );
        List<AttendanceTaskBean> datas = attendanceTaskMapper.getDatas(dataSql + whereSql + orderSql + limitSql );
        Integer count = attendanceTaskMapper.getCount(countSql + whereSql  + orderSql);
        datas.forEach((T)->{T.setAttendanceTime(DateUtil.convertMinuteOfStringToTime(Integer.parseInt(T.getAttendanceTime())));});
        page.setRows(datas);
        page.setTotal(count);
        return page;
    }

    @Override
    public void saveData(AttendanceTaskModel model, PerLvlBean permLvl) {
        attendanceTaskMapper.insert(model);
    }


    @Override
    public void updateDataById(AttendanceTaskModel model, PerLvlBean permLvl) {
        attendanceTaskMapper.updateById(model);
    }

    @Override
    public AttendanceTaskModel getById(AttendanceTaskModel model, PerLvlBean permLvl) {
        return attendanceTaskMapper.selectById(model.getId());
    }

    @Override
    public void deleteById(AttendanceTaskModel model, PerLvlBean permLvl) {
        model.setIsDelete(1);
        attendanceTaskMapper.updateById(model);

        //attendanceTaskMapper.deleteById(sqlUtils.deleteByIdSql(model,tableName,"id"));
        // attendanceTaskMapper.deleteById(model.getId());
    }


}
