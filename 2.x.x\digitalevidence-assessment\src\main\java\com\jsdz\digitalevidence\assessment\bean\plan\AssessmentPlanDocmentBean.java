package com.jsdz.digitalevidence.assessment.bean.plan;

import java.text.SimpleDateFormat;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanDocment;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPolice;
import com.jsdz.digitalevidence.document.model.Document;
/**
 * @类名: AssessmentPlanDocmentBean
 * @说明: 
 *
 * <AUTHOR>
 * @Date	 2019年6月22日上午9:30:43
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentPlanDocmentBean {


	private Long id;
	private Long planId;
	private Long docId;
	private String docName;
	private String docCreateTime;
	private String docUploadTime;
	private String policeCode;
	private String policeName;
	private String orgCode;
	private String orgName;
	
	private Integer alloted;
	
	public void assign(AssessmentPlanDocment sur){
		this.setId(sur.getId());
		this.setPlanId(sur.getPlanId());
		this.setAlloted(sur.getAlloted());
		if (null != sur.getDoc()){
			this.setDocId(sur.getDoc().getId());
			this.setDocName(sur.getDoc().getName());
			if (sur.getDoc().getCreateTime() != null)
				this.setDocCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(sur.getDoc().getCreateTime()));
			if (sur.getDoc().getUploadTime() != null)
				this.setDocUploadTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(sur.getDoc().getUploadTime()));
			if (null != sur.getDoc().getPolice()){
				this.setPoliceCode(sur.getDoc().getPolice().getWorkNumber());
				this.setPoliceName(sur.getDoc().getPolice().getName());
				if (null != sur.getDoc().getPolice().getOrganization()){
					this.setOrgName(sur.getDoc().getPolice().getOrganization().getOrgName());
				}
			}
		}
	}
	
	public AssessmentPlanDocment assignTo(){
		AssessmentPlanDocment dest = new AssessmentPlanDocment();
		assignTo(dest);
		return dest;
	}

	public void assignTo(AssessmentPlanDocment dest){
		dest.setId(this.getId());
		dest.setPlanId(this.getPlanId());
		dest.setAlloted(this.getAlloted());
		if (null!=this.getDocId()){
			Document doc = new Document();
			doc.setId(this.getDocId());
			dest.setDoc(doc);
		}
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getPlanId() {
		return planId;
	}

	public void setPlanId(Long planId) {
		this.planId = planId;
	}

	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}

	public String getDocName() {
		return docName;
	}

	public void setDocName(String docName) {
		this.docName = docName;
	}

	public String getDocCreateTime() {
		return docCreateTime;
	}

	public void setDocCreateTime(String docCreateTime) {
		this.docCreateTime = docCreateTime;
	}

	public String getPoliceCode() {
		return policeCode;
	}

	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}

	public String getPoliceName() {
		return policeName;
	}

	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Integer getAlloted() {
		return alloted;
	}

	public void setAlloted(Integer alloted) {
		this.alloted = alloted;
	}


	public String getDocUploadTime() {
		return docUploadTime;
	}


	public void setDocUploadTime(String docUploadTime) {
		this.docUploadTime = docUploadTime;
	}

	
	
}
