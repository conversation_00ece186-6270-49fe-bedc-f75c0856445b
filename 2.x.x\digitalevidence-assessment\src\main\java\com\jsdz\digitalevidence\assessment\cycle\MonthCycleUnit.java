/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.cycle;

import java.util.Calendar;
import java.util.Date;

import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: MonthCycleUnit
 * @说明: 月周期
 *
 * <AUTHOR>
 * @Date 2017年8月23日 上午9:54:01 修改记录：
 *
 * @see
 */
public class MonthCycleUnit extends CycleUnit {

	@Override
	public Cycle fromDate(Date date) {
		Date start;
		Date end;
		
		System.out.println(day);
		// date当月的周期日，起始或者结束
        Calendar c = Calendar.getInstance();
        c.setTime(date);
//        c.set(Calendar.DAY_OF_MONTH, day);
        c.set(Calendar.DAY_OF_MONTH, 1);
        // 周期边界
        Date bound = c.getTime();
        // date在边界前，即，边界未周期结束日
        if(date.before(bound)) {
        	end = bound;
        	start = DateTimeUtils.getPreMonth(end);
        } else {
        	start = bound;
        	end = DateTimeUtils.getNextMonth(start);
        }  
        //
        Date startDate = DateTimeUtils.StringToDate(DateTimeUtils.DateToString(start), 
        						DateTimeUtils.defaultDatePatten2);
        Date endDate = DateTimeUtils.StringToDate(DateTimeUtils.DateToString(end), 
				DateTimeUtils.defaultDatePatten2);
		return new Cycle(startDate, endDate);
	}
	
	// 上一周期
	public Cycle prevCycle(Cycle cycle) {
		Date end = cycle.getStart();
		Date start = DateTimeUtils.getPreMonth(end);
		return new Cycle(start, end);
	}

	// 下一周期
	public Cycle nextCycle(Cycle cycle) {
		Date start = cycle.getEnd();
		Date end = DateTimeUtils.getNextMonth(start);
		return new Cycle(start, end);
	}
	
	public int getDay() {
		return day;
	}

	public void setDay(int day) {
		this.day = day;
	}

}
