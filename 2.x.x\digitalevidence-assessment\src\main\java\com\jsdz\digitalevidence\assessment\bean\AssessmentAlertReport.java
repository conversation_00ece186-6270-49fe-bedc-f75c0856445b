/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;

/**
 * @类名: AssessmentAlertBean
 * @说明: 考核报警报告Bean
 *        
 *      
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentAlertReport extends AbstractDTO {

	/********警员资料********/
	/** 考核预警Id*/
	private Long alertId;
	/** 警号*/
	private String policeCode;
	/** 姓名*/
	private String policeName;
	/** 性别*/
	private String sex;
	/** 年龄*/
	private Integer age;
	/** 局*/
	private String orgName;
	/** 部门*/
	private String deptName;
	/** 职位*/
	private String position;
    /** 报告生成日期*/
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime; 
    /** 考核周期*/
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date startDate;
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endDate;
    /** 冻结状态*/
    private Boolean frozen;
    
    /** 预警项*/
	/** 项目*/
	private String item;
	/** 值*/
	private Object v;
	/** 参考值*/
	private Object ref;

    /** 反馈*/
    private String feedback;

	public String getPoliceCode() {
		return policeCode;
	}

	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}

	public String getPoliceName() {
		return policeName;
	}

	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public String getDeptName() {
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public String getFeedback() {
		return feedback;
	}

	public void setFeedback(String feedback) {
		this.feedback = feedback;
	}

	public Long getAlertId() {
		return alertId;
	}

	public void setAlertId(Long alertId) {
		this.alertId = alertId;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getItem() {
		return item;
	}

	public void setItem(String item) {
		this.item = item;
	}

	public Object getV() {
		return v;
	}

	public void setV(Object v) {
		this.v = v;
	}

	public Object getRef() {
		return ref;
	}

	public void setRef(Object ref) {
		this.ref = ref;
	}

	public Boolean getFrozen() {
		return frozen;
	}

	public void setFrozen(Boolean frozen) {
		this.frozen = frozen;
	}

}
