package com.jsdz.admin.security.model;
/**
 * 
 * @类名: RoleAction
 * @说明: 角色权限实体类
 *
 * @author: kenny
 * @Date	2017年4月28日下午3:39:29
 * 修改记录：
 *
 * @see
 */
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class RoleAction implements Serializable {

	
	private static final long serialVersionUID = -248123070501241298L;
	
	private Long id;
	//@JsonIgnore
	private Role role; //角色
	//@JsonIgnore
	private Action action; //权限
	private Date createTime;//创建时间
	
	public RoleAction(){
		super();
	}
	public RoleAction(Role role){
		this.role=role;
		this.createTime = new Date();
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Role getRole() {
		return role;
	}
	public void setRole(Role role) {
		this.role = role;
	}

	public Action getAction() {
		return action;
	}
	public void setAction(Action action) {
		this.action = action;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}	
}
