package com.jsdz.admin.security.bean;

import java.util.Date;

import com.jsdz.admin.security.model.SystemMenu;
/**
 * 
 * @类名: SystemMenuBean
 * @说明: SystemMenuBean封装类
 *
 * @author: kenny
 * @Date	2017年9月8日下午6:48:10
 * 修改记录：
 *
 * @see
 */
public class SystemMenuBean {

	
	private Long id;
	private String menuName;//菜单描述
	private String menuUrl; //url
	private Integer menuType; //类型 1 菜单 2按钮 3资源
	private Long parentId;
	private String  parentName; //上级菜单
	private Integer isShow; //是否显示 0/1
	private Integer sort;   //顺序号
	private String imageUrl;  //显示图片
	private Date createTime;//创建时间
	
	
	public void assign(SystemMenu src){
		this.id = src.getId();
		this.menuName = src.getMenuName();
		this.menuUrl = src.getMenuUrl();
		this.menuType = src.getMenuType();
		this.isShow = src.getIsShow();
		this.sort = src.getSort();
		this.imageUrl = src.getImageUrl();
		this.createTime = src.getCreateTime();

		this.parentId = src.getParentMenu() == null?0L:src.getParentMenu().getId();
		this.parentName = src.getParentMenu() == null?null:src.getParentMenu().getMenuName();		
	}
	
	public void assignTo(SystemMenu dest){
		dest.setId(this.id);
		dest.setMenuName(this.menuName);
		dest.setMenuUrl(this.menuUrl);
		dest.setMenuType(this.menuType);
		dest.setIsShow(this.isShow);
		dest.setSort(this.sort);
		dest.setImageUrl(this.imageUrl);
		dest.setCreateTime(this.createTime);
		if (this.parentId == null || this.parentId == 0){
			dest.setParentMenu(null);
		}else{
			SystemMenu p = new SystemMenu();
			p.setId(this.parentId);
			dest.setParentMenu(p);
		}
		
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getMenuName() {
		return menuName;
	}
	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}
	public String getMenuUrl() {
		return menuUrl;
	}
	public void setMenuUrl(String menuUrl) {
		this.menuUrl = menuUrl;
	}
	public Integer getMenuType() {
		return menuType;
	}
	public void setMenuType(Integer menuType) {
		this.menuType = menuType;
	}
	public Long getParentId() {
		return parentId;
	}
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}
	public String getParentName() {
		return parentName;
	}
	public void setParentName(String parentName) {
		this.parentName = parentName;
	}
	public Integer getIsShow() {
		return isShow;
	}
	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public String getImageUrl() {
		return imageUrl;
	}
	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	

}
