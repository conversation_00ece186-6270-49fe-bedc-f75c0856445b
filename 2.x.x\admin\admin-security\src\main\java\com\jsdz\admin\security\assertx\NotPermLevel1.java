package com.jsdz.admin.security.assertx;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.reportquery.dynsql.fork.Assert;

/**
 * @类名: PermLevel1
 * @说明: 级别1断言
 *
 * <AUTHOR>
 * @Date	 2015年3月30日 上午10:18:30
 * 修改记录：
 *
 * @see 	 
 */
public class NotPermLevel1 extends Assert {
	
	/* (non-Javadoc)
	 * @see com.easyget.commons.fork.Assert#assertx(java.lang.String[], java.lang.Object[], java.lang.String)
	 */
	@Override
	public boolean assertx(String[] params, Object[] values, String fn) {
		//
		for(int i=0;i<params.length;i++) {
			if(fn.equals(params[i])) {
				Object v = values[i];
				if(!(v instanceof PerLvlBean))
					throw new IllegalArgumentException("参数错误，["+fn+"]期望是PerLvlBean类型");
				PerLvlBean bean = (PerLvlBean)v;
				// 普通警员
				if(bean.getPermissionLevel()!=1) {
					values[i] = 1;
					return true;
				}
				
			}
			
		}
		return false;

	}

}
