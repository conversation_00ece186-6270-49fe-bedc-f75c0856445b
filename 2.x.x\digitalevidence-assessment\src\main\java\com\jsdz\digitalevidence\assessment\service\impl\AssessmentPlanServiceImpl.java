package com.jsdz.digitalevidence.assessment.service.impl;

import java.util.Date;

/**
 * 
 * @类名: AssessmentPlanServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-06-19 15:40:26
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.digitalevidence.assessment.bean.plan.AssessmentStatisticsBean;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanDao;
import com.jsdz.digitalevidence.assessment.jdbc.ClearPlanDetailHandle;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlan;
import com.jsdz.digitalevidence.assessment.service.AssessmentPlanService;
import com.jsdz.digitalevidence.cache.utils.SysTask;
import com.jsdz.digitalevidence.site.model.Recorder;
import com.jsdz.reportquery.ReportQueryDao;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.bean.plan.ShootingAssessmentBean;

@Service("AssessmentPlanServiceImpl")
public class AssessmentPlanServiceImpl implements AssessmentPlanService {

	@Autowired
	private AssessmentPlanDao assessmentPlanDao;
	
	@Resource(name="rqDao")
	private ReportQueryDao reportQueryDao;

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addAssessmentPlan(AssessmentPlan assessmentPlan) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			if (assessmentPlan == null){
				result.setMsg("没有数据可保存。");
				return result;
			}
			
			if (StringUtils.isEmpty(assessmentPlan.getDescription())){
				result.setMsg("方案描述输入。");
				return result;
			}
			
			if (assessmentPlan.getStatus() == null)
				assessmentPlan.setStatus(0);
			assessmentPlan.setCreateTime(new Date());
			
			assessmentPlanDao.addAssessmentPlan(assessmentPlan);
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("新增保存成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updateAssessmentPlan(AssessmentPlan assessmentPlan) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			if (assessmentPlan == null){
				result.setMsg("没有数据可保存。");
				return result;
			}
			
			if (StringUtils.isEmpty(assessmentPlan.getDescription())){
				result.setMsg("方案描述输入。");
				return result;
			}

			AssessmentPlan AssessmentPlan1 = assessmentPlanDao.locateAssessmentPlanById(assessmentPlan.getId());
			if (null ==AssessmentPlan1){
				result.setMsg("不存在的方案");
				return result;
			}
			
			AssessmentPlan1.setBeginDate(assessmentPlan.getBeginDate());
			AssessmentPlan1.setDescription(assessmentPlan.getDescription());
			AssessmentPlan1.setEndDate(assessmentPlan.getEndDate());
			AssessmentPlan1.setStatus(assessmentPlan.getStatus());
			
			assessmentPlanDao.updateAssessmentPlan(AssessmentPlan1);
			
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("修改成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deleteAssessmentPlan(AssessmentPlan assessmentPlan) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			if (assessmentPlan == null){
				result.setMsg("没有数据可删除。");
				return result;
			}
			assessmentPlan = assessmentPlanDao.locateAssessmentPlanById(assessmentPlan.getId());
			if (null == assessmentPlan){
				result.setMsg("不存在的方案资料");
				return result;
			}

			if (assessmentPlan.getStatus() != 0){
				result.setMsg("非初始状态的方案不能删除");
				return result;
			}
			Long id = assessmentPlan.getId();
			assessmentPlanDao.deleteAssessmentPlan(assessmentPlan);
			
			//删除那三个明细表
			SysTask.Start(new ClearPlanDetailHandle(id));
			;
			result.setSuccess(true);
			result.setCode(200);
			result.setMsg("删除成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 按id查询(游离数据)
	 */ 
 	public AssessmentPlan findAssessmentPlanById(Long id){

		return assessmentPlanDao.findAssessmentPlanById(id);
	}

	/** 
 	 * 按 id 查询
	 */ 
 	public AssessmentPlan locateAssessmentPlanById(Long id) {
		return assessmentPlanDao.locateAssessmentPlanById(id);
	}

	/** 
 	 * 单个查询
	 */ 
 	public AssessmentPlan findAssessmentPlanByParam(String queryStr, String[] paramNames, Object[] values) {
		return assessmentPlanDao.findAssessmentPlanByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlan> findAllAssessmentPlans() {
		return assessmentPlanDao.findAllAssessmentPlans();
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlan> findAssessmentPlansByParam(String queryStr, String[] paramNames, Object[] values) {
		return assessmentPlanDao.findAssessmentPlansByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlan> findAssessmentPlansOnPage(Page<AssessmentPlan> page, String queryStr, String[] paramNames, Object[] values) {
		Page<AssessmentPlan> pos = assessmentPlanDao.findAssessmentPlansOnPage(page, queryStr, paramNames, values);
		return pos;
	}
 	
	@SuppressWarnings("unchecked")
	@Override
	public Page<AssessmentStatisticsBean> queryAssessmentStatistics(Page<AssessmentStatisticsBean> page, String[] paramNames, Object[] values) {
		return reportQueryDao.pageQueryNamedSQL(page,"queryAssessmentStatistics", paramNames, values);
	}
	
	/* 按被考核人查询 
	 * @see com.jsdz.digitalevidence.assessment.service.AssessmentPlanService#queryShootingAssessmentDetail(com.jsdz.core.Page, java.lang.String[], java.lang.Object[])
	 */
	@SuppressWarnings("unchecked")
	@Override
	public Page<ShootingAssessmentBean> queryShootingAssessmentDetail(Page<ShootingAssessmentBean> page, String[] paramNames, Object[] values) {
		return reportQueryDao.pageQueryNamedSQL(page,"queryShootingAssessmentDetail", paramNames, values);
	}
	
	/* 按考核人查询
	 * @see com.jsdz.digitalevidence.assessment.service.AssessmentPlanService#queryShootingAssessmentDetail(com.jsdz.core.Page, java.lang.String[], java.lang.Object[])
	 */
	@SuppressWarnings("unchecked")
	@Override
	public Page<ShootingAssessmentBean> queryShootingAssessmentDetail1(Page<ShootingAssessmentBean> page, String[] paramNames, Object[] values) {
		return reportQueryDao.pageQueryNamedSQL(page,"queryShootingAssessmentDetail1", paramNames, values);
	}
	
}
