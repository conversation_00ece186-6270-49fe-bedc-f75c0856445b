package com.jsdz.digitalevidence.task.bean;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.task.model.TTask;
import org.springframework.beans.BeanUtils;

public class TaskPageBean {

    /**
     * 任务id
     */
    private Long taskId;
    //标题
    private String title;
    //类别
    private Integer cate;
    //内容
    private String content;
    //任务状态 0、未发布 1、已发布
    private Integer status;
    //任务开始时间
    private String startTime;
    //任务结束时间
    private String endTime;
    //机构id
    private Long orgId;
    //是否包含下级单位 0、不包含 1、包含
    private Byte includeSub;
    // 分页
    private Page page;

    private PerLvlBean perLvlBean;

    /**
     * 执法仪编号
     */
    private String code;

    /**
     * 警情编号
     */
    private String alarmCode;

    private String libIdNum;

    private String libPersonName;

    private String libLabel;

    private String libPersonSex;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getCate() {
        return cate;
    }

    public void setCate(Integer cate) {
        this.cate = cate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Byte getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(Byte includeSub) {
        this.includeSub = includeSub;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public PerLvlBean getPerLvlBean() {
        return perLvlBean;
    }

    public void setPerLvlBean(PerLvlBean perLvlBean) {
        this.perLvlBean = perLvlBean;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public TTask toTask() {
        TTask tTask = new TTask();
        BeanUtils.copyProperties(this, tTask);
        return tTask;
    }

    public String getAlarmCode() {
        return alarmCode;
    }

    public void setAlarmCode(String alarmCode) {
        this.alarmCode = alarmCode;
    }

    public String getLibIdNum() {
        return libIdNum;
    }

    public void setLibIdNum(String libIdNum) {
        this.libIdNum = libIdNum;
    }

    public String getLibPersonName() {
        return libPersonName;
    }

    public void setLibPersonName(String libPersonName) {
        this.libPersonName = libPersonName;
    }

    public String getLibLabel() {
        return libLabel;
    }

    public void setLibLabel(String libLabel) {
        this.libLabel = libLabel;
    }

    public String getLibPersonSex() {
        return libPersonSex;
    }

    public void setLibPersonSex(String libPersonSex) {
        this.libPersonSex = libPersonSex;
    }
}
