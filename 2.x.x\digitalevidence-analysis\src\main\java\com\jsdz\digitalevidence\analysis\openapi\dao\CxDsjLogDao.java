package com.jsdz.digitalevidence.analysis.openapi.dao;

/**
 *
 * @类名: CxDsjLogDao
 * @说明:
 * @author: kenny
 * @Date 2017-06-09 16:03:20
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.analysis.openapi.model.CxDsjLog;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface CxDsjLogDao extends GenericORMEntityDAO<CxDsjLog,Long> {

	//新增
	public void addCxDsjLog(CxDsjLog cxDsjLog);

	//修改
	public void updateCxDsjLog(CxDsjLog cxDsjLog);

	//删除
	public void deleteCxDsjLog(CxDsjLog cxDsjLog);

	//按id查询,结果是游离状态的数据
	public CxDsjLog findCxDsjLogById(Long id);

	//按id查询
	public CxDsjLog locateCxDsjLogById(Long id);

	//单个查询
	public CxDsjLog findCxDsjLogByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<CxDsjLog> findAllCxDsjLogs();

	//列表查询
	public List<CxDsjLog> findCxDsjLogsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<CxDsjLog> findCxDsjLogsOnPage(Page<CxDsjLog> page,String queryStr,String[] paramNames,Object[] values);

}

