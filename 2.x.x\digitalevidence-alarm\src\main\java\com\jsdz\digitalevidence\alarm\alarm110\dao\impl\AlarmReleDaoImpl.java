package com.jsdz.digitalevidence.alarm.alarm110.dao.impl;

import java.sql.SQLException;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmReleDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlaBiDoc;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;

@Repository
public class AlarmReleDaoImpl extends GenericEntityDaoHibernateImpl<AlaBiDoc,Long> implements AlarmReleDao{
	
	// 查询是否有重复数据
	@Override
	public AlaBiDoc findMutipleAlaBiDoc(AlaBiDoc alaBiDoc) {
		
		final String  hql = " from AlaBiDoc a "
							+ " where a.docId = :docId	"
							+ "	and a.alarmCode = :alarmCode " ;
				
		AlaBiDoc data = getHibernateTemplate().execute(new HibernateCallback<AlaBiDoc>() {
			public AlaBiDoc doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("docId", alaBiDoc.getDocId());
				query.setParameter("alarmCode", alaBiDoc.getAlarmCode());
				List<AlaBiDoc> list = query.list();
				AlaBiDoc rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}
	
	// 新增数据
	@Override
	public void addAlaBiDoc(AlaBiDoc alaBiDoc) {
		this.saveOrUpdate(alaBiDoc);
	}
	
	// 删除数据
	@Override
	public void deleteAlaBiDoc(AlaBiDoc alaBiDoc) {
		this.delete(alaBiDoc);
	}
	
	// 更新数据
	@Override
	public void updateAlaBiDoc(AlaBiDoc mutipleAlaBiDoc) {
		this.update(mutipleAlaBiDoc);
	}
	
}
