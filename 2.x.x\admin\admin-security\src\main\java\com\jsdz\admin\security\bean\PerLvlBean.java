package com.jsdz.admin.security.bean;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 
 * @类名: PerLvlBean
 * @说明: 权限级别
 *
 * @author: kenny
 * @Date	2017年9月15日下午7:34:52
 * 修改记录：
 *
 * @see
 */
public class PerLvlBean implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -9052879811827614080L;
	
	private Long operatorId;
	private Long empId;
	private String loginName;
	/** 警员所属单位 ID */
	private Long orgId; 
	/** 警员所属单位path */
	private String orgPath;
	/** 为了兼容以前的版本，角色指定的单位path,多个单位用以 ,号隔开*/
	private String roleOrgPaths;
	/** 1 普通警员（只能查看自己的文档）2 资料管理员(能查看本单位的文档) 3 监督管理（能查看下级单位的文档） 9 系统管理员 */
	private Integer permissionLevel;//
	//当前页
	private Integer offset;
	//每页显示多少条
	private Integer pageSize;
	/** 开始时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date beginTime;
	/** 结束时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date endTime;
	
	// 1 视频告警 2 I/O告警  3 设备上下线
	private String alarmMethod;

	// 单位级别
	private Integer orgLevel;


	public String getAlarmMethod() {
		return alarmMethod;
	}
	public void setAlarmMethod(String alarmMethod) {
		this.alarmMethod = alarmMethod;
	}
	public Integer getOffset() {
		return offset;
	}
	public void setOffset(Integer offset) {
		this.offset = offset;
	}
	public Integer getPageSize() {
		return pageSize;
	}
	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}
	public Date getBeginTime() {
		return beginTime;
	}
	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}
	public Date getEndTime() {
		return endTime;
	}
	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}
	public Long getOperatorId() {
		return operatorId;
	}
	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}

	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public String getOrgPath() {
		return orgPath;
	}
	public void setOrgPath(String orgPath) {
		this.orgPath = orgPath;
	}
	public Integer getPermissionLevel() {
		return permissionLevel;
	}
	public void setPermissionLevel(Integer permissionLevel) {
		this.permissionLevel = permissionLevel;
	}
	public Long getEmpId() {
		return empId;
	}
	public void setEmpId(Long empId) {
		this.empId = empId;
	}
	public String getRoleOrgPaths() {
		return roleOrgPaths;
	}
	public void setRoleOrgPaths(String roleOrgPaths) {
		this.roleOrgPaths = roleOrgPaths;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

    public void setOrgLevel(Integer orgLevel) {
        this.orgLevel = orgLevel;
    }

    public Integer getOrgLevel() {
        return orgLevel;
    }
}
