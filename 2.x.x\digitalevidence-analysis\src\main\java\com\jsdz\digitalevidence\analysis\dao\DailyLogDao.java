package com.jsdz.digitalevidence.analysis.dao;

/**
 *
 * @类名: DailyLogDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-31 15:33:22
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.analysis.model.DailyLog;

public interface DailyLogDao extends GenericORMEntityDAO<DailyLog,Long> {

	//新增
	public void addDailyLog(DailyLog dailyLog);

	//修改
	public void updateDailyLog(DailyLog dailyLog);

	//删除
	public void deleteDailyLog(DailyLog dailyLog);

	//按id查询,结果是游离状态的数据
	public DailyLog findDailyLogById(Long id);

	//按id查询
	public DailyLog locateDailyLogById(Long id);

	//单个查询
	public DailyLog findDailyLogByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DailyLog> findAllDailyLogs();

	//列表查询
	public List<DailyLog> findDailyLogsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DailyLog> findDailyLogsOnPage(Page<DailyLog> page,String queryStr,String[] paramNames,Object[] values);

}

