import java.util.ArrayList;
import java.util.List;

import javax.swing.JFileChooser;
import javax.swing.JPanel;
import javax.swing.filechooser.FileNameExtensionFilter;
import com.jsdz.admin.security.utils.ExcelUtils;
/**
 * 
 * @类名: TestFileChoose
 * @说明: 选择保存文件名
 *
 * @author: kenny
 * @Date	2018年6月4日下午3:36:41
 * 修改记录：
 *
 * @see
 */

public class TestFileChoose {
	
	public static void main(String[] args) throws Exception {
		String[] str = new String[0];
		str = add(str,"aaaa");
		str = add(str,"bbbb");
		str = add(str,"cccc");
		str = add(str,"dddd");
		str = add(str,"eeee");
		System.out.println(str.toString());
		
		String s = "";
		for(int i=0;i<str.length;i++){
			s = s + "/" + str[i];
		}
		System.out.println(s);
		String[] titles = new String[]{"警员编号","警员姓名","时间段（开始）","时间段（结束）","音视频总时长","文件总长度","文件总数量",
				"其中：视频时长","音频时长","照片数量","日志数量","重要文件数量","非重要文件数量"};
		
		List<String[]> values = new ArrayList<String[]>();
		String[] val1=new String[]{"654321","Test","2018-05-07 00:00:00","2018-05-07 23:59:59","9秒","133.68M",
			"7","","9秒","1","2","","7"};
		values.add(val1);
		String[] val2=new String[]{"014294","张斯彦","2018-05-08 00:00:00","2018-05-16 23:59:59",
				"20秒","73.45M","11","20秒","-","-","6","-","11"}; 
		values.add(val2);
/*		ExcelUtils.writeExcel03(
	    		titles,
	    		values);
*/	}
	
	// 增加  
    public static String[] add(String[] src,String s) {  
        //定义新数组，长度是原数组长度+1  
        String[] dest = new String[src.length+1];  
        //将原数组的数据拷贝到新数组  
        System.arraycopy(src, 0, dest, 0, src.length);  
        //将新元素放到dest数组的末尾  
        dest[src.length]=s;  
        //将src指向dest  
        return dest;  
    }  
    
	
	public static void chooseFile(){
		JFileChooser chooser = new JFileChooser();
	    FileNameExtensionFilter filter = new FileNameExtensionFilter(
	        "JPG & GIF Images", "jpg", "gif");
	    //设置文件类型
	    chooser.setFileFilter(filter);
	    //打开选择器面板
	    int returnVal = chooser.showOpenDialog(new JPanel());  
        //保存文件从这里入手，输出的是文件名
	    if(returnVal == JFileChooser.APPROVE_OPTION) {
	       System.out.println("你打开的文件是: " +
	            chooser.getSelectedFile().getName());
	       System.out.println("你打开的文件和路径是: " +chooser.getSelectedFile().getPath());
	    }
	}

}
