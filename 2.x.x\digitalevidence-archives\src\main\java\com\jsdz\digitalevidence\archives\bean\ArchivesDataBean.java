package com.jsdz.digitalevidence.archives.bean;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Organization;

import com.jsdz.utils.DateTimeUtils;

import java.util.Date;

import java.io.Serializable;
import java.util.Set;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */

public class ArchivesDataBean implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 档案ID
     */
    private Long archivesId;

    /**
     * 档案编号
     */
    private String archivesCode;

    /**
     * 档案标题
     */
    private String archivesTitle;

    /**
     * 档案类型
     */
    private Integer archivesType;

    /**
     * 档案描述（内容）
     */
    private String archivesDescribe;

    /**
     * 管理部门
     */
    private Organization organization;

    /**
     * 录入人Id
     */
   // private Operator operator;

    private Long inputUserId;

    /**
     * 录入人名称
     */
    private String inputUserName;

    /**
     * 录入时间
     */
    @JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date inputTime;

    /**
     * 修改人id
     */
    private Long updateUserId;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date updateTime;

    /**
     * 档案状态（0：正常,1：删除，2：其它）
     */
    private Integer status;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 保存时间（单位:月）
     */
    private Integer saveMonth;

    /**
     * 过期时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date expirationTime;

    /**
     * 警情
     */
    private Long alarmId;

    /**
     * 视频
     * @return
     */
    private String documents;

    public Long getAlarmId() {
        return alarmId;
    }

    public void setAlarmId(Long alarmId) {
        this.alarmId = alarmId;
    }

    public String getDocuments() {
        return documents;
    }

    public void setDocuments(String documents) {
        this.documents = documents;
    }


    //
//    public Set<DocumentBean> getDocument() {
//        return document;
//    }
//
//    public void setDocument(Set<DocumentBean> document) {
//        this.document = document;
//    }
//
//    public AlarmInfoBean getAlarmInfo() {
//        return alarmInfo;
//    }
//
//    public void setAlarmInfo(AlarmInfoBean alarmInfo) {
//        this.alarmInfo = alarmInfo;
//    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getArchivesId() {
        return archivesId;
    }

    public void setArchivesId(Long archivesId) {
        this.archivesId = archivesId;
    }

    public String getArchivesCode() {
        return archivesCode;
    }

    public void setArchivesCode(String archivesCode) {
        this.archivesCode = archivesCode;
    }

    public String getArchivesTitle() {
        return archivesTitle;
    }

    public void setArchivesTitle(String archivesTitle) {
        this.archivesTitle = archivesTitle;
    }

    public Integer getArchivesType() {
        return archivesType;
    }

    public void setArchivesType(Integer archivesType) {
        this.archivesType = archivesType;
    }

    public String getArchivesDescribe() {
        return archivesDescribe;
    }

    public void setArchivesDescribe(String archivesDescribe) {
        this.archivesDescribe = archivesDescribe;
    }

    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

//    public Operator getOperator() {
//        return operator;
//    }
//
//    public void setOperator(Operator operator) {
//        this.operator = operator;
//    }

    public Long getInputUserId() {
        return inputUserId;
    }

    public void setInputUserId(Long inputUserId) {
        this.inputUserId = inputUserId;
    }

    public String getInputUserName() {
        return inputUserName;
    }

    public void setInputUserName(String inputUserName) {
        this.inputUserName = inputUserName;
    }

    public Date getInputTime() {
        return inputTime;
    }

    public void setInputTime(Date inputTime) {
        this.inputTime = inputTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Integer getSaveMonth() {
        return saveMonth;
    }

    public void setSaveMonth(Integer saveMonth) {
        this.saveMonth = saveMonth;
    }

    public Date getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(Date expirationTime) {
        this.expirationTime = expirationTime;
    }
}
