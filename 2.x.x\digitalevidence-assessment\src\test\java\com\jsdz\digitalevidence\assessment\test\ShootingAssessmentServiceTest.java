package com.jsdz.digitalevidence.assessment.test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.admin.security.service.OperatorService;
import com.jsdz.admin.security.utils.SessionUtils;
import com.jsdz.core.Page;
import com.jsdz.core.SortOrder;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertQueryBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertSumBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentQueryBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentReportBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentReportQueryBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentWorkStatisticsBean;
import com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReportBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumQueryBean;
import com.jsdz.digitalevidence.assessment.cycle.CycleConfiguration;
import com.jsdz.digitalevidence.assessment.cycle.CycleSelector;
import com.jsdz.digitalevidence.assessment.model.AssessmentCate;
import com.jsdz.digitalevidence.assessment.model.ReportType;
import com.jsdz.digitalevidence.assessment.model.ShootingAngleLevel;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessment;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;
import com.jsdz.digitalevidence.assessment.model.ShootingContentLevel;
import com.jsdz.digitalevidence.assessment.model.ShootingResolutionLevel;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert;
import com.jsdz.digitalevidence.assessment.service.AssessmentAlertService;
import com.jsdz.digitalevidence.assessment.service.AssessmentQueryService;
import com.jsdz.digitalevidence.assessment.service.AssessmentService;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.reportquery.dynsql.fork.support.tree.TreeField;
import com.jsdz.utils.BeanUtils;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: ShootingAssessmentServiceTest
 * @说明: 拍摄考核应用服务单元测试
 *
 * <AUTHOR>
 * @Date	 2017年4月24日 下午2:00:03
 * 修改记录：
 *
 * @see 	 
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations={
		"/testApplicationContext-common.xml",
		"/testApplicationContext-cycle.xml",
		"/testApplicationContext-dynsql.xml",
		"/testApplicationContext-scheduler.xml",
		"/testApplicationContext-reportengine.xml",
		"/testApplicationContext-documenttype.xml",
		"/testApplicationContext-storage.xml"})
public class ShootingAssessmentServiceTest {

	/**拍摄考核应用服务*/
	@Autowired
	private AssessmentQueryService assessmentQeuery;
	@Autowired
	private AssessmentService assessmentService;
	@Autowired
	private EmployeesService empService;
	@Autowired
	private OperatorService operatorService;
	@Autowired
	private AssessmentAlertService alertService;

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
	}

	// 查询考核概况, 当月、往月
	@Test
	public void testQueryShootingAssessmentsSummary() throws Exception {
		//
		Page<ShootingAssessmentSumBean> page = new Page<ShootingAssessmentSumBean>();
		page.setPage(1);
		page.setPageSize(50);
		//
		ShootingAssessmentSumQueryBean qb = new ShootingAssessmentSumQueryBean();
		// 周期日
		Date start = DateTimeUtils.StringToDate("2017-09-05 00:00:00");
		qb.setCycleDate(start);
		// CycleSelector selector = CycleSelector.NEXT; 
		//qb.setCycleSelector(selector);
		// qb.setPoliceName("%警察%");
		// 组织
		/*TreeField tr = new TreeField();
		tr.setPath("1/2/");
		tr.setIncludeSub(true);
		qb.setOrgId(tr);
		*/
		// 排序
		qb.setAssessRateOrder(SortOrder.DESC);
		//
		page = assessmentQeuery.queryShootingAssessmentSummary(page, qb);
		//
		BeanUtils.printBean(page);

	}

	// 查找考核资料测试
	@Test
	public void testQueryShootingAssessmentDocument() throws Exception {
		Page<AssessmentDocumentBean> page = new Page<AssessmentDocumentBean>();
		page.setPage(1);
		page.setPageSize(10);
		// 考核开始时间
		Date start = DateTimeUtils.StringToDate("2017-07-05 00:00:00");
		//
		AssessmentDocumentQueryBean qb = new AssessmentDocumentQueryBean();
		qb.setPoliceId(10L);
		qb.setCycleDate(start);
		qb.setCycleSelector(CycleSelector.NEXT);
		//
//		page = assessmentQeuery.queryAssessmentDocument(page, qb);
		//
		System.out.println(page.getRows());

	}

	// 查找考核简要报告测试
	@Test
	public void testQueryShootingAssessmentReport() throws Exception {
		Page<AssessmentReportBean> page = new Page<AssessmentReportBean>();
		page.setPage(0);
		page.setPageSize(10);
		//
		Date startDate = DateTimeUtils.StringToDate("2017-07", "yyyy-MM");
		Date endDate = DateTimeUtils.StringToDate("2017-10", "yyyy-MM");
		//
		AssessmentReportQueryBean qb = new AssessmentReportQueryBean();
		// qb.setPoliceId(1L);
		// qb.setStartDate(startDate);
		// qb.setEndDate(endDate);
		qb.setType(ReportType.Monthly);
		//
		page = assessmentQeuery.queryShootingAccessmentReport(page, qb);
		BeanUtils.printBean(page);
	}

	// 查找我的考核报告测试
	@Test
	public void testQueryMyShootingAssessmentReport() throws Exception {
		Page<AssessmentReportBean> page = new Page<AssessmentReportBean>();
		page.setPage(2);
		page.setPageSize(1);
		//
		Date startDate = DateTimeUtils.StringToDate("2016-01", "yyyy-mm");
		Date endDate = DateTimeUtils.StringToDate("2018-01", "yyyy-mm");
		//
		AssessmentReportQueryBean qb = new AssessmentReportQueryBean();
		qb.setStartDate(startDate);
		qb.setEndDate(endDate);
		//
		page = assessmentQeuery.queryMyShootingAccessmentReport(page, startDate, endDate);
		BeanUtils.printBean(page);
	}

	// 提交拍摄考核测试
	@Test
	@Transactional
	public void testSubmitAssess() {
		Operator operator = operatorService.locateOperatorById(71L);
		// 考核员
		Employees inspector = empService.locateEmployeesById(10L);
		//
		SessionUtils.setCurrentOperator(operator);
		// 考核资料
		Document doc = new Document();
		doc.setId(930L);
		//
		ShootingAssessment sa = new ShootingAssessment();
		sa.setInspector(inspector);
		// 考核日期
		sa.setAssessDate(new Date());
		// 按要求拍摄
		sa.setAYQPS(true);
		// 考核类型
		sa.setCate(AssessmentCate.NORMAL);
		// 考核资料
		sa.setDocument(doc);
		// 没有违法违纪
		sa.setNoWFWJ(true);
		// 文明规范
		sa.setWMGF(false);
		// 按要求运用语言
		sa.setYYYY(true);
		// 拍摄角度
		sa.setShootingAngle(ShootingAngleLevel.ACCURATE);
		sa.setShootingContent(ShootingContentLevel.GENERAL);
		sa.setShootingResolution(ShootingResolutionLevel.HIGH);
		//
		assessmentService.submitAssessment(sa);
	}

	// 生成考核报告测试
	@Test
	public void testGenerateShootingAssessmentReport() throws Exception {
		// 周期日
		Date startDate = DateTimeUtils.StringToDate("2017-07-05 00:00:00");
		Date endDate = DateTimeUtils.StringToDate("2017-08-05 00:00:00");
		//
		ShootingAssessmentReport report = assessmentService.generateAndSaveAccessmentReport(9L, 
				startDate, endDate, ReportType.Monthly);
		//
		BeanUtils.printBean(report);

	}

	// 打开考核测试
	@Test
	public void testOpenAssessment() throws Exception {
		Long docId = 152L;
		ShootingAssessmentBean bean = assessmentService.openAssessment(docId, new Operator(), "");
		BeanUtils.printBean(bean);
	}

	// 获取考核报告测试
	@Test
	public void testGetAccessmentReport() {
		Long reportId = 5L;
		CompleteAssessmentReportBean reportBean = assessmentService.getShootingAccessmentReportBean(reportId);
		BeanUtils.printBean(reportBean);
	}

	// 写评语，考核报告
	@Test
	public void testCommentAccessmentReport() {
		assessmentService.commentShootingAccessmentReport(6L, "评语测试");
	}

	// 冻结考核报告
	public void testFrozenAccessmentReport() {

	}

	/*********考核预警测试*********/
	// 生成警员考核预警
	Date cycleDate = DateTimeUtils.StringToDate("2017-10-5", DateTimeUtils.defaultDatePatten2);

	Date reportDate = DateTimeUtils.StringToDate("2017-10-5", DateTimeUtils.defaultDatePatten2);
	@Test
	public void testGenAssessmentAlertOfPolice() throws Exception {
		AssessmentAlert alert = alertService.genAssessmentAlertOfPolice(4L, cycleDate, reportDate);
		BeanUtils.printBean(alert);
	}

	@Test
	public void testFeedback() throws Exception {
		AssessmentAlert alert = alertService.getAssessmentAlertOfPolice(1L, cycleDate);
		alertService.feedback(alert.getId(), "知道了");

	}

	@Test
	public void testOpenAssessmentAlert() {
		AssessmentAlertBean alertBean = alertService.openAssessmentAlert(9L);
		BeanUtils.printBean(alertBean);
	}

	// 预警查询
	@Test
	public void testQueryAssessmentAlert() throws Exception {
		Page<AssessmentAlert> page = new Page<AssessmentAlert>();
		page.setPage(1);
		page.setPageSize(2);
		//
		AssessmentAlertQueryBean qb = new  AssessmentAlertQueryBean();
		// qb.setPoliceCode("041370");
		// qb.setHasFreeback(true);
		// qb.setNotFreeback(true);
		//
		page = alertService.queryAssessmentAlert(page, qb);
		BeanUtils.printBean(page);
	}

	// 搜索
	@Test
	public void testSearchAssessmentAlert() throws Exception {
		Page<AssessmentAlertSumBean> page = new Page<AssessmentAlertSumBean>();
		page.setPage(1);
		page.setPageSize(2);
		//
		AssessmentAlertQueryBean qb = new  AssessmentAlertQueryBean();
		// qb.setPoliceCode("041370");
		// qb.setHasFreeback(true);
		// qb.setNotFreeback(true);
		//Date startDate = DateTimeUtils.StringToDate("2017-09-28", DateTimeUtils.defaultDatePatten2);
		//qb.setCycleDate(startDate);
		//
		qb.setType(ReportType.Monthly);
		page = alertService.searchAssessmentAlert(page, qb);
		BeanUtils.printBean(page);
	}
	//考核工作统计
	@Test
	public void testsearchWorkStatisticsBeanQuery(){
		Page<AssessmentWorkStatisticsBean> page = new Page<AssessmentWorkStatisticsBean>();
		page.setPage(1);
		page.setPageSize(3);
		Date startDate = DateTimeUtils.StringToDate("2017-11-5", "yyyy-MM");
		Date endDate = DateTimeUtils.StringToDate("2017-12-5", "yyyy-MM");
		Map<String, Object> kvs = new HashMap<String, Object>();
		kvs.put("startDate", startDate);
		kvs.put("endDate", endDate);
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		page=assessmentQeuery.searchWorkStatisticsBean(page, paramNames, values);
		System.out.println(page.getRows().get(0).getName());
	}
}
