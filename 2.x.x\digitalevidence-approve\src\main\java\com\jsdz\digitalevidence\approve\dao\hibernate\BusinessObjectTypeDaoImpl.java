/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.dao.hibernate;

import org.springframework.stereotype.Repository;

import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.approve.dao.BusinessObjectTypeDao;
import com.jsdz.digitalevidence.approve.model.BusinessObjectType;

/**
 * @类名: BusinessObjectTypeDaoImpl
 * @说明: 业务对象类型Dao实现
 *
 * <AUTHOR>
 * @Date	 2017年4月17日 下午5:18:16
 * 修改记录：
 *
 * @see 	 
 */
@Repository
public class BusinessObjectTypeDaoImpl extends GenericEntityDaoHibernateImpl<BusinessObjectType, Long>
					implements BusinessObjectTypeDao {


}
