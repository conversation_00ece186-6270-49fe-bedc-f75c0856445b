package com.jsdz.digitalevidence.statistics.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("SYS_DAILY_DEVICE")
public class SysDailyDeviceModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Long id;

    @TableField("ORGID")
    private Long orgid;

    @TableField("ORGNAME")
    private String orgname;

    @TableField("ORGCODE")
    private String orgcode;

    @TableField("ONLINEQUANTITY")
    private String onlinequantity;

    @TableField("DOCNUM")
    private Long docnum;

    @TableField("DOCDURATION")
    private Long docduration;

    @TableField("DOCSITE")
    private Long docsite;


}
