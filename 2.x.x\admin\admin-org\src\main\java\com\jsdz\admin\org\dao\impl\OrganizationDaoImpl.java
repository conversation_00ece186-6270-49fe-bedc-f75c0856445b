package com.jsdz.admin.org.dao.impl;

import java.sql.SQLException;
import java.util.Date;

/**
 *
 * @类名: OrganizationDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-06 19:18:40
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.org.dao.OrganizationDao;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;


@Repository
public class OrganizationDaoImpl extends GenericEntityDaoHibernateImpl<Organization,Long> implements OrganizationDao{

	//新增
	public void addOrganization(Organization organization) {
		this.saveOrUpdate(organization);
		String path = null;
		if (organization.getParentOrg() == null){
			path = organization.getId().toString() + "/";
		}else{
			Organization parOrg = findOrganizationByCondition("from Organization o where id = :id",new String[]{"id"},new Object[]{organization.getParentOrg().getId()});
			//String parentPath = parOrg.getPath();
			path = parOrg.getPath() + organization.getId().toString() + "/";
		}
		organization.setPath(path);
		this.merge(organization);
	}

	//删除
	public void deleteOrganization(Organization organization) {
		this.delete(organization);
	}

	//修改
	public void updateOrganization(Organization organization) {
		this.merge(organization);// .saveOrUpdate(organization);
		
	}
	//按id查询(游离状态)
	public Organization findOrganizationById(Long id){
		final String  hql =	" from Organization og left join fetch og.parentOrg pog "
						  + " left join fetch og.region r "
				          + " where og.id=:id";
		final Long oid = id;
		Organization data = getHibernateTemplate().execute(new HibernateCallback<Organization>() {
			public Organization doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<Organization> list = query.list();
				Organization rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}	

	//按id查询
	public Organization locateOrganizationById(Long id){

		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(
				"from Organization og left join fetch og.parentOrg pog "
				+ " left join fetch og.region r "
				+ " where og.id=:id");
		query.setParameter("id", id);
		List<Organization> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//单个查询
	public Organization findOrganizationByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		};
		List<Organization> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//列表查询
	public List<Organization> findOrganizationsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<Organization> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<Organization> findOrganizationsOnPage(Page<Organization> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<Organization>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	
	//更新本级及下级的path
	public void updateOrganizationPath(String oldPath,String newPath){
		
		
		//2、取得所有下级单位并更新path
		List<Organization> list = findOrganizationsByCondition(
				"from Organization o where o.path like :path ",
				new String[]{"path"},
				new Object[]{oldPath + '%'});
		for (Organization o : list){
			String path = o.getPath().replaceFirst(oldPath,newPath);
			o.setPath(path);
			this.merge(o);
		}
		
	} 
	
	//
	public Organization findOrganizationAndParent(final long id){
		final String hql = "from Organization og left join fetch op.parentOrg pog " 
				+ "where og.id = :id";
		return getHibernateTemplate().execute(new HibernateCallback<Organization>() {
			public Organization doInHibernate(Session session) throws HibernateException, SQLException {
				// 获取查询对象
				Query queryObject = session.createQuery(hql);
				// 置入参数
				applyNamedParameterToQuery(queryObject, "id", id);
				return (Organization)queryObject.uniqueResult();
			}
		});

	}
	
	@Override
	public List<Object> getOrgListForTree(String sql,String[] paramNames, Object[] values){
		List<Object> data = getHibernateTemplate().execute(new HibernateCallback<List<Object>>() {
			@SuppressWarnings("unchecked")
			@Override
			public List<Object> doInHibernate(Session session) throws HibernateException, SQLException {
				// 获取查询对象
				Query queryObject = session.createSQLQuery(sql);
				// 置入参数
				for (int i=0;i<values.length;i++){
					applyNamedParameterToQuery(queryObject, paramNames[i], values[i]);
				};
				return queryObject.list();
			}
		});
		return data;
	}

}
