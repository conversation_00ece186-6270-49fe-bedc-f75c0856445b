package com.jsdz.digitalevidence.analysis.openapi.dao.impl;

/**
 *
 * @类名: StationInfoDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-06-09 16:03:51
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.analysis.openapi.dao.StationInfoDao;
import com.jsdz.digitalevidence.analysis.openapi.model.StationInfo;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class StationInfoDaoImpl extends GenericEntityDaoHibernateImpl<StationInfo,Long> implements StationInfoDao{

	//新增
	public void addStationInfo(StationInfo stationInfo) {
		this.saveOrUpdate(stationInfo);
	}

	//删除
	public void deleteStationInfo(StationInfo stationInfo) {
		this.delete(stationInfo);
	}

	//修改
	public void updateStationInfo(StationInfo stationInfo) {
		this.merge(stationInfo);
	}

	//按id查询(游离状态)
	public StationInfo findStationInfoById(Long id){

		final String  hql = "from StationInfo s where s.id = :id";
		final Long oid = id;
		StationInfo data = getHibernateTemplate().execute(new HibernateCallback<StationInfo>() {
			public StationInfo doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<StationInfo> list = query.list();
				StationInfo rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public StationInfo locateStationInfoById(Long id){

		final String  hql = "from StationInfo s where s.id = :id";
		final Long oid = id;
		StationInfo data = getHibernateTemplate().execute(new HibernateCallback<StationInfo>() {
			public StationInfo doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<StationInfo> list = query.list();
				StationInfo rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public StationInfo findStationInfoByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		StationInfo data = getHibernateTemplate().execute(new HibernateCallback<StationInfo>() {
		public StationInfo doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<StationInfo> list = query.list();
			StationInfo rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<StationInfo> findAllStationInfos(){
		return this.find("from StationInfo stationInfo ");
	}

	//列表查询
	public List<StationInfo> findStationInfosByCondition(String queryStr,String[] paramNames,Object[] values){
		List<StationInfo> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<StationInfo> findStationInfosOnPage(Page<StationInfo> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<StationInfo>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
