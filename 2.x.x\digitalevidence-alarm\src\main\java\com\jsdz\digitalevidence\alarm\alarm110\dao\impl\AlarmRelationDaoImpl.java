package com.jsdz.digitalevidence.alarm.alarm110.dao.impl;

/**
 *
 * @类名: AlarmRelationDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-08-29 19:53:45
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;

import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmRelationDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class AlarmRelationDaoImpl extends GenericEntityDaoHibernateImpl<AlarmRelation,Long> implements AlarmRelationDao{

	//新增
	public void addAlarmRelation(AlarmRelation alarmRelation) {
		this.saveOrUpdate(alarmRelation);
	}

	//删除
	public void deleteAlarmRelation(AlarmRelation alarmRelation) {
		this.delete(alarmRelation);
	}

	//修改
	public void updateAlarmRelation(AlarmRelation alarmRelation) {
		this.merge(alarmRelation);
	}

	//按id查询(游离状态)
	public AlarmRelation findAlarmRelationById(Long id){

		final String  hql = "from AlarmRelation a "
				+ " left join fetch a.alarmInfo l "
				+ " left join fetch a.mediaInfo m "
				+ " where a.id = :id";
		final Long oid = id;
		AlarmRelation data = getHibernateTemplate().execute(new HibernateCallback<AlarmRelation>() {
			public AlarmRelation doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmRelation> list = query.list();
				AlarmRelation rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public AlarmRelation locateAlarmRelationById(Long id){

		final String  hql = "from AlarmRelation a where a.id = :id";
		final Long oid = id;
		AlarmRelation data = getHibernateTemplate().execute(new HibernateCallback<AlarmRelation>() {
			public AlarmRelation doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmRelation> list = query.list();
				AlarmRelation rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public AlarmRelation findAlarmRelationByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		AlarmRelation data = getHibernateTemplate().execute(new HibernateCallback<AlarmRelation>() {
		public AlarmRelation doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<AlarmRelation> list = query.list();
			AlarmRelation rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<AlarmRelation> findAllAlarmRelations(){
		return this.find("from AlarmRelation alarmRelation ");
	}

	//列表查询
	public List<AlarmRelation> findAlarmRelationsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<AlarmRelation> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<AlarmRelation> findAlarmRelationsOnPage(Page<AlarmRelation> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<AlarmRelation>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	//查询是否重复
	public AlarmRelation findMutipleAlarmRelation(AlarmRelation alarmRelation){
		final String  hql = "from AlarmRelation a  left join fetch a.alarmInfo i "
				+ " left join fetch a.mediaInfo m "
				+ " where i.id = :alarmId "
				+ "  and m.id = :docId";
		AlarmRelation data = getHibernateTemplate().execute(new HibernateCallback<AlarmRelation>() {
			public AlarmRelation doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("alarmId", alarmRelation.getAlarmInfo().getId());
				query.setParameter("docId", alarmRelation.getMediaInfo().getId());
				List<AlarmRelation> list = query.list();
				AlarmRelation rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}
	
	//执行自定义的hql
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}


}
