package com.jsdz.digitalevidence.alarm.alarm110.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.digitalevidence.document.model.Case;
import com.jsdz.digitalevidence.document.model.CaseType;
import com.jsdz.digitalevidence.document.model.ImportantLevel;

/**
 * 
 * @类名: CaseBean
 * @说明: Case封装类
 * <AUTHOR>
 *
 */
public class CaseBean {
	
	// ID
	private Long id ;
	// 案件编号
	private String caseCode;
	// 案件名称
	private String caseName;
	// 案件类型
	private CaseType caseType;

	private Integer caseTypeIndex;
	// 重要级别
	private ImportantLevel level;
	// 接警编号
	private String receiptAlarmN;
	// 出警编号
	private String disposalAlarmN;
	// 案发时间
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date caseTime;
	// 案发地址
	private String caseAddress;
	//是否已关联(0未关联，1已关联)	
	private Integer isRelation; 
	// 出警时间
	private Date proTime;
	// 接警内容
	private String alarmContext;
	// 出警内容描述
	private String procResult;
	// 录入数据库时间
	private Date createTime;
	
	 //处警单位
    private Long orgId;    
    private String orgCode;
    private String orgName;

	// 查询时间
    private String createTimeBegin;
    private String createTimeEnd;
    private String caseTimeBegin;
    private String caseTimeEnd;
	
    //是否可操作(关联，取消关联)
    private Integer canRelation;
    
    private String workNumber1 ; // 出警人1工号
	private String workNumber2 ; // 出警人2工号
	private String caseFlow ; // 案件流程

    public void assign(Case ca){
    	
    	id = ca.getId();
    	isRelation = ca.getIsRelation();
    	caseCode = ca.getCaseCode();
    	caseName = ca.getCaseName();
    	caseType = ca.getCaseType();
		if (caseType != null){
			caseTypeIndex = caseType.ordinal();
		}
    	level = ca.getLevel();
    	receiptAlarmN = ca.getReceiptAlarmN();
    	disposalAlarmN = ca.getDisposalAlarmN();
    	caseTime = ca.getCaseTime();
    	caseAddress = ca.getCaseAddress();
    	proTime = ca.getProcTime();
    	alarmContext = ca.getAlarmContext();
    	procResult = ca.getProcResult();
    	createTime = ca.getCaseTime();
    	workNumber1 = ca.getWorkNumber1();
    	workNumber2 = ca.getWorkNumber2();
    	caseFlow = ca.getCaseFlow();
    	orgId = ca.getProcOrg()   == null ? null : ca.getProcOrg().getId();
    	orgCode = ca.getProcOrg() == null ? null : ca.getProcOrg().getOrgCode();
    	orgName = ca.getProcOrg() == null ? null : ca.getProcOrg().getOrgName();
    }
    
    public void assignTo(Case dest){
    	
    	dest.setId(id);
    	dest.setCaseCode(caseCode);
    	dest.setCaseName(caseName);
    	dest.setCaseType(caseType);
    	dest.setLevel(level);
    	dest.setIsRelation(isRelation);
    	dest.setReceiptAlarmN(receiptAlarmN);
    	dest.setDisposalAlarmN(disposalAlarmN);
    	dest.setCaseTime(caseTime);
    	dest.setCaseAddress(caseAddress);
    	dest.setProcTime(proTime);
    	dest.setAlarmContext(alarmContext);
    	dest.setProcResult(procResult);
    	dest.setCreateTime(createTime);
    	dest.setWorkNumber1(workNumber1);
    	dest.setWorkNumber2(workNumber2);
    	
    	if(orgId != null){
    		
    		Organization org = new Organization();
    		org.setId(orgId);
    		dest.setProcOrg(org);
    		
    	}
    }

	public String getCaseFlow() {
		return caseFlow;
	}

	public void setCaseFlow(String caseFlow) {
		this.caseFlow = caseFlow;
	}

	public Integer getCaseTypeIndex() {
		return caseTypeIndex;
	}

	public void setCaseTypeIndex(Integer caseTypeIndex) {
		this.caseTypeIndex = caseTypeIndex;
	}


	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getCaseCode() {
		return caseCode;
	}

	public void setCaseCode(String caseCode) {
		this.caseCode = caseCode;
	}

	public String getCaseName() {
		return caseName;
	}

	public void setCaseName(String caseName) {
		this.caseName = caseName;
	}

	public CaseType getCaseType() {
		return caseType;
	}

	public void setCaseType(CaseType caseType) {
		this.caseType = caseType;
	}

	public ImportantLevel getLevel() {
		return level;
	}

	public void setLevel(ImportantLevel level) {
		this.level = level;
	}

	public String getReceiptAlarmN() {
		return receiptAlarmN;
	}

	public void setReceiptAlarmN(String receiptAlarmN) {
		this.receiptAlarmN = receiptAlarmN;
	}

	public String getDisposalAlarmN() {
		return disposalAlarmN;
	}

	public void setDisposalAlarmN(String disposalAlarmN) {
		this.disposalAlarmN = disposalAlarmN;
	}

	public Date getCaseTime() {
		return caseTime;
	}

	public void setCaseTime(Date caseTime) {
		this.caseTime = caseTime;
	}

	public String getCaseAddress() {
		return caseAddress;
	}

	public void setCaseAddress(String caseAddress) {
		this.caseAddress = caseAddress;
	}

	public Date getProTime() {
		return proTime;
	}

	public void setProTime(Date proTime) {
		this.proTime = proTime;
	}

	public String getAlarmContext() {
		return alarmContext;
	}

	public void setAlarmContext(String alarmContext) {
		this.alarmContext = alarmContext;
	}

	public String getProcResult() {
		return procResult;
	}

	public void setProcResult(String procResult) {
		this.procResult = procResult;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getCreateTimeBegin() {
		return createTimeBegin;
	}

	public void setCreateTimeBegin(String createTimeBegin) {
		this.createTimeBegin = createTimeBegin;
	}

	public String getCreateTimeEnd() {
		return createTimeEnd;
	}

	public void setCreateTimeEnd(String createTimeEnd) {
		this.createTimeEnd = createTimeEnd;
	}


	public String getCaseTimeBegin() {
		return caseTimeBegin;
	}

	public void setCaseTimeBegin(String caseTimeBegin) {
		this.caseTimeBegin = caseTimeBegin;
	}

	public String getCaseTimeEnd() {
		return caseTimeEnd;
	}

	public void setCaseTimeEnd(String caseTimeEnd) {
		this.caseTimeEnd = caseTimeEnd;
	}

	public Integer getCanRelation() {
		return canRelation;
	}

	public void setCanRelation(Integer canRelation) {
		this.canRelation = canRelation;
	}

	public Integer getIsRelation() {
		return isRelation;
	}

	public void setIsRelation(Integer isRelation) {
		this.isRelation = isRelation;
	}

	public String getWorkNumber1() {
		return workNumber1;
	}

	public void setWorkNumber1(String workNumber1) {
		this.workNumber1 = workNumber1;
	}

	public String getWorkNumber2() {
		return workNumber2;
	}

	public void setWorkNumber2(String workNumber2) {
		this.workNumber2 = workNumber2;
	}
    
}
