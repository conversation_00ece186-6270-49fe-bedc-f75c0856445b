package com.jsdz.admin.org.dao;

/**
 *
 * @类名: OrganizationDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-06 19:18:40
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface OrganizationDao extends GenericORMEntityDAO<Organization,Long> {

	//新增
	public void addOrganization(Organization organization);

	//修改
	public void updateOrganization(Organization organization);

	//删除
	public void deleteOrganization(Organization organization);

	//按id查询
	public Organization locateOrganizationById(Long id);

	//单个查询
	public Organization findOrganizationByCondition(String queryStr,String[] paramNames,Object[] values);

	//列表查询
	public List<Organization> findOrganizationsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Organization> findOrganizationsOnPage(Page<Organization> page,String queryStr,String[] paramNames,Object[] values);

	//更新本级及下级的path
	public void updateOrganizationPath(String oldPath,String newPath);
	
	//按id查询(游离状态)
	public Organization findOrganizationById(Long id);
	
	/**
	 * 原生SQL查询树结构的单位数据
	 * @param sql
	 * @param paramNames
	 * @param values
	 * @return
	 */
	public List<Object> getOrgListForTree(String sql,String[] paramNames, Object[] values);
}

