package com.jsdz.digitalevidence.alarm.common.bean;
/**
 * 
 * @类名: RecoderReviewBean
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年10月26日上午11:08:50
 * 修改记录：
 *
 * @see
 */

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jsdz.admin.security.utils.JSDateFormatUtils;
import com.jsdz.digitalevidence.alarm.common.model.RecoderView;


public class RecoderReviewBean {
	@JsonIgnore
	private String user_key;//记录的id，docid;
	private String glbm;//管理部门编码，观看者管理部门编码
	private String gjbh;//干警编号，观看警员编号 
	private String xm;//干警姓名，观看警员姓名
	private String gxsj;//更新时间，监督回放时间
	private Integer start;//开始时长，视频开始时长，单位秒
	private Integer end;//结束时长，视频结束时长，单位秒
	private String spwt;//审批问题；多选用（;）分割
	/*无问题，不文明使用执法记录仪（抽烟、玩游戏等），使用不规范（静止画面、摄录与执法无关），
	 * 未告知正在使用执法记录仪，超短视频，无声音视频，拍摄画面无效（无当事人正脸、询问信息等）*/
	private String spyj;//审批意见
	private String reserved1;//可为空
	private String reserved2;//可为空	
	private String docName;
	
	public void assign(RecoderView src){
		this.setUser_key(String.valueOf(src.getDoId()));
		this.setGlbm(src.getGlbm());
		this.setGjbh(src.getGjbh());
		this.setXm(src.getXm());
		this.setGxsj(JSDateFormatUtils.formatDateTime(src.getGxsj()));
		this.setStart(src.getStart());
		this.setEnd(src.getEnd());
		this.setSpwt(src.getSpwt());
		this.setSpyj(src.getSpyj());
		this.setReserved1(src.getReserved1());
		this.setReserved2(src.getReserved2());
		this.setDocName(src.getDocName());
	}
	
	public String getGlbm() {
		return glbm;
	}
	public void setGlbm(String glbm) {
		this.glbm = glbm;
	}
	public String getGjbh() {
		return gjbh;
	}
	public void setGjbh(String gjbh) {
		this.gjbh = gjbh;
	}
	public String getXm() {
		return xm;
	}
	public void setXm(String xm) {
		this.xm = xm;
	}
	public String getGxsj() {
		return gxsj;
	}
	public void setGxsj(String gxsj) {
		this.gxsj = gxsj;
	}
	public Integer getStart() {
		return start;
	}
	public void setStart(Integer start) {
		this.start = start;
	}
	public Integer getEnd() {
		return end;
	}
	public void setEnd(Integer end) {
		this.end = end;
	}
	public String getSpwt() {
		return spwt;
	}
	public void setSpwt(String spwt) {
		this.spwt = spwt;
	}
	public String getSpyj() {
		return spyj;
	}
	public void setSpyj(String spyj) {
		this.spyj = spyj;
	}
	public String getReserved1() {
		return reserved1;
	}
	public void setReserved1(String reserved1) {
		this.reserved1 = reserved1;
	}
	public String getReserved2() {
		return reserved2;
	}
	public void setReserved2(String reserved2) {
		this.reserved2 = reserved2;
	}
	public String getUser_key() {
		return user_key;
	}
	public void setUser_key(String user_key) {
		this.user_key = user_key;
	}

	public String getDocName() {
		return docName;
	}

	public void setDocName(String docName) {
		this.docName = docName;
	}
	
}
