package com.jsdz.admin.security.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @类名: Post
 * @说明: 岗位实体类
 *
 * @author: kenny
 * @Date	2017年12月30日上午11:21:20
 * 修改记录：
 *
 * @see
 */
public class Post implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = 3145024137412698522L;
	
	private Long id;
	private String post;//岗位
	private String description;//说明
	private Post parentPost; //上级岗位
	private String path;//岗位
	private Integer index; //显示顺序
	public Integer getIndex() {
		return index;
	}
	public void setIndex(Integer index) {
		this.index = index;
	}
	private Date createTime;
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getPost() {
		return post;
	}
	public void setPost(String post) {
		this.post = post;
	}
	public Post getParentPost() {
		return parentPost;
	}
	public void setParentPost(Post parentPost) {
		this.parentPost = parentPost;
	}
	public String getPath() {
		return path;
	}
	public void setPath(String path) {
		this.path = path;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	
	
}
