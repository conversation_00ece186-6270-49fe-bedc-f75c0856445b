package com.jsdz.digitalevidence.utils;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceTaskModel;

import java.lang.reflect.Field;
import java.util.Date;


/**
 *
 * <AUTHOR>
 * @create 2024/01/17 13:22:41
 *
 */
public class SqlUtils<T> extends StringUtils {


//    public static void main(String[] args) {
//        SqlUtils sqlUtils = new SqlUtils();
//         AttendanceTaskModel model = new AttendanceTaskModel();
//        model.setId(111L);
//        model.setCreateTime(new Date());
//        model.setAttendanceName("萨德罚恶法厄发");
//        String s = sqlUtils.deleteByIdSql(model, "testTable", "id");
//        System.out.println(s);
//    }

    /**
     * 反射获取添加sql
     * @param model
     * @param tableName
     * @return
     */
    public String updateSql(T model, String tableName, String pkId) {
        String columns = "update "+tableName+ " set";
        String  where = " where 1=1 ";
        try {
            Field[] declaredFields = model.getClass().getDeclaredFields();
            //INSERT INTO table_name (column1, column2) VALUES ('value1', 'value2');
            for (Field field : declaredFields) {
                //允许反射获取字段属性
                field.setAccessible(true);
                String column = field.getName();
                if ("serialVersionUID".equals(column)) continue;
                //属性内容拼接
                Object value = field.get(model);
                //字段为空不修改
                if(StringUtils.isBlank(value)) continue;

                if(pkId.equals(column)){
                    where += " and "+column +" = "+value;
                    continue;
                }
                Class<?> type = field.getType();
                if(String.class.equals(type)&& StringUtils.isNoBlank(value)){
                    columns +=  " "+column +" = "+ "'"+value+"', ";
                }else if(Date.class.equals(type) && StringUtils.isNoBlank(value)){
                    columns +=  " "+column +" = "+" STR_TO_DATE('"+DateUtil.dateStr((Date) value)+"','%Y-%m-%d %T'), ";
                }else{
                    columns +=  " "+column +" = "+value+", ";
                }
            }
            columns = columns.substring(0,columns.length()-2);

        }catch (Exception e){
            e.printStackTrace();
        }

        return columns +where;
    }



    /**
     * 反射获取添加sql
     * @param model
     * @param tableName
     * @return
     */
    public String saveSql(T model, String tableName) {
        String columns = "INSERT INTO "+tableName+ "(";
        String  values = " VALUES (";
        try {
            Field[] declaredFields = model.getClass().getDeclaredFields();
            //INSERT INTO table_name (column1, column2) VALUES ('value1', 'value2');
            for (Field field : declaredFields) {
                //允许反射获取字段属性
                field.setAccessible(true);
                String column = field.getName();
                if ("serialVersionUID".equals(column)) continue;

                //属性内容拼接
                Object value = field.get(model);
                //字段为空不添加
                if(StringUtils.isBlank(value)) continue;
                Class<?> type = field.getType();
                System.out.println(type);
                if(String.class.equals(type)&& StringUtils.isNoBlank(value)){
                    values +=  "'"+value+"', ";
                }else if(Date.class.equals(type) && StringUtils.isNoBlank(value)){

                    values +=  "STR_TO_DATE('"+DateUtil.dateStr((Date) value)+"','%Y-%m-%d %T'), ";
                }else{
                    values +=  value+", ";
                }
                //属性名拼接
                columns +=  column+", ";
            }
            columns = columns.substring(0,columns.length()-2)+")";
            values = values.substring(0,values.length()-2)+")";

        }catch (Exception e){
           e.printStackTrace();
        }

        return columns +values;
    }





    public String getByIdSql(T model, String tableName, String pkId){

        String getByIdSql = "select t.* from "+tableName+" t";
        try {
            Field[] declaredFields = model.getClass().getDeclaredFields();
            for (Field field : declaredFields) {
                //允许反射获取字段属性
                field.setAccessible(true);
                String column = field.getName();
                if (!pkId.equals(column)) continue;
                //属性内容拼接
                Object value = field.get(model);
                Class<?> type = field.getType();
                if(String.class.equals(type)&& StringUtils.isNoBlank(value)){
                    getByIdSql += " where t."+pkId+" = '"+value+"'";
                }else{
                    getByIdSql += " where t."+pkId+" = "+value+"";
                }

            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return getByIdSql;
    }

    public String deleteByIdSql(T model, String tableName, String pkId){

        String deleteByIdSql = "delete from "+tableName+" t";
        try {
            Field[] declaredFields = model.getClass().getDeclaredFields();
            for (Field field : declaredFields) {
                //允许反射获取字段属性
                field.setAccessible(true);
                String column = field.getName();
                if (!pkId.equals(column)) continue;
                //属性内容拼接
                Object value = field.get(model);
                Class<?> type = field.getType();
                if(String.class.equals(type)&& StringUtils.isNoBlank(value)){
                    deleteByIdSql += " where t."+pkId+" = '"+value+"'";
                }else{
                    deleteByIdSql += " where t."+pkId+" = "+value+"";
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return deleteByIdSql;
    }




    /**
     * 查询设置权限
     * @param whereSql
     * @param permLvl
     * @return
     */
    public String userLevel(String whereSql, PerLvlBean permLvl) {
        String loginName = permLvl.getLoginName();//用户名
        if( "admin".equals(loginName) || "admin@jsdz".equals(loginName)){
            return whereSql;
        }
        String orgPath = permLvl.getOrgPath();//用户path权限
        Integer permissionLevel = permLvl.getPermissionLevel();//用户权限
        if (permissionLevel<2 && StringUtils.isNoBlank(orgPath)){
            whereSql += " and o.path = "+orgPath;
        }else  if (permissionLevel >= 2 && StringUtils.isNoBlank(orgPath)){
            whereSql += " and o.path like '"+orgPath+"%'";
        }
        return whereSql;
    }
}
