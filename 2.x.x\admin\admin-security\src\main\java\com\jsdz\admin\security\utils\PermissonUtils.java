package com.jsdz.admin.security.utils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.service.OperatorRoleService;
import com.jsdz.admin.security.service.RoleOrganizationService;
import com.jsdz.digitalevidence.cache.utils.Constant;

import java.util.Map;

/**
 * 说明：权限工具类
 * @类名: PermissonUtils
 *
 * <AUTHOR>
 * @Date	 2019年12月19日上午10:34:51
 */
@Component
public class PermissonUtils {
	/*
	 * static 对象是无法自动注入bean, 所以使用@PostConstruct在初始化程序里把自动注入的bean
	 * 赋给静态象
	 */
	@Autowired
	private OperatorRoleService autowiredOperatorRoleService;
	@Autowired
	private RoleOrganizationService autowiredRoleOrganizationService;
	
	private static OperatorRoleService operatorRoleService;
	private static RoleOrganizationService roleOrganizationService;
	
	@PostConstruct
	public void init(){
		operatorRoleService = autowiredOperatorRoleService;
		roleOrganizationService = autowiredRoleOrganizationService;
	}


	/**生成PerLvlBean
	 * @param operator
	 * @return
	 */
	public static PerLvlBean generatePerLvl(Operator operator){
		//存入当前登录用户的角色
		PerLvlBean p = new PerLvlBean();
		p.setOperatorId(operator.getId());
		if (operator.getEmployees() != null){ 
			p.setEmpId(operator.getEmployees().getId());	
			if (operator.getEmployees().getOrganization() != null){
				p.setOrgId(operator.getEmployees().getOrganization().getId());
				p.setOrgPath(operator.getEmployees().getOrganization().getPath());
			}
		}
		//取权限级别
		Integer permissions= 0;
		Integer orgLevel= 0;
		if (operator.getLoginName().equalsIgnoreCase("admin") || operator.getLoginName().equalsIgnoreCase("admin@jsdz")){
			permissions = 9;
			orgLevel= 99999;
		}else {
			Map<String, Object> levelMap = operatorRoleService.findMaxPermission(operator.getId());
			permissions = (Integer) levelMap.get("roleLevel");
			orgLevel = (Integer) levelMap.get("orgLevel");
		}
		p.setPermissionLevel(permissions);//权限级别
		p.setOrgLevel(orgLevel);// 权限单位级别
		
		//取单位权限
		String paths = roleOrganizationService.getRoleOrgPath(operator.getId());
		
		p.setRoleOrgPaths("".equals(paths)?null:paths);
		
		p.setLoginName(operator.getLoginName());
		
		return p;
	}
	
	/**
	 * 取得当前用户的PerLvlBean
	 * @param request
	 * @return
	 */
	public static PerLvlBean getCurrPerLvl(HttpServletRequest request){
		Object obj = request.getSession().getAttribute(Constant.VAR_PERMISSIONS_LEVEL);
		if (obj != null)
			return (PerLvlBean)obj;
		else{
			PerLvlBean p = new PerLvlBean();
			p.setPermissionLevel(0);
			return p;
		}
		
	}
	
}
