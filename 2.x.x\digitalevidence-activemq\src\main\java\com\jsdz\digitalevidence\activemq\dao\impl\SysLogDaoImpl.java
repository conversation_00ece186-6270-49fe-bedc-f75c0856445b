package com.jsdz.digitalevidence.activemq.dao.impl;

import java.util.ArrayList;

/**
 *
 * @类名: SysLogDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2018-01-31 15:36:14
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Projections;
import org.springframework.stereotype.Repository;

import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.Criteria;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.activemq.dao.SysLogDao;
import com.jsdz.digitalevidence.activemq.model.SysLog;

@Repository
public class SysLogDaoImpl extends GenericEntityDaoHibernateImpl<SysLog,Long> implements SysLogDao{

	//新增
	public void addSysLog(SysLog sysLog) {
		this.saveOrUpdate(sysLog);
	}

	//删除
	public void deleteSysLog(SysLog sysLog) {
		this.delete(sysLog);
	}

	//修改
	public void updateSysLog(SysLog sysLog) {
		this.merge(sysLog);
	}

	//按id查询(游离状态)
	public SysLog findSysLogById(Long id){

		final String  hql = "from SysLog s where s.id = :id";
		final Long oid = id;
		SysLog data = getHibernateTemplate().execute(new HibernateCallback<SysLog>() {
			public SysLog doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<SysLog> list = query.list();
				SysLog rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public SysLog locateSysLogById(Long id){

		final String  hql = "from SysLog s where s.id = :id";
		final Long oid = id;
		SysLog data = getHibernateTemplate().execute(new HibernateCallback<SysLog>() {
			public SysLog doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<SysLog> list = query.list();
				SysLog rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public SysLog findSysLogByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		SysLog data = getHibernateTemplate().execute(new HibernateCallback<SysLog>() {
		public SysLog doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<SysLog> list = query.list();
			SysLog rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<SysLog> findAllSysLogs(){
		return this.find("from SysLog sysLog ");
	}

	//列表查询
	public List<SysLog> findSysLogsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<SysLog> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<SysLog> findSysLogsOnPage(Page<SysLog> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<SysLog>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//执行自定义的hql
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
	
	
	/**
	 * findGroupByTopic
	 * 按指定字段分组查询
	 * @return
	 */
	@Override
	public List<String> findGroupBy(String groupByName){
		@SuppressWarnings("deprecation")
		Criteria criteria = super.getSession().createCriteria(SysLog.class);
		criteria.setProjection(Projections.groupProperty(groupByName));
		List list = criteria.list();
		List<String> result = new ArrayList<String>();
		for(Object ob : list){ 
			result.add((String)ob);
		    //System.out.println(ob.toString());      
		}
		return result;
	}
	
	//按条件执行HQL（非查询）
	@Override
	public Integer execHqlByCondition(final String hql,final String[] params,final Object[] values ){
		int data = getHibernateTemplate().execute(new HibernateCallback<Integer>() {      	 
		    public Integer doInHibernate(Session session)throws HibernateException, SQLException {
				Query query = session.createQuery(hql);
				for (int i =0;i<params.length;i++){
					applyNamedParameterToQuery(query, params[i], values[i]);
				}
				return query.executeUpdate();
		    }  
		});
		return data;
	}
}
