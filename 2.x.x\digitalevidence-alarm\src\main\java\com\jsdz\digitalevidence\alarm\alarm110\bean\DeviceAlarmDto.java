package com.jsdz.digitalevidence.alarm.alarm110.bean;

public class DeviceAlarmDto {

    /**
     * 执法仪编号
     */
    private String code;

    /**
     * 警情编号
     */
    private String alarmCode;

    /**
     * 认证token
     */
    private String token;

    /**
     * 情指行接警
     * @return
     */
    private int isQZH;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAlarmCode() {
        return alarmCode;
    }

    public void setAlarmCode(String alarmCode) {
        this.alarmCode = alarmCode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public int getIsQZH() {
        return isQZH;
    }

    public void setIsQZH(int isQZH) {
        this.isQZH = isQZH;
    }
}
