package com.jsdz.digitalevidence.alarm.alarm110.dao.impl;

/**
 *
 * @类名: AlarmInfoDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-08-29 19:34:11
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;

import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmInfoDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class AlarmInfoDaoImpl extends GenericEntityDaoHibernateImpl<AlarmInfo,Long> implements AlarmInfoDao{

	//新增
	public void addAlarmInfo(AlarmInfo alarmInfo) {
		this.saveOrUpdate(alarmInfo);
	}

	//删除
	public void deleteAlarmInfo(AlarmInfo alarmInfo) {
		this.delete(alarmInfo);
	}

	//修改
	public void updateAlarmInfo(AlarmInfo alarmInfo) {
		this.merge(alarmInfo);
	}

	//按id查询(游离状态)
	public AlarmInfo findAlarmInfoById(Long id){

		final String  hql = "from AlarmInfo a where a.id = :id";
		final Long oid = id;
		AlarmInfo data = getHibernateTemplate().execute(new HibernateCallback<AlarmInfo>() {
			public AlarmInfo doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmInfo> list = query.list();
				AlarmInfo rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public AlarmInfo locateAlarmInfoById(Long id){

		final String  hql = "from AlarmInfo a where a.id = :id";
		final Long oid = id;
		AlarmInfo data = getHibernateTemplate().execute(new HibernateCallback<AlarmInfo>() {
			public AlarmInfo doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmInfo> list = query.list();
				AlarmInfo rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public AlarmInfo findAlarmInfoByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		AlarmInfo data = getHibernateTemplate().execute(new HibernateCallback<AlarmInfo>() {
		public AlarmInfo doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			@SuppressWarnings("unchecked")
			List<AlarmInfo> list = query.list();
			AlarmInfo rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	// 多个查询（根据其他字段查询）
	public List<AlarmInfo> findAllAlarmInfosByCondition(String queryStr, String[] paramNames, Object[] values) {
		
		List<AlarmInfo> alarmInfos;
		
		if(paramNames !=null && values !=null){
			
			 alarmInfos = getHibernateTemplate().execute(new HibernateCallback<List<AlarmInfo>>() {
					
					public List<AlarmInfo> doInHibernate(Session session) throws HibernateException, SQLException {
						Query query = session.createQuery(queryStr);
						for(int i=0;i<values.length;i++){
							query.setParameter(paramNames[i], values[i]);
						}
						@SuppressWarnings("unchecked")
						List<AlarmInfo> list = query.list();
						if(list !=null && list.size() <= 0){
							session.evict(list);
						}
						return list;
					}
				});
			 
		}else{
			alarmInfos = this.find(queryStr);
		}
		return alarmInfos;
	}
	
	//查询全部
	public List<AlarmInfo> findAllAlarmInfos(){
		return this.find("from AlarmInfo alarmInfo ");
	}
	

	
	
	//列表查询
	public List<AlarmInfo> findAlarmInfosByCondition(String queryStr,String[] paramNames,Object[] values){
		List<AlarmInfo> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<AlarmInfo> findAlarmInfosOnPage(Page<AlarmInfo> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<AlarmInfo>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	// 得到警情信息中的最大id值
	public Long findMaxId(String queryStr) {
		
		final String hql = queryStr;
		Long maxId = getHibernateTemplate().execute(new HibernateCallback<Long>() {
			public Long doInHibernate(Session session) throws HibernateException, SQLException {
				Query query = session.createQuery(hql);
				Object result = query.uniqueResult();
//				System.out.println("======"+result);
				return (Long) result;
			}
		});
		return maxId;
	}
}
