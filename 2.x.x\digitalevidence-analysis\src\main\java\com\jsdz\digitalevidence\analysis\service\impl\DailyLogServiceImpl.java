package com.jsdz.digitalevidence.analysis.service.impl;

/**
 * 
 * @类名: DailyLogServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-31 15:33:22
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.analysis.dao.DailyLogDao;
import com.jsdz.digitalevidence.analysis.model.DailyLog;
import com.jsdz.digitalevidence.analysis.service.DailyLogService;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("DailyLogServiceImpl")
public class DailyLogServiceImpl implements DailyLogService {

	@Autowired
	private DailyLogDao dailyLogDao;

	//新增
	public AjaxResult addDailyLog(DailyLog dailyLog) {
		AjaxResult result = new AjaxResult();
		dailyLogDao.addDailyLog(dailyLog);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateDailyLog(DailyLog dailyLog) {
		AjaxResult result = new AjaxResult();
		dailyLogDao.updateDailyLog(dailyLog);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteDailyLog(DailyLog dailyLog) {
		AjaxResult result = new AjaxResult();
		dailyLogDao.deleteDailyLog(dailyLog);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public DailyLog findDailyLogById(Long id){

		return dailyLogDao.findDailyLogById(id);
	}

	//按 id 查询
	public DailyLog locateDailyLogById(Long id) {
		return dailyLogDao.locateDailyLogById(id);
	}

	//单个查询
	public DailyLog findDailyLogByParam(String queryStr, String[] paramNames, Object[] values) {
		return dailyLogDao.findDailyLogByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<DailyLog> findAllDailyLogs() {
		return dailyLogDao.findAllDailyLogs();
	}

	//列表查询
	public List<DailyLog> findDailyLogsByParam(String queryStr, String[] paramNames, Object[] values) {
		return dailyLogDao.findDailyLogsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<DailyLog> findDailyLogsOnPage(Page<DailyLog> page, String queryStr, String[] paramNames, Object[] values) {
		Page<DailyLog> pos = dailyLogDao.findDailyLogsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
