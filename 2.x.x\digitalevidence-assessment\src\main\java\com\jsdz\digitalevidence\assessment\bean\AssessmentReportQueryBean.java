/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.assessment.model.ReportType;

/**
 * @类名: AssessmentReportQueryBean
 * @说明: 考核报告查询Bean
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentReportQueryBean extends AbstractDTO {
	
	/** 警员Id*/
	private Long policeId;
	/** 年/月*/
	@JsonFormat(pattern="yyyy-MM")
	private Date startDate;
	@JsonFormat(pattern="yyyy-MM")
	private Date endDate;
	/** 报表类型*/
	private ReportType type;
	
	public Long getPoliceId() {
		return policeId;
	}
	public void setPoliceId(Long policeId) {
		this.policeId = policeId;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public ReportType getType() {
		return type;
	}
	public void setType(ReportType type) {
		this.type = type;
	}
	
}
