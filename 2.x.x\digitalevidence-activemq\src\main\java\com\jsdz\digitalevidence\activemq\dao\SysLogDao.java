package com.jsdz.digitalevidence.activemq.dao;

/**
 *
 * @类名: SysLogDao
 * @说明:
 * @author: kenny
 * @Date 2018-01-31 15:36:14
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.activemq.model.SysLog;

public interface SysLogDao extends GenericORMEntityDAO<SysLog,Long> {

	//新增
	public void addSysLog(SysLog sysLog);

	//修改
	public void updateSysLog(SysLog sysLog);

	//删除
	public void deleteSysLog(SysLog sysLog);

	//按id查询,结果是游离状态的数据
	public SysLog findSysLogById(Long id);

	//按id查询
	public SysLog locateSysLogById(Long id);

	//单个查询
	public SysLog findSysLogByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<SysLog> findAllSysLogs();

	//列表查询
	public List<SysLog> findSysLogsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<SysLog> findSysLogsOnPage(Page<SysLog> page,String queryStr,String[] paramNames,Object[] values);

	//执行指定的HQL文件
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);

	//按指定字段分组查询
	public List<String> findGroupBy(String groupByName);
	
	//按条件执行HQL（非查询）
	public Integer execHqlByCondition(final String hql,final String[] params,final Object[] values );

}

