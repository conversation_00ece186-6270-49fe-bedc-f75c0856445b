/**
 * %项目描述%
 * %版本信息%
 */
package TestService;

import java.util.List;

import javax.annotation.Resource;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.bean.MenuTreeBean;
import com.jsdz.reportquery.ReportQueryDao;

/**
 * @类名: SystemMenuReportQueryTest
 * @说明: 系统菜单报表查询测试
 * 
 * <AUTHOR>
 * @Date 2012-6-27 下午5:21:18
 * @修改记录：
 * 
 * @see
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(
		locations={
				"/testApplicationContext-common.xml",
				"/testApplicationContext-dynsql.xml",
				})
public class SystemMenuReportQueryTest {
	
	@Resource(name="rqDao")
	private ReportQueryDao rqDao;
	
	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
	}
	
	@SuppressWarnings({ "unchecked"})
	@Test
	@Transactional(readOnly=true)
	public void testGetShootingItem() throws Exception {
		//
		List<MenuTreeBean> items  = (List<MenuTreeBean>)rqDao.queryNamedSQL("findMenuByParentId", 
					new String[]{}, 
					new Object[]{});
		System.out.println(items);

	}
	
}
