<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:beans="http://cxf.apache.org/configuration/beans"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://cxf.apache.org/configuration/beans http://cxf.apache.org/schemas/configuration/cxf-beans.xsd
		http://www.springframework.org/schema/mvc11 http://www.springframework.org/schema/mvc/spring-mvc-4.1.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-4.1.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.1.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-4.1.xsd">
	
	
<!-- 	<bean id="alarmRelevance" class="com.jsdz.digitalevidence.alarm.alarm110.quartz.AlarmRelevance"></bean> -->
<!-- 	<bean id="alarmRelevanceService" class="com.jsdz.digitalevidence.alarm.alarm110.service.impl.AlarmRelevanceServiceImpl"></bean> -->
	<!-- 创建一个任务的实例  警情和视频自动关联 -->
	<bean id="Alarms" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
		<!-- 目标对象 -->
		<property name="targetObject" ref="AlarmRelevanceServiceImpl"></property>
		<!--  目标方法-->
		<property name="targetMethod" value="seckill"></property>
	</bean>
	
	<!-- 配置触发器   警情-->
	<bean id="cronTrigger1" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<!--  配置触发的实例-->
		<property name="jobDetail"  ref="Alarms"></property>
		<!--  cron表达式  0 30 23 * * ?-->
		<property name="cronExpression" value="0 30 23 * * ?"></property>
	</bean>
	
	<!-- 创建一个任务的实例   案件和视频自动关联的-->
	<bean id="CaseSS" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
		<!-- 目标对象 -->
		<property name="targetObject" ref="CaseRelevanceServiceImpl"></property>
		<!--  目标方法-->
		<property name="targetMethod" value="timingMethod"></property>
	</bean>
	<!-- 配置触发器   案件-->
	<bean id="cronTrigger2" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<!--  配置触发的实例-->
		<property name="jobDetail"  ref="CaseSS"></property>
		<!--  cron表达式 -->
		<property name="cronExpression" value="0 50 23 * * ?"></property>
	</bean>
	
	<!-- 警情和视频自动关联(吉林) -->
	<bean id="AlarmRele" class="org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean">
		<property name="targetObject" ref="AlarmReleServiceImpl"></property>
		<property name="targetMethod" value="executeAla"></property>
	</bean>
	<!-- 配置触发器   自动关联-->
	<bean id="cronTrigger3" class="org.springframework.scheduling.quartz.CronTriggerFactoryBean">
		<!--  配置触发的实例-->
		<property name="jobDetail"  ref="AlarmRele"></property>
		<!--  cron表达式  0 30 23 * * ?-->
		<property name="cronExpression" value="0 0 23 * * ?"></property>
	</bean>
	
	<!-- 使用Scheduler进行任务调度 -->
	<bean class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
		<property name="triggers">
			<list>
				<!-- 传入触发器 -->
				<ref bean="cronTrigger1"/>
				<ref bean="cronTrigger2"/>
				<ref bean="cronTrigger3"/>
			</list>
		</property>
	</bean>
	
	
</beans>
