package com.jsdz.digitalevidence.analysis.dao;

/**
 *
 * @类名: DocDailyDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-31 15:31:04
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.analysis.model.DocDaily;

public interface DocDailyDao extends GenericORMEntityDAO<DocDaily,Long> {

	//新增
	public void addDocDaily(DocDaily docDaily);

	//修改
	public void updateDocDaily(DocDaily docDaily);

	//删除
	public void deleteDocDaily(DocDaily docDaily);

	//按id查询,结果是游离状态的数据
	public DocDaily findDocDailyById(Long id);

	//按id查询
	public DocDaily locateDocDailyById(Long id);

	//单个查询
	public DocDaily findDocDailyByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DocDaily> findAllDocDailys();

	//列表查询
	public List<DocDaily> findDocDailysByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DocDaily> findDocDailysOnPage(Page<DocDaily> page,String queryStr,String[] paramNames,Object[] values);
	
	////执行非查询的Hql语句
	public Integer executeHql(final String StringHql,final String[] paramNames, final Object[] values);

}

