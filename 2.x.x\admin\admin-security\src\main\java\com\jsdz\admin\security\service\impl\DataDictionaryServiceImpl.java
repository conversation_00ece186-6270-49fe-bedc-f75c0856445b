package com.jsdz.admin.security.service.impl;

import java.util.Date;

/**
 * 
 * @类名: DataDictionaryServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-10-31 11:43:33
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
//import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.admin.security.dao.DataDictionaryDao;
import com.jsdz.admin.security.dao.DictionaryValuesDao;
import com.jsdz.admin.security.model.DataDictionary;
import com.jsdz.admin.security.service.DataDictionaryService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("DataDictionaryServiceImpl")
public class DataDictionaryServiceImpl implements DataDictionaryService {

	@Autowired
	private DataDictionaryDao dataDictionaryDao;
	@Autowired
	private DictionaryValuesDao dictionaryValuesDao;

	//新增
	public AjaxResult addDataDictionary(DataDictionary dataDictionary) {
		AjaxResult result = new AjaxResult(0,false,"操作失败",null);
		if (dataDictionary.getName()==null || dataDictionary.getName().equals("")){
			result.setMsg("字典名称必须提供");
			return result;
		}
		String queryStr = " from DataDictionary d where d.name = :name ";
		DataDictionary d = dataDictionaryDao.findDataDictionaryByCondition(queryStr, new String[]{"name"}, new Object[]{dataDictionary.getName()});
		if (d != null){
			result.setMsg("字典名称重复");
			return result;
		}
		dataDictionary.setCreateTime(new Date());
		dataDictionaryDao.addDataDictionary(dataDictionary);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateDataDictionary(DataDictionary dataDictionary) {
		AjaxResult result = new AjaxResult(0,false,"",null);
		if (dataDictionary.getName() == null || "".equals(dataDictionary.getName())){
			result.setMsg("字典名称必须输入。");
			return result;
		}

		if (dataDictionary.getName() != null || "".equals(dataDictionary.getName())){
			if (null!=findDataDictionaryByParam(
					"from DataDictionary d where d.name = :name and d.id != :id",
					new String[]{"name","id"},
					new Object[]{dataDictionary.getName(),dataDictionary.getId()})){
				result.setMsg("字典名称重复。");
				return result;
			}
		}	

		DataDictionary d = locateDataDictionaryById(dataDictionary.getId());
		
		d.setName(dataDictionary.getName());
		d.setNote(dataDictionary.getNote());
		d.setOrder(dataDictionary.getOrder());
		dataDictionaryDao.updateDataDictionary(d);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteDataDictionary(DataDictionary dataDictionary) {
		AjaxResult result = new AjaxResult(0,false,"",null);
		DataDictionary d1 =findDataDictionaryByParam(
				"from DataDictionary d where d.id = :id",
				new String[]{"id"},
				new Object[]{dataDictionary.getId()});
		if (d1 == null){
			result.setSuccess(true);
			result.setMsg("删除成功");
			return result;
		}
		
		if (null!=dictionaryValuesDao.findDictionaryValuesByCondition(
				"from DictionaryValues v where v.dictionary.id=:id ", 
				new String[]{"id"}, new Object[]{dataDictionary.getId()})){
			result.setMsg("请先清空字典内容再删除字典。");
			return result;
		}
		
		
		dataDictionaryDao.deleteDataDictionary(d1);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public DataDictionary findDataDictionaryById(Long id){

		return dataDictionaryDao.findDataDictionaryById(id);
	}

	//按 id 查询
	public DataDictionary locateDataDictionaryById(Long id) {
		return dataDictionaryDao.locateDataDictionaryById(id);
	}

	//单个查询
	public DataDictionary findDataDictionaryByParam(String queryStr, String[] paramNames, Object[] values) {
		return dataDictionaryDao.findDataDictionaryByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<DataDictionary> findAllDataDictionarys() {
		return dataDictionaryDao.findAllDataDictionarys();
	}

	//列表查询
	public List<DataDictionary> findDataDictionarysByParam(String queryStr, String[] paramNames, Object[] values) {
		return dataDictionaryDao.findDataDictionarysByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<DataDictionary> findDataDictionarysOnPage(Page<DataDictionary> page, String queryStr, String[] paramNames, Object[] values) {
		Page<DataDictionary> pos = dataDictionaryDao.findDataDictionarysOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
