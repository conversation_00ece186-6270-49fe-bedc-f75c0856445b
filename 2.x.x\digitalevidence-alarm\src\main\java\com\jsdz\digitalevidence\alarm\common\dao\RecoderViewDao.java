package com.jsdz.digitalevidence.alarm.common.dao;
/**
 * 
 * @类名: RecoderViewDao
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年10月26日上午10:39:09
 * 修改记录：
 *
 * @see
 */
import java.util.List;

import org.springframework.stereotype.Repository;

import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.alarm.common.model.RecoderView;

@Repository
public interface RecoderViewDao extends GenericORMEntityDAO<RecoderView,Long> {

	//新增
	public void addRecoderView(RecoderView recoderView);

	//修改
	public void updateRecoderView(RecoderView recoderView);

	//删除
	public void deleteRecoderView(RecoderView recoderView);

	//按id查询,结果是游离状态的数据
	public RecoderView findRecoderViewById(Long id);

	//按id查询
	public RecoderView locateRecoderViewById(Long id);

	//单个查询
	public RecoderView findRecoderViewByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<RecoderView> findAllRecoderViews();

	//列表查询
	public List<RecoderView> findRecoderViewsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RecoderView> findRecoderViewsOnPage(Page<RecoderView> page,String queryStr,String[] paramNames,Object[] values);

}
