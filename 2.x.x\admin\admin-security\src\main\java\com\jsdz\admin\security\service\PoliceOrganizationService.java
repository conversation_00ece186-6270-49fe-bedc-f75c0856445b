package com.jsdz.admin.security.service;

/**
 * 
 * @类名: PoliceOrganizationService
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-12-19 15:29:11
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.bean.RoleMenuBean;
import com.jsdz.admin.security.model.PoliceOrganization;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface PoliceOrganizationService {

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addPoliceOrganization(PoliceOrganization policeOrganization);

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updatePoliceOrganization(PoliceOrganization policeOrganization);

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deletePoliceOrganization(PoliceOrganization policeOrganization);

	/** 
 	 * 按id查询,结果是游离状态的数据
	 */ 
 	public PoliceOrganization findPoliceOrganizationById(Long id);

	/** 
 	 * 按id查询
	 */ 
 	public PoliceOrganization locatePoliceOrganizationById(Long id);

	/** 
 	 * 单个查询
	 */ 
 	public PoliceOrganization findPoliceOrganizationByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 查询全部
	 */ 
 	public List<PoliceOrganization> findAllPoliceOrganizations();

	/** 
 	 * 列表查询
	 */ 
 	public List<PoliceOrganization> findPoliceOrganizationsByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 分页查询
	 */ 
 	public Page<PoliceOrganization> findPoliceOrganizationsOnPage(Page<PoliceOrganization> page, String queryStr,String[] paramNames,Object[] values);

    /**
     * 查询所有的单位给角色设置
     * @param EmployeesId
     * @return
     * @throws Exception
     */
    public List<RoleMenuBean> findAllOrganitionForEmployessSet(Long employeesId) throws Exception;

    /**
     * 设置警员单位
     * @param oldObj
     * @param newObj
     * @return
     */
    public AjaxResult changeOrgPolice(
			PoliceOrganization oldObj,PoliceOrganization newObj);
    /**
     * 设置警员单位权限
     * @param orgIds
     * @param empId
     * @return
     */
    public AjaxResult setPoliceOrganization(String orgIds,Long empId);
    
}

