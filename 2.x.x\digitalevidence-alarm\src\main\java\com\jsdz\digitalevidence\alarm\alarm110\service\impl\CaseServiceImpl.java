/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.tika.Tika;
import org.apache.tika.metadata.Metadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.admin.security.utils.SessionUtils;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.document.DocumentExtractException;
import com.jsdz.digitalevidence.document.DocumentUtils;
import com.jsdz.digitalevidence.document.NotACaseDocException;
import com.jsdz.digitalevidence.document.NotAnEvidenceDocException;
import com.jsdz.digitalevidence.document.NotAnEvidenceSourceDocException;
import com.jsdz.digitalevidence.document.UnknownContentType;
import com.jsdz.digitalevidence.document.dao.CaseDao;
import com.jsdz.digitalevidence.document.dao.DocumentDao;
import com.jsdz.digitalevidence.document.model.Case;
import com.jsdz.digitalevidence.document.model.ContentTypeDocClassMapper;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.digitalevidence.document.model.DocumentConsts;
import com.jsdz.digitalevidence.document.model.Evidence;
import com.jsdz.digitalevidence.document.model.ImportantLevel;
import com.jsdz.digitalevidence.document.model.namestrategy.NameStrategy;
import com.jsdz.digitalevidence.document.model.pathstrategy.PathStrategy;
import com.jsdz.digitalevidence.document.service.CaseService;
import com.jsdz.digitalevidence.document.service.StrorageService;
import com.jsdz.reportquery.ReportQueryDao;

/**
 * @类名: CaseServiceImpl
 * @说明: 案件/证据应用服务实现
 *
 * <AUTHOR>
 * @Date	 2017年5月25日 下午8:09:25
 * 修改记录：
 *
 * @see 	 
 */
@Service
public class CaseServiceImpl implements CaseService {

	private Logger log = LoggerFactory.getLogger(this.getClass());
	
	/** 存储根目录*/
	@Value("${document.storage.rootpath}")
	private String rootPath;
	@Autowired
	private CaseDao caseDao;
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao drqDao;
	@Autowired
	private DocumentDao docDao;
	// @Autowired
	private Tika tika;
	@Resource(name="npNameStrategy")
	private NameStrategy nameStrategy;
	@Autowired
	private ContentTypeDocClassMapper mapper;
	@Autowired
	private DocumentDao documentDao;
	@Autowired
	private PathStrategy pathStrategy;
	@Autowired
	private StrorageService storageService;
	@Autowired
	private EmployeesService empService;
	
	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.document.service.CaseService#addCase(com.jsdz.digitalevidence.document.model.Case)
	 */
	@Override
	public Case addCase(Case cs) {
		Evidence e = new Evidence();
		cs.setEvidences(e);
		e.setC(cs);
		caseDao.saveOrUpdate(cs);
		return cs;
	}

	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.document.service.CaseService#getCase(java.lang.Long)
	 */
	@Override
	public Case getCase(Long caseId) {
		return caseDao.get(caseId);
	}

	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.document.service.CaseService#searchCase(com.jsdz.digitalevidence.document.model.Case)
	 */
	@Override
	@Transactional(readOnly=true)
	public Page<Case> searchCase(Page<Case> page, String[] paramNames, Object[] values) {
		page =  drqDao.pageQueryNamedSQL(page, "searchCase", paramNames, values);
		return page;
	}
	
	// 获取案件文档
	@Override
	@Transactional(readOnly=true)
	public Page<Document> getCaseDocPage(Page<Document> page, Long caseId) {
		return caseDao.getCaseDocPage(page, caseId);
		
	}
	
	// 获取证据源文档
	@Override
	@Transactional(readOnly=true)
	public Page<Document> getEvidenceSourcePage(Page<Document> page, Long caseId) {
		return caseDao.getEvidenceSourcePage(page, caseId);
	}

	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.document.service.CaseService#addEvidenceSource(java.lang.Long)
	 */
	@Override
	@Transactional
	public void addEvidenceSource(Long caseId, Long docId) throws NotACaseDocException {
		Case c = caseDao.get(caseId);
		Document source = this.docDao.get(docId);
		boolean contianed = c.getDocs().contains(source);
		if(!contianed)
			throw new NotACaseDocException();
		// 是否已经是证据源文档
		if(c.getEvidences().getOrigins().contains(source))
			return;
		c.getEvidences().getOrigins().add(source);
		caseDao.saveOrUpdate(c);

	}

	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.document.service.CaseService#removeEvidenceSource(java.lang.Long)
	 */
	@Override
	@Transactional()
	public void removeEvidenceSource(Long caseId, Long docId) throws NotAnEvidenceSourceDocException {
		Case c = caseDao.get(caseId);
		Document source = this.docDao.get(docId);		
		// 是否已经是证据源文档
		if(!c.getEvidences().getOrigins().contains(source))
			throw new NotAnEvidenceSourceDocException();
		c.getEvidences().getOrigins().remove(source);
		caseDao.saveOrUpdate(c);

	}
	
	/**************证据文档*****************/
	@Override
	@Transactional(readOnly=true)
	public Page<Document> getEvidenceDocPage(Page<Document> page, Long caseId) {
		return caseDao.getEvidenceDocPage(page, caseId);
		
	}

	@Override
	public InputStream downloadEvidenceDoc(Long[] docIds) {
		// TODO Auto-generated method stub
		return null;
	}

	// 上传证据文档
	@Override
	@Transactional(rollbackFor=Exception.class)
	public Document uploadEvidenceDoc(String fileName, Long caseId, File file) throws DocumentExtractException, UnknownContentType, IOException {
		// 采集资料属性
		Map<String, Object> info;
		try {
			info = DocumentUtils.extract(tika, nameStrategy, fileName, file);
		} catch (Exception e) {
			log.error("获取文档属性异常：{}", e.getMessage());
			throw new DocumentExtractException(e);
		}
		// 创建Document实体
		Metadata meta = (Metadata)info.get(DocumentConsts.DOC_PROPERTY_KEY_METADATA);
		String conttype = meta.get(DocumentConsts.DOC_PROPERTY_KEY_CONTENT_TYPE);
		Class<Document> clazz = mapper.getClazzOfContentType(conttype);
		if(clazz==null)
			throw new UnknownContentType(conttype);
		// 获取资料存放路径/更新document uri属性
		Document doc;
		try {
			doc = clazz.newInstance();
		} catch (IllegalAccessException | InstantiationException e) {
			throw new UnknownContentType("实例化资料类型异常：" + e.getMessage());
		}
		// 文件名称
		doc.setName(fileName);
		//
		doc.setImpLevel(ImportantLevel.IMPORTANCE);
		// 媒体属性
		doc.assignMeta(info);
		// 证据提交人
		Operator o = SessionUtils.operatorSession.get();
		Employees e = empService.locateEmployeesById(o.getEmployees().getId());
		doc.setPolice(e);
		//
		documentDao.saveOrUpdate(doc);
		// 加到案件
		Case c = caseDao.get(caseId);
		c.getEvidences().getDocs().add(doc);
		caseDao.saveOrUpdate(c);
		// 保存到存储
		String path = pathStrategy.genPath(doc);
		String fullPath = rootPath + pathStrategy.genPath(doc);
		//
		doc.setUri(path);
		//
		storageService.storeFile(fileName, file, fullPath);
		return doc;
		
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public void deleteEvidenceDoc(Long caseId, Long docId) throws NotAnEvidenceDocException, IOException {
		Document doc = caseDao.getEvidenceDoc(caseId, docId);
		if(doc==null)
			throw new NotAnEvidenceDocException();
		Case c = caseDao.get(caseId);
		c.getEvidences().getDocs().remove(doc);
		documentDao.delete(doc);
		//
		String fullPath = rootPath + doc.getUri() + doc.getName();
		storageService.deleteFile(fullPath);
		
	}

	// 打包
	@Override
	public void sendEvidenceToLegalAffair() {
		// TODO Auto-generated method stub
		
	}

	public CaseDao getCaseDao() {
		return caseDao;
	}

	public void setCaseDao(CaseDao caseDao) {
		this.caseDao = caseDao;
	}

	public DocumentDao getDocDao() {
		return docDao;
	}

	public void setDocDao(DocumentDao docDao) {
		this.docDao = docDao;
	}

	@Override
	public Page<Case> findCaseInfosOnPage(Page<Case> page, String queryStr, String[] paramNames, Object[] values) {
		return caseDao.findCaseInfosOnPage(page,queryStr,paramNames,values);
	}

}
