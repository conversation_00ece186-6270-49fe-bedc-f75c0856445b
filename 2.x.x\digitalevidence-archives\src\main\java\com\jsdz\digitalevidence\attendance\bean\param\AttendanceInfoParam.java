package com.jsdz.digitalevidence.attendance.bean.param;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceInfoBean;

public class AttendanceInfoParam {

    Page<AttendanceInfoBean> page;

    private String orgPath;

    private String sortName;

    private String sortOrder;
    //是否包含下级 （0不，1包含）
    private String includeSub;
    private String taskId;


    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public AttendanceInfoParam(){
        page = new Page<AttendanceInfoBean>();
        page.setOffset(0);
        page.setPageSize(20);
    }

    public Page<AttendanceInfoBean> getPage() {
        return page;
    }

    public void setPage(Page<AttendanceInfoBean> page) {
        this.page = page;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public String getSortName() {
        return sortName;
    }

    public void setSortName(String sortName) {
        this.sortName = sortName;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(String includeSub) {
        this.includeSub = includeSub;
    }
}
