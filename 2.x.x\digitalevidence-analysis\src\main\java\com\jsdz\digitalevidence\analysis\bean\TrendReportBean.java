package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: TrendReportBean
 * @说明: 趋势报表：包括趋势研判，趋势对比 封装 类
 *
 * @author: kenny
 * @Date	2018年3月28日下午2:23:40
 * 修改记录：
 *
 * @see
 */
import java.io.Serializable;

public class TrendReportBean implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -8750595331530738790L;
	
	private Long id;
	private String ObjCode;//项目，可能是单位，警员，日期等
	private Long fileCount;//文件数量
	private Long vedioCount;//视频数量
	private Long vedioQualify;//视频质量
	private Double sumDuration;//视频时长(秒)
	private Double sumHdDuration;//高清时长(秒)
	private Double alarmRate;//警情关联率
	private Double caseRate;//案件关联率
	private String sumDurationStr;// 视频时长    小时分秒
	private String sumHdDurationStr;//视频时长 小时分秒
	
	public void assign(TrendReport src){
		this.setId(src.getId());
		this.setObjCode(src.getObjCode());
		this.setFileCount(src.getFileCount());
		this.setVedioCount(src.getVedioCount());
		this.setVedioQualify(src.getVedioQualify());
		this.setSumDuration(rounded(src.getSumDuration(),0));
		this.setSumHdDuration(rounded(src.getSumHdDuration(),0));
		this.setAlarmRate(rounded(src.getAlarmRate(),2));
		this.setCaseRate(rounded(src.getCaseRate(),2));
		this.setSumDurationStr(this.secondToHmsStr(src.getSumDuration()));
		this.setSumHdDurationStr(this.secondToHmsStr(src.getSumHdDuration()));
	}
	
	private String secondToHmsStr(Double value){
		String result="";
		if (value == null){
			return null;
		}
		
		Long lValue = Math.round(value);
		long hour =0L;
		long min =0L;
		long second = 0L;
		if (lValue >= 3600){
			hour = lValue/3600;
			lValue = lValue % 3600;
		}
		
		if (lValue >= 60L){
			min = lValue/60;
			second = lValue % 60;
		}else{
			second = lValue;
		}
		
		if (hour > 0){
			result = result+String.valueOf(hour) + "小时";
		}
		if (min > 0){
			result = result+String.valueOf(min) + "分";
		}
		if (second > 0){
			result = result+String.valueOf(second) + "秒";
		}
		return result;
	}
	
	/*
	 * 四舍五入
	 * bitNum  小数位
	 */
	private Double rounded(Double value,int bitNum){
		double result = 0;
		double stepval = 1.0;
		for (int i=0;i<bitNum;i++){
			stepval = stepval * 10;
		}
		if (value != null){
			result = Math.round(value*stepval)/stepval;
		}
		return result;
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getFileCount() {
		return fileCount;
	}
	public void setFileCount(Long fileCount) {
		this.fileCount = fileCount;
	}
	public Long getVedioCount() {
		return vedioCount;
	}
	public void setVedioCount(Long vedioCount) {
		this.vedioCount = vedioCount;
	}
	public Long getVedioQualify() {
		return vedioQualify;
	}
	public void setVedioQualify(Long vedioQualify) {
		this.vedioQualify = vedioQualify;
	}
	public Double getSumDuration() {
		return sumDuration;
	}
	public void setSumDuration(Double sumDuration) {
		this.sumDuration = sumDuration;
	}
	public Double getSumHdDuration() {
		return sumHdDuration;
	}
	public void setSumHdDuration(Double sumHdDuration) {
		this.sumHdDuration = sumHdDuration;
	}
	public Double getAlarmRate() {
		return alarmRate;
	}
	public void setAlarmRate(Double alarmRate) {
		this.alarmRate = alarmRate;
	}
	public Double getCaseRate() {
		return caseRate;
	}
	public void setCaseRate(Double caseRate) {
		this.caseRate = caseRate;
	}
	public String getSumDurationStr() {
		return sumDurationStr;
	}
	public void setSumDurationStr(String sumDurationStr) {
		this.sumDurationStr = sumDurationStr;
	}
	public String getSumHdDurationStr() {
		return sumHdDurationStr;
	}
	public void setSumHdDurationStr(String sumHdDurationStr) {
		this.sumHdDurationStr = sumHdDurationStr;
	}

	public String getObjCode() {
		return ObjCode;
	}

	public void setObjCode(String objCode) {
		ObjCode = objCode;
	}
	

	
}
