package com.jsdz.digitalevidence.assessment.model.alert.rule;

import java.util.ArrayList;
import java.util.List;

import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.ruleengine.RuleState;
import com.jsdz.ruleengine.annotation.Given;
import com.jsdz.ruleengine.annotation.Param;
import com.jsdz.ruleengine.annotation.Result;
import com.jsdz.ruleengine.annotation.Rule;
import com.jsdz.ruleengine.annotation.Then;
import com.jsdz.ruleengine.annotation.When;

/**
 * @类名: ShootingAssessmentTotalDocsRule
 * @说明: 拍摄考核上传文件总数规则
 *
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 修改记录：
 *
 * @see
 */
@Rule(name="uploadDocRule", description="上传文档规则")
public class ShootingAssessmentTotalDocsRule {

	/** 上传文档总数阀值*/
	@Param(name="totalUploadDoc", description="上传文档总数")
	private int threshold;

	@Given
	private ShootingAssessmentSummary sum;
	
	/** */
	@Result
	private List<AssessmentAlertItem<?>> _result;

	@When
	public boolean when() {
		Integer t = sum.getTotalDoc();
		if(t<threshold)
			return true;
		return false;
	}

	@Then
	public RuleState then() {
		AssessmentAlertItem<Integer> item = new AssessmentAlertItem<Integer>();
		item.setItem("上传文件总数");
		item.setV(sum.getTotalDoc());
		item.setRef(threshold);
		if(_result==null)
			_result = new ArrayList<AssessmentAlertItem<?>>();
		_result.add(item);
		// 总数不足，跳出
		return RuleState.BREAK;
	}

}
