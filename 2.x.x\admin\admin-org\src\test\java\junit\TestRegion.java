package junit;


import java.io.UnsupportedEncodingException;
import java.util.Date;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.dao.RegionDao;
import com.jsdz.admin.org.dao.RegionTypeDao;
import com.jsdz.admin.org.model.Region;
import com.jsdz.admin.org.model.RegionType;


/**
 * 
 * @类名: TestRegion
 * @说明: 测试region
 *
 * @author: kenny
 * @Date	2017年4月24日下午7:38:46
 * 修改记录：
 *
 * @see
 */
@RunWith(SpringJUnit4ClassRunner.class)
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations={"classpath:/testApplicationContext-common.xml"})
public class TestRegion {
	@Autowired
	public RegionDao regionDao;
	@Autowired
	public RegionTypeDao regionTypeDao;	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}
	
	
	//@Test
	@Transactional
	public void testAddRegionType() throws UnsupportedEncodingException {
    	RegionType rt1 = new RegionType("省级",1);
    	RegionType rt2 = new RegionType("市级",2);
    	RegionType rt3 = new RegionType("县/区级",3);
    	RegionType rt4 = new RegionType("镇/街道办/乡级",4);
        
        regionTypeDao.saveOrUpdate(rt1);
        regionTypeDao.saveOrUpdate(rt2);
        regionTypeDao.saveOrUpdate(rt3);
        regionTypeDao.saveOrUpdate(rt4);
        //System.out.println(rt.getRegionTypeName());
	}
	
	
	

	//@Test
	@Transactional
	public void testAddRegion() throws UnsupportedEncodingException {
    	Region o = new Region();  
        o.setRegionName("广东省");  
        o.setCreateTime(new Date());
        Region o1 = new Region();  
        o1.setRegionName("广州市");  
        Region o2 = new Region();  
        o2.setRegionName("深圳市");  
        Region o3 = new Region();  
        o3.setRegionName("东莞市");  
        Region o11 = new Region();  
        o11.setRegionName("白云区");  
        Region o12 = new Region();  
        o12.setRegionName("黄浦区");  
        Region o21 = new Region();  
        o21.setRegionName("福田区"); 
        Region o22 = new Region();
        String S = "龙华区";
        String newStr = new String(S.getBytes(),"UTF-8");
        o22.setRegionName(newStr); 
        
         
        o.getSubRegion().add(o1);  
        o.getSubRegion().add(o2);  
        o.getSubRegion().add(o3);  
        o1.getSubRegion().add(o11);  
        o1.getSubRegion().add(o12);  
        o2.getSubRegion().add(o21);
        o2.getSubRegion().add(o22);
        
          
        o1.setParentRegion(o);  
        o2.setParentRegion(o);  
        o3.setParentRegion(o);  
        o11.setParentRegion(o1);  
        o12.setParentRegion(o1);  
        o21.setParentRegion(o2);
        o22.setParentRegion(o2);
        
         
        regionDao.saveOrUpdate(o);
        regionDao.saveOrUpdate(o1);
        regionDao.saveOrUpdate(o2);
        regionDao.saveOrUpdate(o3);
        regionDao.saveOrUpdate(o11);
        regionDao.saveOrUpdate(o12);
        regionDao.saveOrUpdate(o21);
        regionDao.saveOrUpdate(o22);
		
	}
	//@Test 
    @Transactional
    public void testFindById() {
		//Long id = (long) 1;
		Region o = regionDao.locateRegionById((long) 1);
		System.out.println(o.toString());  
		//Region o = new Region();
	}
	
    @Test 
    @Transactional
    public void testLoad() {  
/*    	List<Region> os = regionDao. findAll();
    	
    	if (os != null){
    		for(Region o : os){
    			print(o,0);
    			//System.out.println(o.toString());  
    		}
    	}else
    		System.out.println("Result is null");  */
    }  

    private void print(Region o, int level) {  
        String preStr = "";  
        for(int i=0;i<level;i++){  
            preStr +="----";  
        } 
        System.out.println(preStr+o.getRegionName());  

        
        for(Region org : o.getSubRegion()){  
            print(org,level+1);  
        }  
        
    } 
     	

}  
