/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.model;

import java.util.Date;
import java.util.List;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.AbstractDTO;

/**
 * @类名: BusinessToken
 * @说明: 业务令牌
 *
 * <AUTHOR>
 * @Date	 2017年4月18日 下午6:03:12
 * 修改记录：
 *
 * @see 	 
 */
public class BusinessToken extends AbstractDTO {

	/** Id*/
	private Long tokenId;
	/** 拥有者*/
	private Employees owner;
	/** 流程Id*/
	private String procInstId;
	/** 创建日期*/
	private Date createTime;
	/** 生效日期*/
	private Date startDate;
	/** 失效日期*/
	private Date endDate;
	/** 最近使用日期*/
	private Date updateTime;
	/** 使用次数*/
	private int times = 3;
	/** 业务*/
	private ApproveBusiness business;
	/** 业务参数*/
	private String params;
	/** 业务对象*/
	private List<BusinessObject> bos;
	
	public Long getTokenId() {
		return tokenId;
	}
	public void setTokenId(Long tokenId) {
		this.tokenId = tokenId;
	}
	public ApproveBusiness getBusiness() {
		return business;
	}
	public void setBusiness(ApproveBusiness business) {
		this.business = business;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public int getTimes() {
		return times;
	}
	public void setTimes(int times) {
		this.times = times;
	}
	public Employees getOwner() {
		return owner;
	}
	public void setOwner(Employees owner) {
		this.owner = owner;
	}
	public String getProcInstId() {
		return procInstId;
	}
	public void setProcInstId(String procInstId) {
		this.procInstId = procInstId;
	}
	public List<BusinessObject> getBos() {
		return bos;
	}
	public void setBos(List<BusinessObject> bos) {
		this.bos = bos;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public String getParams() {
		return params;
	}
	public void setParams(String params) {
		this.params = params;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
}
