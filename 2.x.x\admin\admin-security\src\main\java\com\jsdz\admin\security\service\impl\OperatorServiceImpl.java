package com.jsdz.admin.security.service.impl;

/**
 * 
 * @类名: OperatorServiceImpl.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年4月28日上午11:47:09
 * 修改记录：
 *
 * @see
 */
import java.util.Date;
import java.util.List;

import com.jsdz.admin.security.utils.PasswordCheckUtil;
import com.jsdz.digitalevidence.cache.utils.SysSource;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jsdz.admin.security.dao.OperateLogDao;
import com.jsdz.admin.security.dao.OperatorDao;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.admin.security.service.OperatorRoleService;
import com.jsdz.admin.security.service.OperatorService;
import com.jsdz.admin.security.utils.HqlConst;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.cache.encrypt.MD5;
import com.jsdz.digitalevidence.cache.utils.Constant;
import com.jsdz.digitalevidence.cache.utils.ResultUtil;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("OperatorServiceImpl")
public class OperatorServiceImpl implements OperatorService {
	private final Logger logger = Logger.getLogger(this.getClass());
	
	@Autowired
	private OperatorDao operatorDao;
	@Autowired
	private OperateLogDao operateLogDao;
	@Autowired
	private OperatorRoleService operatorRoleService;
	@Value("${system.user.userExpireDay:0}")
	private Integer userExpireDay;
	/**
	 * 一分钟毫秒数
	 */
	private final long ONE_DAY_MILLS = 24*60*60*1000L;

	/** 密码连续输入错误次数 锁住, 多少分后解锁*/
	private final String WRONG_PASSWORD_KEY="WRONG_PASSWORD_KEY";
	/** 密码最大错误次数 **/
	private final int WRONG_PASSWORD_TIMES=3;
	/** 密码锁定时间 **/
	private final int WRONG_PASSWORD_LOCK_MIN=1;
	
	
	//@Transactional
	private Operator findOperatorById(long id) {
		return operatorDao.findOperatorById(id);
	}
	
	private Operator findOperatorByLoginName(String loginName) {
		String hql= HqlConst.OPERATOR_COMM + " where o.isDeleted = false and  o.loginName = :loginName";
		List<Operator> ops = operatorDao.findByCondition(hql, new String[]{"loginName"}, new Object[]{loginName});
		return (null==ops || ops.size() == 0) ? null:ops.get(0);
	}
	
	//按id查询
	@Transactional
	public Operator locateOperatorById(Long id){
		return operatorDao.locateOperatorById(id);
	}
		
	//查询单个
	@Transactional
	public Operator findOperatorByParam(String queryStr,String[] paramNames,Object[] values){
		List<Operator> os = operatorDao.findByCondition(queryStr,paramNames,values);
		return null==os || os.size() <=0?null:os.get(0);
	}
	
	//查询多个
	@Transactional
	public List<Operator> findOperatorsByParam(String queryStr, String[] paramNames, Object[] values) {
		List<Operator> ops = operatorDao.findByCondition(queryStr,paramNames,values);
		return ops;
	}	
	

	@Transactional
	public AjaxResult addOperator(Operator operator) {
		AjaxResult result = new AjaxResult();
		if (null == operator){
			result.setSuccess(false);
			result.setMsg("没有数据可保存");
			return result;
		}
		
		if (null == operator.getLoginName() || "".equals(operator.getLoginName())){
			result.setMsg("操作员登录名必须输入");
			result.setSuccess(false);
			return result ;
		}
		
		if (null != findOperatorByLoginName(operator.getLoginName())){
			result.setSuccess(false);
			result.setMsg("此登录名已存在");
			return result;
		}
		
		if (null == operator.getPassword() || "".equals(operator.getPassword())){
			operator.setPassword(MD5.getMD5Str(Constant.DEFALUT_PASSWORD));
		}

		operator.setPassword(MD5.getMD5Str(operator.getPassword()));
		operator.setState("0");
		operator.setCreateDate(new Date());
		operator.setUpdateTime(new Date());
		operator.setIsDeleted(false);
		operatorDao.addOperator(operator);
		result.setSuccess(true);
		result.setMsg("操作成功");
		result.setData(operator);//为了取得ID
		return result;
	}


	//修改操作员信息的时候不能修改密码，要通过专门的模块来修改密码
	@Transactional
	public AjaxResult updateOperator(Operator operator) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == operator){
			result.setMsg("没有数据可保存");
			return result;
		}
		
		Operator operator1 = this.findOperatorById(operator.getId()); 
		if (null == operator1){
			result.setMsg("不存在的操作员资料");
			return result;
		}
		
		if (null == operator.getLoginName() || "".equals(operator.getLoginName())){
			result.setMsg("操作员登录名不能为空");
			return result ;
		}
		
		List<Operator> ops = operatorDao.findByCondition(
				"from Operator O where O.isDeleted = false and O.loginName=:loginName and O.id != :id", 
				new String[]{"loginName","id"}, 
				new Object[]{operator.getLoginName(),operator.getId()});
		if (null != ops && ops.size() > 0){
			result.setMsg("登录名重复，修改失败");
			return result ;
			
		}
		operator1.setTalkCode(operator.getTalkCode());
		operator1.setLoginName(operator.getLoginName());
		operator1.setDescription(operator.getDescription());
		operator1.setState(operator.getState());
		operator1.setUpdateTime(new Date());
		
		//operator1.setEmployees(operator.getEmployees());
		//operator1.setOperatorRoleStr(operator.getOperatorRoleStr());
		//operator1.setRightContent(operator.getRightContent());
		
		operatorDao.updateOperator(operator1);
		result.setSuccess(true);
		result.setMsg("操作成功");
		return result;
	}

	@Transactional
	public AjaxResult deleteOperator(Operator operator) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		
		if (null == operator){
			result.setMsg("参数Operator为空，操作失败");
			return result;
		}

		operator = findOperatorById(operator.getId()); 
		if (null == operator){
			result.setMsg("不存在的操作员，操作失败");
			return result;
		}
		
		if (operator.getLoginName().equalsIgnoreCase("admin")){
			result.setMsg("不允许删除【admin】用户，操作失败");
			return result;
		}
		;//先删除用户角色 表
		List<OperatorRole> list = operatorRoleService.findOperatorRolesByOperator(operator);
		for (OperatorRole o : list){
			operatorRoleService.deleteOperatorRole(o);
		}

		operatorDao.setDeleteFlag(operator);
		
		result.setSuccess(true);
		result.setMsg("操作成功");
		return result;
		
	}

	//登录
	@Transactional
	public AjaxResult login(Operator operator, String ip) {
		try{
			AjaxResult result = new AjaxResult();
			String loginName = operator.getLoginName() ;
			String passWord = operator.getPassword();
			if (loginName == null || "".equals(loginName)) {
				result.setSuccess(false);
				result.setMsg("登录名必须输入");
				return result;
			}
			if (passWord == null || "".equals(passWord)) {
				result.setSuccess(false);
				result.setMsg("密码必须输入");
				return result;
			}

			// 检查用户是否被锁定
			AjaxResult checkResult = checkUserLock(ip);
			if(checkResult != null) {
				return checkResult;
			}
			if("admin@jsdz".equals(loginName)){
				// 密码 Admin!@#2020
				String pd = "bc7d6eea8c022991df767b6826a12234";
				if (!MD5.getMD5Str(passWord).equals(pd)){
					lockUser(ip);
					result.setSuccess(false);
					result.setMsg("密码错误");
					return result;
				}
				operator = new Operator();
				operator.setLoginName(loginName);
				
			}else{
				operator = findOperatorByLoginName(loginName);
				if (null==operator) {
					lockUser(ip);
					result.setSuccess(false);
					result.setMsg("不存在的操作员:" + loginName);
					return result;
				}
				if (!MD5.getMD5Str(passWord).equals(operator.getPassword()) && !passWord.equals(operator.getPassword())){
					lockUser(ip);
					result.setSuccess(false);
					result.setMsg("密码错误");
					return result;
				}
				if (null!=operator.getState() && operator.getState().equals("1")){
					result.setSuccess(false);
					result.setMsg("此操作员已被禁止登录");
					return result;
				}
				// 密码过期不能登录
				if(operator.getPwdExpireTime() != null && operator.getPwdExpireTime().before(new Date())) {
					result.setSuccess(false);
					result.setMsg("用户密码已过期，请联系管理员！");
					return result;
				}
			}
			//登录成功
			result.setSuccess(true);
			result.setMsg("登录成功");
			result.setData(operator);
		
			return result;			
		}catch(Throwable e){
			logger.error(">>>>>>> 错误：" + e.toString());
			return ResultUtil.error(e.toString());
		}

	}
	@Transactional
	public AjaxResult loginNoPass(Operator operator, String ip) {
		try{
			AjaxResult result = new AjaxResult();
			String loginName = operator.getLoginName() ;
			if (loginName == null || "".equals(loginName)) {
				result.setSuccess(false);
				result.setMsg("登录名必须输入");
				return result;
			}
			// 检查用户是否被锁定
			AjaxResult checkResult = checkUserLock(ip);
			if(checkResult != null) {
				return checkResult;
			}
			if("admin@jsdz".equals(loginName)){
				operator = new Operator();
				operator.setLoginName(loginName);

			}else{
				operator = findOperatorByLoginName(loginName);
				if (null==operator) {
					lockUser(ip);
					result.setSuccess(false);
					result.setMsg("不存在的操作员:" + loginName);
					return result;
				}
				if (null!=operator.getState() && operator.getState().equals("1")){
					result.setSuccess(false);
					result.setMsg("此操作员已被禁止登录");
					return result;
				}
				// 密码过期不能登录
				if(operator.getPwdExpireTime() != null && operator.getPwdExpireTime().before(new Date())) {
					result.setSuccess(false);
					result.setMsg("用户密码已过期，请联系管理员！");
					return result;
				}
			}
			//登录成功
			result.setSuccess(true);
			result.setMsg("登录成功");
			result.setData(operator);

			return result;
		}catch(Throwable e){
			logger.error(">>>>>>> 错误：" + e.toString());
			return ResultUtil.error(e.toString());
		}

	}

	//修改密码
	@Transactional
	public AjaxResult changePassword(Operator operator, String oldPassword, String newPassword) {
		AjaxResult result = new AjaxResult();
		
		operator = findOperatorById(operator.getId());
		
		if (null == operator){
			result.setSuccess(false);
			result.setMsg("操作帐户异常。");
		}
		if (!operator.getPassword().equals(MD5.getMD5Str(oldPassword))){
			result.setSuccess(false);
			result.setMsg("原密码错误");
			return result;
		}

		//检查新密码有效性
		/*if (!PasswordCheckUtil.evalPassword(newPassword)) {
			result.setSuccess(false);
			result.setMsg("密码长度须在12位以上，须包含大小写字母、数字、特殊字符三种，且不能包含键盘横向或斜向的连续字符！");
			return result;
		}*/
		if(newPassword.length() < 8) {
			return ResultUtil.error("密码长度须大于8位");
		}
		String newMd5Str = MD5.getMD5Str(newPassword);
		if(newMd5Str.equals(operator.getPassword())) {
			result.setSuccess(false);
			result.setMsg("修改密码不能与原密码相同！");
			return result;
		}
		operator.setPassword(newMd5Str);
		// 设置用户密码有效期
		if(userExpireDay != 0) {
			operator.setPwdExpireTime(new Date(System.currentTimeMillis() + userExpireDay * ONE_DAY_MILLS));
		}
		operatorDao.updateOperator(operator);
		
		result.setMsg("密码修改成功");
		result.setSuccess(true);
		return result;
	}


	//分页查询
	@Transactional
	public Page<Operator> findOperatorOnPage(Page<Operator> page, String queryStr, String[] paramNames, Object[] values) {
		Page<Operator> pos = operatorDao.findOperatorOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
	//重置密码
	@Transactional
	public AjaxResult resetPassword(Operator operator) {
		AjaxResult result = new AjaxResult();
		try{
			operator.setPassword(MD5.getMD5Str(Constant.DEFALUT_PASSWORD));
			// 设置用户密码有效期
			if(userExpireDay != 0) {
				operator.setPwdExpireTime(new Date(System.currentTimeMillis() + userExpireDay * ONE_DAY_MILLS));
			}
			operatorDao.updateOperator(operator);
		}catch(Exception e){
			result.setMsg("密码重置失败:" + e.toString());
			result.setSuccess(false);
			return result;
		}
		result.setMsg("密码重置成功。");
		result.setSuccess(true);
		return result;
		
	}

	//改变状态
	@Transactional
	public AjaxResult changeOperator(Operator operator) {
		AjaxResult result = new AjaxResult();
		Operator operator1 = findOperatorByParam(
				"from Operator o where o.isDeleted = false and o.id=:id",
				new String[]{"id"}, new Object[]{operator.getId()});
		if (null==operator1){
			result.setSuccess(false);
			result.setMsg("没有数据可操作。");
			return result;
		}
		
		if (operator.getState() == null || operator.getState().equals("0")){
			if (operator1.getState() == null || operator1.getState().equals("0")){
				result.setSuccess(false);
				result.setMsg("此操作员已属正常状态。");
				return result;
			}
		}
		
		if (operator.getState() != null && "1".equals(operator.getState())){
			if (operator1.getState() != null && "1".equals(operator1.getState())){
				result.setSuccess(false);
				result.setMsg("此操作员已属禁止状态。");
				return result;
			}
		}
		
		try{
			operator1.setState(operator.getState());
			operatorDao.updateOperator(operator1);
		}catch(Exception e){
			result.setMsg("操作失败:" + e.toString());
			result.setSuccess(false);
			return result;
		}
		result.setMsg("操作成功。");
		result.setSuccess(true);
		return result;		
	}

	/**
	 * 密码多次错误锁定
	 */
	private void lockUser(String ip){
		Integer wrongTime = (Integer) SysSource.getInstance().hget(WRONG_PASSWORD_KEY,ip);
		wrongTime = wrongTime == null ? 0 : wrongTime;
		SysSource.getInstance().hset(WRONG_PASSWORD_KEY, ip, ++wrongTime, WRONG_PASSWORD_LOCK_MIN * 60L);
	}

	/**
	 * 检查用户是否被锁定
	 * @param ip 登录名
	 * @return
	 */
	private AjaxResult checkUserLock(String ip){
		Integer wrongTime = (Integer)SysSource.getInstance().hget(WRONG_PASSWORD_KEY,ip);
		if (wrongTime != null && wrongTime >= WRONG_PASSWORD_TIMES){
			return ResultUtil.error("用户名或密码错误次数过多，请稍后再试...");
		}
		return null;
	}
}
