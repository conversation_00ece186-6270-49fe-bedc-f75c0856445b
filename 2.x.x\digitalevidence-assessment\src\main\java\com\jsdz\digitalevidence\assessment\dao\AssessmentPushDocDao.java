/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.dao;

import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.assessment.model.push.AssessmentPushDoc;

/**
 * @类名: AssessmentPushDocDao
 * @说明: 推送考核视频Dao
 *
 * <AUTHOR>
 * @Date 2017年8月7日 下午5:51:34 修改记录：
 *
 * @see
 */
public interface AssessmentPushDocDao extends GenericORMEntityDAO<AssessmentPushDoc, Long> {

	// 更新推送文档考核状态
	public int updatePushDocStatus(Long pushingDocId, boolean isAssessed);

}
