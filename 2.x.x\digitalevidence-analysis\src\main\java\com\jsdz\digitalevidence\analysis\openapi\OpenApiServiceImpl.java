package com.jsdz.digitalevidence.analysis.openapi;

import com.jsdz.digitalevidence.analysis.bean.DocDailyDto;
import com.jsdz.reportquery.ReportQueryDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @类名: OpenApiServiceImpl
 * @说明:
 * @author: kenny
 * @Date 2017年6月8日下午1:53:13 修改记录：
 * @see
 */

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("OpenApiServiceImpl")
public class OpenApiServiceImpl implements OpenApiService{
	
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<DocDailyDto> reportQueryDao;
	

	//查询接口: 执法记录仪日志信息查询
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public List<CxdsjLogResultBean> findCXDSJLOG(String[] paramNames,Object[] values) {
		List<Object> datas= reportQueryDao.QueryNamedAllDataSQL("CXDSJLOG",paramNames,values);
		
		List<CxdsjLogResultBean> result = new ArrayList<CxdsjLogResultBean>();
		for (Object o : datas){
			result.add((CxdsjLogResultBean)o);
		}
		return result;
	}
	//查询接口: 执法数据采集设备基本信息查询
	@Override
	public List<StationInfoResultBean> findQUERYSTATIONINFO(String[] paramNames,Object[] values) {
		List<Object> datas= reportQueryDao.QueryNamedAllDataSQL("QUERYSTATIONINFO",paramNames,values);
		
		List<StationInfoResultBean> result = new ArrayList<StationInfoResultBean>();
		for (Object o : datas){
			result.add((StationInfoResultBean)o);
		}
		return result;
	}
	
	//查询接口: 执法数据采集设备日志信息查询
	@Override
	public List<StationLogResultBean> findQUERYSTATIONLOG(String[] paramNames,Object[] values){
		List<Object> datas= reportQueryDao.QueryNamedAllDataSQL("QUERYSTATIONLOG",paramNames,values);
		
		List<StationLogResultBean> result = new ArrayList<StationLogResultBean>();
		for (Object o : datas){
			result.add((StationLogResultBean)o);
		}
		return result;
	}

}
