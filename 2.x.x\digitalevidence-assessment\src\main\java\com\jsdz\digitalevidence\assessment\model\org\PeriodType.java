package com.jsdz.digitalevidence.assessment.model.org;
/**
 * 
 * @类名: PeriodType
 * @说明: 周期类型
 *
 * @author: kenny
 * @Date	2018年1月11日下午4:17:30
 * 修改记录：
 *
 * @see
 */
public enum PeriodType {
	/*小时，天，周，月，年*/
	HOUR(1, "小时"), DAY(2, "天"), WEE<PERSON>(3,"周"),MONTH(4, "月"),YEAR(5,"年");
	
	public int index;
	public String name;
	
	private PeriodType(int index, String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	

}
