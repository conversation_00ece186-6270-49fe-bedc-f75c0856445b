/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.model.push;

import java.util.Date;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.document.model.Document;

/**
 * @类名: AssessmentPushDoc
 * @说明: 推送视频
 *        
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午6:15:56
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentPushDoc extends AbstractDTO {
	
	/** */
	private Long pushDocId;
	/** 所属警员*/
	private Employees police; 
	/** 推送文档*/
	private Document doc;
	/** 考核员*/
	private Employees assessor;
	/** 是否已考核*/
	private boolean hasAssessed;
	/** 推送时间*/
	private Date createTime;

	public Employees getPolice() {
		return police;
	}

	public void setPolice(Employees police) {
		this.police = police;
	}

	public Employees getAssessor() {
		return assessor;
	}

	public void setAssessor(Employees assessor) {
		this.assessor = assessor;
	}

	public Document getDoc() {
		return doc;
	}

	public void setDoc(Document doc) {
		this.doc = doc;
	}

	public Long getPushDocId() {
		return pushDocId;
	}

	public void setPushDocId(Long pushDocId) {
		this.pushDocId = pushDocId;
	}
	
	public boolean getHasAssessed() {
		return hasAssessed;
	}
	
	public void setHasAssessed(boolean hasAssessed) {
		this.hasAssessed = hasAssessed;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	
}
