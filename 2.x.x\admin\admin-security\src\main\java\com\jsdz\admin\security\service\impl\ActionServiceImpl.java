package com.jsdz.admin.security.service.impl;

/**
 * 
 * @类名: ActionServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-03 19:35:10
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.admin.security.dao.ActionDao;
import com.jsdz.admin.security.model.Action;
import com.jsdz.admin.security.service.ActionService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.cache.encrypt.MD5;

@Service("ActionServiceImpl")
public class ActionServiceImpl implements ActionService {

	@Autowired
	private ActionDao actionDao;

	//新增
	public AjaxResult addAction(Action action) {
		AjaxResult result = new AjaxResult();
		if (null == action){
			result.setSuccess(false);
			result.setMsg("没有数据可保存");
			return result;
		}
		
		if (null == action.getName() || "".equals(action.getName())){
			result.setMsg("任务描述必须输入");
			result.setSuccess(false);
			return result ;
		}
		if (null == action.getTag() || "".equals(action.getTag())){
			result.setMsg("任务标识必须输入");
			result.setSuccess(false);
			return result ;
		}
		
		if (null != actionDao.findActionByCondition("from Action a where a.tag = :tag",
				new String[]{"tag"},new Object[]{action.getTag()})){
			result.setSuccess(false);
			result.setMsg("此任务标识已存在");
			return result;
		}
		
		actionDao.addAction(action);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateAction(Action action) {
		AjaxResult result = new AjaxResult();
		if (null == action){
			result.setSuccess(false);
			result.setMsg("没有数据可保存");
			return result;
		}
		
		if (null == action.getName() || "".equals(action.getName())){
			result.setMsg("任务描述必须输入");
			result.setSuccess(false);
			return result ;
		}
		if (null == action.getTag() || "".equals(action.getTag())){
			result.setMsg("任务标识必须输入");
			result.setSuccess(false);
			return result ;
		}
		
		Action action1 = actionDao.findActionByCondition("from Action a where a.id=:id",
				new String[]{"id"},new Object[]{action.getId()});
		if (action1 == null){
			result.setMsg("不存在的任务");
			result.setSuccess(false);
			return result ;
		}
		
		
		if (!action.getTag().equals(action1.getTag())){
			result.setMsg("任务标识不能修改");
			result.setSuccess(false);
			return result ;
		}
		actionDao.updateAction(action);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteAction(Action action) {
		AjaxResult result = new AjaxResult();
		if (null == action){
			result.setSuccess(false);
			result.setMsg("没有数据可删除");
			return result;
		}
		
		actionDao.deleteAction(action);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//单个查询
	public Action findActionByParam(String queryStr, String[] paramNames, Object[] values) {
		return actionDao.findActionByCondition(queryStr,paramNames,values);
	}
	
	//查询所有
	public List<Action> findALlActions(String queryStr){
		return actionDao.findAll();
	}

	//列表查询
	public List<Action> findActionsByParam(String queryStr, String[] paramNames, Object[] values) {
		return actionDao.findActionsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<Action> findActionsOnPage(Page<Action> page, String queryStr, String[] paramNames, Object[] values) {
		Page<Action> pos = actionDao.findActionsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

	public List<Action> findAll() {
		return actionDao.findAll();
	}
	

}
