package com.jsdz.digitalevidence.utils;

import java.util.Map;
import java.util.concurrent.*;

public class TimedContainerUtil {

    private static final ConcurrentHashMap<String, Long> insertionTimes = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Object> dataMap = new ConcurrentHashMap<>();
    private static final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
    private static final long EXPIRATION_TIME_MILLIS =  1000 * 60; // 10 minutes in milliseconds

    static {
        scheduler.scheduleAtFixedRate(TimedContainerUtil::cleanUp, EXPIRATION_TIME_MILLIS, EXPIRATION_TIME_MILLIS, TimeUnit.MILLISECONDS);
    }

    public static void put(String key, Object value) {
        dataMap.put(key, value);
        insertionTimes.put(key, System.currentTimeMillis());
    }

    public static Object get(String key) {
        return dataMap.get(key);
    }

    public static boolean remove(String key) {
        return dataMap.remove(key) != null && insertionTimes.remove(key) != null;
    }

    public static boolean containsKey(String key) {
        return dataMap.containsKey(key);
    }

    private static void cleanUp() {
        long currentTimeMillis = System.currentTimeMillis();
        insertionTimes.entrySet().removeIf(entry -> {
            long insertionTime = entry.getValue();
            return currentTimeMillis - insertionTime > EXPIRATION_TIME_MILLIS;
        });

        // Remove expired entries from dataMap based on insertionTimes
        dataMap.keySet().removeIf(key -> !insertionTimes.containsKey(key));
    }

    public static void shutdown() {
        scheduler.shutdown();
    }

    // Prevent instantiation of this utility class
    private TimedContainerUtil() {
        throw new AssertionError("Utility class");
    }

//    public static void main(String[] args) {
//        // Usage example
//        TimedContainerUtil.put("key1", "value1");
//
//        System.out.println(TimedContainerUtil.get("key1")); // Should print "value1"
//
//        try {
//            Thread.sleep(EXPIRATION_TIME_MILLIS + 1000); // Wait for expiration time plus some extra time
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
//
//        System.out.println(TimedContainerUtil.get("key1")); // Should print null, as the entry has expired
//
//        // Remember to shut down the scheduler when you no longer need it
//        TimedContainerUtil.shutdown();
//        TimedContainerUtil.put("key2", "value2");
//        System.out.println(TimedContainerUtil.get("key2")); // Should print null, as the entry has expired
//
//    }
}