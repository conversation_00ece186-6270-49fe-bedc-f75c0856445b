<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

	<!-- *********考核监督模型********* -->
	<!-- 拍摄考核报告 -->
	<class 
		name="com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport" table="T_ASSESSMENT_REPORT">
		<id name="reportId" column="REPORT_ID" >
            <generator class="native"/>
        </id>
        
        <!-- 被考核警员 -->
		<many-to-one name="police" cascade="none"
		 	class="com.jsdz.admin.security.model.Employees" column="POLICE_ID">
		</many-to-one>
        
        <!-- 考核时段 -->
        <!-- 报告开始日期 -->
		<property name="startDate" column="START_DATE"></property>
        <!-- 报告结束日期 -->
		<property name="endDate" column="END_DATE"></property>
		<!-- 报告提交日期 -->
		<property name="submitDate" column="SUBMIT_DATE"></property>
		<!--  -->
		<property name="frozen" column="IS_FROZEN"></property>
		<!--  -->
		<property name="comments" column="COMMENTS"></property>
		
		<!-- 拍摄统计 -->  
        <many-to-one name="sumOfShooting"
                entity-name="shootingSummary"
        		cascade="delete,save-update"
        		column="SHOOTING_SUM_ID">
        </many-to-one>
        
        <!-- 拍摄考核统计 -->  
        <many-to-one name="sumOfShootingAssessment"
        		entity-name="shootingAssessmentSummary"
        		cascade="delete,save-update"
        		column="SHOOTING_ASSESSMENT_SUM_ID">
        </many-to-one>
        <!-- 报告类型 -->
		<property name="type" column="REPORT_TYPE">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.assessment.model.ReportType
            		</param>
            	</type>
		</property>	
        
	</class>
	
	<!-- 拍摄考核 -->
	<class name="com.jsdz.digitalevidence.assessment.model.ShootingAssessment" table="T_ASSESSMENT_SHOOTING">
		<id name="id" column="ID" type="long">
			<generator class="native"/>
		</id>
		<!-- 考核类型 -->
		<property name="cate" column="ASSESSMENT_CATE">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.assessment.model.AssessmentCate
            		</param>
            	</type>
		</property>
		<!-- 是否文明规范 -->
		<property name="isWMGF" column="IS_WMGF"></property>
		<!-- 是否不存在违法违纪 -->
		<property name="isNoWFWJ" column="IS_NO_WFWJ"></property>
		<!-- 是否按要求拍摄 -->
		<property name="isAYQPS" column="IS_AYQPS"></property>
		<!-- 是否按要求运用语言 -->
		<property name="isYYYY" column="IS_YYYY"></property>
		<!-- 拍摄角度-->
		<property name="shootingAngle" column="SHOOTING_ANGLE">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.assessment.model.ShootingAngleLevel
            		</param>
            	</type>
		</property>		
		<!-- 拍摄内容要素-->
		<property name="shootingContent" column="SHOOTING_CONTENT">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.assessment.model.ShootingContentLevel
            		</param>
            	</type>
		</property>	
		<!-- 拍摄分辨率-->
		<property name="shootingResolution" column="SHOOTING_RESLUTION">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.assessment.model.ShootingResolutionLevel
            		</param>
            	</type>
		</property>
		<!-- 考核时间 -->
		<property name="assessDate" column="ASSESS_DATE" type="timestamp"></property>
		<!-- 考核目标文档 -->
		<many-to-one name="document" cascade="none"
		 	class="com.jsdz.digitalevidence.document.model.Document" column="DOC_ID">
		</many-to-one>
		<!-- 考核员 -->
		<many-to-one name="inspector" cascade="none"
		 	class="com.jsdz.admin.security.model.Employees" column="POLICE_ID">
		</many-to-one>
		
	</class>
		
	<!-- 拍摄统计 -->
	<class 
		name="com.jsdz.digitalevidence.assessment.model.ShootingSummary" table="T_ANALYSIS_SHOOTING_SUMMARY"
						entity-name="shootingSummary">
		<id name="id" column="ID" type="long">
			<generator class="native"/>
		</id>
		
		<!-- 视频数 -->
		<property name="countOfVedio" column="COUNT_OF_VEDIO"></property>
		<!-- 视频时长 -->
		<property name="lengthOfVedio" column="LENGTH_OF_VEDIO"></property>
		<!-- 视频大小 -->
		<property name="sizeOfVedio" column="SIZE_OF_VEDIO"></property>
		
		<!-- 音频数 -->
		<property name="countOfAudio" column="COUNT_OF_AUDIO"></property>
		<!-- 音频时长 -->
		<property name="lengthOfAudio" column="LENGTH_OF_AUDIO"></property>
		<!-- 音频大小 -->
		<property name="sizeOfAudio" column="SIZE_OF_AUDIO"></property>
		
		<!-- 图片数 -->
		<property name="countOfPic" column="COUNT_OF_PIC"></property>
		<!-- 图片大小 -->
		<property name="sizeOfPic" column="SIZE_OF_PIC"></property>
		
		<!-- 日志数 -->
		<property name="countOfLog" column="COUNT_OF_LOG"></property>
		<!-- 日志大小 -->
		<property name="sizeOfLog" column="SIZE_OF_LOG"></property>
		
	</class>
	
	<!-- 拍摄考核统计 -->
	<class 
		name="com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary" table="T_ASSESSMENT_SHOOTING_SUMMARY"
		entity-name="shootingAssessmentSummary">
		<id name="id" column="ID">
		    <generator class="native"/>
		</id>
		
		<!-- 文明规范数 -->
		<property name="countOfWMGF"></property>
		<!-- 不存在违法违纪 -->
		<property name="countOfNoWFWJ" ></property>
		
		<!-- 按要求拍摄数 -->
		<property name="countOfAYQPS" ></property>
		<!-- 按要求运用语言 -->
		<property name="countOfYYYY" ></property>
		
		<!-- 拍摄角度准确数 -->
		<property name="countOfShootingAngleA" ></property>
		<!-- 拍摄角度一般数 -->
		<property name="countOfShootingAngleG" ></property>
        <!-- 拍摄角度错误数 -->
		<property name="countOfShootingAngleE" ></property>
		
		<!-- 拍摄内容准确数 -->
		<property name="countOfShootingContentA" ></property>
		<!-- 拍摄内容一般数 -->
		<property name="countOfShootingContentG" ></property>
        <!-- 拍摄内容错误数 -->
		<property name="countOfShootingContentE" ></property>
		
		<!-- 拍摄分辨率高数 -->
		<property name="countOfShootingResolutionH" ></property>
		<!-- 拍摄分辨率一般数 -->
		<property name="countOfShootingResolutionG" ></property>
        <!-- 拍摄分辨率低数 -->
		<property name="countOfShootingResolutionL" ></property>
		
		<!-- 期间，考核视频数 -->
		<property name="totalAssessingDoc" ></property>
        <!-- 期间，上传视频总数 -->
		<property name="totalDoc" ></property>
		
	</class>
	
	<!-- *******预警模型****** -->
	<!-- 预警报告 -->
	<class name="com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert" table="T_ASSESSMENT_ALERT">
		<id name="id" column="ID">
		    <generator class="native"/>
		</id>
		<!-- 所属警员 -->
		<many-to-one name="police" cascade="none"
		 	class="com.jsdz.admin.security.model.Employees" column="POLICE_ID">
		</many-to-one>
		<!-- 考核时间 -->
		<property name="createDate" column="CREATE_DATE" type="timestamp"></property>
		 <!-- 考核时段 -->
        <!-- 报告开始日期 -->
		<property name="startDate" column="START_DATE"></property>
        <!-- 报告结束日期 -->
		<property name="endDate" column="END_DATE"></property>
		<!-- 预警项 -->
    	<list name="items" cascade="all">  
            <key column="alert_id"></key>  
            <index column="ind"></index>
            <one-to-many class="com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem"/>  
        </list>  
	    <!-- 反馈 -->
		<property name="feedBack" column="FEE_BACK"></property>
		<!-- 报告类型 -->
		<property name="type" column="REPORT_TYPE">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.assessment.model.ReportType
            		</param>
            	</type>
		</property>
	</class>
	
	<!-- 考核预警项 -->
	<class name="com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem"
								table="T_ASSESSMENT_ALERT_ITEM">  
		<id name="alertItemId" column="alert_item_id" >
            <generator class="native"/>
        </id>
        <property name="item" column="ALTER_ITEM"></property>  
	    <property name="v" column="V" type="com.jsdz.core.PersistentObject"></property>  
	    <property name="ref" column="REF" type="com.jsdz.core.PersistentObject"></property>
    </class>
	
	<!-- *******推送考核视频****** -->
	<!-- 推送视频 -->
	<class name="com.jsdz.digitalevidence.assessment.model.push.AssessmentPushDoc" table="T_ASSESSMENT_PUSH_DOCUMENT">
		<id name="pushDocId" column="ID">
		    <generator class="native"/>
		</id>
		<!-- 所属警员 -->
		<many-to-one name="police" cascade="none"
		 	class="com.jsdz.admin.security.model.Employees" column="POLICE_ID">
		</many-to-one>
		<!-- 所属警员 -->
		<many-to-one name="doc" cascade="none"
		 	class="com.jsdz.digitalevidence.document.model.Document" column="DOC_ID">
		</many-to-one>
		<!-- 考核员 -->
		<many-to-one name="assessor" cascade="none"
		 	class="com.jsdz.admin.security.model.Employees" column="ASSESSOR_ID">
		</many-to-one>
		<!-- 是否已考核 -->
		<property name="hasAssessed" column="HAS_ASSESSED"></property>
		<!-- 推送时间 -->
		<property name="createTime" column="CREATE_TIME" type="timestamp"></property>
	</class>
	
	<!-- 考评方案 -->
	<class name="com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlan" table="T_ASSESSMENT_PLAN">
		<id name="id" column="id">
		    <generator class="native"/>
		</id>
		<!-- 描述 -->
		<property name="description" column="description"></property>
		<!-- 开始时间  -->
		<property name="beginDate" column="begin_date"></property>
		<!-- 结束 时间 -->
		<property name="endDate" column="end_date"></property>
		<!-- 状态0 初始 1启动 2结束  -->
		<property name="status" column="status"></property>
		<property name="createTime" column="create_time"></property>
	</class>
	
	<!-- 考评方案文档 -->
	<class name="com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanDocment" 
		table="T_ASSESSMENT_PLAN_DOC">
		<id name="id" column="id">
		    <generator class="native"/>
		</id>
		<!-- 方案id -->
		<property name="planId" column="plan_id"></property>
		<!-- 文档  -->
		<many-to-one name="doc" cascade="none"
		 	class="com.jsdz.digitalevidence.document.model.Document" column="doc_id">
		</many-to-one>
		
		<!-- 分配 -->
		<property name="alloted" column="alloted"></property>
	</class>
	
	<!-- 考评方案考评员 -->
	<class name="com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPolice" 
		table="T_ASSESSMENT_PLAN_POLICE">
		<id name="id" column="id">
		    <generator class="native"/>
		</id>
		<!-- 方案id -->
		<property name="planId" column="plan_id"></property>
		<!-- 警员  -->
		<many-to-one name="police" cascade="none"
		 	class="com.jsdz.admin.security.model.Employees" column="police_id">
		</many-to-one>		
	</class>
	
	<!-- 考评分配 -->
	<class name="com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPoliceDocument" 
		table="T_ASSESSMENT_PLAN_POLICE_DOC">
		<id name="id" column="id">
		    <generator class="native"/>
		</id>
		<!-- 方案id -->
		<property name="planId" column="plan_id"></property>
		<!-- 警员  -->
		<many-to-one name="police" cascade="none"
		 	class="com.jsdz.admin.security.model.Employees" column="police_id">
		</many-to-one>
		<!-- 文档  -->
		<many-to-one name="doc" cascade="none"
		 	class="com.jsdz.digitalevidence.document.model.Document" column="doc_id">
		</many-to-one>
			
	</class>

	
	
</hibernate-mapping>