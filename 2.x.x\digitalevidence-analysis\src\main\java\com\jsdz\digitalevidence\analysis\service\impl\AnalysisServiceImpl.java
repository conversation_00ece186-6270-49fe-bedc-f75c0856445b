package com.jsdz.digitalevidence.analysis.service.impl;
import javax.annotation.Resource;

/**
 * 
 * @类名: AnalysisServiceImpl
 * @说明: 查询分析：系统简报，趋势研判，趋势对比
 *
 * @author: kenny
 * @Date	2018年3月28日上午11:44:25
 * 修改记录：
 *
 * @see
 */
import org.springframework.stereotype.Service;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.analysis.bean.AlarmDocRelationRateBean;
import com.jsdz.digitalevidence.analysis.bean.AnalysisDocumentBean;
import com.jsdz.digitalevidence.analysis.bean.BriefReport;
import com.jsdz.digitalevidence.analysis.bean.TrendReport;
import com.jsdz.digitalevidence.analysis.service.AnalysisService;
import com.jsdz.reportquery.ReportQueryDao;

@Service("AnalysisServiceImpl")
public class AnalysisServiceImpl implements AnalysisService{
	/** 动态查询Dao*/
/*	@Resource(name="rqDao")
	private ReportQueryDao<BriefReport> reportQueryDao;
*/	
	@Resource(name="rqDao")
	private ReportQueryDao reportQueryDao;
	
	//系统简报查询
	@SuppressWarnings("unchecked")
	@Override
	public Page<BriefReport> findBriefReport(Page<BriefReport> page, String[] paramNames, Object[] values) {
		return reportQueryDao.pageQueryNamedSQL(page,"findBriefReport", paramNames, values);
	}

	//趋势对比
	@SuppressWarnings("unchecked")
	@Override
	public Page<TrendReport> findTrendCompareReport(Page<TrendReport> page, String[] paramNames, Object[] values) {
		return reportQueryDao.pageQueryNamedSQL(page,"findTrendCompareReport", paramNames, values);
	}

	//趋势研判
	@SuppressWarnings("unchecked")
	@Override
	public Page<TrendReport> findTrendForecastReport(Page<TrendReport> page, String[] paramNames, Object[] values) {
		return reportQueryDao.pageQueryNamedSQL(page,"findTrendForecastReport", paramNames, values);
	}

	//警情视频关联率查询
	@Override
	public Page<AlarmDocRelationRateBean> findAlarmDocRelationFromSql(Page<AlarmDocRelationRateBean> page,String[] paramNames, Object[] values){
		Page<AlarmDocRelationRateBean> p1 = reportQueryDao.pageQueryNamedSQL(page,"findAlarmDocRelateReport",paramNames,values);		
		return p1;
	}
	
/*	//按警员统计上传文件
	@SuppressWarnings("unchecked")
	@Override
	public Page<AnalysisDocumentBean> findAnalysisDocByPoliceFromSql(Page<AnalysisDocumentBean> page,String[] paramNames, Object[] values){
		Page<AnalysisDocumentBean> p1 = reportQueryDao.pageQueryNamedSQL(page,"analysisDocByPolice",paramNames,values);		
		return p1;
	}
	
	//按站点统计上传文件
	@SuppressWarnings("unchecked")
	@Override
	public Page<AnalysisDocumentBean> findAnalysisDocBySiteFromSql(Page<AnalysisDocumentBean> page,String[] paramNames, Object[] values){
		Page<AnalysisDocumentBean> p1 = reportQueryDao.pageQueryNamedSQL(page,"analysisDocBySite",paramNames,values);		
		return p1;
	}
*/	
	//按警员,站点，设备，单位统计上传文件
	@SuppressWarnings("unchecked")
	@Override
	public Page<AnalysisDocumentBean> findAnalysisDocumentFromSql(Page<AnalysisDocumentBean> page,String sqlName,String[] paramNames, Object[] values){
		Page<AnalysisDocumentBean> p1 = reportQueryDao.pageQueryNamedSQL(page,sqlName,paramNames,values);		
		return p1;
	}	
	
}
