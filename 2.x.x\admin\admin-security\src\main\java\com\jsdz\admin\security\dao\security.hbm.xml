<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">

<hibernate-mapping >
	<!-- 区域类 -->	
	<class name="com.jsdz.admin.org.model.Region" table="admin_region">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
<!-- 		<property name="shiYan" type="java.lang.String"  unique="true" 
			 update="false" index="person_index" column="SHI_YAN" length="20" >
       </property>
 -->

		
		<property name="regionName" column="regionName"></property>

		<many-to-one name="parentRegion" class="com.jsdz.admin.org.model.Region" fetch="join">
            <column name="parentId"><comment>上级区域编号</comment></column>
        </many-to-one>
         
		<many-to-one name="regionType" cascade="none" class="com.jsdz.admin.org.model.RegionType" fetch="join">
            <column name="regionTypeId"><comment>地区类别编号</comment></column>
        </many-to-one>
        
        <!-- 下级区
        <set name="subRegion" fetch="join" inverse="true" lazy="false">
            <key>
                <column name="parentId">
                <comment>下级区域编号</comment>
                </column>
                parentId" 是指明了在T_region表里有一个parentId的列名，是指向T_region表的外键   
            </key>
            <one-to-many class="com.jsdz.admin.org.model.Region" />
        </set>
        -->
		<property name="createTime" column="createTime"></property>
	</class>

	<!-- 地区类别 -->
	<class name="com.jsdz.admin.org.model.RegionType" table="admin_regiontype">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="name" column="name"></property>
		<property name="level" column="region_level"></property>
		<property name="description" column="description"></property>
	</class>
	
	<!-- 部门 -->
	<class name="com.jsdz.admin.org.model.Department" table="admin_department">
		<id name="id" column="ID" type="long">	
			<generator class="native" />
		</id>
		<property name="name" column="name"></property>
		<property name="description" column="description"></property>
		<property name="createTime" column="createTime"></property>
	</class>
	<!-- 职位 -->
	<class name="com.jsdz.admin.org.model.Position" table="admin_position">
		<id name="id" column="ID" type="long">	
			<generator class="native" />
		</id>
		<property name="position" column="position"></property>
		<property name="description" column="description"></property>
		<property name="createTime" column="createTime"></property>
	</class>

	<!--组织机构类 -->	
	<class name="com.jsdz.admin.org.model.Organization" table="admin_organization">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="orgCode" column="orgCode"></property>
		<property name="orgName" column="orgName"></property>
		<property name="orgNameJC" column="orgNameJC"></property>
		<property name="orgType" column="orgType"></property>
		<property name="path" column="path"></property>
		<property name="createTime" column="createTime"></property>
		<!--区域 -->
		<many-to-one name="region" cascade="none" fetch="join" 
		    class="com.jsdz.admin.org.model.Region" column="regionId">
        </many-to-one>
        <!-- 上级机构 -->
		<many-to-one name="parentOrg" cascade="none" fetch="join" 
            class="com.jsdz.admin.org.model.Organization" column ="parentId">
        </many-to-one>
        <!-- 下级机构 
        <set name="subOrg" fetch="join" inverse="true" lazy="false">
            <key>
                <column name="parentId"><comment>下级区域编号</comment></column>
            </key>
            <one-to-many class="com.jsdz.admin.org.model.Organization" />
        </set>
        -->
 	</class>
 	

	<!-- 员工类 -->	
	<class name="com.jsdz.admin.security.model.Employees" table="admin_employees">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
	    
		<property name="name" column="name"></property>
		<property name="male" column="male"></property>
		<property name="birthDate" column="birthDate"></property>
		<property name="idNum" column="idNum"></property>
		<property name="mail" column="mail"></property>
		<property name="mobile" column="mobile"></property>
		<property name="age" column="age"></property>
		<property name="workNumber" column="workNumber"></property>
		<property name="createTime" column="createTime"></property>
		<property name="post" column="post"></property>
		<property name="isPolice" column="isPolice"></property>
		
		<!--  更新时间-->
		<property name="updateTime" column="update_time"></property>
		<!--  是否已删除-->
		<property name="isDeleted" column="is_deleted" ></property>
		<!--  原code,为防止加入新编号重复-->
		<property name="deleteCode" column="delete_code"></property>
		<!--  删除时间-->
		<property name="deleteTime" column="delete_time"></property>
		<!--  删除人-->
		<property name="deleteBy" column="delete_by"></property>
		
		<!--  人脸特征 值-->
		<property name="faceFeatures" column="face_features"></property>
		

		<many-to-one name="department" cascade="none" fetch="join" 
		    class="com.jsdz.admin.org.model.Department" column ="departmentId">
        </many-to-one>
        
		<many-to-one name="organization" cascade="none" fetch="join" 
		    class="com.jsdz.admin.org.model.Organization" column ="organizationId">
        </many-to-one>
        
		<many-to-one name="position" cascade="none" fetch="join" 
		    class="com.jsdz.admin.org.model.Position" column ="positionId">
        </many-to-one>
        
        
	</class>
	
	<!-- 岗位实体类 -->
	<!-- 
	<class name="com.jsdz.admin.security.model.Post" table="admin_post">
 		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="post" column="post"></property>
		<property name="path" column="path"></property>
		<property name="index" column="ind"></property>
		<property name="createTime" column="create_time"></property>
		<property name="description" column="description"></property>
		
		<many-to-one name="parentPost" class="com.jsdz.admin.security.model.Post" fetch="join">
            <column name="parent_id"><comment>上级岗位</comment></column>
        </many-to-one>
	</class>
	-->
	
	<!-- 操作员类 -->
 	<class name="com.jsdz.admin.security.model.Operator" table="admin_operators">
 		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
   		<property name="loginName" column="loginName"></property>
       	<property name="password" column="password"></property>
       	<property name="state" column="state"></property>
       	<property name="description" column="description"></property>
       	<property name="operatorRoleStr" column="operatorRoleStr"></property>
       	
       	<!--  更新时间-->
		<property name="updateTime" column="update_time"></property>
		<!-- 密码失效时间 -->
		<property name="pwdExpireTime" column="pwd_expire_time"></property>
		<!--  是否已删除-->
		<property name="isDeleted" column="is_deleted" ></property>
		<!--  原code,为防止加入新编号重复-->
		<property name="deleteCode" column="delete_code"></property>
		<!--  删除时间-->
		<property name="deleteTime" column="delete_time"></property>
		<!--  删除人-->
		<property name="deleteBy" column="delete_by"></property>
       	<property name="talkCode" column="talk_code"></property>
       	
		<many-to-one name="employees" cascade="none" fetch="join" 
		    class="com.jsdz.admin.security.model.Employees" column ="employeesId">
        </many-to-one>
 	</class>	
	
	<!-- 角色-->
	<class name="com.jsdz.admin.security.model.Role" table="admin_roles">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="roleName" column="roleName"></property>
		<property name="description" column="description"></property>
		<property name="actionStr" column="actionStr"></property>
		<property name="actionNames" column="actionNames"></property>
		<property name="orgNames" column="orgNames"></property>
		<property name="createTime" column="createTime"></property>
		<property name="roleLevel" column="roleLevel" ></property>
		<property name="orgLevel" column="orgLevel" ></property>
	</class>
	
	<!-- 角色单位-->
	<class name="com.jsdz.admin.security.model.RoleOrganization" table="admin_roles_organization">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
	    <!-- 权限所属单位 -->
		<many-to-one name="role" cascade="none" fetch="join" 
            class="com.jsdz.admin.security.model.Role" column ="role_id">
        </many-to-one>
		
	    <!-- 权限所属单位 -->
		<many-to-one name="organization" cascade="none" fetch="join" 
            class="com.jsdz.admin.org.model.Organization" column ="org_id">
        </many-to-one>
	</class>
        

	<!-- 操作员角色-->
	<class name="com.jsdz.admin.security.model.OperatorRole" table="admin_operatorroles">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<many-to-one name="operator" cascade="none" fetch="join"
		    class="com.jsdz.admin.security.model.Operator" column ="operatorId">
        </many-to-one>
		<many-to-one name="role" cascade="none" fetch="join"
		    class="com.jsdz.admin.security.model.Role" column ="roleId">
        </many-to-one>
 		<many-to-one name="createBy" cascade="none" fetch="join"
		    class="com.jsdz.admin.security.model.Operator" column ="createById">
        </many-to-one>
      		
		<property name="createTime" column="createTime" ></property>
	</class>
	
	
	<!-- 权限类 -->
	<class name="com.jsdz.admin.security.model.Action" table="admin_actions">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="name" column="name"></property>
		<property name="index" column="ind"></property>
		<property name="tag" column="tag"></property>
		<property name="url" column="url"></property>
	</class>
	
	<!-- 角色权限-->
	<class name="com.jsdz.admin.security.model.RoleAction" table="admin_roleactions">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<many-to-one name="role" cascade="none" fetch="join"
		    class="com.jsdz.admin.security.model.Role" column ="roleId">
        </many-to-one>
		<many-to-one name="action" cascade="none" fetch="join"
		    class="com.jsdz.admin.security.model.Action" column ="actionId">
        </many-to-one>
      		
		<property name="createTime" column="createTime" ></property>
	</class>
		
	
	<!-- 操作日志类 -->
	<class name="com.jsdz.admin.security.model.OperateLog" table="admin_operateLogs">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		
		<property name="logContent" column="logContent"></property>
		<property name="logCreateTime" column="logCreateTime"></property>
		<property name="logType" column="logType"></property>
		<property name="queryBeginTime" column="queryBeginTime"></property>
		<property name="queryEndTime" column="queryEndTime"></property>
		<property name="logTypeStr" column="logTypeStr"></property>
		<property name="loginName" column="loginName"></property>
		<property name="workNumber" column="workNumber"></property>
		<property name="name" column="name"></property>
		<property name="fromIp" column="ips"></property>
		<property name="operatorId" column="operatorId"></property>
<!-- 		<many-to-one name="logCreator" cascade="none" fetch="join"
		    class="com.jsdz.admin.security.model.Operator" column ="logCreatorId">
        </many-to-one>   
 -->
		<many-to-one name="organization" cascade="none" fetch="join"
			 class="com.jsdz.admin.org.model.Organization" column ="orgId">
		</many-to-one>
	</class>
	
	<!-- 系统参数类 -->
	<class name="com.jsdz.admin.security.model.SysConfig" table="admin_sysconfig">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="sysConfigKey" column="sysConfigKey"></property>
		<property name="sysConfigValue" column="sysConfigValue"></property>
		<property name="sysConfigDesc" column="sysConfigDesc"></property>
		<property name="sysReserved" column="sysReserved"></property>
		<property name="sysConfigCreateTime" column="sysConfigCreateTime"></property>
	</class>
	<!-- 保存RSA公钥私钥类 -->
	<class name="com.jsdz.admin.security.model.RsaKey" table="admin_rsakey">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="uniqueKey" column="uniqueKey"></property>
		<property name="publicKey" column="publicKey" length="2000" />
		<property name="privateKey" column="privateKey" length="2000" />
		<property name="aesKey" column="aesKey" length="150" />
		<property name="createTime" column="createTime"></property>
	</class>
	<!-- 组织机构封装类 -->
	<class name="com.jsdz.admin.org.bean.OrganizationBean" table="admin_organizationbean_bean">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="orgCode" />
		<property name="orgName" />
		<property name="orgType" />
		<property name="parentId"  />
		<property name="parentCode"  />
		<property name="parentName"  />
		<property name="regionId"  />
		<property name="regionName" />
		<property name="createTime" />
		<property name="path" />
		<property name="state" />
	</class>
	<!-- 区域封装类 -->
	<class name="com.jsdz.admin.org.bean.RegionBean" table="admin_regionBean_bean">
	    <id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="regionName"  />
		<property name="regionTypeId" />
		<property name="regionTypeName"  />
		<property name="parentId"  />
		<property name="parentname"  />
		<property name="createTime"  />
	</class>
	
	<!-- 菜单类 -->
	<class name="com.jsdz.admin.security.model.SystemMenu" table="admin_systemmenu">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="menuName" column="menu_name" ></property>
		<property name="menuUrl" column="menu_url"></property>
		<property name="menuType" column="menu_type"></property>
		<property name="isShow" column="is_show"></property>
		<property name="sort" column="sort"></property>
		<property name="imageUrl" column="image_url"></property>
		<property name="createTime" column="create_time"></property>
		<many-to-one name="parentMenu" cascade="none" fetch="join"
		    class="com.jsdz.admin.security.model.SystemMenu" column ="parent_id">
        </many-to-one>		
	</class>
	
	<!-- 角色菜单(权限) -->
	<class name="com.jsdz.admin.security.model.RoleMenu" table="admin_role_menu">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>

		<property name="createTime" column="create_time"></property>
		<many-to-one name="role" cascade="none" fetch="join"
		    class="com.jsdz.admin.security.model.Role" column ="role_id">
        </many-to-one>		
		<many-to-one name="menu" cascade="none" fetch="join"
		    class="com.jsdz.admin.security.model.SystemMenu" column ="menu_id">
        </many-to-one>		

	</class>
	
	<!-- 菜单封装类 -->
	<class name="com.jsdz.admin.security.bean.SystemMenuBean" >
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="menuName" ></property>
		<property name="menuUrl" ></property>
		<property name="menuType" ></property>
		<property name="isShow" ></property>
		<property name="sort" ></property>
		<property name="imageUrl" ></property>
		<property name="createTime" ></property>
		<property name="parentId" ></property>
		<property name="parentName" ></property>
		
	</class>	
	
	<!-- 菜单权限封装类 -->
	<class name="com.jsdz.admin.security.bean.RoleMenuBean" >
	    <id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="pid" column="pid" type="long"></property>
		<property name="name" column="name"></property>
		<property name="checked" column="checked"></property>
	</class>
	
	<!-- 页面权限封装类 -->
	<class name="com.jsdz.admin.security.bean.RoleForViewBean" >
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="menuName" ></property>
	</class>
	
	<!-- 数据字典 -->
	<class name="com.jsdz.admin.security.model.DataDictionary" table="admin_dictionary">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="code" column="dict_code"></property>
		<property name="name" column="dict_name"></property>
		<property name="note" column="dict_note"></property>
		<property name="order" column="dict_order"></property>
		<property name="createTime" column="create_time"></property>
	</class>		

	<!-- 数据字典明细 -->
	<class name="com.jsdz.admin.security.model.DictionaryValues" table="admin_dictionary_value">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="value" column="dict_value"></property>
		<property name="note" column="dict_note"></property>
		<property name="order" column="dict_order"></property>
		<property name="createTime" column="create_time"></property>
		
		<many-to-one name="dictionary" cascade="none" 
		    class="com.jsdz.admin.security.model.DataDictionary" column ="dictionary_id">
        </many-to-one>		
	</class>		
			
	<!-- 警员导入临时表 -->
	<class name="com.jsdz.admin.security.model.EmployeesImport" table="admin_employees_import">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
		<property name="policeCode" column="police_code"></property>
		<property name="policeName" column="police_name"></property>
		<property name="orgCode" column="org_code"></property>
		<property name="createTime" column="create_time"></property>
	</class>	
	
	<!-- 角色单位-->
	<class name="com.jsdz.admin.security.model.PoliceOrganization" table="admin_police_organization">
		<id name="id" column="id" type="long">	
			<generator class="native" />
		</id>
	    <!-- 权限所属单位 -->
		<many-to-one name="police" cascade="none" fetch="join" 
            class="com.jsdz.admin.security.model.Employees" column ="police_id">
        </many-to-one>
		
	    <!-- 权限所属单位 -->
		<many-to-one name="organization" cascade="none" fetch="join" 
            class="com.jsdz.admin.org.model.Organization" column ="org_id">
        </many-to-one>
	</class>

	
	<!-- 在导入的数据中，数据有效性检查 -->
	<class name="com.jsdz.admin.security.bean.ImportCheckValueBean" >
		<id name="id"  type="long"> <generator class="native" /></id>
		<property name="importValue" ></property>
		<property name="originalValue" ></property>
		<property name="repeatCount" ></property>
	</class>
	
	<!-- 警员导入封装类 -->
	<class name="com.jsdz.admin.security.bean.EmployeesImportBean" >
		<id name="id"  type="long"> <generator class="native" /></id>
		<property name="policeCode" ></property>
		<property name="originalId" ></property>
		<property name="policeName" ></property>
		<property name="orgCode" ></property>
		<property name="createTime" ></property>
		<property name="orgId" ></property>
	</class>
</hibernate-mapping>