package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: RsaKeyDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-06-02 11:38:34
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.RsaKeyDao;
import com.jsdz.admin.security.model.RsaKey;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class RsaKeyDaoImpl extends GenericEntityDaoHibernateImpl<RsaKey,Long> implements RsaKeyDao{

	//新增
	public void addRsaKey(RsaKey rsaKey) {
		this.saveOrUpdate(rsaKey);
	}

	//删除
	public void deleteRsaKey(RsaKey rsaKey) {
		this.delete(rsaKey);
	}

	//修改
	public void updateRsaKey(RsaKey rsaKey) {
		this.merge(rsaKey);
	}

	//按id查询(游离状态)
	public RsaKey findRsaKeyById(Long id){

		final String  hql = "from RsaKey r where r.id = :id";
		final Long oid = id;
		RsaKey data = getHibernateTemplate().execute(new HibernateCallback<RsaKey>() {
			public RsaKey doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<RsaKey> list = query.list();
				RsaKey rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public RsaKey locateRsaKeyById(Long id){

		final String  hql = "from RsaKey r where r.id = :id";
		final Long oid = id;
		RsaKey data = getHibernateTemplate().execute(new HibernateCallback<RsaKey>() {
			public RsaKey doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<RsaKey> list = query.list();
				RsaKey rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public RsaKey findRsaKeyByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		RsaKey data = getHibernateTemplate().execute(new HibernateCallback<RsaKey>() {
		public RsaKey doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<RsaKey> list = query.list();
			RsaKey rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<RsaKey> findAllRsaKeys(){
		return this.find("from RsaKey rsaKey ");
	}

	//列表查询
	public List<RsaKey> findRsaKeysByCondition(String queryStr,String[] paramNames,Object[] values){
		List<RsaKey> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<RsaKey> findRsaKeysOnPage(Page<RsaKey> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<RsaKey>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
