package com.jsdz.digitalevidence.activemq.service.impl;

import javax.jms.Connection;
import javax.jms.ConnectionFactory;
import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.MessageListener;
import javax.jms.Session;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.jsdz.digitalevidence.activemq.service.ConsumerService;

/**
 * 
 * @类名: ConsumerServiceImpl
 * @说明: 消息订阅接口实现类
 *
 * @author: kenny
 * @Date	2017年10月27日上午11:35:15
 * 修改记录：
 *
 * @see
 */
@Service("ConsumerServiceImpl")
public class ConsumerServiceImpl implements ConsumerService{
	private Logger log = LoggerFactory.getLogger(this.getClass());
	
	//注册监听器
	@Override
	//public void consumerMesaage(ConnectionFactory connectionFactory,Class<MessageListener> clazz,String topicName){
	public void consumerMesaage(ConnectionFactory connectionFactory,MessageListener listener,String topicName){
        Connection connection = null; // 连接  
        Session session; // 会话 接受或者发送消息的线程  
        Destination destination; // 消息的目的地  
        MessageConsumer messageConsumer; // 消息的消费者
        
        try {  
            // 通过连接工厂获取连接  
            connection=connectionFactory.createConnection();  // 通过连接工厂获取连接  
            connection.start(); // 启动连接  
            session=connection.createSession(Boolean.FALSE, Session.AUTO_ACKNOWLEDGE); // 创建Session  
            destination=session.createTopic(topicName);   
            messageConsumer=session.createConsumer(destination); // 创建消息消费者  
            try {
            	//log.info("*** clazz.Name：" + clazz.getName()); 
				//messageConsumer.setMessageListener((MessageListener)clazz.forName(clazz.getName()).newInstance());// 注册消息监听
            	messageConsumer.setMessageListener(listener);// 注册消息监听
			} catch (Exception e){//(InstantiationException | IllegalAccessException | ClassNotFoundException e) {
				log.error("*** 错误：" + e.toString()); 
			}  
        } catch (JMSException e) {  
        	log.error("*** 错误：" + e.toString()); 
        }          
	}
}
