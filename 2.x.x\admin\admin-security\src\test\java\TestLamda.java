import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;




public class TestLamda {

	private static String hello = "Nice to meet you";
    private static String name = "my name is h<PERSON><PERSON><PERSON>";
    
	public static class MyFunction implements Function<String,Integer>{

	    @Override
	    public Integer apply(String s) {
	        return s.length();
	    }
	}
	
	public static class MyBiFunction implements BiFunction<String, String, String> {
	    @Override
	    public String apply(String s, String s2) {
	        return s+";"+s2;
	    }
	}
	
	

    public static void main(String[] args) {
    	 MyFunction myFunction = new MyFunction();
         MyBiFunction biFunction = new MyBiFunction();
         int num = myFunction.apply(hello);
         String valueBi = biFunction.apply(hello, name);
         //hello长度返回
         System.out.println(num);
         //语句整合返回
         System.out.println(valueBi);
    }
}