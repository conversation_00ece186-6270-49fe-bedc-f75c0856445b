package TestService;
import java.util.Date;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
/**
 * 
 * @类名: TestRegion
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月10日上午11:27:29
 * 修改记录：
 *
 * @see
 */
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.model.Region;
import com.jsdz.admin.org.service.OrganizationService;
import com.jsdz.admin.org.service.RegionService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.TreeBean;

import net.sf.json.JSONArray;

@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { "classpath:/testApplicationContext-common.xml" })
public class TestRegion {
	@Autowired
	private RegionService regionService;
	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}
	
	
	//@Test
	@Transactional
	public void testFindRegionTree()
	{
		List<TreeBean> list = regionService.findRegionOnTree();

		JSONArray json = JSONArray.fromObject(list);//将java对象转换为json对象
		System.out.println("【结果】:\r\n" +  json.toString());//将json对象转换为字符串
	}
	
	//@Test
	//@Transactional
	public void testAddRegion()
	{
		Region region = new Region();
		region.setRegionName("长沙市");
		region.setCreateTime(new Date());
		Region region1 = new Region();
		
		region.setParentRegion(region1);
	
		AjaxResult result = regionService.addRegion(region);
		

		//JSONArray regionStr = JSONArray.fromObject(region);//将java对象转换为json对象
		//System.out.println("【region】:\r\n" +  regionStr.toString());//将json对象转换为字符串
		JSONArray json = JSONArray.fromObject(result);//将java对象转换为json对象
		System.out.println("【结果】:\r\n" +  json.toString());//将json对象转换为字符串
	}
	
	@Test
	@Transactional
	public void testUpdateRegion()
	{
		Region region = regionService.locateRegionById(10L);
		region.setRegionName("长沙市");
		region.setCreateTime(new Date());
		
		Region parentRegion = new Region();
		parentRegion.setId(3L);
		region.setParentRegion(parentRegion);
		AjaxResult result = regionService.updateRegion(region);

		JSONArray regionStr = JSONArray.fromObject(region);//将java对象转换为json对象
		System.out.println("【region】:\r\n" +  regionStr.toString());//将json对象转换为字符串
		JSONArray json = JSONArray.fromObject(result);//将java对象转换为json对象
		System.out.println("【结果】:\r\n" +  json.toString());//将json对象转换为字符串
	}
	
	//@Test
	@Transactional
	public void testDeleteRegion()
	{
		Region region = regionService.locateRegionById(11L);
		AjaxResult result = regionService.deleteRegion(region);

		JSONArray regionStr = JSONArray.fromObject(region);//将java对象转换为json对象
		System.out.println("【region】:\r\n" +  regionStr.toString());//将json对象转换为字符串
		JSONArray json = JSONArray.fromObject(result);//将java对象转换为json对象
		System.out.println("【结果】:\r\n" +  json.toString());//将json对象转换为字符串
	}	
}
