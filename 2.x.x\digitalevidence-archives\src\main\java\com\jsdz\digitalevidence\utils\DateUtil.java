package com.jsdz.digitalevidence.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期工具类
 * <AUTHOR>
 *
 */
public class DateUtil {

	public static Date date = null;

	public static DateFormat dateFormat = null;

	public static Calendar calendar = null;

	/**
	 * 功能描述：格式化日期
	 * 
	 * @param dateStr
	 *            String 字符型日期
	 * @param format
	 *            String 格式
	 * @return Date 日期
	 */
	public static Date parseDate(String dateStr, String format) {
		try {
			dateFormat = new SimpleDateFormat(format);
			date = (Date) dateFormat.parse(dateStr);
		} catch (Exception e) {
		}
		return date;
	}
	
	/**
	 * 功能描述：格式化日期
	 * 
	 * @param dateStr
	 *            String 字符型日期
	 * @param dateStr
	 *            String 格式
	 * @return Date 日期
	 */
	public static Date parseDateYMD(String dateStr) {
		try {
			dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			date = (Date) dateFormat.parse(dateStr);
		} catch (Exception e) {
		}
		return date;
	}

	/**
	 * 功能描述：格式化日期
	 * 
	 * @param dateStr
	 *            String 字符型日期：YYYY-MM-DD HH:mm:ss 格式
	 * @return Date
	 */
	public static Date parseDate(String dateStr) {
		return parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
	}
	
	/**
	 * 功能描述：格式化输出日期
	 * 
	 * @param date
	 *            Date 日期
	 * @param format
	 *            String 格式
	 * @return 返回字符型日期
	 */
	public static String format(Date date, String format) {
		String result = "";
		try {
			if (date != null) {
				dateFormat = new SimpleDateFormat(format);
				result = dateFormat.format(date);
			}
		} catch (Exception e) {
		}
		return result;
	}

	/**
	 * 功能描述：格式化输出日期
	 * 
	 * @param date
	 *            Date 日期
	 * @param date
	 *            String 格式
	 * @return 返回字符型日期: yyyy-MM-dd HH:mm:ss
	 */
	public static String dateStr(Date date) {
		String result = "";
		try {
			if (date != null) {
				dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				result = dateFormat.format(date);
			}
		} catch (Exception e) {
		}
		return result;
	}

	/**
	 * 功能描述：
	 * 
	 * @param date
	 *            Date 日期
	 * @return
	 */
	public static String format(Date date) {
		return format(date, "yyyy/MM/dd");
	}
	
	/**
	 * 功能描述：将java.time.LocalDateTime 转成 java.lang.String
	 * @param localDateTime
	 * @return java.lang.String
	 */
	public static String LocalDateToStr(LocalDateTime localDateTime) {
		DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		return df.format(localDateTime);
	}
	
	/**
	 * 功能描述：将java.lang.String 转成 java.time.LocalDateTime
	 * @param str
	 * @return java.time.LocalDateTime
	 */
	public static LocalDateTime StrToLocalDate(String str) {
		DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		return LocalDateTime.parse(str,df);
	}
	
	/**
	 * 功能描述：将java.time.LocalDateTime 转成 java.util.Date
	 * @param localDateTime
	 * @return java.util.Date
	 */
	public static Date timeToDate(LocalDateTime localDateTime) {
		ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
	}
	
	/**
	 * 功能描述：将java.util.Date 转成 java.time.LocalDateTime
	 * @param date
	 * @return localDateTime
	 */
	public static LocalDateTime dateToTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        return instant.atZone(zoneId).toLocalDateTime();
	}

	/**
	 * 功能描述：返回年份
	 * 
	 * @param date
	 *            Date 日期
	 * @return 返回年份
	 */
	public static int getYear(Date date) {
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.YEAR);
	}

	/**
	 * 功能描述：返回月份
	 * 
	 * @param date
	 *            Date 日期
	 * @return 返回月份
	 */
	public static int getMonth(Date date) {
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.MONTH) + 1;
	}

	/**
	 * 功能描述：返回日份
	 * 
	 * @param date
	 *            Date 日期
	 * @return 返回日份
	 */
	public static int getDay(Date date) {
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.DAY_OF_MONTH);
	}

	/**
	 * 功能描述：返回小时
	 * 
	 * @param date
	 *            日期
	 * @return 返回小时
	 */
	public static int getHour(Date date) {
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.HOUR_OF_DAY);
	}

	/**
	 * 功能描述：返回分钟
	 * 
	 * @param date
	 *            日期
	 * @return 返回分钟
	 */
	public static int getMinute(Date date) {
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.MINUTE);
	}

	/**
	 * 返回秒钟
	 * 
	 * @param date
	 *            Date 日期
	 * @return 返回秒钟
	 */
	public static int getSecond(Date date) {
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.get(Calendar.SECOND);
	}

	/**
	 * 功能描述：返回毫秒
	 * 
	 * @param date
	 *            日期
	 * @return 返回毫秒
	 */
	public static long getMillis(Date date) {
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		return calendar.getTimeInMillis();
	}

	/**
	 * 功能描述：返回字符型日期
	 * 
	 * @param date
	 *            日期
	 * @return 返回字符型日期 yyyy/MM/dd 格式
	 */
	public static String getDate(Date date) {
		return format(date, "yyyy/MM/dd");
	}

	/**
	 * 功能描述：返回字符型时间
	 * 
	 * @param date
	 *            Date 日期
	 * @return 返回字符型时间 HH:mm:ss 格式
	 */
	public static String getTime(Date date) {
		return format(date, "HH:mm:ss");
	}

	/**
	 * 功能描述：返回字符型日期时间
	 * 
	 * @param date
	 *            Date 日期
	 * @return 返回字符型日期时间 yyyy/MM/dd HH:mm:ss 格式
	 */
	public static String getDateTime(Date date) {
		return format(date, "yyyy/MM/dd HH:mm:ss");
	}

	/**
	 * 功能描述：日期相加
	 * 
	 * @param date
	 *            Date 日期
	 * @param day
	 *            int 天数
	 * @return 返回相加后的日期
	 */
	public static Date addDate(Date date, int day) {
		calendar = Calendar.getInstance();
		long millis = getMillis(date) + ((long) day) * 24 * 3600 * 1000;
		calendar.setTimeInMillis(millis);
		return calendar.getTime();
	}
	
	/**
	 * 功能描述：日期相减
	 * 
	 * @param date
	 *            Date 日期
	 * @param day
	 *            int 天数
	 * @return 返回相加后的日期
	 */
	public static Date minusDate(Date date, int day) {
		calendar = Calendar.getInstance();
		long millis = getMillis(date) - ((long) day) * 24 * 3600 * 1000;
		calendar.setTimeInMillis(millis);
		return calendar.getTime();
	}
	
	/**
	 * 功能描述：日期相减
	 * 
	 * @param date
	 *            Date 日期
	 * @param hour
	 *            int 小时
	 * @return 返回相加后的日期
	 */
	public static Date minusDateHour(Date date, int hour) {
		calendar = Calendar.getInstance();
		long millis = getMillis(date) - ((long) hour) * 3600 * 1000;
		calendar.setTimeInMillis(millis);
		return calendar.getTime();
	}

	/**
	 * 功能描述：日期相减
	 * 
	 * @param date
	 *            Date 日期
	 * @param date1
	 *            Date 日期
	 * @return 返回相减后的日期
	 */
	public static int diffDate(Date date, Date date1) {
		return (int) ((getMillis(date) - getMillis(date1)) / (24 * 3600 * 1000));
	}

	/**
	 * 功能描述：取得指定月份的第一天
	 * 
	 * @param strdate
	 *            String 字符型日期
	 * @return String yyyy-MM-dd 格式
	 */
	public static String getMonthBegin(String strdate) {
		date = parseDate(strdate);
		return format(date, "yyyy-MM") + "-01";
	}

	/**
	 * 功能描述：取得指定月份的最后一天
	 * 
	 * @param strdate
	 *            String 字符型日期
	 * @return String 日期字符串 yyyy-MM-dd格式
	 */
	public static String getMonthEnd(String strdate) {
		date = parseDate(getMonthBegin(strdate));
		calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, 1);
		calendar.add(Calendar.DAY_OF_YEAR, -1);
		return formatDate(calendar.getTime());
	}

	/**
	 * 功能描述：常用的格式化日期
	 * 
	 * @param date
	 *            Date 日期
	 * @return String 日期字符串 yyyy-MM-dd格式
	 */
	public static String formatDate(Date date) {
		return formatDateByFormat(date, "yyyy-MM-dd");
	}

	/**
	 * 功能描述：以指定的格式来格式化日期
	 * 
	 * @param date
	 *            Date 日期
	 * @param format
	 *            String 格式
	 * @return String 日期字符串
	 */
	public static String formatDateByFormat(Date date, String format) {
		String result = "";
		if (date != null) {
			try {
				SimpleDateFormat sdf = new SimpleDateFormat(format);
				result = sdf.format(date);
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
		return result;
	}
	/**
	 * @param date
	 * @param day 想要获取的日期与传入日期的差值 比如想要获取传入日期前四天的日期 day=-4即可
	 * @return
	 */
	public static Date getSomeDay(Date date, int day){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, day);
		return calendar.getTime();
	}

	/**
	 * 日期差天数、小时、分钟、秒数组
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static long[] getDisTime(Date startDate, Date endDate){
		long timesDis = Math.abs(startDate.getTime() - endDate.getTime());
		long day = timesDis / (1000 * 60 * 60 * 24);
		long hour = timesDis / (1000 * 60 * 60) - day * 24;
		long min = timesDis / (1000 * 60) - day * 24 * 60 - hour * 60;
		long sec = timesDis / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60;
		return new long[]{day, hour, min, sec};
	}

	/**
	 * 日期差天数
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static long getDisDay(Date startDate, Date endDate){
		long[] dis = getDisTime(startDate, endDate);
		long day = dis[0];
		if (dis[1] > 0 || dis[2] > 0 || dis[3] > 0) {
			day += 1;
		}
		return day;
	}
	
	/**
	  * 日期减几年
	  */
	 public static Date dateSubYears(Date date,int years){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.YEAR, -years);// 日期减years年
		 return rightNow.getTime();
	 }
	 
	 /**
	  * 日期加几年
	  */
	 public static Date dateAddYears(Date date,int years){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.YEAR, years);// 日期加years年
		 return rightNow.getTime();
	 }
	 
	 /**
	  * 日期减几月
	  */
	 public static Date dateSubMonths(Date date,int months){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.MONTH, -months);// 日期减months月
		 return rightNow.getTime();
	 }
	 
	 /**
	  * 日期加几月
	  */
	 public static Date dateAddMonths(Date date,int months){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.MONTH, months);// 日期加months月
		 return rightNow.getTime();
	 }
	 
	 /**
	  * 日期减几天
	  */
	 public static Date dateSubDays(Date date,int days){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.DAY_OF_YEAR, -days);// 日期减days天
		 return rightNow.getTime();
	 }
	 
	 /**
	  * 日期加几天
	  */
	 public static Date dateAddDays(Date date,int days){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.DAY_OF_YEAR, days);// 日期加days天
		 return rightNow.getTime();
	 }
	 
	 /**
	  * 日期减几小时
	  */
	 public static Date dateSubHours(Date date,int hours){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.HOUR, -hours);// 日期减hours小时
		 return rightNow.getTime();
	 }
	 
	 /**
	  * 日期加几小时
	  */
	 public static Date dateAddHours(Date date,int hours){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.HOUR, hours);// 日期加hours小时
		 return rightNow.getTime();
	 }
	 
	 /**
	  * 日期减几分钟
	  */
	 public static Date dateSubMinutes(Date date,int minutes){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.MINUTE, -minutes);// 日期减minutes分钟
		 return rightNow.getTime();
	 }
	 
	 /**
	  * 日期减几分钟
	  */
	 public static Date dateAddMinutes(Date date,int minutes){
		 Calendar rightNow = Calendar.getInstance();
		 rightNow.setTime(date);
		 rightNow.add(Calendar.MINUTE, minutes);// 日期加minutes分钟
		 return rightNow.getTime();
	 }

	/**
	 * 日期差文字描述
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public static String getDisTimeStr(Date startDate, Date endDate){
		long[] dis = getDisTime(startDate, endDate);
		return new StringBuilder().append(dis[0]).append("天").append(dis[1]).append("小时").append(dis[2]).append("分钟")
				.append(dis[3]).append("秒").toString();
	}

	public static Date getDate() {
		return new Date();
	}

	/**
	 * 获取每天0点
	 * @return
	 */

	public  static Date getDate000() {
		// 获取当前日期
		LocalDate currentDate = LocalDate.now();

		// 创建0:0:0的时间
		LocalTime zeroTime = LocalTime.of(0, 0, 0);

		// 将日期和时间组合起来
		LocalDateTime dateTime = LocalDateTime.of(currentDate, zeroTime);

		// 使用ZoneId获取系统默认时区
		ZoneId zoneId = ZoneId.systemDefault();

		// 转换为ZonedDateTime，它包含时区信息
		java.time.ZonedDateTime zonedDateTime = dateTime.atZone(zoneId);

		// 将ZonedDateTime转换为Date
		return Date.from(zonedDateTime.toInstant());
	}


	/**
	 * 获取每月第一天
	 * @return
	 */
	public static Date getDate1000() {
		// 获取当前日期
		LocalDate currentDate = LocalDate.now();

		// 获取当前月份的第一天
		LocalDate firstDayOfMonth = currentDate.withDayOfMonth(1);

		// 转换为LocalDateTime并设置时间为00:00:00
		LocalDateTime dateTime = LocalDateTime.of(firstDayOfMonth, LocalTime.MIDNIGHT);

		// 格式化日期时间为字符串
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		String format = dateTime.format(formatter);

		return parseDate(format);
	}




	public static int calculateMinuteOfDay(LocalTime time) {
		return (int) ChronoUnit.MINUTES.between(LocalTime.MIDNIGHT, time);
	}

	public static int calculateMinuteOfDate(Date time) {
		return  calculateMinuteOfDay(convertDateToLocalTime(time));
	}


	/**
	 * Date时间转LocalTime
	 * @param date
	 * @return
	 */
	public static LocalTime convertDateToLocalTime(Date date) {
		// 使用系统默认时区将Date转换为ZonedDateTime
		ZonedDateTime zonedDateTime = date.toInstant().atZone(ZoneId.systemDefault());
		// 从ZonedDateTime中提取LocalTime
		return zonedDateTime.toLocalTime();
	}

	/**
	 * 每天多少分钟转LocalTime
	 * @param minuteOfDay
	 * @return
	 */
	public static LocalTime convertMinuteOfDayToTime(int minuteOfDay) {
		if (minuteOfDay < 0 || minuteOfDay >= 24 * 60) {
			throw new IllegalArgumentException("分钟数必须在0到1440之间（包含）");
		}
		int hours = minuteOfDay / 60;
		int minutes = minuteOfDay % 60;
		return LocalTime.of(hours, minutes);
	}


	/**
	 * 每天多少分钟转 时间字符 hh：mm
	 * @param minuteOfDay
	 * @return
	 */
	public static String convertMinuteOfStringToTime(int minuteOfDay) {
		if (minuteOfDay < 0 || minuteOfDay >= 24 * 60) {
			throw new IllegalArgumentException("分钟数必须在0到1440之间（包含）");
		}
		int hours = minuteOfDay / 60;
		int minutes = minuteOfDay % 60;

		return (hours <10 ? "0"+hours:hours)+":"+(minutes <10 ? "0"+minutes:minutes)+":00";
	}

//	public static void main(String[] args) {
//		String localTime = getTimeByMinuteFormatted(1400);
//		System.out.println("该时间是一天中的第 " + localTime + " 分钟");
//	}

	/**
	 *  时间字符 hh：mm 转 每天多少分钟
	 * @param minuteOfDay
	 * @return
	 */
	public static int convertMinuteToString(String minuteOfDay) {
		String[] split = minuteOfDay.split(":");
		int i = calculateMinuteOfDay(LocalTime.of(Integer.valueOf(split[0]),Integer.valueOf(split[1])));
		return i;
	}


	public static String getTimeByMinuteFormatted(int minuteOfDay) {
		// 检查分钟数是否在0到1439之间
		if (minuteOfDay < 0 || minuteOfDay > 1439) {
			throw new IllegalArgumentException("Minute of day must be between 0 and 1439.");
		}
		// 获取当前日期
		LocalDateTime currentDate = LocalDateTime.now().toLocalDate().atStartOfDay();
		// 计算当天该分钟数对应的时间
		LocalDateTime timeByMinute = currentDate.plusMinutes(minuteOfDay);
		// 定义日期时间格式
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
		// 格式化时间
		String formattedTime = timeByMinute.format(formatter);
		return formattedTime;
	}

	/**
	 * 获取月的第一天
	 * @param month
	 * @return
	 */
	public static Date getFirstDayOfMonth(Date month) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(month);
		calendar.set(Calendar.DAY_OF_MONTH, 1); // 设置为当月的第一天
		calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
		calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
		calendar.set(Calendar.SECOND, 0); // 设置秒为0
		calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0
		return calendar.getTime();
	}

	/**
	 * 获取月的最后一天
	 * @param month
	 * @return
	 */
	public static Date getLastDayOfMonth(Date month) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(month);

		// 获取下个月的第一天
		calendar.add(Calendar.MONTH, 1);
		calendar.set(Calendar.DAY_OF_MONTH, 1);

		// 减去一天得到当前月的最后一天
		calendar.add(Calendar.DAY_OF_MONTH, -1);

		// 设置时间为 23:59:59
		calendar.set(Calendar.HOUR_OF_DAY, 23);
		calendar.set(Calendar.MINUTE, 59);
		calendar.set(Calendar.SECOND, 59);
		calendar.set(Calendar.MILLISECOND, 999);

		return calendar.getTime();
	}
}
