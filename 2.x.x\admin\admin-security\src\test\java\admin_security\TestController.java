package admin_security;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;


import junit.framework.Assert;

/**
 * 
 * @类名: TestController.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年4月28日下午4:44:11
 * 修改记录：
 *
 * @see
 */
@RunWith(SpringJUnit4ClassRunner.class)  
@WebAppConfiguration  
@ContextConfiguration(locations = {"classpath:/testApplicationContext-common.xml", 
		                           "classpath:resources/spring-mvc.xml" })
public class TestController {
	    @Autowired  
	    //private OperatorController operatorController;//引入要测试的controller  
	    
	    private MockMvc mockMvc;  
	    
	    @Test  
	    @Rollback(false) //插入需要此注解，不然插不进数据库  
	    public void login(){  
	        String result = "" ;  
	        try {  
	            ResultActions ra = this.mockMvc.perform(MockMvcRequestBuilders.post("/operatorController/login")  
	                    .param("loginName", "hhj").param("password", "423545erwrw").param("shopid", "9562ed5d02b348d1bfec1699ba1df36b")  
	                    .param("commentuserid", "d07136c725754cd7827a35581d4a4456").param("consumption", "20.5")  
	                    .param("url", "comment/images")  
	                    .param("palate", "4").param("environment", "4").param("service", "4")  
	                    //.param("key", "[{id:'',imagename:'a1',imageupdatename:'',url:'a1',commentid:'',createdate:''},{id:'',imagename:'a2',imageupdatename:'',url:'a2',commentid:'',createdate:''}]")//前台应出入json格式  
	                    .param("imagename", "袋鼠,兔子")//前台传入逗号分割的字符串  
	                    .param("recommends", "蛋炒西红柿,橙汁")  
	                    );  
	            MvcResult mr = ra.andReturn();  
	            result = mr.getResponse().getContentAsString();  
	            System.out.println("+++++++++++"+result);  
	        } catch (Exception e) {  
	            Assert.fail("fail");  
	            e.printStackTrace();  
	        }  
	          
	    }  	    
	    
}
