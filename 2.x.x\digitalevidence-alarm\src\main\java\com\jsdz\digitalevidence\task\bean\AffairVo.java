package com.jsdz.digitalevidence.task.bean;

import java.util.List;

/**
 * 待办已办事件
 */
public class AffairVo {

    /**
     * 时间id
     */
    private Long id;

    /**
     * 事件类型
     */
    private String type;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;
    /**
     * 等级
     */
    private Integer level;
    /**
     * 时间
     */
    private String time;
    /**
     * 地点
     */
    private String address;
    /**
     * 机构名
     */
    private String orgName;
    /**
     * 来源
     */
    private String source;
    /**
     * 状态
     */
    private String status;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 警情编号
     */
    private String alarmCode;

    /**
     * 执法仪警员定位信息
     */
    List<AffairRecorderBean> recorderBeans;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<AffairRecorderBean> getRecorderBeans() {
        return recorderBeans;
    }

    public void setRecorderBeans(List<AffairRecorderBean> recorderBeans) {
        this.recorderBeans = recorderBeans;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getAlarmCode() {
        return alarmCode;
    }

    public void setAlarmCode(String alarmCode) {
        this.alarmCode = alarmCode;
    }
}
