package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

/**
 * 
 * @类名: AssessmentWorkStatisticsBean
 * @说明: 考核工作统计Bean
 *
 * <AUTHOR>
 * @Date	 2017年12月5日 上午9:33:41
 * 修改记录：
 *
 * @see
 */
public class AssessmentWorkStatisticsBean {
	private Integer id;//警员id
	private String name;//警员名称
	private Integer countOfChecking;//考核抽查数
	private Integer countOfTotalAssessing;//考核总数
	private Integer countOfPushAssessing;//考核推送数
	private Integer countOfPushed;//收到推送数 
	private Date startDate;//开始时间
	private Date endDate;//结束时间
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getCountOfChecking() {
		return countOfChecking;
	}
	public void setCountOfChecking(Integer countOfChecking) {
		this.countOfChecking = countOfChecking;
	}
	public Integer getCountOfTotalAssessing() {
		return countOfTotalAssessing;
	}
	public void setCountOfTotalAssessing(Integer countOfTotalAssessing) {
		this.countOfTotalAssessing = countOfTotalAssessing;
	}
	public Integer getCountOfPushAssessing() {
		return countOfPushAssessing;
	}
	public void setCountOfPushAssessing(Integer countOfPushAssessing) {
		this.countOfPushAssessing = countOfPushAssessing;
	}
	public Integer getCountOfPushed() {
		return countOfPushed;
	}
	public void setCountOfPushed(Integer countOfPushed) {
		this.countOfPushed = countOfPushed;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
}
