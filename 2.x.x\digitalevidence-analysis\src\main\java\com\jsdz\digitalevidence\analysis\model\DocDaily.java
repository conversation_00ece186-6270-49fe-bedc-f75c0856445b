package com.jsdz.digitalevidence.analysis.model;
/**
 * 
 * @类名: DocDaily
 * @说明: 文档日结实体类
 *
 * @author: kenny
 * @Date	2017年5月30日下午6:03:37
 * 修改记录：
 *
 * @see
 */

import java.io.Serializable;
import java.util.Date;

import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.model.ImportantLevel;

public class DocDaily implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -8721556654252538300L;
	private Long id;
	private Date dateFrom; // 日期
	private Date dateTo; // 日期
	private DailyType dailyType; //日结/月结/年结
	private DailyObject dailyObject; //统计项目
	private Long objectId; //项目ID
	private Long fileSize; // 文件长度;
	private Long fileCount; //文件总数
	private DocumentCate cate;//分类
	private ImportantLevel impLevel;//等级
	private Long vedioCount; //视频文件总数
	private Long duration; // 视频时长;
	private Long hdDuration; //高清时长（包括超清和高清）
	private Long alarmCunt; //警情关联数
	private Long caseCunt; //案件关联数
	private Long qualifyCount;//合格数
	

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public DailyObject getDailyObject() {
		return dailyObject;
	}
	public void setDailyObject(DailyObject dailyObject) {
		this.dailyObject = dailyObject;
	}
	public Long getFileCount() {
		return fileCount;
	}
	public void setFileCount(Long fileCount) {
		this.fileCount = fileCount;
	}
	public DocumentCate getCate() {
		return cate;
	}
	public void setCate(DocumentCate cate) {
		this.cate = cate;
	}
	public ImportantLevel getImpLevel() {
		return impLevel;
	}
	public void setImpLevel(ImportantLevel impLevel) {
		this.impLevel = impLevel;
	}
	public Long getObjectId() {
		return objectId;
	}
	public void setObjectId(Long objectId) {
		this.objectId = objectId;
	}
	public Long getFileSize() {
		return fileSize;
	}
	public void setFileSize(Long fileSize) {
		this.fileSize = fileSize;
	}
	public Date getDateFrom() {
		return dateFrom;
	}
	public void setDateFrom(Date dateFrom) {
		this.dateFrom = dateFrom;
	}
	public Date getDateTo() {
		return dateTo;
	}
	public void setDateTo(Date dateTo) {
		this.dateTo = dateTo;
	}
	public DailyType getDailyType() {
		return dailyType;
	}
	public void setDailyType(DailyType dailyType) {
		this.dailyType = dailyType;
	}
	public Long getVedioCount() {
		return vedioCount;
	}
	public void setVedioCount(Long vedioCount) {
		this.vedioCount = vedioCount;
	}
	public Long getDuration() {
		return duration;
	}
	public void setDuration(Long duration) {
		this.duration = duration;
	}
	public Long getHdDuration() {
		return hdDuration;
	}
	public void setHdDuration(Long hdDuration) {
		this.hdDuration = hdDuration;
	}
	public Long getAlarmCunt() {
		return alarmCunt;
	}
	public void setAlarmCunt(Long alarmCunt) {
		this.alarmCunt = alarmCunt;
	}
	public Long getCaseCunt() {
		return caseCunt;
	}
	public void setCaseCunt(Long caseCunt) {
		this.caseCunt = caseCunt;
	}
	public Long getQualifyCount() {
		return qualifyCount;
	}
	public void setQualifyCount(Long qualifyCount) {
		this.qualifyCount = qualifyCount;
	}
	
	
}
