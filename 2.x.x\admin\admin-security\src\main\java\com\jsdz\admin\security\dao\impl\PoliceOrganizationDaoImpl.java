package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: PoliceOrganizationDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2019-12-19 15:29:12
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.PoliceOrganizationDao;
import com.jsdz.admin.security.model.PoliceOrganization;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class PoliceOrganizationDaoImpl extends GenericEntityDaoHibernateImpl<PoliceOrganization,Long> implements PoliceOrganizationDao{

	/** 
 	 * 新增
	 */ 
 	public void addPoliceOrganization(PoliceOrganization policeOrganization) {
		this.saveOrUpdate(policeOrganization);
	}

	/** 
 	 * 删除
	 */ 
 	public void deletePoliceOrganization(PoliceOrganization policeOrganization) {
		this.delete(policeOrganization);
	}

	/** 
 	 * 修改
	 */ 
 	public void updatePoliceOrganization(PoliceOrganization policeOrganization) {
		this.merge(policeOrganization);
	}

	/** 
 	 * 按id查询(游离状态)
	 */ 
 	public PoliceOrganization findPoliceOrganizationById(Long id){

		final String  hql = "from PoliceOrganization p where p.id = :id";
		final Long oid = id;
		PoliceOrganization data = getHibernateTemplate().execute(new HibernateCallback<PoliceOrganization>() {
			public PoliceOrganization doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<PoliceOrganization> list = query.list();
				PoliceOrganization rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 按id查询
	 */ 
 	public PoliceOrganization locatePoliceOrganizationById(Long id){

		final String  hql = "from PoliceOrganization p where p.id = :id";
		final Long oid = id;
		PoliceOrganization data = getHibernateTemplate().execute(new HibernateCallback<PoliceOrganization>() {
			public PoliceOrganization doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<PoliceOrganization> list = query.list();
				PoliceOrganization rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 单个查询(根据其它字段查询)
	 */ 
 	public PoliceOrganization findPoliceOrganizationByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		PoliceOrganization data = getHibernateTemplate().execute(new HibernateCallback<PoliceOrganization>() {
		public PoliceOrganization doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			query.setMaxResults(1);
			List<PoliceOrganization> list = query.list();
			PoliceOrganization rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<PoliceOrganization> findAllPoliceOrganizations(){
		return this.find("from PoliceOrganization policeOrganization ");
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<PoliceOrganization> findPoliceOrganizationsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<PoliceOrganization> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<PoliceOrganization> findPoliceOrganizationsOnPage(Page<PoliceOrganization> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<PoliceOrganization>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	/** 
 	 * 执行自定义的hql
	 */ 
 	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
}
