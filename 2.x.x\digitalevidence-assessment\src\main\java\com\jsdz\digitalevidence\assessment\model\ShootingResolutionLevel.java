package com.jsdz.digitalevidence.assessment.model;

/**
 * 
 * @类名: ShootingResolutionLevel
 * @说明: 拍摄分辨率
 *
 * <AUTHOR>
 * @Date	 2017年4月13日 下午5:46:34
 * 修改记录：
 *
 * @see
 */
public enum ShootingResolutionLevel {
	
	HIGH(1, "高"), 
	GENERAL(2, "中"),
	LOW(3, "低"); 
	
	public int index;
	public String name;
	
	private ShootingResolutionLevel(int index, String name) {
		this.name = name;
	}
	public int getIndex() {
		return index;
	}
	public void setIndex(int index) {
		this.index = index;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
}
