package com.jsdz.digitalevidence.activemq.service;

/**
 * 
 * @类名: SysLogTopicService
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-03-15 14:58:15
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.activemq.model.SysLogTopic;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface SysLogTopicService {

	//新增
	public AjaxResult addSysLogTopic(SysLogTopic sysLogTopic);

	//修改
	public AjaxResult updateSysLogTopic(SysLogTopic sysLogTopic);

	//删除
	public AjaxResult deleteSysLogTopic(SysLogTopic sysLogTopic);

	//按id查询,结果是游离状态的数据
	public SysLogTopic findSysLogTopicById(Long id);

	//按id查询
	public SysLogTopic locateSysLogTopicById(Long id);

	//单个查询
	public SysLogTopic findSysLogTopicByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<SysLogTopic> findAllSysLogTopics();

	//列表查询
	public List<SysLogTopic> findSysLogTopicsByParam(String queryStr,String[] paramNames,Object[] values);

/*	//分页查询
	public Page<SysLogTopic> findSysLogTopicsOnPage(Page<SysLogTopic> page, String queryStr,String[] paramNames,Object[] values);
*/
}

