package com.jsdz.digitalevidence.alarm.alarm110.service;

/**
 * 
 * @类名: AlarmFeedbackService
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-08-28 11:11:14
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmFeedback;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface AlarmFeedbackService {

	//新增
	public AjaxResult addAlarmFeedback(AlarmFeedback alarmFeedback);

	//修改
	public AjaxResult updateAlarmFeedback(AlarmFeedback alarmFeedback);

	//删除
	public AjaxResult deleteAlarmFeedback(AlarmFeedback alarmFeedback);

	//按id查询,结果是游离状态的数据
	public AlarmFeedback findAlarmFeedbackById(Long id);

	//按id查询
	public AlarmFeedback locateAlarmFeedbackById(Long id);

	//单个查询
	public AlarmFeedback findAlarmFeedbackByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmFeedback> findAllAlarmFeedbacks();

	//列表查询
	public List<AlarmFeedback> findAlarmFeedbacksByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmFeedback> findAlarmFeedbacksOnPage(Page<AlarmFeedback> page, String queryStr,String[] paramNames,Object[] values);

}

