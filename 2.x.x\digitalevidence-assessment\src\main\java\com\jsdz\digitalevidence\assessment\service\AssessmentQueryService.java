/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.service;

import java.util.Date;


import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentQueryBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentReportBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentReportQueryBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentWorkStatisticsBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumQueryBean;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;

/**
 * @类名: AssessmentQueryService
 * @说明: 拍摄考核查询服务
 *        1. 查询当月考核概况
 *        2. 查询警员考核明细，当月，上月，。。。， 一季度，。。。
 *        3. 查询警员考核资料
 *        4. 
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午6:04:13
 * 修改记录：
 *
 * @see 	 
 */
public interface AssessmentQueryService {

	/**
	 * @说明：查询拍摄考核概况
	 *
	 * <AUTHOR>
	 * @param qb
	 * @return
	 * @throws Exception 
	 * 
	 */
	public Page<ShootingAssessmentSumBean> queryShootingAssessmentSummary(Page<ShootingAssessmentSumBean> page,
			ShootingAssessmentSumQueryBean qb) throws Exception;

	/**
	 * @说明：查看警员考核报告
	 *
	 * <AUTHOR>
	 * @param page
	 * @param policeCode
	 * @param startDate  年/月
	 * @param endDate
	 * @return
	 * 
	 */
	public ShootingAssessmentReport getAssessmentReport(
			Long policeId,
			Date cycleDate);
	public Page<ShootingAssessmentReport> queryAssessmentReport(
			Page<ShootingAssessmentReport> page,
			Long policeId,
			Date startDate, Date endDate);

	/**
	 * @说明：查询警员考核资料, 周期
	 *
	 * <AUTHOR>
	 * @param qb
	 * @return
	 * @throws Exception 
	 * 
	 */
	public Page<AssessmentDocumentBean> queryAssessmentDocument(Page<AssessmentDocumentBean> page,
			AssessmentDocumentQueryBean qb,Long planId,Long policeId) throws Exception;

	/**
	 * @说明：查询警员考核简要报告，考核员
	 *
	 * <AUTHOR>
	 * @param reportId
	 * @return
	 * @throws Exception 
	 * 
	 */
	public Page<AssessmentReportBean> queryShootingAccessmentReport(Page<AssessmentReportBean> page, 
			AssessmentReportQueryBean qb) throws Exception;

	/**
	 * @说明：查找我的考核简要报告
	 *
	 * <AUTHOR>
	 * @param page
	 * @param cycleDate
	 * @param selector
	 * @return
	 * @throws Exception 
	 * 
	 */
	public Page<AssessmentReportBean> queryMyShootingAccessmentReport(Page<AssessmentReportBean> page, 
			Date startDate,
			Date endDate) throws Exception;
	/**
	 * 
	 * @说明：考核工作统计方法
	 *
	 * <AUTHOR>
	 * @param page
	 * @param paramNames
	 * @param values
	 * @return
	 *
	 */
	public Page<AssessmentWorkStatisticsBean> searchWorkStatisticsBean(Page<AssessmentWorkStatisticsBean> page, String[] paramNames, Object[] values);
}
