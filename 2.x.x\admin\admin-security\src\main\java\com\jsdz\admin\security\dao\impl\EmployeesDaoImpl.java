package com.jsdz.admin.security.dao.impl;

import java.sql.SQLException;
import java.util.Date;

/**
 *
 * @类名: EmployeesDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-13 13:48:18
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.EmployeesDao;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.utils.HqlConst;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class EmployeesDaoImpl extends GenericEntityDaoHibernateImpl<Employees,Long> implements EmployeesDao{

	//新增
	public void addEmployees(Employees employees) {
		this.saveOrUpdate(employees);
	}

	//删除
	public void deleteEmployees(Employees employees) {
		this.delete(employees);
	}

	//修改
	public void updateEmployees(Employees employees) {
		this.saveOrUpdate(employees);
	}

	//按id查询(游离状态)
	public Employees findEmployeesById(Long id){

		final String  hql = "from Employees e where e.isDeleted = false and e.id = :id";
		final Long oid = id;
		Employees data = getHibernateTemplate().execute(new HibernateCallback<Employees>() {
			public Employees doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<Employees> list = query.list();
				Employees rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}
	
	//按id查询
	public Employees locateEmployeesById(Long id){

		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery("from Employees o where o.isDeleted = false and o.id=:id");
		query.setParameter("id", id);
		List<Employees> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	
	@Override
	public Employees locateEmployeesByWorkNumber(String workNumber) {
		String hql = "from Employees e where e.isDeleted = false and e.workNumber = :workNumber";
		List<Employees> ps = this.findByNamedParam(hql, new String[]{"workNumber"}, new Object[]{workNumber});
		if (ps != null && ps.size() > 0)
			return ps.get(0);
		else
			return null;
	}

	//单个查询
	public Employees findEmployeesByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		Employees data = getHibernateTemplate().execute(new HibernateCallback<Employees>() {
		public Employees doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<Employees> list = query.list();
			Employees rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
		
		
	}

	//查询全部
	public List<Employees> findAllEmployeess(){
		List<Employees> list =  this.find(HqlConst.EMPLOYEES_COMM + " where  e.isDeleted = false ");
		return list;
	}

	@Override
	public List<Employees> findAllEmployeess(Long orgId){
		List<Employees> list =  this.find(HqlConst.EMPLOYEES_COMM + " where  e.isDeleted = false and o.id = "+orgId);
		return list;
	}

	//列表查询
	public List<Employees> findEmployeessByCondition(String queryStr,String[] paramNames,Object[] values){
		List<Employees> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<Employees> findEmployeessOnPage(Page<Employees> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<Employees>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	//按岗位查询
	public List<Employees> findEmployeessByPostId(Long id){
		String queryStr = HqlConst.EMPLOYEES_COMM + " where e.isDeleted = false and s.id=:id";
		return this.findByNamedParam(queryStr,new String[]{"id"},new Object[]{id});
	}
	
	
	/* 
	 * 
	 **/
	public Employees setDeleteFlag(Employees employees){
		Date now = new Date();
		Employees employees1 = this.get(employees.getId());
		employees1.setIsDeleted(true);
		employees1.setUpdateTime(now);
		employees1.setDeleteTime(now);
		employees1.setDeleteBy(employees.getDeleteBy());
		employees1.setDeleteCode(employees1.getWorkNumber());
		employees1.setWorkNumber("DELETE_"+ employees1.getWorkNumber());
		employees1.setIdNum("DELETE_"+employees1.getIdNum());
		this.merge(employees1);
		return employees1;
	}

}
