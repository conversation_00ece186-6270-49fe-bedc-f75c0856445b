package com.jsdz.digitalevidence.alarm.alarm110.bean;

import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;

public class AlarmApiVo {

    private String alarmCode;    //警情号

    private String alarmContext;    //报警内容

    private String alarmPolices;    //处警人，多个人以,号隔开

    private Integer status;     // 警情状态  0、未出警 1、已出警 2、结束出警

    private int qr; // 是否发送二维码

    private String token; //验证token

    private String base64;

    public String getAlarmCode() {
        return alarmCode;
    }

    public void setAlarmCode(String alarmCode) {
        this.alarmCode = alarmCode;
    }

    public String getAlarmContext() {
        return alarmContext;
    }

    public void setAlarmContext(String alarmContext) {
        this.alarmContext = alarmContext;
    }

    public String getAlarmPolices() {
        return alarmPolices;
    }

    public void setAlarmPolices(String alarmPolices) {
        this.alarmPolices = alarmPolices;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public int getQr() {
        return qr;
    }

    public void setQr(int qr) {
        this.qr = qr;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getBase64() {
        return base64;
    }

    public void setBase64(String base64) {
        this.base64 = base64;
    }

    public AlarmApiVo sign(AlarmInfo info) {
        this.alarmCode = info.getAlarmCode();
        this.alarmContext = info.getAlarmContext();
        this.alarmPolices = info.getAlarmPolices();
        this.status = info.getStatus();
        return this;
    }
}
