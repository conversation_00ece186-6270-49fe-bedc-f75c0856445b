package com.jsdz.admin.security.service;

/**
 * 
 * @类名: OperatorRoleService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-03 14:48:29
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import java.util.Map;

import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface OperatorRoleService {

	//新增
	public AjaxResult addOperatorRole(OperatorRole operatorRole);

	//修改
	public AjaxResult updateOperatorRole(OperatorRole operatorRole);

	//删除
	public AjaxResult deleteOperatorRole(OperatorRole operatorRole);
	
	//查单个
	public OperatorRole findOperatorRoleByParam(String queryStr,String[] paramNames,Object[] values);

	//查询列表
	public List<OperatorRole> findOperatorRolesByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<OperatorRole> findOperatorRolesOnPage(Page<OperatorRole> page, String queryStr,String[] paramNames,Object[] values);

	//按用户查询所有的角色
	public List<OperatorRole> findOperatorRolesByOperator(Operator operator);
	
	//批量设置用户角色
	public AjaxResult updateBatchOperatorRole(Long oid,Long[] rid,Operator createBy);
	
	//查询与该操作员关联的角色
	public List<OperatorRole> findRoleByOperator(Operator operator)	;
	
	//查询最高级别的权限
	public Map<String,Object> findMaxPermission(Long userId);
}

