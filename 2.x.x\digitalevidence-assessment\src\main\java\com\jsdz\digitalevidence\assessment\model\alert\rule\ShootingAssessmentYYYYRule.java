package com.jsdz.digitalevidence.assessment.model.alert.rule;

import java.util.ArrayList;
import java.util.List;

import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.ruleengine.RuleState;
import com.jsdz.ruleengine.annotation.Given;
import com.jsdz.ruleengine.annotation.Param;
import com.jsdz.ruleengine.annotation.Result;
import com.jsdz.ruleengine.annotation.Rule;
import com.jsdz.ruleengine.annotation.Then;
import com.jsdz.ruleengine.annotation.When;

/**
 * @类名: ShootingAssessmentYYYYRule
 * @说明: 拍摄考核按要求运用语言比规则
                     
 *
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 
 * 修改记录：
 *
 * @see
 */
@Rule(name="yyyyPercent", description="按要求运用语言百分比规则")
public class ShootingAssessmentYYYYRule extends ShootingAssessmentRuleBase {
	
	public static final String key = "按要求运用语言百分比";

	@Param(name="yyyyPercent", description="按要求运用语言百分比")
	private float yyyyPercent;
	
	/** */
	@Result
	private List<AssessmentAlertItem<?>> _result;
	
	@Given
	private ShootingAssessmentSummary sum;
	
	@When
	public boolean when() {
	    if((float)sum.getCountOfYYYY()/sum.getTotalAssessingDoc()<yyyyPercent)
	    	return true;
		return false;
	}

	@Then
	public RuleState then() {
		AssessmentAlertItem<Float> item = new AssessmentAlertItem<Float>();
		item.setItem(key);
		item.setV((float)sum.getCountOfYYYY()/sum.getTotalAssessingDoc());
		item.setRef(yyyyPercent);
		if(_result==null)
			_result = new ArrayList<AssessmentAlertItem<?>>();
		_result.add(item);
		return RuleState.NEXT;
	}
	
}
