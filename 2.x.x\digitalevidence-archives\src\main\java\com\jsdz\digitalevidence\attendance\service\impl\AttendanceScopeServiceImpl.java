package com.jsdz.digitalevidence.attendance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.digitalevidence.attendance.mapper.AttendanceInfoMapper;
import com.jsdz.digitalevidence.attendance.mapper.AttendanceScopeMapper;
import com.jsdz.digitalevidence.attendance.model.AttendanceScopeModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceTaskModel;
import com.jsdz.digitalevidence.attendance.service.IAttendanceScopeService;
import com.jsdz.digitalevidence.utils.SqlUtils;
import com.jsdz.digitalevidence.utils.StringUtils;
import com.jsdz.digitalevidence.utils.response.ResponseUtil;
import com.jsdz.digitalevidence.utils.response.Result;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Service
public class AttendanceScopeServiceImpl  extends ServiceImpl<AttendanceScopeMapper,AttendanceScopeModel> implements IAttendanceScopeService {
    private final Logger logger = Logger.getLogger(this.getClass());
    @Resource
    private AttendanceScopeMapper attendanceScopeMapper;
    //设置表名
    private String tableName = "sys_attendance_scope";
    SqlUtils sqlUtils = new SqlUtils<AttendanceScopeModel>();
    @Override
    public Result getDatas(String taskId, PerLvlBean permLvl) {
        String dataSql  = " select t.*";
        String whereSql = " from "+tableName+" t where 1=1";
        //分页
        String orderSql = " order by coordinateOrder";
        //其他调教查询补充
        if(StringUtils.isNoBlank(taskId)){
            whereSql += " and taskId = "+taskId;
        }
        logger.info(dataSql + whereSql + orderSql );
        List<AttendanceScopeModel> datas = attendanceScopeMapper.getDatas(dataSql + whereSql + orderSql );
        return ResponseUtil.ok(datas);
    }


    @Override
    public List<AttendanceScopeModel> getDatas(Long taskId) {
        String dataSql  = " select t.*";
        String whereSql = " from "+tableName+" t ";
        //分页
        String orderSql = " order by coordinateOrder";
        //其他调教查询补充
        if(StringUtils.isNoBlank(taskId)){
            whereSql += " where taskId = "+taskId;
        }
        logger.info(dataSql + whereSql + orderSql );
        return attendanceScopeMapper.getDatas(dataSql + whereSql + orderSql );
    }

    @Override
    public void saveData(AttendanceScopeModel model, PerLvlBean permLvl) {
        attendanceScopeMapper.insert(model);
        //attendanceScopeMapper.saveData(SqlUtils.saveSql(model,tableName));
    }

    @Override
    public void updateDataById(AttendanceScopeModel model, PerLvlBean permLvl) {
        attendanceScopeMapper.updateById(model);
    }

    @Override
    public AttendanceScopeModel getById(AttendanceScopeModel model, PerLvlBean permLvl) {
        return attendanceScopeMapper.selectById(model.getId());
    }

    @Override
    public void deleteById(AttendanceScopeModel model, PerLvlBean permLvl) {
        attendanceScopeMapper.deleteById(sqlUtils.deleteByIdSql(model,tableName,"id"));
    }

    @Override
    public void deleteByTaskId(AttendanceScopeModel model, PerLvlBean permLvl) {
//        AttendanceScopeModel model = new AttendanceScopeModel();
//        model.setTaskId(taskId);
//        String taskId1 = sqlUtils.deleteByIdSql(model, tableName, "taskId");
//        logger.info(taskId1);
        attendanceScopeMapper.deleteById(sqlUtils.deleteByIdSql(model,tableName,"taskId"));
    }

    @Override
    public void deleteByTaskId(Long taskId, PerLvlBean permLvl) {
        AttendanceScopeModel model = new AttendanceScopeModel();
        model.setTaskId(taskId);
//        String taskId1 = sqlUtils.deleteByIdSql(model, tableName, "taskId");
//        logger.info(taskId1);
        attendanceScopeMapper.deleteById(sqlUtils.deleteByIdSql(model,tableName,"taskId"));
    }
}
