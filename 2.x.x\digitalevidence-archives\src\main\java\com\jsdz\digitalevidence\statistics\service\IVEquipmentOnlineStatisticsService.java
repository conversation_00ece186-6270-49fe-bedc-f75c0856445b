package com.jsdz.digitalevidence.statistics.service;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.model.VEquipmentOnlineStatisticsModel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jsdz.digitalevidence.statistics.bean.param.VEquipmentOnlineStatisticsParam;

/**
 * <p>
 * VIEW 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface IVEquipmentOnlineStatisticsService extends IService<VEquipmentOnlineStatisticsModel> {

    Page<VEquipmentOnlineStatisticsModel> getDatas(VEquipmentOnlineStatisticsParam param, PerLvlBean permLvl);
}
