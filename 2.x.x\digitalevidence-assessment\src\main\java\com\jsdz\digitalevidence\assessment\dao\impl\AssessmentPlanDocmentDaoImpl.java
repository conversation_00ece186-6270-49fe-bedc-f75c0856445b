package com.jsdz.digitalevidence.assessment.dao.impl;

import java.util.Date;

/**
 *
 * @类名: AssessmentPlanDocmentDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2019-06-19 16:05:25
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanDocmentDao;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanDocment;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class AssessmentPlanDocmentDaoImpl extends GenericEntityDaoHibernateImpl<AssessmentPlanDocment,Long> implements AssessmentPlanDocmentDao{

	/** 
 	 * 新增
	 */ 
 	public void addAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment) {
		this.saveOrUpdate(assessmentPlanDocment);
	}

	/** 
 	 * 删除
	 */ 
 	public void deleteAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment) {
		this.delete(assessmentPlanDocment);
	}

	/** 
 	 * 修改
	 */ 
 	public void updateAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment) {
		this.merge(assessmentPlanDocment);
	}

	/** 
 	 * 按id查询(游离状态)
	 */ 
 	public AssessmentPlanDocment findAssessmentPlanDocmentById(Long id){

		final String  hql = "from AssessmentPlanDocment a where a.id = :id";
		final Long oid = id;
		AssessmentPlanDocment data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlanDocment>() {
			public AssessmentPlanDocment doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AssessmentPlanDocment> list = query.list();
				AssessmentPlanDocment rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlanDocment locateAssessmentPlanDocmentById(Long id){

		final String  hql = "from AssessmentPlanDocment a where a.id = :id";
		final Long oid = id;
		AssessmentPlanDocment data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlanDocment>() {
			public AssessmentPlanDocment doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AssessmentPlanDocment> list = query.list();
				AssessmentPlanDocment rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 单个查询(根据其它字段查询)
	 */ 
 	public AssessmentPlanDocment findAssessmentPlanDocmentByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		AssessmentPlanDocment data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlanDocment>() {
		public AssessmentPlanDocment doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<AssessmentPlanDocment> list = query.list();
			AssessmentPlanDocment rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanDocment> findAllAssessmentPlanDocments(){
		return this.find("from AssessmentPlanDocment assessmentPlanDocment ");
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanDocment> findAssessmentPlanDocmentsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<AssessmentPlanDocment> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanDocment> findAssessmentPlanDocmentsOnPage(Page<AssessmentPlanDocment> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<AssessmentPlanDocment>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	/** 
 	 * 执行自定义的hql
	 */ 
 	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
 	
	/* 随机取得DOC数据
	 * @see com.jsdz.digitalevidence.assessment.dao.AssessmentPlanDocmentDao#getRadomDoc(java.lang.Long)
	 */
	@Override
	public List<Object> getRadomDoc(Date time1,Date time2,Integer num){
		final String sql = 
				"select t.doc_id,t.doc_name,t.create_time,t.upload_time,e.workNumber,e.name,"
				+ " o.orgCode,o.orgName "
				+ " from t_doc t "
				+ " left join admin_employees e on e.id = t.POLICE_ID "
				+ " left join admin_organization o on o.id = t.organization_id "
				+ " where t.upload_time >= :time1 "
				+ "   and t.upload_time <= :time2 "
				+ " order by RAND() limit " + num.toString();
		List<Object> data = getHibernateTemplate().execute(new HibernateCallback<List<Object>>() {
			@SuppressWarnings("unchecked")
			@Override
			public List<Object> doInHibernate(Session session) throws HibernateException, SQLException {
				// 获取查询对象
				Query queryObject = session.createSQLQuery(sql);
				// 置入参数
				applyNamedParameterToQuery(queryObject, "time1", time1);
				applyNamedParameterToQuery(queryObject, "time2", time2);
				return queryObject.list();
			}
		});
		return data;

	}
}
