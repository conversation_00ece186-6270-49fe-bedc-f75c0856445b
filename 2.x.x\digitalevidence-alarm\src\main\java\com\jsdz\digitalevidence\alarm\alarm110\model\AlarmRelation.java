package com.jsdz.digitalevidence.alarm.alarm110.model;

import java.io.Serializable;
/**
 * 
 * @类名: AlarmRelation
 * @说明: 警情和音视频关联
 *
 * @author: kenny
 * @Date	2017年8月29日下午7:40:52
 * 修改记录：
 *
 * @see
 */
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.digitalevidence.document.model.Document;

public class AlarmRelation implements Serializable , Cloneable{
	
	private static final long serialVersionUID = -772575428879462176L;
	
	private Long id;
	private AlarmInfo alarmInfo;//警情
	private Document mediaInfo;//音视频
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date relationTime; //关联时间
    private Operator operator;    //操作员
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public AlarmInfo getAlarmInfo() {
		return alarmInfo;
	}
	public void setAlarmInfo(AlarmInfo alarmInfo) {
		this.alarmInfo = alarmInfo;
	}
	public Document getMediaInfo() {
		return mediaInfo;
	}
	public void setMediaInfo(Document mediaInfo) {
		this.mediaInfo = mediaInfo;
	}
	public Date getRelationTime() {
		return relationTime;
	}
	public void setRelationTime(Date relationTime) {
		this.relationTime = relationTime;
	}
	public Operator getOperator() {
		return operator;
	}
	public void setOperator(Operator operator) {
		this.operator = operator;
	}
	
    
	
}
