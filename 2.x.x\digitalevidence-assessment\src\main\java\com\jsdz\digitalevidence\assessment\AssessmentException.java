/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment;

/**
 * @类名: AssessmentException
 * @说明: 考核异常
 * 
 * <AUTHOR>
 * @Date	 2017年4月27日 下午2:05:19
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentException extends Exception {

	public AssessmentException() {
		super();
		// TODO Auto-generated constructor stub
	}

	public AssessmentException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
		// TODO Auto-generated constructor stub
	}

	public AssessmentException(String message, Throwable cause) {
		super(message, cause);
		// TODO Auto-generated constructor stub
	}

	public AssessmentException(String message) {
		super(message);
		// TODO Auto-generated constructor stub
	}

	public AssessmentException(Throwable cause) {
		super(cause);
		// TODO Auto-generated constructor stub
	}
	
	

}
