package com.jsdz.digitalevidence.utils;

import com.beust.jcommander.internal.Nullable;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.digitalevidence.cache.utils.Constant;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.text.DecimalFormat;


/**
 *
 * <AUTHOR>
 * @create 2020/08/16 13:22:41
 *
 */
public abstract class StringUtils extends org.springframework.util.StringUtils {

    public static boolean isNoEmpty(@Nullable Object str) {
        return !isEmpty(str);
    }

    public static boolean isBlank(@Nullable Object str) {
        return !isEmpty(str)?isEmpty(str.toString().trim()):true;
    }

    public static boolean isNoBlank(@Nullable Object str) {
        return !isBlank(str);
    }

    public static String toString(@Nullable Object str) {return isNoEmpty(str)?str.toString():"";}

    public static String getRequestIp(HttpServletRequest request) throws UnknownHostException {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if (ip.equals("127.0.0.1")) {
                InetAddress inetAddress = InetAddress.getLocalHost();
                ip = inetAddress.getHostAddress();
            }
        }

        if (ip != null && ip.length() > 15 && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(","));
        }

        return "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : ip;
    }

    public static Logger logger(Class<?> cla){
        return LoggerFactory.getLogger(cla.getClass());
    }

    public static Logger logger(){
        return LoggerFactory.getLogger("");
    }

    
    public static Operator getCurrentOperator(HttpServletRequest request){
        if(request.getSession()!=null){
            return (Operator) request.getSession().getAttribute(Constant.VAR_LOGIN_OPERATOR);
        }
        return null;
    }

    public static String getTimeString(String millisecondStr) {
        try {
            if(isEmpty(millisecondStr)) return null;
            Long millisecond = Long.parseLong(millisecondStr);
            if (millisecond < 1000) {
                return "0" + "秒";
            }
            long second = millisecond / 1000;
            long seconds = second % 60;
            long minutes = second / 60;
            long hours = 0;
            if (minutes >= 60) {
                hours = minutes / 60;
                minutes = minutes % 60;
            }
            String timeString = "";
            String secondString = "";
            String minuteString = "";
            String hourString = "";
            if (seconds < 10) {
                secondString = "0" + seconds + "秒";
            } else {
                secondString = seconds + "秒";
            }
            if (minutes < 10 && hours < 1) {
                minuteString = minutes + "分";
            } else if (minutes < 10){
                minuteString =  "0" + minutes + "分";
            } else {
                minuteString = minutes + "分";
            }
            if (hours < 10) {
                hourString = hours + "时";
            } else {
                hourString = hours + "" + "时";
            }
            if (hours != 0) {
                timeString = hourString + minuteString + secondString;
            } else {
                timeString = minuteString + secondString;
            }
            return timeString;
        }catch (Exception e){
            return null;
        }
    }




    public static String readableFileSize(String sizeStr) {
        try {
            Long size = Long.parseLong(sizeStr);
            if (size <= 0) {
                return "0";
            }
            final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
            int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
            return new DecimalFormat("#,###.##").format(size / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
        }catch (Exception e){
            return "0";
        }

    }


}
