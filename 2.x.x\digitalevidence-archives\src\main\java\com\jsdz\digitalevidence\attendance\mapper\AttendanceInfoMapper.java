package com.jsdz.digitalevidence.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceInfoBean;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceInfoTaskBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceCheckModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceInfoModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceInfoModel;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AttendanceInfoMapper extends BaseMapper<AttendanceInfoModel> {
    @Select("${sql}")
    List<AttendanceInfoBean> getDatas(@Param("sql") String sql);

    @Select("${sql}")
    Integer getCount(@Param("sql") String sql);

    @Insert("${sql}")
    void saveData(@Param("sql") String sql);

    @Update("${sql}")
    void updateData(@Param("sql") String sql);

    @Select("${sql}")
    AttendanceInfoModel getById(@Param("sql") String sql);

    @Delete("${sql}")
    void deleteById(@Param("sql") String sql);

    @Select("select count(1) from sys_attendance_info where deviceId = #{deviceId} and attendanceTaskId = #{taskId} and isDelete = 0")
    Integer findisSave(@Param("taskId")Long taskId,@Param("deviceId") Long deviceId);


    @Select("select i.id as attendanceInfoId,i.attendanceTaskId,i.lastaattendancetime as todayClockTime,t.attendanceTime,t.attendanceName,t.advanceByMinutes from sys_attendance_info i join sys_attendance_task t on i.attendancetaskId = t.id where i.isdelete = 0 and t.isdelete = 0 and i.DEVICECODE = #{code}")
    List<AttendanceInfoTaskBean> getInfoTaskAll(@Param("code") String code);
}
