package com.jsdz.digitalevidence.analysis.openapi.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.analysis.openapi.dao.DsjInfoDao;
import com.jsdz.digitalevidence.analysis.openapi.model.DsjInfo;
import com.jsdz.digitalevidence.analysis.openapi.service.DsjInfoService;

/**
 * 
 * @类名: DsjInfoCheckServiceImpl
 * @说明: 过检版的的实现类
 *
 * @author: kenny
 * @Date 2017-06-09 15:15:00
 * 修改记录：
 *
 * @see
*/
@Service("DsjInfoCheckServiceImpl")
public class DsjInfoCheckServiceImpl implements DsjInfoService {
	@Autowired
	private DsjInfoDao dsjInfoDao;

	@Override
	public AjaxResult addDsjInfo(String siteCode, DsjInfo dsjInfo) {
		AjaxResult result = new AjaxResult();
		dsjInfoDao.addDsjInfo(dsjInfo);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	@Override
	public AjaxResult updateDsjInfo(DsjInfo dsjInfo) {
		AjaxResult result = new AjaxResult();
		dsjInfoDao.updateDsjInfo(dsjInfo);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	@Override
	public AjaxResult deleteDsjInfo(DsjInfo dsjInfo) {
		AjaxResult result = new AjaxResult();
		dsjInfoDao.deleteDsjInfo(dsjInfo);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	@Override
	public DsjInfo findDsjInfoById(Long id) {
		return dsjInfoDao.findDsjInfoById(id);	
	}

	@Override
	public DsjInfo locateDsjInfoById(Long id) {
		return dsjInfoDao.findDsjInfoById(id);
	}

	@Override
	public DsjInfo findDsjInfoByParam(String queryStr, String[] paramNames, Object[] values) {
		return dsjInfoDao.findDsjInfoByCondition(queryStr,paramNames,values);
	}

	@Override
	public List<DsjInfo> findAllDsjInfos() {
		return dsjInfoDao.findAllDsjInfos();
	}

	@Override
	public List<DsjInfo> findDsjInfosByParam(String queryStr, String[] paramNames, Object[] values) {
		return dsjInfoDao.findDsjInfosByCondition(queryStr,paramNames,values);
	}

	@Override
	public Page<DsjInfo> findDsjInfosOnPage(Page<DsjInfo> page, String queryStr, String[] paramNames, Object[] values) {
		Page<DsjInfo> pos = dsjInfoDao.findDsjInfosOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
