package com.jsdz.admin.security.service.impl;

import java.util.Date;

/**
 * 
 * @类名: RoleActionServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-03 17:59:01
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import com.jsdz.admin.security.dao.ActionDao;
import com.jsdz.admin.security.dao.RoleActionDao;
import com.jsdz.admin.security.dao.RoleDao;
import com.jsdz.admin.security.model.Action;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleAction;
import com.jsdz.admin.security.service.RoleActionService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.cache.encrypt.MD5;

@Service("RoleActionServiceImpl")
public class RoleActionServiceImpl implements RoleActionService {

	@Autowired
	private RoleActionDao roleActionDao;
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private ActionDao actionDao;
	
	

	//新增
	public AjaxResult addRoleAction(RoleAction roleAction) {
		AjaxResult result = new AjaxResult();
		
		if (null == roleAction){
			result.setSuccess(false);
			result.setMsg("没有数据可保存。");
			return result;
		}
		
		if (null == roleAction.getRole()){
			result.setSuccess(false);
			result.setMsg("角色必须输入。");
			return result;
		}
		
		if (null == roleAction.getAction() ){
			result.setSuccess(false);
			result.setMsg("任务必须输入。");
			return result;
		}
		List<RoleAction> ors = roleActionDao.findRoleActionsByCondition(
				"from RoleAction r where r.role.id=:rid and r.action.id=:oid",
				new String[]{"rid","oid"},
				new Object[]{roleAction.getRole().getId(),roleAction.getAction().getId()});
		if (null == ors || ors.size() > 0){
			result.setSuccess(false);
			result.setMsg("此角色和任务关系已存在。");
			return result;
		}  
		roleActionDao.addRoleAction(roleAction);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateRoleAction(RoleAction roleAction) {
		AjaxResult result = new AjaxResult();
		
		if (null == roleAction){
			result.setSuccess(false);
			result.setMsg("没有数据可修改。");
			return result;
		}
		if (null == roleAction.getRole()){
			result.setSuccess(false);
			result.setMsg("角色必须输入。");
			return result;
		}
		
		if (null == roleAction.getAction()){
			result.setSuccess(false);
			result.setMsg("任务必须输入。");
			return result;
		}
		List<RoleAction> ors = roleActionDao.findRoleActionsByCondition(
				"from RoleAction r where r.role.id=:rid and r.action.id=:aid and o.id != :id",
				new String[]{"rid","aid","id"},
				new Object[]{roleAction.getRole().getId(),roleAction.getAction().getId(),roleAction.getId()});
		if (null != ors && ors.size() > 0){
			result.setSuccess(false);
			result.setMsg("此角色和任务关系重复。");
			return result;
		}		
		roleActionDao.updateRoleAction(roleAction);
		result.setSuccess(true);
		result.setMsg("保存成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteRoleAction(RoleAction roleAction) {
		AjaxResult result = new AjaxResult();
		if (null == roleAction){
			result.setSuccess(false);
			result.setMsg("没有数据可删除。");
			return result;
		}

		roleActionDao.deleteRoleAction(roleAction);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}
	
	//批量设置角色权限
	@Transactional
	public AjaxResult updateBatchRoleAction(Long rid,Long[] aids,Operator createBy){
		AjaxResult result = new AjaxResult();
	    result.setSuccess(false);
	    
		try{
			result.setSuccess(false);
			List<Role> rs = roleDao.findRolesByCondition("from Role r where r.id = :id", new String[]{"id"},new Object[]{rid});
			Role role = rs.get(0);
			if (role == null){
				result.setMsg("不存在的角色。");
				return result;
			}
			//先移除该用户关联的所有角色
			roleActionDao.deleteAllByRole(role);
			
			
			StringBuilder actionStr = new StringBuilder();
			StringBuilder actionNames = new StringBuilder();
			
			if (aids != null){
				//再一个一个地加
				for (Long id : aids){
					Action action = actionDao.findActionByCondition("from  Action a where a.id = :id ", new String[]{"id"},new Object[]{id});
					if (action != null){
						actionStr.append(action.getTag());
						actionStr.append("; ");
						actionNames.append(action.getName());
						actionNames.append("; ");
						
						RoleAction roleAction = new RoleAction();
						roleAction.setAction(action);
						roleAction.setRole(role);
						roleAction.setCreateTime(new Date());
						roleActionDao.addRoleAction(roleAction);
					}
				}
			}
			//更新role的actionStr
			
			role.setActionStr(actionStr.toString());
			role.setActionNames(actionNames.toString());
			roleDao.update(role);
			
		}catch(Exception e){
			//好象不会自动回滚，这里加手动，以后再仔细研究
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly(); 
			result.setMsg("操作失败：" + e.toString());
			return result ;			
		}

		result.setSuccess(true);
		result.setMsg("设置成功");
		return result ;
	}	

	//单个查询
	public RoleAction findRoleActionByParam(String queryStr, String[] paramNames, Object[] values) {
		return roleActionDao.findRoleActionByCondition(queryStr,paramNames,values);
	}

	//列表查询
	public List<RoleAction> findRoleActionsByParam(String queryStr, String[] paramNames, Object[] values) {
		return roleActionDao.findRoleActionsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<RoleAction> findRoleActionsOnPage(Page<RoleAction> page, String queryStr, String[] paramNames, Object[] values) {
		Page<RoleAction> pos = roleActionDao.findRoleActionsOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	//按角色查所有动作
	public List<RoleAction> findRoleActionsByRole(Role role){
		return findRoleActionsByParam(
				"from RoleAction ra "
				+ " left join fetch ra.role r "
				+ " left join fetch ra.action a where r.id = :id", 
				new String[]{"id"}, new Object[]{role.getId()});
	}
}
