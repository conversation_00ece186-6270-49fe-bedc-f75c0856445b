package com.jsdz.digitalevidence.assessment.dao.impl;

/**
*
* @类名: OrgAssPeriodDaoImpl
* @说明:
* @author: kenny
* @Date 2018-01-11 17:27:52
* 修改记录：
* @see
* @see
*/

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.assessment.dao.OrgAssPeriodDao;
import com.jsdz.digitalevidence.assessment.model.org.OrgAssPeriod;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class OrgAssPeriodDaoImpl extends GenericEntityDaoHibernateImpl<OrgAssPeriod,Long> implements OrgAssPeriodDao{

	//新增
	public void addOrgAssPeriod(OrgAssPeriod orgAssPeriod) {
		this.saveOrUpdate(orgAssPeriod);
	}

	//删除
	public void deleteOrgAssPeriod(OrgAssPeriod orgAssPeriod) {
		this.delete(orgAssPeriod);
	}

	//修改
	public void updateOrgAssPeriod(OrgAssPeriod orgAssPeriod) {
		this.merge(orgAssPeriod);
	}

	//按id查询(游离状态)
	public OrgAssPeriod findOrgAssPeriodById(Long id){

		final String  hql = "from OrgAssPeriod a where a.id = :id";
		final Long oid = id;
		OrgAssPeriod data = getHibernateTemplate().execute(new HibernateCallback<OrgAssPeriod>() {
			public OrgAssPeriod doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<OrgAssPeriod> list = query.list();
				OrgAssPeriod rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public OrgAssPeriod locateOrgAssPeriodById(Long id){

		final String  hql = "from OrgAssPeriod a where a.id = :id";
		final Long oid = id;
		OrgAssPeriod data = getHibernateTemplate().execute(new HibernateCallback<OrgAssPeriod>() {
			public OrgAssPeriod doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<OrgAssPeriod> list = query.list();
				OrgAssPeriod rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public OrgAssPeriod findOrgAssPeriodByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		OrgAssPeriod data = getHibernateTemplate().execute(new HibernateCallback<OrgAssPeriod>() {
		public OrgAssPeriod doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<OrgAssPeriod> list = query.list();
			OrgAssPeriod rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<OrgAssPeriod> findAllOrgAssPeriods(){
		return this.find("from OrgAssPeriod orgAssPeriod ");
	}

	//列表查询
	public List<OrgAssPeriod> findOrgAssPeriodsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<OrgAssPeriod> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<OrgAssPeriod> findOrgAssPeriodsOnPage(Page<OrgAssPeriod> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<OrgAssPeriod>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//执行自定义的hql
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
}
