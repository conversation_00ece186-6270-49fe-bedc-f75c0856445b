package com.jsdz.digitalevidence.analysis.openapi;

import com.jsdz.admin.security.utils.JSDateFormatUtils;
import com.jsdz.digitalevidence.analysis.openapi.model.StationLog;

/**
 * 
 * @类名: StationLogResultBean
 * @说明: 执法数据采集设备日志信息查询结果
 *
 * @author: kenny
 * @Date	2017年6月7日下午3:28:42
 * 修改记录：
 *
 * @see
 */
public class StationLogResultBean {

	private Long id;//
	private String gzz_xh;//执法数据采集设备产品编码
	private String rzlx;// 日志类型
	private String dxbh;//对象编号
	private String rzrq;//日志时间(yy-MM-dd HH:mm:ss)
	
	public void assign(StationLog src){
		this.setGzz_xh(src.getGzz_xh());
		this.setRzlx(src.getRzlx());
		this.setDxbh(src.getDxbh());
		this.setRzrq(JSDateFormatUtils.formatDateTime(src.getRzrq()));
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getGzz_xh() {
		return gzz_xh;
	}
	public void setGzz_xh(String gzz_xh) {
		this.gzz_xh = gzz_xh;
	}
	public String getRzlx() {
		return rzlx;
	}
	public void setRzlx(String rzlx) {
		this.rzlx = rzlx;
	}
	public String getDxbh() {
		return dxbh;
	}
	public void setDxbh(String dxbh) {
		this.dxbh = dxbh;
	}
	public String getRzrq() {
		return rzrq;
	}
	public void setRzrq(String rzrq) {
		this.rzrq = rzrq;
	}

	
}
