package com.jsdz.admin.security.service;

/**
 * 
 * @类名: RoleService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-05 17:43:37
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.Role;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface RoleService {

	//新增
	public AjaxResult addRole(Role role);

	//修改
	public AjaxResult updateRole(Role role);

	//删除
	public AjaxResult deleteRole(Role role);

	//查所有
	public List<Role> findAll();
		
	//按id查询
	public Role locateRoleById(Long id);
	
	//单个查询
	public Role findRoleByParam(String queryStr,String[] paramNames,Object[] values);

	//列表查询
	public List<Role> findRolesByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Role> findRolesOnPage(Page<Role> page, String queryStr,String[] paramNames,Object[] values);

}

