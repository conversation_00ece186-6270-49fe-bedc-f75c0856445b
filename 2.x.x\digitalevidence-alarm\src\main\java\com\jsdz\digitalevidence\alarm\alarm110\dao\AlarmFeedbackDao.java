package com.jsdz.digitalevidence.alarm.alarm110.dao;

/**
 *
 * @类名: AlarmFeedbackDao
 * @说明:
 * @author: kenny
 * @Date 2018-08-28 11:11:14
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmFeedback;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface AlarmFeedbackDao extends GenericORMEntityDAO<AlarmFeedback,Long> {

	//新增
	public void addAlarmFeedback(AlarmFeedback alarmFeedback);

	//修改
	public void updateAlarmFeedback(AlarmFeedback alarmFeedback);

	//删除
	public void deleteAlarmFeedback(AlarmFeedback alarmFeedback);

	//按id查询,结果是游离状态的数据
	public AlarmFeedback findAlarmFeedbackById(Long id);

	//按id查询
	public AlarmFeedback locateAlarmFeedbackById(Long id);

	//单个查询
	public AlarmFeedback findAlarmFeedbackByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmFeedback> findAllAlarmFeedbacks();

	//列表查询
	public List<AlarmFeedback> findAlarmFeedbacksByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmFeedback> findAlarmFeedbacksOnPage(Page<AlarmFeedback> page,String queryStr,String[] paramNames,Object[] values);

	//执行指定的HQL文件
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);

}

