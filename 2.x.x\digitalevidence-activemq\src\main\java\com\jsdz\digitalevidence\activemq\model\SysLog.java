package com.jsdz.digitalevidence.activemq.model;
/**
 * 
 * @类名: SysLog
 * @说明: 系统日志
 *
 * @author: kenny
 * @Date	2018年1月29日下午3:33:00
 * 修改记录：
 *
 * @see
 */
import java.io.Serializable;
import java.util.Date;
import org.apache.log4j.Logger;
import org.springframework.util.StringUtils;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.digitalevidence.activemq.utils.LogType;

public class SysLog implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 316095510974203260L;
	//public Logger logger = Logger.getLogger(SysLog.class);
	
	private Long id;
	private String javaClass;
	private String logType;
	private String topic; //主题
	private String keyWord; //关键字
	private String context; //内容
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTime; //日志时间

	public static SysLog get(SysLog sysLog,LogType logType,String keyWord,String context){
		if (logType != null)
			sysLog.setLogType(logType.getName());
		
		if (!StringUtils.isEmpty(keyWord))
			sysLog.setKeyWord(keyWord);
		
		if (!StringUtils.isEmpty(context))
			sysLog.setContext(context);
		return sysLog;
	}
	public static SysLog get(SysLog sysLog,LogType logType,String context){
		if (logType != null)
			sysLog.setLogType(logType.getName());
		if (!StringUtils.isEmpty(context))
			sysLog.setContext(context);
		return sysLog;
	}	
	public SysLog(){
		super();
	}
	public SysLog(Class clazz, String topic) {
		super();
		this.setJavaClass(clazz.getName());
		this.setTopic(topic);
	}
	public SysLog(Class clazz, String topic,String keyWord) {
		super();
		this.setJavaClass(clazz.getName());
		this.setTopic(topic);
		this.setKeyWord(keyWord);
	}

	public Long getId() {
		return id;
	}
	
	public void setId(Long id) {
		this.id = id;
	}
	public String getJavaClass() {
		return javaClass;
	}
	public void setJavaClass(String javaClass) {
		this.javaClass = javaClass;
	}
	public String getLogType() {
		return logType;
	}
	public void setLogType(String logType) {
		this.logType = logType;
	}
	public String getTopic() {
		return topic;
	}
	public void setTopic(String topic) {
		this.topic = topic;
	}
	public String getKeyWord() {
		return keyWord;
	}
	public void setKeyWord(String keyWord) {
		this.keyWord = keyWord;
	}
	public String getContext() {
		return context;
	}
	public void setContext(String context) {
		this.context = context;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public String toString(){
		String result="";
		result = "topic="+this.getTopic() + ";" + "keyword=" + this.getKeyWord() + ";"
				+ "context=" + this.getContext() + ";";
		return result;
	}
}
