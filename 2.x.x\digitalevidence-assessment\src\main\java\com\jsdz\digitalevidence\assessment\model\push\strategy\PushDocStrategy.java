/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.model.push.strategy;

import java.util.List;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.digitalevidence.assessment.bean.AssessmentAssessorSumBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.sun.xml.bind.v2.TODO;

/**
 * @类名: PushDocStrategy
 * @说明: 考核视频推送策略
 *        
 *               
 * <AUTHOR>
 * @Date	 2017年10月14日 下午2:44:00
 * 修改记录：
 *
 * @see 	 
 * 
 * TODO
 *   *. 支持并行推送
 *   *. 多线程
 *   
 */
/*
// 推送考核视频策略
// 哪个警员，考核率低于设定阀值，包括推送未考核
// 什么视频，未考核过，未推送过，考核期内的文档, top n	
//   其他，不同属性维度，拍摄时间维度等
// 推到那个考核员，考核视频量，包括已推送但未考核
*/
public interface PushDocStrategy {
	
	/**
	 * @说明：推送
	 *
	 * <AUTHOR>
	 * @param cycle 考核周期
	 * 
	 */
	public void push(Cycle cycle);
	public void doPush(Cycle cycle, ShootingAssessmentSumBean assessmentSum, List<AssessmentAssessorSumBean> assessors);
	public void pushByOrg(Cycle cycle);
	
}
