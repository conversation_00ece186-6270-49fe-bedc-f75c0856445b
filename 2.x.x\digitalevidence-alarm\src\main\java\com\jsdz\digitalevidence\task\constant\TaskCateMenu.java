package com.jsdz.digitalevidence.task.constant;

public enum TaskCateMenu {

    /**
     * 默认任务
     */
    defualt(0),
    /**
     * 人脸抓拍
     */
    face_shot(1);

    private int value;

    TaskCateMenu(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static boolean check(int value) {
        for (TaskCateMenu taskCateMenu : values()) {
         if(taskCateMenu.getValue() == value) return true;
        }
        return false;
    }

}
