package com.jsdz.digitalevidence.utils.response;



/**
 *
 * <AUTHOR>
 * @create 2023/12/19 10:32:22
 *
 */

public class Result<T> {

	private Integer code;
	private String msg;

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public T getData() {
		return data;
	}

	public void setData(T data) {
		this.data = data;
	}

	private T data;

	public Result fillArgs(Object... args) {
		this.msg = String.format(this.msg, args);
		return this;
	}

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}
}
