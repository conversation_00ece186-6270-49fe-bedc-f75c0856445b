/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.cycle;

import java.util.Date;

/**
 * @类名: CycleConfiguration
 * @说明: 周期设置
 *        
 * <AUTHOR>
 * @Date	 2017年8月18日 下午4:27:51
 * 修改记录：
 *
 * @see 	 
 *
 */
public class CycleConfiguration {
	
	/** 
	 * 周期开始日
	 * -周周期
	 * -月周期
	 * -季周期
	 * -年周期
	 * 
	 * day<29
	 */
	private int day;
	/** 周期单位*/
	private CycleUnit unit;
	
	public CycleConfiguration(int day, CycleUnit unit) {
		super();
		this.day = day;
		this.unit = unit;
		this.unit.setDay(day);
	}

	/**
	 * @说明：获取当前周期
	 *
	 * <AUTHOR>
	 * @return
	 * 
	 */
	public Cycle getCurrentCycle() {
		return fromDate(new Date());
	}
	
	/**
	 * @说明：获取某日所在周期
	 *
	 * <AUTHOR>
	 * @param date
	 * @return
	 * 
	 */
	public Cycle fromDate(Date date) {
		return unit.fromDate(date);
	}
	
	/**
	 * @说明：验证周期是否属于本配置
	 *
	 * <AUTHOR>
	 * @param cycle
	 * @return
	 * 
	 */
	public boolean validate(Cycle cycle) {
		return false;
	}
	
	// 上一周期
	public Cycle prevCycle(Cycle cycle) {
		return unit.prevCycle(cycle);
	}
	
	// 下一周期
	public Cycle nextCycle(Cycle cycle) {
		return unit.nextCycle(cycle);
	}
	
}
