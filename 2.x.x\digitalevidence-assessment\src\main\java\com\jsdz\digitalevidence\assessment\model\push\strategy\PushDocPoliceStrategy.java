package com.jsdz.digitalevidence.assessment.model.push.strategy;

import java.util.Date;
import java.util.List;

import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean;

/**
 * @类名: PushDocPoliceStrategy
 * @说明: 考核视频推送查找警员策略
 *
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 
 * 修改记录：
 *
 * @see
 */
public interface PushDocPoliceStrategy {

	public List<ShootingAssessmentSumBean> findPushingPolice(Date startDate, Date endDate, float assessRate, int topN);
	
}
