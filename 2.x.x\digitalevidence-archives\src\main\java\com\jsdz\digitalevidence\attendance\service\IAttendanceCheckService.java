package com.jsdz.digitalevidence.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.param.AttendanceCheckParam;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceCheckBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceCheckModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceCheckModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceInfoModel;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface IAttendanceCheckService  extends IService<AttendanceCheckModel> {

    Page<AttendanceCheckBean> getDatas(AttendanceCheckParam param, PerLvlBean permLvl);

    void saveData(AttendanceCheckModel model, PerLvlBean permLvl);

    void updateDataById(AttendanceCheckModel model, PerLvlBean permLvl);

    AttendanceCheckModel getById(AttendanceCheckModel model, PerLvlBean permLvl);

    void deleteById(AttendanceCheckModel model, PerLvlBean permLvl);

    List<AttendanceCheckModel> getDataByTime(List<Long> recorderId);

    void attendanceAutomaticCheck(String deviceId, String currentTime, String longitude, String latitude);
}
