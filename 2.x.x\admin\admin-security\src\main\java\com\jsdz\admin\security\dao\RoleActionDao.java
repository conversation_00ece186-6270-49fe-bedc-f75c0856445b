package com.jsdz.admin.security.dao;

/**
 *
 * @类名: RoleActionDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-03 17:59:01
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleAction;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface RoleActionDao extends GenericORMEntityDAO<RoleAction,Long> {

	//新增
	public void addRoleAction(RoleAction roleAction);

	//修改
	public void updateRoleAction(RoleAction roleAction);

	//删除
	public void deleteRoleAction(RoleAction roleAction);

	//单个查询
	public RoleAction findRoleActionByCondition(String queryStr,String[] paramNames,Object[] values);

	//列表查询
	public List<RoleAction> findRoleActionsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RoleAction> findRoleActionsOnPage(Page<RoleAction> page,String queryStr,String[] paramNames,Object[] values);
	
	//删除该角色关联的所有权限
	public void deleteAllByRole(Role role);

}

