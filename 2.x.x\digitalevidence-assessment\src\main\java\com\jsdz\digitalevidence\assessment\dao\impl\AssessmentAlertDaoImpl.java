/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.dao.impl;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import org.hibernate.Criteria;
import org.hibernate.HibernateException;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAlertBean;
import com.jsdz.digitalevidence.assessment.dao.AssessmentAlertDao;
import com.jsdz.digitalevidence.assessment.model.ReportType;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert;

/**
 * @类名: ShootingAssessmentDaoImpl
 * @说明: 拍摄考核dao实现
 *
 * <AUTHOR>
 * @Date	 2017年8月14日 下午4:07:05
 * 修改记录：
 *
 * @see 	 
 */
@Repository
public class AssessmentAlertDaoImpl extends GenericEntityDaoHibernateImpl<AssessmentAlert, Long> 
					implements AssessmentAlertDao {

	@Override
	public AssessmentAlertBean getAssessmentAlertBean(Long alertId) {
		//
		List<AssessmentAlertBean> beans =  getHibernateTemplate().execute(new HibernateCallback<List<AssessmentAlertBean>>() {
			@Override
			public List<AssessmentAlertBean> doInHibernate(Session session) throws HibernateException, SQLException {
				// 获取查询对象
				SQLQuery queryObject = (SQLQuery)session.getNamedQuery("getAssessmentAlertBean");
				queryObject.addEntity("a", AssessmentAlertBean.class);
				queryObject.addJoin("items", "a.items");
				queryObject.addEntity("a", AssessmentAlertBean.class);
				queryObject.setResultTransformer(Criteria.DISTINCT_ROOT_ENTITY);
				// 置入参数
				applyNamedParameterToQuery(queryObject, "alertId", alertId);
				//
				return (List<AssessmentAlertBean>) queryObject.list();
			}
		});
		return beans.get(0);
	}

	@Override
	public AssessmentAlert getAssessmentAlert(Long policeId, Date cycleDate) {
		String hql = "from AssessmentAlert alert "
				+ "where alert.police.id = ? "
				+ "and alert.startDate <= ? "
				+ "and alert.endDate > ? "
				+ "and alert.type = ?";
		//
		List<AssessmentAlert> alerts = this.findByParam(hql, new Object[]{policeId, cycleDate, cycleDate, ReportType.Monthly});
		if(alerts!=null&&alerts.size()>0)
			return alerts.get(0);
		return null;
	}

	@Override
	public AssessmentAlert getWeeklyAssessmentAlert(Long policeId, Date reportDate) {
		String hql = "from AssessmentAlert alert "
				+ "where alert.police.id = ? "
				+ "and alert.endDate = ? "
				+ "and alert.type = ?";
		//
		List<AssessmentAlert> alerts = this.findByParam(hql, new Object[]{policeId, reportDate, ReportType.Weekly});
		if(alerts!=null&&alerts.size()>0)
			return alerts.get(0);
		return null;
	}

}
