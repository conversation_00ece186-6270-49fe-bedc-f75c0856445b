package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.alarm110.dao.CaseRelationDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;
import com.jsdz.digitalevidence.alarm.alarm110.model.CaseRelation;
import com.jsdz.digitalevidence.alarm.alarm110.service.CaseRelationService;
import com.jsdz.digitalevidence.document.dao.CaseDao;
import com.jsdz.digitalevidence.document.dao.DocumentDao;
import com.jsdz.digitalevidence.document.model.Case;
import com.jsdz.digitalevidence.document.model.Document;

@Service("CaseRelationServiceImpl")
public class CaseRelationServiceImpl implements CaseRelationService{
	
	@Autowired
	private CaseRelationDao caseRelationDao;
	
	@Autowired
	private CaseDao caseDao ;
	
	@Autowired
	private DocumentDao documentDao;
	
	// 分页查询
	public Page<CaseRelation> findCaseRelationOnPage(Page<CaseRelation> page, String queryStr, String[] paramNames,
			Object[] values) {
		
		Page<CaseRelation> pa = caseRelationDao.findCaseRelationOnPage(page,queryStr,paramNames,values);
		return pa;
	}
	
	
	// 增加关联
	@Transactional
	public AjaxResult addCaseRelation(CaseRelation caseRelation) {
		
		AjaxResult ajaxResult = new AjaxResult(200,false,"",null);
		
		//检查案件是否为空
		if(caseRelation.getCas() == null){
			ajaxResult.setMsg("案件必须必须提供");
			return ajaxResult;
		}
		
		if (caseRelation.getMediaInfo() == null){
			ajaxResult.setMsg("视频必须提供。");
			return ajaxResult;
			
		}
		
		if (caseRelation.getOperator() == null){
			ajaxResult.setMsg("操作员必须提供。");
			return ajaxResult;
		}
		
		//检查重复
		if(caseRelationDao.findMutipleAlarmRelation(caseRelation) != null){
			ajaxResult.setMsg("此案件和视频已关联。");
			return ajaxResult;
		}
		
		caseRelation.setRelationTime(new Date());
		caseRelationDao.addCaseRelation(caseRelation);
		
		// 更新案件为关联状态
		Case cs = caseDao.findCaseById(caseRelation.getCas().getId());
		
		
		if(cs != null && (cs.getIsRelation() == null || cs.getIsRelation() == 0)){
			
			cs.setIsRelation(1);
			caseDao.updateCase(cs);
			
		}
		
		Long docId = caseRelation.getMediaInfo().getId();
		Document doc = documentDao.get(docId);
		doc.setIsRelationType("1");
		documentDao.updateDocument(doc);
		
		ajaxResult.setSuccess(true);
		ajaxResult.setMsg("新增保存成功。");
		return ajaxResult; 
	}
	
	
	// 根据id查询(非游离)
	public CaseRelation findCaseRelation(Long id) {
		return caseRelationDao.findCaseRelation(id);
	}

	// 删除关联
	@Transactional
	public AjaxResult deleteCaseRelation(CaseRelation caseRelation) {
		AjaxResult result = new AjaxResult(200,false,"",null);
		if (caseRelation == null){
			result.setMsg("没有数据可删除");
			return result;
		}
		
		Long casId = caseRelation.getCas().getId();
		Long docId = caseRelation.getMediaInfo().getId();
		
		caseRelationDao.deleteCaseRelation(caseRelation);
		
		//更新警情为未关联
		String queryStr = "from CaseRelation cr where cr.cas.id = :id ";
		String[] paramNames = {"id"};
		Object[] values = {caseRelation.getCas().getId()};
		List<CaseRelation> list =  caseRelationDao.findCaseRelationsByCondition(queryStr,paramNames,values);
		
		if( list.size() == 0){
			Case cs = caseDao.findCaseById(casId);
			if(cs != null && cs.getIsRelation() == 1){
				
				cs.setIsRelation(0);
				caseDao.updateCase(cs);
			}
			
		}
		
		Document doc = documentDao.get(docId);
		doc.setIsRelationType("0");
		documentDao.updateDocument(doc);
		
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}
	
	
	
}
