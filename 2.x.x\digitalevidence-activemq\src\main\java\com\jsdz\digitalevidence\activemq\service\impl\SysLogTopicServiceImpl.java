package com.jsdz.digitalevidence.activemq.service.impl;

/**
 * 
 * @类名: SysLogTopicServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-03-15 14:58:15
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.digitalevidence.activemq.dao.SysLogTopicDao;
import com.jsdz.digitalevidence.activemq.model.SysLogTopic;
import com.jsdz.digitalevidence.activemq.service.SysLogTopicService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("SysLogTopicServiceImpl")
public class SysLogTopicServiceImpl implements SysLogTopicService {

	@Autowired
	private SysLogTopicDao sysLogTopicDao;

	//新增
	public AjaxResult addSysLogTopic(SysLogTopic sysLogTopic) {
		AjaxResult result = new AjaxResult();
		sysLogTopicDao.addSysLogTopic(sysLogTopic);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateSysLogTopic(SysLogTopic sysLogTopic) {
		AjaxResult result = new AjaxResult();
		sysLogTopicDao.updateSysLogTopic(sysLogTopic);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteSysLogTopic(SysLogTopic sysLogTopic) {
		AjaxResult result = new AjaxResult();
		sysLogTopicDao.deleteSysLogTopic(sysLogTopic);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public SysLogTopic findSysLogTopicById(Long id){

		return sysLogTopicDao.findSysLogTopicById(id);
	}

	//按 id 查询
	public SysLogTopic locateSysLogTopicById(Long id) {
		return sysLogTopicDao.locateSysLogTopicById(id);
	}

	//单个查询
	public SysLogTopic findSysLogTopicByParam(String queryStr, String[] paramNames, Object[] values) {
		return sysLogTopicDao.findSysLogTopicByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<SysLogTopic> findAllSysLogTopics() {
		return sysLogTopicDao.findAllSysLogTopics();
		//String queryStr = "from SysLogTopic s";
		//return sysLogTopicDao.findSysLogTopicsByCondition(queryStr, null, null);

	}

	//列表查询
	public List<SysLogTopic> findSysLogTopicsByParam(String queryStr, String[] paramNames, Object[] values) {
		return sysLogTopicDao.findSysLogTopicsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<SysLogTopic> findSysLogTopicsOnPage(Page<SysLogTopic> page, String queryStr, String[] paramNames, Object[] values) {
		Page<SysLogTopic> pos = sysLogTopicDao.findSysLogTopicsOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	

}
