package com.jsdz.admin.org.service.impl;

/**
 * 
 * @类名: OrganizationServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-06 19:18:40
 * 修改记录：
 *
 * @see
*/
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jsdz.admin.org.bean.OrganizationBean;
import com.jsdz.admin.org.dao.OrganizationDao;
import com.jsdz.admin.org.dao.mapper.OrganizationMapper;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.service.OrganizationService;
import com.jsdz.admin.org.utils.MD5;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.core.TreeBean;
import com.jsdz.reportquery.ReportQueryDao;

@Service("OrganizationServiceImpl")
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
public class OrganizationServiceImpl implements OrganizationService {

	@Autowired
	private OrganizationDao organizationDao;
	@Autowired
	private OrganizationMapper organizationMapper;
	
	
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<OrganizationBean> reportQueryDao;

	//新增
	public AjaxResult addOrganization(Organization organization) {
		AjaxResult result = new AjaxResult();
		if (null == organization){
			result.setSuccess(false);
			result.setMsg("没有数据可保存");
			return result;
		}
		
		if (null == organization.getOrgCode()  || "".equals(organization.getOrgCode())){
			result.setMsg("单位编号必须输入");
			result.setSuccess(false);
			return result ;
		}		
		if (null == organization.getOrgName() || "".equals(organization.getOrgName())){
			result.setMsg("单位名称必须输入");
			result.setSuccess(false);
			return result ;
		}
		
		if (null != organizationDao.findOrganizationByCondition("from Organization o where o.orgCode=:orgCode",
				  new String[]{"orgCode"},new Object[]{organization.getOrgCode()})){
			result.setSuccess(false);
			result.setMsg("此单位编号已存在");
			return result;
		}
		
		if (null != organizationDao.findOrganizationByCondition("from Organization o where o.orgName=:orgName",
				  new String[]{"orgName"},new Object[]{organization.getOrgName()})){
			result.setSuccess(false);
			result.setMsg("此单位名称已存在");
			return result;
		}
		
		if (!checkEndlessLoop(organization.getParentOrg(),organization)){
			result.setSuccess(false);
			result.setMsg("上级单位有冲突");
			return result;
		}
		
		organization.setCreateTime(new Date());
		organizationDao.addOrganization(organization);
		result.setSuccess(true);
		result.setData(organization);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	@Transactional
	public AjaxResult updateOrganization(Organization organization) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == organization){
			result.setSuccess(false);
			result.setMsg("没有数据可保存");
			return result;
		}
		
		if (null == organization.getOrgCode()  || "".equals(organization.getOrgCode())){
			result.setMsg("单位编号必须输入");
			result.setSuccess(false);
			return result ;
		}		
		
		if (null == organization.getOrgName() || "".equals(organization.getOrgName())){
			result.setMsg("单位名称必须输入");
			result.setSuccess(false);
			return result ;
		}
		
		Organization srcOrg = organizationDao.locateOrganizationById(organization.getId());
		if (srcOrg == null){
			result.setMsg("不存在的单位资料");
			return result ;
		}
		
		if (null != organizationDao.findOrganizationByCondition(
					"from Organization o where o.orgCode=:orgCode and o.id != :id",
				  new String[]{"orgCode","id"},new Object[]{organization.getOrgCode(),organization.getId()})){
			result.setSuccess(false);
			result.setMsg("单位编号重复");
			return result;
		}
		
		
		if (null != organizationDao.findOrganizationByCondition(
					"from Organization o where o.orgName=:orgName and o.id != :id",
				  new String[]{"orgName","id"},new Object[]{organization.getOrgName(),organization.getId()})){
			result.setSuccess(false);
			result.setMsg("单位名称重复");
			return result;
		}
		
		if (!checkEndlessLoop(organization.getParentOrg(),organization)){
			result.setSuccess(false);
			result.setMsg("上级单位有冲突");
			return result;
		}
		
		Organization organization1 = organizationDao.locateOrganizationById(organization.getId());
		if (organization1==null){
			result.setSuccess(false);
			result.setMsg("不存在的单位资料");
			return result;
		}
		//是否修改了上级单位，如果修改了同时更新所有下级的path
		Long srcParId = srcOrg.getParentOrg()==null?0:srcOrg.getParentOrg().getId()==null?0:srcOrg.getParentOrg().getId();
		Long destParId = organization.getParentOrg()==null?0:organization.getParentOrg().getId()==null?0:organization.getParentOrg().getId();
		String oldPath = organization1.getPath();
		
		organization1.setOrgCode(organization.getOrgCode());
		organization1.setOrgName(organization.getOrgName());
		organization1.setParentOrg(organization.getParentOrg());
		organization1.setRegion(organization.getRegion());
		
		String newPath = "";
		if (organization1.getParentOrg() != null){
			Organization parOrg = locateOrganizationById(organization1.getParentOrg().getId());
			newPath = parOrg.getPath() ;
		}
		newPath += organization.getId().toString();
		newPath	+= "/";

		organization1.setPath(newPath);
		organizationDao.updateOrganization(organization1);

		

		if (srcParId != destParId){
			organizationDao.updateOrganizationPath(oldPath,newPath);
		}
		
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteOrganization(Organization organization) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == organization){
			result.setMsg("没有数据可删除");
			return result;
		}
		
		if (null == organizationDao.locateOrganizationById(organization.getId())){
			result.setMsg("不存在的单位资料");
			return result ;
		}

		if (null != organizationDao.findOrganizationByCondition(
				"from Organization o where o.parentOrg.id=:id",
			  new String[]{"id"},new Object[]{organization.getId()})){
			result.setMsg("单位有下级，不能删除");
			return result;
		}
		
		organizationDao.deleteOrganization(organization);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

    //按 id 查询
	public Organization locateOrganizationById(Long id) {
		return organizationDao.locateOrganizationById(id);
	}

	//单个查询
	public Organization findOrganizationByParam(String queryStr, String[] paramNames, Object[] values) {
		return organizationDao.findOrganizationByCondition(queryStr,paramNames,values);
	}

	//列表查询
	public List<Organization> findOrganizationsByParam(String queryStr, String[] paramNames, Object[] values) {
		return organizationDao.findOrganizationsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<Organization> findOrganizationsOnPage(Page<Organization> page, String queryStr, String[] paramNames, Object[] values) {
		Page<Organization> pos = organizationDao.findOrganizationsOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
	//查询所有的单位并生成tree
	public List<TreeBean> findOrganizationOnTree(){
		List<Organization> list =  organizationDao.findOrganizationsByCondition(
				"from Organization org where org.parentOrg is null ",null,null);

		List<TreeBean> resultList = null ;
		if (list != null){
			resultList = new ArrayList<TreeBean>();
			for (Organization org : list){
				TreeBean tb = new TreeBean();
				tb.setId(org.getId());
				tb.setText(org.getOrgName());
				tb.setPath(org.getPath());
				resultList.add(tb);
				findSubOrg(org,tb);
			}
		}
		return resultList;
	}
	
	//递归查询子单位
	private void findSubOrg(Organization org,TreeBean parentTb){
		List<Organization> list =  organizationDao.findOrganizationsByCondition(
				"from Organization org where org.parentOrg.id = :id ",
				new String[]{"id"},
				new Object[]{org.getId()});
		
		if (list != null && list.size() > 0){
			List<TreeBean> nodes = new ArrayList<TreeBean>();
			parentTb.setNodes(nodes);
			for (Organization  organization: list){
				TreeBean tb = new TreeBean();
				tb.setText(organization.getOrgName());
				tb.setId(organization.getId());
				tb.setPath(organization.getPath());
				nodes.add(tb);
				
				//递归查询下级
				findSubOrg(organization,tb);
			}
		}
	}
	
	//检查下级关系冲突,返回true为正确,没有冲突
	public boolean checkEndlessLoop(Organization parentOrg, Organization curOrg ){
		boolean result = true;
		if (parentOrg == null){
			return result;
		}
		
		if (parentOrg.getId() == curOrg.getId()){
			return false;
		}
		
		//如果没下级了不再继续检查
		List<Organization> orgs = organizationDao.findOrganizationsByCondition(
				"from Organization o where o.parentOrg.id = :id",
				new String[]{"id"},new Object[]{curOrg.getId()});
		if (orgs == null){
			return result;
		}
		
		for (int i=0;i<orgs.size();i++){
			if (orgs.get(i).getId() == parentOrg.getId()){
				result = false;
				break;
			}
			
			//递归查找
			result = checkEndlessLoop(parentOrg,orgs.get(i));
			if (result == false){
				return result;
			}
		}
		return result;
	}

	//分页查询最上级数据给前端easyUi treegrid使用
	@Override
	public Page<OrganizationBean> findOrgByTopLevel(Page<OrganizationBean> page, String[] paramNames, Object[] values){
		page = reportQueryDao.queryNamedNoTotalSQL(page,"findOrgByTopLevel",paramNames,values);
		
		for(OrganizationBean o : page.getRows()){
			o.setState(o.getState()==null?"open":"closed");
		}
		
		Integer total = reportQueryDao.getTotalQueryNamedSQL("findOrgByTopLevelTotalPage",new String[0],new Object[0]);
		page.setTotal(total);
		return page;
	}
	
	//按上级ID查询
	public List<OrganizationBean> findOrgByParentId(Long parentId){
		Page<OrganizationBean> page = new Page<OrganizationBean>();

		
		page.setOffset(0);
		page.setPageSize(10000);
		Map<String, Object> kvs = new HashMap<String, Object>();
		kvs.put("parentId", parentId);
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		page = reportQueryDao.queryNamedNoTotalSQL(page,"findOrgByParent",paramNames,values);
		//改为open 或 closed
		for(OrganizationBean o : page.getRows()){
			o.setState(o.getState()==null?"open":"closed");
		}

		
		return page.getRows();
	}

	//分页查询最上级数据给前端easyUi datagrid使用
	@Override
	public Page<OrganizationBean> findOrgOnPageForSy(Page<OrganizationBean> page, String[] paramNames, Object[] values){
		page = reportQueryDao.pageQueryNamedSQL(page, "findOrgOnPageForSy", paramNames, values);
		//page = reportQueryDao.QueryNamedNoTotalSQL(page,"findOrgByTopLevel",paramNames,values);
		//Integer total = reportQueryDao.getTotalQueryNamedSQL("findOrgByTopLevelTotalPage",new String[0],new Object[0]);
		//page.setTotal(total);
		return page;
	}

	@Override
	public List<Object> getOrgListForTree(String sql, String[] paramNames, Object[] values) {
		return organizationDao.getOrgListForTree(sql,paramNames,values);
	}
	@Override
	public Page<OrganizationBean> findOrgByTopLevelMybits(Page<OrganizationBean> page,Map<String, Object> kvs){
		List<OrganizationBean> rows = organizationMapper.findOrgByTopLevel(kvs);
		for(OrganizationBean o : rows){
			o.setState(o.getState()==null?"open":"closed");
		}
		Integer total = organizationMapper.findOrgByTopLevelCount(kvs);
		page.setRows(rows);
		page.setTotal(total);
		
		return page;
	}
	
	
}
