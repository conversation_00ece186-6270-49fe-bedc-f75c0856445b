package com.jsdz.admin.security.service.impl;

import java.util.Date;
import java.util.HashMap;

/**
 * 
 * @类名: RoleMenuServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-09-08 12:03:22
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jsdz.admin.security.dao.RoleDao;
import com.jsdz.admin.security.dao.RoleMenuDao;
import com.jsdz.admin.security.dao.SystemMenuDao;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleMenu;
import com.jsdz.admin.security.model.SystemMenu;
import com.jsdz.admin.security.service.RoleMenuService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("RoleMenuServiceImpl")
public class RoleMenuServiceImpl implements RoleMenuService {

	@Autowired
	private RoleMenuDao roleMenuDao;
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private SystemMenuDao systemMenuDao;
	
	
	
	//新增
	public AjaxResult addRoleMenu(RoleMenu roleMenu) {
		AjaxResult result = new AjaxResult();
		roleMenuDao.addRoleMenu(roleMenu);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateRoleMenu(RoleMenu roleMenu) {
		AjaxResult result = new AjaxResult();
		roleMenuDao.updateRoleMenu(roleMenu);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteRoleMenu(RoleMenu roleMenu) {
		AjaxResult result = new AjaxResult();
		roleMenuDao.deleteRoleMenu(roleMenu);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public RoleMenu findRoleMenuById(Long id){

		return roleMenuDao.findRoleMenuById(id);
	}

	//按 id 查询
	public RoleMenu locateRoleMenuById(Long id) {
		return roleMenuDao.locateRoleMenuById(id);
	}

	//单个查询
	public RoleMenu findRoleMenuByParam(String queryStr, String[] paramNames, Object[] values) {
		return roleMenuDao.findRoleMenuByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<RoleMenu> findAllRoleMenus() {
		return roleMenuDao.findAllRoleMenus();
	}

	//列表查询
	public List<RoleMenu> findRoleMenusByParam(String queryStr, String[] paramNames, Object[] values) {
		return roleMenuDao.findRoleMenusByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<RoleMenu> findRoleMenusOnPage(Page<RoleMenu> page, String queryStr, String[] paramNames, Object[] values) {
		Page<RoleMenu> pos = roleMenuDao.findRoleMenusOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
    //设置角色菜单权限
    @Transactional
    @Override
    public AjaxResult setSysMenuRole(String menuIds,Long roleId){
    	AjaxResult result = new AjaxResult(0,false,"",null);
   	 try{
   		Map<Long,Object> map = new HashMap<Long,Object>();
   		
   		List<SystemMenu> list = systemMenuDao.findAllSystemMenus();
   		for(SystemMenu m : list){
   			map.put(m.getId(), m);
   		}
   		 
   		Role role = roleDao.locateRoleById(roleId); 
   		roleMenuDao.deleteRoleMenuByRole(role);
   		
   		String actionNames = "";
   	   	 String[] menuArray = menuIds.split(",");
       	 for (int i=0;i<menuArray.length;i++){
       		 RoleMenu roleMenu = new RoleMenu();
       		 
       		 Long menuId = Long.valueOf(menuArray[i]);
       		 SystemMenu systemMenu = new SystemMenu();
       		 systemMenu.setId(menuId);
       		 roleMenu.setMenu(systemMenu); 
       		 roleMenu.setRole(role);
       		roleMenu.setCreateTime(new Date());
       		 roleMenuDao.addRoleMenu(roleMenu);
       		 
       		SystemMenu m = (SystemMenu)map.get(menuId);
       		if (m.getParentMenu() == null){
       			actionNames = actionNames + m.getMenuName() + ";";
       		}
       	 }
       	role.setActionNames(actionNames);
       	roleDao.updateRole(role);
       	 
   	 }catch(Exception e){
   		 result.setMsg("内部错误：" + e);
   		 result.setSuccess(false);
   	 }

   	 result.setSuccess(true);
   	 return result;
    }

}
