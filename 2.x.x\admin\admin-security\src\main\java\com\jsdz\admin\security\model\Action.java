package com.jsdz.admin.security.model;

import java.io.Serializable;
/**
 * 
 * @类名: Action
 * @说明: 权限操作类
 *
 * @author: kenny
 * @Date	2017年4月27日下午7:39:03
 * 修改记录：
 *
 * @see
 */
public class Action implements Serializable{
	
	private static final long serialVersionUID = 6213599806428240880L;
	
	private Long id;
    private int index; //序号
    private String tag; //标识
    private String name;//权限名称
    private String url;
    
	public Action(){
		super();
	}
	public Action(String name,String tag,int index){
		this.name=name;
		this.tag=tag;
		this.index = index;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public int getIndex() {
		return index;
	}
	public void setIndex(int index) {
		this.index = index;
	}
	public String getTag() {
		return tag;
	}
	public void setTag(String tag) {
		this.tag = tag;
	}

	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}	
	

}
