<?xml version="1.0" encoding="utf-8"?>
<!-- 考核监督动态sql -->
<sqlTemplates>
  <entry>
  	<!-- 
  	  打开考核视频
  	-->
    <string>openAssessment</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
		 select
				b.workNumber policeCode,
				b.name name,
				c.position position,
				b.male sex,
				b.age age,
				d.doc_id,
				f.alarm_code alarmcode,
				f.alarm_name alarmname,
				f.alarm_tel alarmtel,
				f.id id,
				f.alarm_context alarmcontext,
				f.is_relation isrelation,
				d.content_type contentType
				from
			    t_doc d 
				left join admin_employees b on b.id = d.police_id
				left join admin_position c on b.positionId = c.id
				left join admin_organization o on o.id = b.organizationId
				left join admin_region r on o.regionId = r.id
				left join admin_department d1 on d1.id = b.departmentId
				left join t_alarm_relation e on e.document_id=d.DOC_ID
				left join t_alarm_info f on f.id=e.alarminfo_id
				where 1=1 {0}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>docId</paramName>
                <t>and d.doc_id = :docId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>

  <entry>
  	<!-- 警员拍摄分类统计(hql) -->
    <string>sumOfShootingItem</string>
    <sqlTemplate>
      <!-- hql模式 -->
      <sqlPattern>
	          select
			    d.cate as cate, 
			    count(*) as count,
			    sum(d.size) as size,
                sum(d.duration) as duration
			from Document d
			where 1=1 {0} {1} {2}
			group by d.cate
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.model.ShootingItem</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeId</paramName>
                <t>and d.police.id = :policeId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startTime</paramName>
                <t>and d.uploadTime &gt;= :startTime</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endTime</paramName>
                <t>and d.uploadTime &lt; :endTime</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 警员拍摄考核统计 -->
    <string>sumOfShootingAssessment</string>
    <sqlTemplate>
      <!-- sql模式 -->
      <sqlPattern>
	      select  
                sum(case when IS_WMGF=1 then 1 end) countOfWMGF, 
				sum(case when IS_NO_WFWJ=1 then 1 end) countOfNoWFWJ,
                sum(case when IS_AYQPS=1 then 1 end) countOfAYQPS,
                sum(case when IS_YYYY=1 then 1 end) countOfYYYY,                
                sum(case when SHOOTING_ANGLE=0 then 1 end)  countOfShootingAngleA,
                sum(case when SHOOTING_ANGLE=1 then 1 end) countOfShootingAngleG,
                sum(case when SHOOTING_ANGLE=2 then 1 end)  countOfShootingAngleE,             
				sum(case when SHOOTING_CONTENT=0 then 1 end) countOfShootingContentA,
                sum(case when SHOOTING_CONTENT=1 then 1 end) countOfShootingContentG,
                sum(case when SHOOTING_CONTENT=2 then 1 end) countOfShootingContentE,
                sum(case when SHOOTING_RESLUTION=0 then 1 end) countOfShootingResolutionH,
                sum(case when SHOOTING_RESLUTION=1 then 1 end) countOfShootingResolutionG,
                sum(case when SHOOTING_RESLUTION=2 then 1 end) countOfShootingResolutionL,              
				count(*) totalAssessingDoc,
				(select
		            count(*) total
					from t_doc c 
					where 1=1 
				        {0} {1} {2}) totalDoc
				from t_assessment_shooting d 
					left join t_doc c on d.doc_Id = c.doc_id
                where 1=1 {0} {1} {2} 
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeId</paramName>
                <t>and c.police_id = :policeId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and c.UPLOAD_TIME &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and c.UPLOAD_TIME  &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
 
  <entry>
  	<!-- 
  	    拍摄考核概况, 当月    
  	-->
    <string>sumOfShootingAssessmentAll</string>
    <sqlTemplate>
      <!-- sql模式 -->
      <sqlPattern>
		 select 
		   b.id policeId,
		   b.workNumber workNumber, 
		   b.name name,
		   c.position position,
		   b.male sex,
		   b.age age,
		   IFNULL(cast(a.uploadDocs as signed), 0),
		   IFNULL(cast(a.assessDocs as signed), 0),
		   IFNULL(assessDocs/uploadDocs, 0) assessRate from 
		   admin_employees b
		   left join
		   (
				 select
					 e.id, 
					 count(d.doc_id) uploadDocs, 
					 sum(case when s.ID is not null then 1 else 0 end) assessDocs
					 FROM admin_employees e  
					    left join t_doc d on e.id = d.POLICE_ID
						left join t_assessment_shooting s on s.DOC_ID = d.DOC_ID
					    where 1=1 {0} {1}  
					    group by e.id ) a on a.id = b.id
		    left join admin_position c on b.positionId = c.id
		    left join admin_organization o on o.id = b.organizationId
		    left join admin_region r on o.regionId = r.id
		    left join admin_department d1 on d1.id = b.departmentId
		    where 1=1 
		    {2} {3} {4} {5} {6}
		    {7} 
  
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and d.UPLOAD_TIME &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and d.UPLOAD_TIME  &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, 区域 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>regionId</paramName>
                <t>and r.id = :regionId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, 组织 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>orgId</paramName>
                <t>and o.path like :orgId</t>
                <a class="IsTree"/>
              </path>
              <path>
                <paramName>orgId</paramName>
                <t>and o.path = :orgId</t>
                <a class="IsNotTree"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4, 部门 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>deptId</paramName>
                <t>and d1.id like :deptId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 5, 警号 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeCode</paramName>
                <t>and b.workNumber like :policeCode</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 6, 警员姓名 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeName</paramName>
                <t>and  b.name like :policeName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 7, 考核率排序 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>assessRateOrder</paramName>
                <t></t>
                <a class="IsNull"/>
              </path>
              <path>
                <paramName>assessRateOrder</paramName>
                <t>order by assessRate ASC, uploadDocs DESC</t>
                <a class="OrderByASC"/>
              </path>
              <path>
                <paramName>assessRateOrder</paramName>
                <t>order by assessRate DESC, uploadDocs DESC</t>
                <a class="OrderByDESC"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 
  	    拍摄考核概况, 当月, 总页数
  	-->
    <string>sumOfShootingAssessmentAllTotalPage</string>
    <sqlTemplate>
      <!-- sql模式 -->
      <sqlPattern>
		 select 
		   count(*) total from 
		   admin_employees b
		   left join
		   (
				 select
					 e.id, 
					 count(d.doc_id) uploadDocs, 
					 sum(case when s.ID is not null then 1 else 0 end) assessDocs
					 FROM admin_employees e  
					    left join t_doc d on e.id = d.POLICE_ID
						left join t_assessment_shooting s on s.DOC_ID = d.DOC_ID
					    where 1=1 {0} {1}  
					    group by e.id ) a on a.id = b.id
		    left join admin_position c on b.positionId = c.id
		    left join admin_organization o on o.id = b.organizationId
		    left join admin_region r on o.regionId = r.id
		    left join admin_department d1 on d1.id = b.departmentId
		    where 1=1 
		    {2} {3} {4} {5} {6}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and d.UPLOAD_TIME &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and d.UPLOAD_TIME  &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, 市/县 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>regionId</paramName>
                <t>and r.id = :regionId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, 组织 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>orgId</paramName>
                <t>and o.path like :orgId</t>
                <a class="IsTree"/>
              </path>
              <path>
                <paramName>orgId</paramName>
                <t>and o.path = :orgId</t>
                <a class="IsNotTree"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4, 部门 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>deptId</paramName>
                <t>and d1.id like :deptId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 5, 警号 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeCode</paramName>
                <t>and b.workNumber like :policeCode</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 6, 警员姓名 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeName</paramName>
                <t>and b.name like :policeName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 
  	   拍摄考核概况, 往月 hql
  	   直接读取考核报告	
  	-->
    <string>sumOfPassShootingAssessmentAll</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
		 select 
		   b.id policeId,
		   b.workNumber policeCode, 
		   b.name name,
		   c.position position,
		   b.male sex,
		   b.age age, 
		   IFNULL(a.totalDoc, 0),
		   IFNULL(a.totalAssessingDoc, 0),
		   IFNULL(a.totalAssessingDoc/a.totalDoc, 0) assessRate
		   from 
		    admin_employees b 
		    left join admin_position c on b.positionId = c.id
		    left join admin_organization o on o.id = b.organizationId
		    left join admin_region r on o.regionId = r.id
		    left join admin_department d1 on d1.id = b.departmentId
		    left join (
			    select * from
			        t_assessment_report report left join
			        t_assessment_shooting_summary a on report.shooting_assessment_sum_id = a.id       
			    where
			        1=1 and report.REPORT_TYPE = 2 
			        {0} {1} <!-- 考核周期 --> 
            ) a on a.police_id = b.id 
		    where 1=1 
			      {2} {3} {4}<!-- 所在市/县，局, 部门 -->
			      {5} <!-- 警号，模糊 -->
			      {6} <!-- 警员姓名，模糊  -->
		          {7} <!-- 考核率排序 -->
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and (report.START_DATE = :startDate or report.report_id is null)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and (report.END_DATE = :endDate or report.report_id is null)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, 市/县 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>regionName</paramName>
                <t>and r.regionName = :regionName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 组织 -->
        <!-- 3, org  -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>orgId</paramName>
                <t>and o.path like :orgId</t>
                <a class="IsTree"/>
              </path>
              <path>
                <paramName>orgId</paramName>
                <t>and o.path = :orgId</t>
                <a class="IsNotTree"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4，部门 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>deptName</paramName>
                <t>and d1.name like :deptName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 5，警号 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeCode</paramName>
                <t>and b.workNumber like :policeCode</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 6，警员姓名 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeName</paramName>
                <t>and b.name like :policeName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 7，考核率排序 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>assessRateOrder</paramName>
                <t></t>
                <a class="IsNull"/>
              </path>
              <path>
                <paramName>assessRateOrder</paramName>
                <t>order by a.totalAssessingDoc/a.totalDoc ASC, totalAssessingDoc DESC</t>
                <a class="OrderByASC"/>
              </path>
              <path>
                <paramName>assessRateOrder</paramName>
                <t>order by a.totalAssessingDoc/a.totalDoc DESC, totalAssessingDoc DESC</t>
                <a class="OrderByDESC"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 
  	   拍摄考核概况, 往月 , 总页数	
  	-->
    <string>sumOfPassShootingAssessmentAllTotalPage</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
		 select 
		   count(*) total
		   from 
		    admin_employees b 
		    left join admin_position c on b.positionId = c.id
		    left join admin_organization o on o.id = b.organizationId
		    left join admin_region r on o.regionId = r.id
		    left join admin_department d1 on d1.id = b.departmentId
		    left join (
			    select * from
			        t_assessment_report report left join
			        t_assessment_shooting_summary a on report.shooting_assessment_sum_id = a.id       
			    where
			        1=1 and report.REPORT_TYPE = 2 
			        {0} {1} <!-- 考核周期 --> 
            ) a on a.police_id = b.id 
		    where 1=1 
			      {2} {3} {4}<!-- 所在市/县，局, 部门 -->
			      {5} <!-- 警号，模糊 -->
			      {6} <!-- 警员姓名，模糊  -->
		   
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and report.START_DATE = :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and report.END_DATE = :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, 市/县 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>regionName</paramName>
                <t>and r.regionName = :regionName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 组织 -->
        <!-- 2, org  -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>orgId</paramName>
                <t>and o.path like :orgId</t>
                <a class="IsTree"/>
              </path>
              <path>
                <paramName>orgId</paramName>
                <t>and o.path = :orgId</t>
                <a class="IsNotTree"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 部门 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>deptName</paramName>
                <t>and d1.name like :deptName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 警号 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeCode</paramName>
                <t>and b.workNumber like :policeCode</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 警员姓名 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeName</paramName>
                <t>and b.name like :policeName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 分页，查询警员视频，带考核信息，拍摄时间排序 -->
    <string>queryAssessmentDocs</string>
    <sqlTemplate>
      <!-- sql -->
      <sqlPattern>
	      SELECT d.doc_id id,
	        d.doc_name docName, 
	        d.cate cate,
      		d.content_type type,
      		d.duration, 
      		d.doc_size fileM, 
      		d.video_clarity clarity, 
      		d.imp_level_rec impLevelRec,
      		d.imp_level impLevel, 
      		d.comment comments,
      		e.NAME enforceTypeName,
      		d.upload_time uploadTime, 
      		d.create_time createTime,
		    case when a.ID is not null then true else false end isAssessed,
		    a.assessment_cate assessmentCate,
		    a.ASSESS_DATE assessDate,
		    o.orgName orgName,
            o.path orgPath,
            p.workNumber policeCode, 
            p.name policeName,
            s.site_no siteCode, 
            s.address siteAddr, 
      		r.code equimentCode,
      		d.comment comments,
      		e.ID enforceTypeId
		  FROM t_doc d
		  left join t_assessment_shooting a on d.DOC_ID = a.DOC_ID
		  left join t_enforce_type e on d.enforce_id = e.ID
		  left join admin_employees p on d.police_id = p.id
		  left join admin_organization o on o.id = p.organizationId
		  left join t_site s on d.site_id = s.id 
          left join t_recorder r on d.recorder_id = r.id
		  where 1=1 {0} {1} {2} {3} {4} {5} {6} {7} {8} {9} {10}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeId</paramName>
                <t>and d.police_id = :policeId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, doc_name -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>docName</paramName>
                <t>and d.doc_name like :docName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 2, videoClarity -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>clarity</paramName>
                <t>and d.video_clarity = :clarity</t>
                <a class="IsNotNull"/>
              </path>          
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, imp level -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>impLevel</paramName>
                <t >and d.imp_level = :impLevel</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4, type -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>cate</paramName>
                <t >and d.cate = :cate</t>
                <a class="IsNotNull"/>
              </path>           
            </paths>
          </fork>
        </sqlPiece>
        <!-- 5 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>comments</paramName>
                <t >and d.comment like :comments</t>
                <a class="IsNotNull"/>
              </path>              
            </paths>
          </fork>
        </sqlPiece>
        <!-- 6 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>enforceTypeName</paramName>
                <t >and e.NAME like :enforceTypeName</t>
                <a class="IsNotNull"/>
              </path>              
            </paths>
          </fork>
        </sqlPiece>
        <!-- 7 -->
        <sqlPiece>
          <fork>
            <paths>  
              <path>
                <paramName>isAssessed</paramName>
                <t >and a.id is not null and :isAssessed = true</t>
                <a class="IsNotNull"/>
              </path>          
            </paths>
          </fork>
        </sqlPiece>
        <!-- 8, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and (d.upload_time &gt;= :startDate)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 9, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and (d.upload_time &lt; :endDate)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 10, 考核时间排序 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>assessingTimeOrder</paramName>
                <t></t>
                <a class="IsNull"/>
              </path>
              <path>
                <paramName>assessingTimeOrder</paramName>
                <t>order by d.upload_time ASC </t>
                <a class="com.jsdz.reportquery.dynsql.fork.support.OrderByASC"/>
              </path>
              <path>
                <paramName>assessingTimeOrder</paramName>
                <t>order by d.upload_time DESC </t>
                <a class="com.jsdz.reportquery.dynsql.fork.support.OrderByDESC"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 查询警员视频，带考核信息，总页数 -->
    <string>queryAssessmentDocsTotalPage</string>
    <sqlTemplate>
      <!-- sql -->
      <sqlPattern>
	      SELECT count(*) total
		  FROM t_doc d
		  left join t_assessment_shooting a on d.DOC_ID = a.DOC_ID
		  left join t_enforce_type e on d.enforce_id = e.ID
		  where 1=1 {0} {1} {2} {3} {4} {5} {6} {7} {8} {9}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 0, policeId -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeId</paramName>
                <t>and d.police_id = :policeId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, doc_name -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>docName</paramName>
                <t>and d.doc_name like :docName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 2, videoClarity -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>clarity</paramName>
                <t>and d.video_clarity = :clarity</t>
                <a class="IsNotNull"/>
              </path>          
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, imp level -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>impLevel</paramName>
                <t >and d.imp_level = :impLevel</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4, type -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>cate</paramName>
                <t >and d.cate = :cate</t>
                <a class="IsNotNull"/>
              </path>           
            </paths>
          </fork>
        </sqlPiece>
        <!-- 5 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>comments</paramName>
                <t >and d.comment like :comments</t>
                <a class="IsNotNull"/>
              </path>              
            </paths>
          </fork>
        </sqlPiece>
        <!-- 6 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>enforceTypeName</paramName>
                <t >and e.NAME like :enforceTypeName</t>
                <a class="IsNotNull"/>
              </path>              
            </paths>
          </fork>
        </sqlPiece>
        <!-- 7 -->
        <sqlPiece>
          <fork>
            <paths>   
              <path>
                <paramName>isAssessed</paramName>
                <t >and a.id is not null and :isAssessed = true</t>
                <a class="IsNotNull"/>
              </path>          
            </paths>
          </fork>
        </sqlPiece>
        <!-- 8, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and (d.upload_time &gt;= :startDate)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 9, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and (d.upload_time &lt; :endDate)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  <entry>
		<!-- sql名称 -->
		<!-- 考核工作考核统计 -->
		<string>searchWorkStatisticsBeanQuery</string>
		<sqlTemplate>
			<!-- sql模式 -->
			<sqlPattern>
			select 
			  a.*,
			 countOfTotalAssessing-countOfPushAssessing countOfChecking
			from (
			SELECT 
			    a.POLICE_ID id,
				b.name name,
				d1.upload_time startDate,
	            d1.upload_time endDate,
			    COUNT(*) countOfTotalAssessing,
			    SUM(CASE
					WHEN a.ASSESSMENT_CATE = 1 THEN 1
					ELSE 0
					END) countOfPushAssessing,
			    (select count(*) 
				   from t_assessment_push_document pd
			       LEFT JOIN t_doc d ON pd.DOC_ID = d.DOC_ID
			       where 1=1 and pd.ASSESSOR_ID = a.POLICE_ID {0} {1}) countOfPushed
			FROM
			    t_assessment_shooting a
					 LEFT JOIN admin_employees b on b.id=a.POLICE_ID
			     LEFT JOIN t_doc d1 ON a.DOC_ID = d1.DOC_ID
					 where 1=1 {0} {1}
			GROUP BY a.POLICE_ID) a
			</sqlPattern>
			<!-- 返回Bean -->
			<itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentWorkStatisticsBean</itemClass>
			<!-- sql可变块 -->
		<!-- 1, startDate -->
		<pieces>
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and (d1.upload_time &gt;= :startDate)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endDate -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and (d1.upload_time &lt; :endDate)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        </pieces>
		</sqlTemplate>
	</entry>
	<!-- 总页数 -->
	<entry>
		<string>searchWorkStatisticsBeanQueryTotalPage</string>
		<sqlTemplate>
			<!-- sql模式 -->
			<sqlPattern>
			select COUNT(*) total
			from (
			SELECT 
			    a.POLICE_ID id,
					b.name name,
			    COUNT(*) countOfTotalAssessing,
			    SUM(CASE
					WHEN a.ASSESSMENT_CATE = 1 THEN 1
					ELSE 0
					END) countOfPushAssessing,
			    (select count(*) 
				   from t_assessment_push_document pd
			       LEFT JOIN t_doc d ON pd.DOC_ID = d.DOC_ID
			       where 1=1 and pd.ASSESSOR_ID = a.POLICE_ID {0} {1}) countOfPushed
			FROM
			    t_assessment_shooting a
					 LEFT JOIN admin_employees b on b.id=a.POLICE_ID
			     LEFT JOIN t_doc d1 ON a.DOC_ID = d1.DOC_ID
					 where 1=1 {0} {1}
			GROUP BY a.POLICE_ID) a
			</sqlPattern>
			<!-- 返回Bean -->
			<itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentWorkStatisticsBean</itemClass>
			<!-- sql可变块 -->
			<!-- 1, startDate -->
		<pieces>
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and (d1.upload_time &gt;= :startDate)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endDate -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and (d1.upload_time &lt; :endDate)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        </pieces>
		</sqlTemplate>
	</entry>
</sqlTemplates>