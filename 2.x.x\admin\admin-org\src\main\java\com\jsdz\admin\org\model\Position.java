package com.jsdz.admin.org.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @类名: Position
 * @说明: 职位
 *
 * @author: kenny
 * @Date	2017年4月26日上午11:52:22
 * 修改记录：
 *
 * @see
 */
public class Position implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3973062140152047921L;
	
	private Long id;
	private String position; //职位
	private String description; //说明
	private Date createTime;
	
	public Position(){
		super();
	}
	
	public Position(String position) {
		this.position = position;
	}
		
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}

	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getPosition() {
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}
	

	

}
