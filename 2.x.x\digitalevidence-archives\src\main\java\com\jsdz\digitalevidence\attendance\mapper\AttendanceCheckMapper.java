package com.jsdz.digitalevidence.attendance.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceCheckBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceCheckModel;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface AttendanceCheckMapper extends BaseMapper<AttendanceCheckModel> {
    @Select("${sql}")
    List<AttendanceCheckBean> getDatas(@Param("sql") String sql);

    @Select("${sql}")
    Integer getCount(@Param("sql") String sql);

    @Insert("${sql}")
    void saveData(@Param("sql") String sql);

    @Update("${sql}")
    void updateData(@Param("sql") String sql);

    @Select("${sql}")
    AttendanceCheckModel getById1(@Param("sql") String sql);

    @Delete("${sql}")
    void deleteById(@Param("sql") String sql);
}
