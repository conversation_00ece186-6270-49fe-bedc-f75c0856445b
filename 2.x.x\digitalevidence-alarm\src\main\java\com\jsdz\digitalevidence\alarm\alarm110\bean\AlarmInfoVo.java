package com.jsdz.digitalevidence.alarm.alarm110.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AjaxResult;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;

import java.util.Date;
import java.util.List;

public class AlarmInfoVo {

    private String alarmCode;    //警情号
    private String alarmName;    //报警人姓名
    private String alarmTel;    //报警电话
    private String alarmContext;    //报警内容
    private String alarmAddress;//警情发生地址
    private String alarmPolices;//处警人，多个人以,号隔开
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date alarmTime;    //报警时间
    private List<String> docList; //文件列表

    public String getAlarmCode() {
        return alarmCode;
    }

    public void setAlarmCode(String alarmCode) {
        this.alarmCode = alarmCode;
    }

    public String getAlarmName() {
        return alarmName;
    }

    public void setAlarmName(String alarmName) {
        this.alarmName = alarmName;
    }

    public String getAlarmTel() {
        return alarmTel;
    }

    public void setAlarmTel(String alarmTel) {
        this.alarmTel = alarmTel;
    }

    public String getAlarmContext() {
        return alarmContext;
    }

    public void setAlarmContext(String alarmContext) {
        this.alarmContext = alarmContext;
    }

    public String getAlarmAddress() {
        return alarmAddress;
    }

    public void setAlarmAddress(String alarmAddress) {
        this.alarmAddress = alarmAddress;
    }

    public String getAlarmPolices() {
        return alarmPolices;
    }

    public void setAlarmPolices(String alarmPolices) {
        this.alarmPolices = alarmPolices;
    }

    public List<String> getDocList() {
        return docList;
    }

    public void setDocList(List<String> docList) {
        this.docList = docList;
    }

    public Date getAlarmTime() {
        return alarmTime;
    }

    public void setAlarmTime(Date alarmTime) {
        this.alarmTime = alarmTime;
    }

    public AlarmInfoVo sign(AlarmInfo alarmInfo) {
        this.alarmCode = alarmInfo.getAlarmCode();
        this.alarmName = alarmInfo.getAlarmName();
        this.alarmTel = alarmInfo.getAlarmTel();
        this.alarmContext = alarmInfo.getAlarmContext();
        this.alarmAddress = alarmInfo.getAlarmAddress();
        this.alarmPolices = alarmInfo.getAlarmPolices();
        return this;
    }
}
