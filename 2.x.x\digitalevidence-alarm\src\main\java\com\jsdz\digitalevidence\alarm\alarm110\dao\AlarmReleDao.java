package com.jsdz.digitalevidence.alarm.alarm110.dao;

import com.jsdz.digitalevidence.alarm.alarm110.model.AlaBiDoc;

/**
 * 
 * @类名: AlarmReleDao
 * @说明: AlaBiDoc相关的dao
 *
 * <AUTHOR>
 * @Date	 2018年4月23日 下午4:02:23
 * 修改记录：
 *
 * @see
 */
public interface AlarmReleDao {
	
	// 查询是否重复
	public AlaBiDoc findMutipleAlaBiDoc(AlaBiDoc alaBiDoc);
	
	// 新增AlaBiDoc
	public void addAlaBiDoc(AlaBiDoc alaBiDoc);
	
	// 删除AlaBiDoc
	public void deleteAlaBiDoc(AlaBiDoc alaBiDoc);
	
	// 更新操作
	public void updateAlaBiDoc(AlaBiDoc mutipleAlaBiDoc);
	
}
