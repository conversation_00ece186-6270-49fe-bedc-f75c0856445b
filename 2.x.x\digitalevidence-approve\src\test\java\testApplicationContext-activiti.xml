<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans   http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="processEngineConfiguration" class="org.activiti.spring.SpringProcessEngineConfiguration">
		<property name="dataSource" ref="dataSource" />
		<property name="transactionManager" ref="transactionManager" />
		<!-- <property name="databaseSchemaUpdate" value="drop-create"/> -->
		<!-- <property name="databaseSchemaUpdate" value="true" /> -->
		<property name="databaseSchemaUpdate" value="false"/>
		<!-- 邮件 -->
		<!-- 
		<property name="mailServerHost" value="smtp.sina.com" />  
        <property name="mailServerPort" value="587" />   
        <property name="mailServerDefaultFrom" value="<EMAIL>" />  
        <property name="mailServerUsername" value="<EMAIL>"></property>    
        <property name="mailServerPassword" value="lzqiang4683568"></property>   
        <property name="mailServerUseSSL" value="true"></property>	
		-->
		<property name="jobExecutorActivate" value="false" />
		<property name="asyncExecutorEnabled" value="true" />
		<property name="asyncExecutorActivate" value="true" />
		<!-- 事件监听器 -->
		<property name="eventListeners">  
	      <list>  
	        <ref bean="globalEventListener"/>  
	      </list>  
	    </property>  
		<!-- 自定义Id服务 -->
		<!-- <property name="customSessionFactories">
			<list>
				<bean
					class="com.quauq.yanzhenxing.activiti.service.ext.ActUserEntityServiceFactory" />
				<bean
					class="com.quauq.yanzhenxing.activiti.service.ext.ActGroupEntityServiceFactory" />
			</list>
		</property> -->
		<property name="activityFontName" value="宋体"/>
        <property name="labelFontName" value="宋体"/>
	</bean>

    <bean id="processEngine" class="org.activiti.spring.ProcessEngineFactoryBean">
        <property name="processEngineConfiguration" ref="processEngineConfiguration"/>
    </bean>

    <bean id="repositoryService" factory-bean="processEngine" factory-method="getRepositoryService"/>
    <bean id="runtimeService" factory-bean="processEngine" factory-method="getRuntimeService"/>
    <bean id="taskService" factory-bean="processEngine" factory-method="getTaskService"/>
    <bean id="historyService" factory-bean="processEngine" factory-method="getHistoryService"/>
    <bean id="managementService" factory-bean="processEngine" factory-method="getManagementService"/>
    <bean id="identityService" factory-bean="processEngine" factory-method="getIdentityService" />
    <bean id="formService" factory-bean="processEngine" factory-method="getFormService" />

</beans>