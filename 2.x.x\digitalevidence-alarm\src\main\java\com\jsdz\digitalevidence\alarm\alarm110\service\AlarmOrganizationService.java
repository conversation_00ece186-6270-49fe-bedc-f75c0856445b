package com.jsdz.digitalevidence.alarm.alarm110.service;

/**
 * 
 * @类名: AlarmOrganizationService
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-08-29 21:08:18
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmOrganization;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface AlarmOrganizationService {

	//新增
	public AjaxResult addAlarmOrganization(AlarmOrganization alarmOrganization);

	//修改
	public AjaxResult updateAlarmOrganization(AlarmOrganization alarmOrganization);

	//删除
	public AjaxResult deleteAlarmOrganization(AlarmOrganization alarmOrganization);

	//按id查询,结果是游离状态的数据
	public AlarmOrganization findAlarmOrganizationById(Long id);

	//按id查询
	public AlarmOrganization locateAlarmOrganizationById(Long id);

	//单个查询
	public AlarmOrganization findAlarmOrganizationByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmOrganization> findAllAlarmOrganizations();

	//列表查询
	public List<AlarmOrganization> findAlarmOrganizationsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmOrganization> findAlarmOrganizationsOnPage(Page<AlarmOrganization> page, String queryStr,String[] paramNames,Object[] values);

}

