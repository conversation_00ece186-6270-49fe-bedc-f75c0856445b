package com.jsdz.digitalevidence.assessment.model.alert.rule;

import java.util.ArrayList;
import java.util.List;

import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.ruleengine.RuleState;
import com.jsdz.ruleengine.annotation.Given;
import com.jsdz.ruleengine.annotation.Param;
import com.jsdz.ruleengine.annotation.Result;
import com.jsdz.ruleengine.annotation.Rule;
import com.jsdz.ruleengine.annotation.Then;
import com.jsdz.ruleengine.annotation.When;

/**
 * @类名: ShootingAssessmentShootingAngle
 * @说明: 拍摄考核拍摄角度规则
                   
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 
 * 修改记录：
 *
 * @see
 */
@Rule(name="psAngleAcurateRule", description="拍摄角度准确百分比规则")
public class ShootingAssessmentAngleRule {
	
	public static final String key = "拍摄角度准确百分比";

	@Param(name="psAngleAcuratePercent", description="按要求拍摄百分比")
	private float psAngleAcuratePercent;
	
	/** */
	@Result
	private List<AssessmentAlertItem<?>> _result;
	
	@Given
	private ShootingAssessmentSummary sum;
	
	@When
	public boolean when() {
		if((float)sum.getCountOfShootingAngleA()/sum.getTotalAssessingDoc()<psAngleAcuratePercent)
			return true;
		return false;
	}

	@Then
	public RuleState then() {
		AssessmentAlertItem<Float> item = new AssessmentAlertItem<Float>();
		item.setItem(key);
		item.setV((float)sum.getCountOfShootingAngleA()/sum.getTotalAssessingDoc());
		item.setRef(psAngleAcuratePercent);
		if(_result==null)
			_result = new ArrayList<AssessmentAlertItem<?>>();
		_result.add(item);
		return RuleState.NEXT;
	}
	
}
