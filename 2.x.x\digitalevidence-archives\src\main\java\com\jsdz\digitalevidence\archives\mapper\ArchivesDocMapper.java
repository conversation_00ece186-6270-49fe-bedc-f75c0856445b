package com.jsdz.digitalevidence.archives.mapper;

import com.jsdz.digitalevidence.archives.model.ArchivesDoc;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * VIEW Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-11
 */
public interface ArchivesDocMapper {

    @Select("select * from v_archives_doc where archivesId = #{archivesId}")
    List<ArchivesDoc> findDocByArchivesId(Long archivesId);
}
