package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: EmployeesImportDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-11-04 18:25:26
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.EmployeesImportDao;
import com.jsdz.admin.security.model.EmployeesImport;
import com.jsdz.admin.security.model.Role;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class EmployeesImportDaoImpl extends GenericEntityDaoHibernateImpl<EmployeesImport,Long> implements EmployeesImportDao{

	//新增
	public void addEmployeesImport(EmployeesImport employeesImport) {
		this.saveOrUpdate(employeesImport);
	}

	//删除
	public void deleteEmployeesImport(EmployeesImport employeesImport) {
		this.delete(employeesImport);
	}

	//修改
	public void updateEmployeesImport(EmployeesImport employeesImport) {
		this.merge(employeesImport);
	}

	//按id查询(游离状态)
	public EmployeesImport findEmployeesImportById(Long id){

		final String  hql = "from EmployeesImport e where e.id = :id";
		final Long oid = id;
		EmployeesImport data = getHibernateTemplate().execute(new HibernateCallback<EmployeesImport>() {
			public EmployeesImport doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<EmployeesImport> list = query.list();
				EmployeesImport rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public EmployeesImport locateEmployeesImportById(Long id){

		final String  hql = "from EmployeesImport e where e.id = :id";
		final Long oid = id;
		EmployeesImport data = getHibernateTemplate().execute(new HibernateCallback<EmployeesImport>() {
			public EmployeesImport doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<EmployeesImport> list = query.list();
				EmployeesImport rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public EmployeesImport findEmployeesImportByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		EmployeesImport data = getHibernateTemplate().execute(new HibernateCallback<EmployeesImport>() {
		public EmployeesImport doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<EmployeesImport> list = query.list();
			EmployeesImport rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<EmployeesImport> findAllEmployeesImports(){
		return this.find("from EmployeesImport employeesImport ");
	}

	//列表查询
	public List<EmployeesImport> findEmployeesImportsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<EmployeesImport> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<EmployeesImport> findEmployeesImportsOnPage(Page<EmployeesImport> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<EmployeesImport>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//执行简单的Hql
	@Override
	public int execSimpleHql(String execHql,String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(execHql);
		for(int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		int result = query.executeUpdate();
		session.close();
		return result;

/*		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlDelete = "delete RoleAction r where r.role.id = :id";
		int deleteds = session.createQuery(hqlDelete)
				.setString( "id", role.getId().toString())
				.executeUpdate();
		session.close();
*/	}
}
