package com.jsdz.admin.security.service;
/**
 * 
 * @类名: OperatorService
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年4月28日上午11:46:39
 * 修改记录：
 *
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;


public interface OperatorService {
	
		/**
		 * @说明：按登录名查询操作员
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年5月2日 上午8:34:25
		 */
	//public Operator findOperator(Operator operator);
	
	public AjaxResult addOperator(Operator operator);
	
	public AjaxResult updateOperator(Operator operator);

	public AjaxResult deleteOperator(Operator operator);

	//按id查询
	public Operator locateOperatorById(Long id);
	
	//查询多个
	public List<Operator> findOperatorsByParam(String queryStr,String[] paramNames,Object[] values);
	
	//查询单个
	public Operator findOperatorByParam(String queryStr,String[] paramNames,Object[] values);
	
		/**
		 * @说明：登录
		 *
		 * <AUTHOR>
		 * @param operator
		 * @return
		 * 2017年4月29日 上午11:41:29
		 */
	public AjaxResult login(Operator operator, String ip);
	
		/**
		 * @说明：修改密码
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年4月29日 下午5:03:58
		 */
	public AjaxResult changePassword(Operator operator,String oldPassword,String newPassword); 
	

		/**
		 * @说明：操作员分页查询
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年5月2日 上午8:33:35
		 */
	public Page<Operator> findOperatorOnPage(Page<Operator> page, String queryStr, String[] paramNames,
			Object[] values); 
	
		/**
		 * @说明：重置密码
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年5月5日 下午3:35:31
		 */
	public AjaxResult resetPassword(Operator operator); 
	
	//改变状态
		/**
		 * @说明：改变状态
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年5月6日 下午4:32:52
		 */

	AjaxResult changeOperator(Operator operator);

	AjaxResult loginNoPass(Operator operator, String remoteIp);
}
