package com.jsdz.admin.security.model;

import java.util.Date;

/**
 * 
 * @类名: DictionaryValues
 * @说明: 数据字典明细
 *
 * @author: kenny
 * @Date	2017年10月31日上午11:26:21
 * 修改记录：
 *
 * @see
 */
public class DictionaryValues {
	private Long id;
	private String value;
	private String note;
	private Integer order; //显示顺序
	private DataDictionary dictionary;
	private Date createTime;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public Integer getOrder() {
		return order;
	}
	public void setOrder(Integer order) {
		this.order = order;
	}
	public DataDictionary getDictionary() {
		return dictionary;
	}
	public void setDictionary(DataDictionary dictionary) {
		this.dictionary = dictionary;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
}
