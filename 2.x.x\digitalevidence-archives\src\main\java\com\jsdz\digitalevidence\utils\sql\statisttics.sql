-- 设备在线情况统计 (具备按部门、采集站台数、执法记录仪数量、执法人员数量、日均开机台数、录制时长、日均录制时长、采集文件总数、日均采集文件总数)

---------------------------------------------------------------------------------------------------------------

-- 新建统计表
CREATE TABLE SYS_DAILY_DEVICE
(
    ID             BIGINT PRIMARY KEY AUTO_INCREMENT,
    ORGID          BIGINT,
    ORGNAME        VARCHAR(255),
    ORGCODE        VARCHAR(255),
    ONLINEQUANTITY VARCHAR(255),
    DOCNUM         BIGINT,
    DOCDURATION    BIGINT,
    DOCSITE        BIGINT
);
----------------------------------------------------------------------------------------------------------------
-- 初始化数据

INSERT INTO SYS_DAILY_DEVICE (ORGID, ORGNAME, ORGCODE, ONLINEQUANTITY, DOCNUM, DOCDURATION, DOCSITE, STATISTICALTIME)
SELECT
    D.ORG_ID,
    O.ORGNAME,
    O.ORGCODE,
    COUNT(DISTINCT DR.DEVICE_ID) AS ONLINE_T_RECORDER,
    SUM(CASE WHEN T.UPLOAD_DATE IS NOT NULL THEN 1 ELSE 0 END) AS ZONGSHU,
    SUM(CASE WHEN T.UPLOAD_DATE IS NOT NULL THEN T.TOTAL_DURATION ELSE 0 END) AS DURATION,
    SUM(CASE WHEN T.UPLOAD_DATE IS NOT NULL THEN T.TOTAL_DOC_SIZE ELSE 0 END) AS DOC_SIZE,
    TO_CHAR(DR.GPS_TIME, 'YYYY-MM-DD') AS DATA_DATE
FROM
    T_RECORDER D
        JOIN
    GRAPHENSEMBLE_GPS_INFO DR ON D.ID = DR.DEVICE_ID
        JOIN
    ADMIN_ORGANIZATION O ON D.ORG_ID = O.ID
        LEFT JOIN (
        SELECT
            ORGANIZATION_ID,
            TO_CHAR(UPLOAD_TIME, 'YYYY-MM-DD') AS UPLOAD_DATE,
            COUNT(1) AS DOC_COUNT,
            SUM(DURATION) AS TOTAL_DURATION,
            SUM(DOC_SIZE) AS TOTAL_DOC_SIZE
        FROM
            T_DOC
        GROUP BY
            ORGANIZATION_ID,
            TO_CHAR(UPLOAD_TIME, 'YYYY-MM-DD')
    ) T ON D.ORG_ID = T.ORGANIZATION_ID AND TO_CHAR(DR.GPS_TIME, 'YYYY-MM-DD') = T.UPLOAD_DATE
GROUP BY
    D.ORG_ID,
    O.ORGNAME,
    O.ORGCODE,
    TO_CHAR(DR.GPS_TIME, 'YYYY-MM-DD')
HAVING
    COUNT(DISTINCT DR.DEVICE_ID) > 0;

-----------------------------------------------------------------------------------------------------------------
--新增存储过程
CREATE OR REPLACE PROCEDURE "ROOT"."DAILY_DEVICE_STATISTICS"
AUTHID DEFINER

AS
    PREV_DAY DATE; -- 前一天时间
CURSOR CUR_DAILY_STATS IS
SELECT
    A.ID AS ORGID,
    A.ORGCODE,
    A.ORGNAME,
    COUNT(DISTINCT G.DEVICE_ID) AS ONLINEQUANTITY,
    COUNT(1) AS DOCNUM,
    SUM(D.DURATION) AS DOCDURATION,
    SUM(D.DOC_SIZE) AS DOCSIZE
FROM
    ADMIN_ORGANIZATION A
        LEFT JOIN
    GRAPHENSEMBLE_GPS_INFO G ON G.DEVICE_ID IN (SELECT ID FROM T_RECORDER WHERE ORG_ID = A.ID AND GPS_TIME >= TRUNC(SYSDATE) - 1)
        LEFT JOIN
    T_DOC D ON D.ORGANIZATION_ID = A.ID AND D.UPLOAD_TIME >= TRUNC(SYSDATE) - 1
GROUP BY
    A.ID, A.ORGCODE, A.ORGNAME;
BEGIN
    -- 获取前一天的日期
SELECT TRUNC(SYSDATE) - 1 INTO PREV_DAY FROM DUAL;

-- 批量插入数据
FOR R_DAILY_STATS IN CUR_DAILY_STATS LOOP
        INSERT INTO SYS_DAILY_DEVICE (ORGID, ORGNAME, ORGCODE, ONLINEQUANTITY, DOCNUM, DOCDURATION, DOCSITE, STATISTICALTIME)
        VALUES (R_DAILY_STATS.ORGID, R_DAILY_STATS.ORGNAME, R_DAILY_STATS.ORGCODE, R_DAILY_STATS.ONLINEQUANTITY, R_DAILY_STATS.DOCNUM, R_DAILY_STATS.DOCDURATION, R_DAILY_STATS.DOCSIZE, PREV_DAY);
END LOOP;

COMMIT;
EXCEPTION
    WHEN OTHERS THEN
        -- 异常处理
        DBMS_OUTPUT.PUT_LINE('AN ERROR OCCURRED: ' || SQLERRM);
ROLLBACK; -- 如果发生异常，回滚事务
END;

----------------------------------------------------------------------------------------------------------------
-- 新增统计视图（设备在线情况统计）
CREATE VIEW V_EQUIPMENT_ONLINE_STATISTICS AS
SELECT
    ORG.ID,
    ORG.ORGCODE,
    ORG.ORGNAME,
    ORG.PATH PATH,
    ORG.PARENTID ,
    (SELECT COUNT(1) FROM T_SITE S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE S.SITE_TYPE=1 AND O.PATH LIKE CONCAT(ORG.PATH,'')) SITEONESELF ,
    (SELECT COUNT(1) FROM T_SITE S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE S.SITE_TYPE=1 AND O.PATH LIKE CONCAT(ORG.PATH,' %')) SITEALL ,
    (SELECT COUNT(1) FROM T_SITE S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE S.SITE_TYPE=2 AND O.PATH LIKE CONCAT(ORG.PATH,'')) STORAGEONESELF ,
    (SELECT COUNT(1) FROM T_SITE S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE S.SITE_TYPE=2 AND O.PATH LIKE CONCAT(ORG.PATH,' %')) STORAGEALL ,
    (SELECT COUNT(1) FROM ADMIN_EMPLOYEES S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORGANIZATIONID = O.ID WHERE O.PATH LIKE CONCAT(ORG.PATH,'')) EMPLOYEESONESELF,
    (SELECT COUNT(1) FROM ADMIN_EMPLOYEES S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORGANIZATIONID = O.ID WHERE O.PATH LIKE CONCAT(ORG.PATH,'%')) EMPLOYEESALL,
    (SELECT COUNT(1) FROM T_RECORDER S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORG_ID = O.ID WHERE O.PATH LIKE CONCAT(ORG.PATH,'')) RECORDERONESELF,
    (SELECT COUNT(1) FROM T_RECORDER S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORG_ID = O.ID WHERE O.PATH LIKE CONCAT(ORG.PATH,'%')) RECORDERALL,
    (SELECT COUNT(1) FROM T_RECORDER S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORG_ID = O.ID WHERE O.PATH LIKE CONCAT(ORG.PATH,'') AND S.IS_DELETED = 0) RECORDERNORMALONESELF,
    (SELECT COUNT(1) FROM T_RECORDER S LEFT JOIN ADMIN_ORGANIZATION O ON S.ORG_ID = O.ID WHERE O.PATH LIKE CONCAT(ORG.PATH,'%') AND S.IS_DELETED = 0) RECORDERNORMALALL,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'')) AS DOCONESELF,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCALL,
    (SELECT SUM(DURATION) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'')) AS ZONGDOCDURATIONONESELF,
    (SELECT SUM(DURATION) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS ZONGDOCDURATIONALL,
    (SELECT AVG(ONLINEQUANTITY) FROM SYS_DAILY_DEVICE  S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,''))	AS	ONLINEQUANTITYONESELF,
    (SELECT AVG(ONLINEQUANTITY)  FROM SYS_DAILY_DEVICE  S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS	ONLINEQUANTITYALL,
    (SELECT AVG(DOCNUM) FROM  SYS_DAILY_DEVICE  S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,''))	AS	DOCUNMONESELF,
    (SELECT AVG(DOCNUM)  FROM SYS_DAILY_DEVICE  S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'%'))	AS	DOCUNM,
    (SELECT AVG(DOCDURATION) FROM  SYS_DAILY_DEVICE  S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,''))	AS	DOCDURATIONONESELF,
    (SELECT AVG(DOCDURATION) FROM  SYS_DAILY_DEVICE  S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'%'))	AS	DOCDURATIONALL,
    (SELECT AVG(DOCSITE) FROM  SYS_DAILY_DEVICE  S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,''))	AS	DOCSIZEONESELF,
    (SELECT AVG(DOCSITE) FROM  SYS_DAILY_DEVICE  S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCSIZEALL
FROM  ADMIN_ORGANIZATION ORG;


SELECT * FROM V_EQUIPMENT_ONLINE_STATISTICS;
-----------------------------------------------------------------------------------------------------------------------
-- 具备按部门、电子证据总数、音频、视频、图片数量、归档情况、超时未上传、未关联事件证据、关联事件数量等维度统计分析能力。
-- 电子证据总数、音频、视频、图片数量、归档情况、超时未上传、未关联事件证据、关联事件数量

CREATE VIEW V_EVIDENCE_STATISTICS AS
SELECT
    ORG.ID,
    ORG.ORGCODE,
    ORG.ORGNAME,
    ORG.PATH PATH,
    ORG.PARENTID ,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'')) AS DOCONESELF,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCALL,

    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE S.CATE = 2 AND  O.PATH LIKE CONCAT(ORG.PATH,'')) AS DOCMP4ONESELF,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  S.CATE = 2 AND  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCMP4ALL,

    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE S.CATE = 1 AND  O.PATH LIKE CONCAT(ORG.PATH,'')) AS DOCAUDIOONESELF,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  S.CATE = 1 AND  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCAUDIOALL,

    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE S.CATE = 0 AND  O.PATH LIKE CONCAT(ORG.PATH,'')) AS DOCPICTUREONESELF,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  S.CATE = 0 AND  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCPICTUREALL,

    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE S.ARCHIVES_ID IS NOT NULL AND  O.PATH LIKE CONCAT(ORG.PATH,'')) AS ARCHIVESYES,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  S.ARCHIVES_ID IS  NULL AND  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS ARCHIVESNO,

    (SELECT COUNT(1) FROM T_DOC S  LEFT JOIN T_ALARM_RELATION A ON S.DOC_ID = A.DOCUMENT_ID  LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE A.DOCUMENT_ID IS NOT NULL AND  O.PATH LIKE CONCAT(ORG.PATH,'')) AS RELATIONYES,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN T_ALARM_RELATION A ON S.DOC_ID = A.DOCUMENT_ID  LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  A.DOCUMENT_ID IS  NULL AND  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS RELATIONNO

FROM  ADMIN_ORGANIZATION ORG;

SELECT * FROM V_EVIDENCE_STATISTICS;

-----------------------------------------------------------------------------------------------------------
--对包括不限于诸如执法无视频、未按时上传、超短视频、接处警到达时间过长等异常数据，并形成监督记录。
-- 移动执法视音频调度平台可对各单位及个人的异常项目
-- （包括不限于执法记录仪空录、执法记录仪7天闲置、评价打分情况、工作站在线时长低于50%、工作站容量超80%、文件修改和文件删除）进行统计分析和预警的功能。
---------------------------------------------
--暂定思路 新建异常统计表 id ，类型（文档-未按时上传、超短视频、接处警到达时间过长，采集站-在线时长，执法仪），类型名称，异常详情，异常统计时间，
-- 执法仪编号，采集站编号，警员名称，警号，单位编号，单位名称


CREATE TABLE  SYS_ABNORMAL_STATISTICS(
     ID             BIGINT PRIMARY KEY AUTO_INCREMENT,
     TYPECODE		BIGINT,
     TYPENAME    VARCHAR(255),
     ABNORMALDOC VARCHAR(255),
     ORGNAME        VARCHAR(255),
     ORGCODE        VARCHAR(255),
     ORGPATH        VARCHAR(255),
     DESCRIBE 		VARCHAR(4000),
     RECORDERCODE  VARCHAR(255),
     SITECODE   VARCHAR(255),
     SITENAME VARCHAR(255),
     USERCODE VARCHAR(255),
     USERNAME VARCHAR(255),
     CREATETIME  	TIMESTAMP
);

-------------------------------------------------------------
--初始化执法场所视图
CREATE VIEW V_VIDEO_RELATED_ATTRIBUTES
    AS SELECT
C.CASE_CODE,
R.RELATION_TIME,
D.DOC_NAME,
D.TYPE AS DOC_TYPE,
D.CATE AS DOC_CATE,
D.DOC_SIZE,
D.DURATION AS DOC_DURATION,
CONCAT(
  S1.HTTP,
  '/',
  D.DOC_URI,
  '/',
  D.DOC_NAME
) AS SITE_PALY,
CONCAT(
  S2.HTTP,
  '/',
  D.DOC_URI,
  '/',
  D.DOC_NAME
) AS STORAGE_PALY
FROM
T_CASE_RELATION R
JOIN T_CASE C ON R.CASE_ID = C.CASE_ID
JOIN T_DOC D ON R.DOCUMENT_ID = D.DOC_ID
JOIN T_SITE S1 ON D.SITE_ID = S1.ID
JOIN T_SITE S2 ON D.STORAGE_ID = S2.ID;


--------------------------------------------------------------------------------------------------------
--设置执法场所用户
DROP USER ZFBAYSP ;
CREATE USER ZFBAYSP IDENTIFIED BY "ZFBAYSP@2024";
GRANT SELECT ON JSDZ_4GDB.V_VIDEO_RELATED_ATTRIBUTES TO ZFBAYSP;


----------------------------------------------------------------------------------------------------------
--考勤全部查询20240322
CREATE VIEW V_ATTENDANCE_ALL AS SELECT
     C.ID,
     C.LONGITUDE,
     C.LATITUDE,
     C.ATTENDANCETIME,
     C.ISAUTOMATIC,
     T.ATTENDANCENAME,
     T.ATTENDANCETIME AS TATTENDANCETIME,
     T.ADVANCEBYMINUTES,
     T.ATTENDANCEDESCRIBE,
     O.ID AS ORGID,
     O.ORGNAME,
     O.ORGCODE,
     O.PATH,
     E. NAME AS USERNAME,
     E.WORKNUMBER,
     R. CODE AS DEVICECODE,
     R.ID AS DEVICEID
 FROM
     SYS_ATTENDANCE_CHECK C
         LEFT JOIN SYS_ATTENDANCE_INFO I ON C.ATTENDANCEINFOID = I.ID
         LEFT JOIN SYS_ATTENDANCE_TASK T ON I.ATTENDANCETASKID = T.ID
         LEFT JOIN ADMIN_ORGANIZATION O ON I.ATTENDANCEORGID = O.ID
         LEFT JOIN ADMIN_EMPLOYEES E ON I.ATTENDANCEUSERID = E.ID
         LEFT JOIN T_RECORDER R ON I.DEVICEID = R.ID;
------------------------------------------------------------------
--20240322

DROP TABLE "SYS_ATTENDANCE_INFO" RESTRICT;
CREATE TABLE "SYS_ATTENDANCE_INFO"
(
    "ID" BIGINT IDENTITY(3, 1) NOT NULL,
    "ATTENDANCETASKID" BIGINT,
    "ATTENDANCEORGID" BIGINT,
    "ATTENDANCEUSERID" VARCHAR(255),
    "DEVICEID" BIGINT,
    "ISAUTOMATIC" VARCHAR(255),
    "ATTENDANCESCOPERICE" VARCHAR(255),
    "ADVANCEBYMINUTES" VARCHAR(255),
    "LASTAATTENDANCETIME" TIMESTAMP(0),
    "CREATETIME" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
    "UPDATETIME" TIMESTAMP(0),
    NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."ADVANCEBYMINUTES" IS '可提前多少分钟打卡,如果不在任务表范围内这字段失效';
COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."ATTENDANCEORGID" IS '单位';
COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."ATTENDANCESCOPERICE" IS '离考勤范围多少米可打卡,如果不在任务表范围内这字段失效';
COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."ATTENDANCETASKID" IS '执行K考勤任务的ID';
COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."ATTENDANCEUSERID" IS '考勤人';
COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."CREATETIME" IS '创建时间';
COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."DEVICEID" IS '考勤设备';
COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."ISAUTOMATIC" IS '是否允许自动打卡（0允许，1不允许）,如果任务不允许自动打卡这字段失效';
COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."LASTAATTENDANCETIME" IS '最后一次打卡时间';
COMMENT ON COLUMN "SYS_ATTENDANCE_INFO"."UPDATETIME" IS '修改时间';

----------------------------------------------------------------------------------------
--20240327
CREATE TABLE SYS_DELETE_APPROVAL (
     ID BIGINT PRIMARY KEY AUTO_INCREMENT,
     DOCID BIGINT NOT NULL,
     REQUESTUSERDESCRIBE VARCHAR(4000),
     REQUESTUSERID BIGINT NOT NULL,
     REQUESTUSERNAME VARCHAR(50) NOT NULL,
     REQUESTORGID BIGINT NOT NULL,
     REQUESTORGNAME VARCHAR(50) NOT NULL,
     APPROVEDUSEID BIGINT,
     APPROVEDUSENAME VARCHAR(50),
     APPROVEDORGID BIGINT,
     APPROVEDORGNAME VARCHAR(100),
     REVIEWCOMMENTS VARCHAR(50),
     ISAGREE INT ,
     APPROVEDTIME TIMESTAMP,
     EXPIRETIME TIMESTAMP,
     TASKSTATUS INT DEFAULT 0,
     CREATETIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
     UPDATETIME TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-----------------------------------------------------------
--20240329修改考勤表字段
ALTER TABLE SYS_ATTENDANCE_TASK ADD COLUMN isDelete INT DEFAULT 0;
ALTER TABLE SYS_ATTENDANCE_INFO ADD COLUMN isDelete INT DEFAULT 0;
ALTER TABLE SYS_ATTENDANCE_INFO ADD COLUMN deviceCode VARCHAR(200);
-----------------------------------------------------------


--20240407
--执行之前需修改t_doc_org 视图 改 为 D.*
drop view v_doc_enfor;
create view v_doc_enfor as
select D.*,ai.alarm_CODE as alarmCode,ai.alarm_name as alarmName,1 alarmType,ALARM_CONTEXT,AR.RELATION_TIME from T_ALARM_RELATION AR left join T_ALARM_INFO AI ON AR.ALARMINFO_ID = AI.ID
     LEFT JOIN T_DOC_ORG D ON AR.DOCUMENT_ID = D.DOC_ID where d.IS_SEND_TO_STORAGE is not null and d.IS_SEND_TO_STORAGE >0
union all
select D.*,ai.CASE_CODE as alarmCode,ai.case_name as alarmName,2 as alarmType,ALARM_CONTEXT,AR.RELATION_TIME from T_case_RELATION AR left join T_case AI ON AR.case_ID = AI.case_ID
     LEFT JOIN T_DOC_ORG D ON AR.DOCUMENT_ID = D.DOC_ID where d.IS_SEND_TO_STORAGE is not null and d.IS_SEND_TO_STORAGE >0  ;



-------------------------------------------------------------------
----20240408
--优化证据管理sql 清理咋不用字段
drop VIEW V_EVIDENCE_STATISTICS ;
CREATE VIEW V_EVIDENCE_STATISTICS AS
SELECT
    ORG.ID,
    ORG.ORGCODE,
    ORG.ORGNAME,
    ORG.PATH PATH,
    ORG.PARENTID ,
    '' AS DOCONESELF,
    '' AS DOCMP4ONESELF,
    '' AS DOCAUDIOONESELF,
    '' AS DOCPICTUREONESELF,
    '' AS ARCHIVESNO,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCALL,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  S.CATE = 2 AND  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCMP4ALL,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  S.CATE = 1 AND  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCAUDIOALL,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  S.CATE = 0 AND  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS DOCPICTUREALL,
    (SELECT COUNT(1) FROM T_DOC S LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE S.IS_SEND_TO_STORAGE>0  AND  O.PATH LIKE CONCAT(ORG.PATH,'')) AS ARCHIVESYES,
    (SELECT COUNT(1) FROM T_ALARM_RELATION A  LEFT JOIN T_DOC S ON A.DOCUMENT_ID = S.DOC_ID LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE A.DOCUMENT_ID IS NOT NULL AND  O.PATH LIKE CONCAT(ORG.PATH,'')) AS RELATIONYES,
    (SELECT COUNT(1) FROM T_ALARM_RELATION A  LEFT JOIN T_DOC S ON A.DOCUMENT_ID = S.DOC_ID LEFT JOIN  ADMIN_ORGANIZATION O ON S.ORGANIZATION_ID = O.ID WHERE  A.DOCUMENT_ID IS  NULL AND  O.PATH LIKE CONCAT(ORG.PATH,'%')) AS RELATIONNO
FROM  ADMIN_ORGANIZATION ORG;

-- 为 SYS_DAILY_DEVICE 表的 ORGID 字段创建索引
CREATE INDEX idx_sys_daily_device_orgid ON SYS_DAILY_DEVICE(ORGID);

-- 为 ADMIN_ORGANIZATION 表的 ID 字段创建索引
CREATE INDEX idx_admin_organization_id ON ADMIN_ORGANIZATION(ID);

-- 为 ADMIN_ORGANIZATION 表的 PATH 字段创建索引
CREATE INDEX idx_admin_organization_path ON ADMIN_ORGANIZATION(PATH);

-- 为T_DOC表的ORGANIZATION_ID、CATE和IS_SEND_TO_STORAGE字段创建索引
CREATE INDEX idx_t_doc_organization_id ON T_DOC(ORGANIZATION_ID);

CREATE INDEX idx_t_doc_cate ON T_DOC(CATE);

CREATE INDEX idx_t_doc_is_send_to_storage ON T_DOC(IS_SEND_TO_STORAGE);

-- 为T_ALARM_RELATION表的DOCUMENT_ID字段创建索引
CREATE INDEX idx_t_alarm_relation_document_id ON T_ALARM_RELATION(DOCUMENT_ID);


---------------------------------------------------------------------------------------------------
---------------------------------------------------------------------------------------------------
ALTER TABLE T_DOC
    ADD IS_ERROR_VERIFICATION INTEGER DEFAULT 0;

COMMENT
ON COLUMN T_DOC.IS_ERROR_VERIFICATION IS '异常统计 0、未统计 1、已统计';

ALTER TABLE T_ALARM_INFO
    ADD IS_ERROR_VERIFICATION INTEGER DEFAULT 0;

COMMENT
ON COLUMN T_ALARM_INFO.IS_ERROR_VERIFICATION IS '异常统计 0、未统计 1、已统计';



ALTER TABLE T_CASE
    ADD IS_ERROR_VERIFICATION INTEGER DEFAULT 0;

COMMENT
ON COLUMN T_CASE.IS_ERROR_VERIFICATION IS '异常统计 0、未统计 1、已统计';


create view v_ultra_short_video_statistics as
select
    d.doc_id as id,
    1  as typecode,
    '超短视频' as typename,
    concat('文件名称',d.doc_name) as abnormaldoc,
    o.orgname as orgname,
    o.orgcode as orgcode,
    o.path as orgpath,
    concat('视频时长小于一分钟',d.doc_name) as describedetails,
    r.code as recordercode,
    s.site_no as sitecode,
    s.site_name as sitename,
    e.worknumber as usercode,
    e.name as username,
    sysdate  as createtime
from t_doc d
         left join admin_organization o on d.organization_id = o.id
         left join admin_employees e on d.police_id = e.id
         left join t_site s on d.site_id = s.id
         left join t_recorder r on d.recorder_id = r.id
where cate = 2 and duration <600000 and is_error_verification = 0 ;



create view v_ultra_short_alarm_statistics as
select
    d.id as id,
    2  as typecode,
    '警情超时未关联' as typename,
    concat('警情编号',d.alarm_code) as abnormaldoc,
    o.orgname as orgname,
    o.orgcode as orgcode,
    o.path as orgpath,
    concat('警情未及时关联,名称:',d.alarm_name,',警情编号:',alarm_code,',执法时间:',alarm_time) as describedetails,
    null as recordercode,
    null as sitecode,
    null as sitename,
    null as usercode,
    d.alarm_name as username,
    sysdate  as createtime
from t_alarm_info d
         left join admin_organization o on d.org_id = o.id
WHERE TIMESTAMPDIFF(HOUR, alarm_time, NOW()) >= 72 and IS_RELATION =0  and IS_ERROR_VERIFICATION = 0 ;



create view v_ultra_short_case_statistics as
select
    d.case_id as id,
    3  as typecode,
    '案件超时未关联' as typename,
    concat('案件编号',d.case_code) as abnormaldoc,
    o.orgname as orgname,
    o.orgcode as orgcode,
    o.path as orgpath,
    concat('案件未及时关联,名称:',d.case_name,',案件编号:',case_code,',执法时间:',case_time) as describedetails,
    null as recordercode,
    null as sitecode,
    null as sitename,
    d.work_number01 as usercode,
    null as username,
    sysdate  as createtime
from t_case d
         left join admin_organization o on d.case_org_id = o.id
WHERE TIMESTAMPDIFF(HOUR, case_time, NOW()) >= 72 and IS_RELATION =0  and IS_ERROR_VERIFICATION = 0 ;

