package com.jsdz.digitalevidence.alarm.alarm110.dao.impl;

import java.sql.SQLException;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.alarm.alarm110.dao.CaseRelationDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;
import com.jsdz.digitalevidence.alarm.alarm110.model.CaseRelation;

@Repository
public class CaseRelationDaoImpl extends GenericEntityDaoHibernateImpl<CaseRelation,Long> implements CaseRelationDao{
	
	
	//分页查询
	public Page<CaseRelation> findCaseRelationOnPage(Page<CaseRelation> page, String queryStr, String[] paramNames,
			Object[] values) {
		
		return (Page<CaseRelation>)this.pageQueryHQL(page, queryStr, paramNames, values);
	}

	// 查询是否重复
	public CaseRelation findMutipleAlarmRelation(CaseRelation caseRelation) {
		
		final String hql = " from CaseRelation cr "
						 + " left join fetch cr.cas ca "
						 + " left join fetch cr.mediaInfo md "
						 + " where ca.id = :casId "
						 + " and md.id = :docId " ;
		CaseRelation data = getHibernateTemplate().execute(new HibernateCallback<CaseRelation>() {
			public CaseRelation doInHibernate(Session session) throws HibernateException, SQLException {
				Query query = session.createQuery(hql);
				query.setParameter("casId", caseRelation.getCas().getId());
				query.setParameter("docId", caseRelation.getMediaInfo().getId());
				List<CaseRelation> list = query.list();
				CaseRelation rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		
		return data;
	}

	// 新增
	public void addCaseRelation(CaseRelation caseRelation) {
		this.saveOrUpdate(caseRelation);
	}

	// 根据id查询(非游离)
	public CaseRelation findCaseRelation(Long id) {
		
		final String hql = " from CaseRelation cr where cr.id = :id ";
		final Long oid = id;
		CaseRelation data = getHibernateTemplate().execute(new HibernateCallback<CaseRelation>() {
			public CaseRelation doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<CaseRelation> list = query.list();
				CaseRelation rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
		
	}

	// 删除关联
	public void deleteCaseRelation(CaseRelation caseRelation) {
		this.delete(caseRelation);
	}
	
	// 列表查询
	public List<CaseRelation> findCaseRelationsByCondition(String queryStr, String[] paramNames, Object[] values) {
		
		List<CaseRelation> lists = null;
		if (values == null || values.length <= 0 || values == null){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

}
