package TestService;

import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.admin.security.service.OperatorService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;


/**
 * 
 * @类名: TestService.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年4月28日下午4:50:58
 * 修改记录：
 *
 * @see
 */

@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { "classpath:/testApplicationContext-common.xml" })
public class TestService {
	@Autowired  
    private OperatorService operatorService;//分享Service  
	
	@Autowired
	private EmployeesService empService;
	
	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}
	
	public void TestParam(){
		GetOperatorParam param = new GetOperatorParam();
		param.getPage().setPage(1);
		param.getPage().setPageSize(20);

		List<Operator>  ops = operatorService.findOperatorsByParam("from Operator",null,null);
		
		param.getPage().setRows(ops);
		
		
		//String str = json.toString();//将json对象转换为字符串
		
		//System.out.println(str);
		
	}
	
	//@Test
	@Transactional
	public void deleteOperator(){
		List<Operator> os = operatorService.findOperatorsByParam("from Operator o where o.id=:id",
				new String[]{"id"}, 
				new Object[]{(long)4});

		operatorService.deleteOperator(os.get(0));
	}
	
	//@Test
	@Transactional
	public void login()
	{
		Operator operator = new Operator();
		operator.setId((long)1);
		operator.setLoginName("hhj");
		operator.setPassword("123456");
		//AjaxResult result = operatorService.changePassword(operator,"123456","654321");
		AjaxResult result = operatorService.login(operator, "127.0.0.1");
		if (null!=result){
			System.out.println("【"+result.getMsg()+"】");
		}else{
			System.out.println("【resut==null】");
		}
			
	}
	//@Test
	@Transactional
	public void changePassword()
	{
		Operator operator = new Operator();
		operator.setId((long)1);
		operator.setLoginName("hhj");
		operator.setPassword("1234567890");
		AjaxResult result = operatorService.changePassword(operator,"654321","123456");
		//AjaxResult result = operatorService.login(operator);
		if (null!=result){
			System.out.println("【"+result.getMsg()+"】");
		}else{
			System.out.println("【resut==null】");
		}
			
	}	
	
	//@Test
	@Transactional
	public void findOperatorOnPage()
	{
		Page page = new Page<Operator>();
		page.setPage(1);
		page.setPageSize(50);
		page.setTotalQueryString("select count(*) from Operator");
		String queryStr = "from Operator ";
		page = operatorService.findOperatorOnPage(page, queryStr, null, null);
		
		if (null!=page){
			//System.out.println("【"+page.toString()+"】");
			List<Operator> ops = page.getRows();
			
			for (int i=0;i<=ops.size()-1;i++){
				System.out.println("【i=" + i + ": "+ops.get(i).toString()+"】");
			}
			System.out.println("【页数："+page.getPage()+"】");
			System.out.println("【页长："+page.getPageSize()+"】");
			System.out.println("【记录数："+page.getTotal()+"】");
			System.out.println("【结果集记录数："+ops.size()+"】");
		}else{
			System.out.println("【resut==null】");
		}

	}	
	
}
