package com.jsdz.digitalevidence.alarm.common.service.impl;
/**
 * 
 * @类名: RecoderViewServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年10月26日上午10:51:45
 * 修改记录：
 *
 * @see
 */
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.common.dao.RecoderViewDao;
import com.jsdz.digitalevidence.alarm.common.model.RecoderView;
import com.jsdz.digitalevidence.alarm.common.service.RecoderViewService;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("RecoderViewServiceImpl")
public class RecoderViewServiceImpl implements RecoderViewService {
	@Autowired
	private RecoderViewDao recoderViewDao;

	//新增
	public AjaxResult addRecoderView(RecoderView recoderView) {
		AjaxResult result = new AjaxResult();
		recoderViewDao.addRecoderView(recoderView);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateRecoderView(RecoderView recoderView) {
		AjaxResult result = new AjaxResult();
		recoderViewDao.updateRecoderView(recoderView);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteRecoderView(RecoderView recoderView) {
		AjaxResult result = new AjaxResult();
		recoderViewDao.deleteRecoderView(recoderView);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public RecoderView findRecoderViewById(Long id){

		return recoderViewDao.findRecoderViewById(id);
	}

	//按 id 查询
	public RecoderView locateRecoderViewById(Long id) {
		return recoderViewDao.locateRecoderViewById(id);
	}

	//单个查询
	public RecoderView findRecoderViewByParam(String queryStr, String[] paramNames, Object[] values) {
		return recoderViewDao.findRecoderViewByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<RecoderView> findAllRecoderViews() {
		return recoderViewDao.findAllRecoderViews();
	}

	//列表查询
	public List<RecoderView> findRecoderViewsByParam(String queryStr, String[] paramNames, Object[] values) {
		return recoderViewDao.findRecoderViewsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<RecoderView> findRecoderViewsOnPage(Page<RecoderView> page, String queryStr, String[] paramNames, Object[] values) {
		Page<RecoderView> pos = recoderViewDao.findRecoderViewsOnPage(page, queryStr, paramNames, values);
		return pos;
	}
}
