package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: RoleActionDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-03 17:59:01
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.RoleActionDao;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleAction;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class RoleActionDaoImpl extends GenericEntityDaoHibernateImpl<RoleAction,Long> implements RoleActionDao{

	//新增
	public void addRoleAction(RoleAction roleAction) {
		this.saveOrUpdate(roleAction);
	}

	//删除
	public void deleteRoleAction(RoleAction roleAction) {
		this.delete(roleAction);
	}

	//修改
	public void updateRoleAction(RoleAction roleAction) {
		this.saveOrUpdate(roleAction);
	}

	//单个查询
	public RoleAction findRoleActionByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		};
		List<RoleAction> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//列表查询
	public List<RoleAction> findRoleActionsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<RoleAction> ops = this.findByNamedParam(queryStr,paramNames,values);
		return ops;
	}

	//分页查询
	public Page<RoleAction> findRoleActionsOnPage(Page<RoleAction> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<RoleAction>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	//删除该角色关联的所有权限
	public void deleteAllByRole(Role role) {
		//按用户删除角色关联
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlDelete = "delete RoleAction r where r.role.id = :id";
		int deleteds = session.createQuery(hqlDelete)
				.setString( "id", role.getId().toString())
				.executeUpdate();
		session.close();
	}

}
