package com.jsdz.admin.security.utils;
/**
 * 
 * @类名: HqlUtil
 * @说明: Hql结果类
 *
 * @author: kenny
 * @Date	2017年5月2日下午3:24:48
 * 修改记录：
 *
 * @see
 */
import java.util.ArrayList;

public class HqlUtil {

	private String HqlString;
	private ArrayList<String> arrayParamList = new ArrayList();
	private ArrayList<Object> arrayValueList = new ArrayList();
	
	public String[] paramNames(){
		return arrayParamList.size() == 0? null : (String[])arrayParamList.toArray(new String[0]);  
	}
	public Object[] values(){
		return arrayValueList.size() == 0? null : (Object[])arrayValueList.toArray(new Object[0]);  
	}
	
	public String getHqlString() {
		return HqlString;
	}
	public void setHqlString(String hqlString) {
		HqlString = hqlString;
	}
	public ArrayList<String> getArrayParamList() {
		return arrayParamList;
	}
	public void setArrayParamList(ArrayList<String> arrayParamList) {
		this.arrayParamList = arrayParamList;
	}
	public ArrayList<Object> getArrayValueList() {
		return arrayValueList;
	}
	public void setArrayValueList(ArrayList<Object> arrayValueList) {
		this.arrayValueList = arrayValueList;
	}
	
	public String toString(){
		StringBuilder sb = new StringBuilder();
		sb.append("paramNames=");
		String[] ss = paramNames();
		for (int i=0;i<ss.length ;i++){
			sb.append(ss[i]);
			sb.append(",");
		}
		
		sb.append("  values=");
		Object[] obj = values();
		for (int i=0;i<obj.length ;i++){
			sb.append(obj[i]);
			sb.append(",");
		}
		return sb.toString();

	}
	
}
