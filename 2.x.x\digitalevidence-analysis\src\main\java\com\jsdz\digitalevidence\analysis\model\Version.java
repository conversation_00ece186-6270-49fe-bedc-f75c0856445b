package com.jsdz.digitalevidence.analysis.model;

/**
 * 
 * 版本
 * <AUTHOR>
 *
 */
public enum Version {
	CHECK(1,"过检版本"),
	OFFICIAL(2,"正式使用版本");

	private Integer value;

    private String name;

    Version(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
		return value;
	}
	public void setValue(Integer value) {
		this.value = value;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
}
