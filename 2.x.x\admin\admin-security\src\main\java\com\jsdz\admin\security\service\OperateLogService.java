package com.jsdz.admin.security.service;

/**
 * 
 * @类名: OperateLogService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-11 08:56:21
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.OperateLog;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface OperateLogService {

	//新增
	public AjaxResult addOperateLog(OperateLog operateLog);

	//修改
	public AjaxResult updateOperateLog(OperateLog operateLog);

	//删除
	public AjaxResult deleteOperateLog(OperateLog operateLog);

	//按id查询
	public OperateLog locateOperateLogById(Long id);

	//单个查询
	public OperateLog findOperateLogByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<OperateLog> findAllOperateLogs();

	//列表查询
	public List<OperateLog> findOperateLogsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<OperateLog> findOperateLogsOnPage(Page<OperateLog> page, String queryStr,String[] paramNames,Object[] values);

	//按条件执行HQL（非查询）
	public Integer execHqlByCondition(final String hql,final String[] params,final Object[] values );
	
	//根据系统参数设置定时删除时间过得太久的日志
	public Integer deleteLogBySetting();
}

