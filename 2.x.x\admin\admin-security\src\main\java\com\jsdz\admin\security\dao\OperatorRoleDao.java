package com.jsdz.admin.security.dao;

/**
 *
 * @类名: OperatorRoleDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-03 14:48:29
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import java.util.Map;

import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface OperatorRoleDao extends GenericORMEntityDAO<OperatorRole,Long> {

	//新增
	public void addOperatorRole(OperatorRole operatorRole);

	//修改
	public void updateOperatorRole(OperatorRole operatorRole);

	//删除
	public void deleteOperatorRole(OperatorRole operatorRole);

	//查询单个
	public OperatorRole findOperatorRoleByCondition(String queryStr,String[] paramNames,Object[] values);
	
	//查询
	public List<OperatorRole> findOperatorRolesByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<OperatorRole> findOperatorRolesOnPage(Page<OperatorRole> page,String queryStr,String[] paramNames,Object[] values);

	//删除该用户关联的所有角色
	public void deleteAllRoleByOperator(Operator operator);
	
	//查询与该操作员关联的角色
	public List<OperatorRole> findRoleByOperator(Operator operator);
	
	//取最大权限级别
	public List<Map<String,Object>> findMaxPermission(Long userId);
	
}

