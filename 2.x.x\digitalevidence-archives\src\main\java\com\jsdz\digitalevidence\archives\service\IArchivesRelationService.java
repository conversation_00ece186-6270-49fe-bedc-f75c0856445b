package com.jsdz.digitalevidence.archives.service;

import com.jsdz.digitalevidence.archives.model.ArchivesRelation;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-11
 */
public interface IArchivesRelationService {

    boolean removeByRid(Long rid);

    List<ArchivesRelation> findArchivesDoc(Long archivesId, String docId);

    boolean saveArchivesRelationsBatch(List<ArchivesRelation> archivesRelations);
}
