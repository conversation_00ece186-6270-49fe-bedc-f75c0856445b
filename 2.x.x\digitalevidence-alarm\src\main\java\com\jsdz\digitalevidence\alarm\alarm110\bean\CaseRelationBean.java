package com.jsdz.digitalevidence.alarm.alarm110.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.digitalevidence.alarm.alarm110.model.CaseRelation;
import com.jsdz.digitalevidence.document.model.Case;
import com.jsdz.digitalevidence.document.model.CaseType;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.digitalevidence.document.model.DocumentCate;

public class CaseRelationBean {
	
	private Long id ;
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date relationTime; //关联时间
	
	// 案件
	private Long caseId;
	private String caseCode; //案件编号
	private CaseType caseType; //案件编号
	private Integer caseTypeIndex; //案件编号


	private Date caseTime;   //案发时间
	private String caseContext;//接警内容
	
	//处警单位
    private Long orgId;    
    private String orgCode;
    private String orgName;
	
	//视频
    private Long docId;
    private String docName;
    private String documentName;

	private DocumentCate cate;

	private String type;

    public void assign (CaseRelation sur){
    	
    	id = sur.getId();
    	relationTime = sur.getRelationTime();
    	if(sur.getCas() != null){
    		caseId = sur.getCas().getId();
    		caseCode = sur.getCas().getCaseCode();
			caseType = sur.getCas().getCaseType();
			caseTypeIndex = sur.getCas().getCaseType().ordinal();
    		caseTime = sur.getCas().getCaseTime();
    		caseContext = sur.getCas().getAlarmContext();
    		
    		if(sur.getCas().getProcOrg() != null){
    			orgId = sur.getCas().getProcOrg().getId();
    			orgCode = sur.getCas().getProcOrg().getOrgCode();
    			orgName = sur.getCas().getProcOrg().getOrgName();
    		}
    	}
    	if(sur.getMediaInfo() !=null){
    		docId = sur.getMediaInfo().getId();
    		docName = sur.getMediaInfo().getName();
    		documentName = sur.getMediaInfo().getCate().getName();
			cate = sur.getMediaInfo().getCate();
			type = sur.getMediaInfo().getContentType();
    	}
    }
    
    public void assignTo(CaseRelation dest){
    	
    	dest.setId(id);
    	dest.setRelationTime(relationTime);
    	if(caseId != null ){
    		
    		Case ca = new Case();
    		ca.setId(caseId);
    		dest.setCas(ca);
    	}
    	
    	if(docId != null){
    		
    		Document doc = new Document();
    		doc.setId(docId);
    		dest.setMediaInfo(doc);
    	}
    }
	public Integer getCaseTypeIndex() {
		return caseTypeIndex;
	}

	public void setCaseTypeIndex(Integer caseTypeIndex) {
		this.caseTypeIndex = caseTypeIndex;
	}

	public CaseType getCaseType() {
		return caseType;
	}

	public void setCaseType(CaseType caseType) {
		this.caseType = caseType;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Date getRelationTime() {
		return relationTime;
	}

	public void setRelationTime(Date relationTime) {
		this.relationTime = relationTime;
	}

	public Long getCaseId() {
		return caseId;
	}

	public void setCaseId(Long caseId) {
		this.caseId = caseId;
	}

	public String getCaseCode() {
		return caseCode;
	}

	public void setCaseCode(String caseCode) {
		this.caseCode = caseCode;
	}

	public Date getCaseTime() {
		return caseTime;
	}

	public void setCaseTime(Date caseTime) {
		this.caseTime = caseTime;
	}

	public String getCaseContext() {
		return caseContext;
	}

	public void setCaseContext(String caseContext) {
		this.caseContext = caseContext;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}

	public String getDocName() {
		return docName;
	}

	public void setDocName(String docName) {
		this.docName = docName;
	}

	public String getDocumentName() {
		return documentName;
	}

	public void setDocumentName(String documentName) {
		this.documentName = documentName;
	}

	public DocumentCate getCate() {
		return cate;
	}

	public void setCate(DocumentCate cate) {
		this.cate = cate;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
