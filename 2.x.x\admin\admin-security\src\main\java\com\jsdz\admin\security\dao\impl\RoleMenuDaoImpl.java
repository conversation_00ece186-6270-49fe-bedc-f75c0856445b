package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: RoleMenuDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-09-08 12:03:22
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.RoleMenuDao;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleMenu;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class RoleMenuDaoImpl extends GenericEntityDaoHibernateImpl<RoleMenu,Long> implements RoleMenuDao{

	//新增
	public void addRoleMenu(RoleMenu roleMenu) {
		this.saveOrUpdate(roleMenu);
	}

	//删除
	public void deleteRoleMenu(RoleMenu roleMenu) {
		this.delete(roleMenu);
	}

	//修改
	public void updateRoleMenu(RoleMenu roleMenu) {
		this.merge(roleMenu);
	}

	//按id查询(游离状态)
	public RoleMenu findRoleMenuById(Long id){

		final String  hql = "from RoleMenu s where s.id = :id";
		final Long oid = id;
		RoleMenu data = getHibernateTemplate().execute(new HibernateCallback<RoleMenu>() {
			public RoleMenu doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<RoleMenu> list = query.list();
				RoleMenu rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public RoleMenu locateRoleMenuById(Long id){

		final String  hql = "from RoleMenu s where s.id = :id";
		final Long oid = id;
		RoleMenu data = getHibernateTemplate().execute(new HibernateCallback<RoleMenu>() {
			public RoleMenu doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<RoleMenu> list = query.list();
				RoleMenu rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public RoleMenu findRoleMenuByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		RoleMenu data = getHibernateTemplate().execute(new HibernateCallback<RoleMenu>() {
		public RoleMenu doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<RoleMenu> list = query.list();
			RoleMenu rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<RoleMenu> findAllRoleMenus(){
		return this.find("from RoleMenu roleMenu ");
	}

	//列表查询
	public List<RoleMenu> findRoleMenusByCondition(String queryStr,String[] paramNames,Object[] values){
		List<RoleMenu> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<RoleMenu> findRoleMenusOnPage(Page<RoleMenu> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<RoleMenu>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	//删除该角色关联的所有权限
	public void deleteRoleMenuByRole(Role role) {
		//按用户删除角色关联
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlDelete = "delete RoleMenu r where r.role.id = :id";
		int deleteds = session.createQuery(hqlDelete)
				.setString( "id", role.getId().toString())
				.executeUpdate();
		session.close();
	}

}
