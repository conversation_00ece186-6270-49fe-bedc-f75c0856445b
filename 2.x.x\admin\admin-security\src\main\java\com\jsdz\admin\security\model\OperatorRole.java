package com.jsdz.admin.security.model;
/**
 * 
 * @类名: OpratorRole
 * @说明: 操作员角色类（一个操作员可拥用多个角色）
 *
 * @author: kenny
 * @Date	2017年4月27日下午2:15:47
 * 修改记录：
 *
 * @see
 */
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class OperatorRole implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -3973623871361812536L;
	
	private Long id;
	//@JsonIgnore
	private Operator operator;
	//@JsonIgnore
	private Role role ;
	@JsonIgnore
	private Operator createBy;
	private Date createTime;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Operator getOperator() {
		return operator;
	}
	public void setOperator(Operator operator) {
		this.operator = operator;
	}

	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Role getRole() {
		return role;
	}
	public void setRole(Role role) {
		this.role = role;
	}
	public Operator getCreateBy() {
		return createBy;
	}
	public void setCreateBy(Operator createBy) {
		this.createBy = createBy;
	}


	
	
}
