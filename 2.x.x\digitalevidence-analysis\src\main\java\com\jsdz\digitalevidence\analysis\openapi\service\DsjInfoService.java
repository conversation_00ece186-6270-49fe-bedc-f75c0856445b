package com.jsdz.digitalevidence.analysis.openapi.service;

/**
 * 
 * @类名: DsjInfoService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-06-09 15:15:00
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.analysis.openapi.model.DsjInfo;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface DsjInfoService {

	//新增
	public AjaxResult addDsjInfo(String siteCode,DsjInfo dsjInfo) ;

	//修改
	public AjaxResult updateDsjInfo(DsjInfo dsjInfo);

	//删除
	public AjaxResult deleteDsjInfo(DsjInfo dsjInfo);

	//按id查询,结果是游离状态的数据
	public DsjInfo findDsjInfoById(Long id);

	//按id查询
	public DsjInfo locateDsjInfoById(Long id);

	//单个查询
	public DsjInfo findDsjInfoByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DsjInfo> findAllDsjInfos();

	//列表查询
	public List<DsjInfo> findDsjInfosByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DsjInfo> findDsjInfosOnPage(Page<DsjInfo> page, String queryStr,String[] paramNames,Object[] values);

}

