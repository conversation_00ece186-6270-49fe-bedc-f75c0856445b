package com.jsdz.digitalevidence.alarm.alarm110.dao;

import java.util.List;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.alarm110.model.CaseRelation;

public interface CaseRelationDao {
	
	
	// 分页查询
	Page<CaseRelation> findCaseRelationOnPage(Page<CaseRelation> page, String queryStr, String[] paramNames,
			Object[] values);
	
	
	// 查询是否重复
	CaseRelation findMutipleAlarmRelation(CaseRelation caseRelation);
	
	// 新增
	void addCaseRelation(CaseRelation caseRelation);

	// 根据id查询(非游离)
	CaseRelation findCaseRelation(Long id);

	// 删除
	void deleteCaseRelation(CaseRelation caseRelation);

	// 列表查询
	List<CaseRelation> findCaseRelationsByCondition(String queryStr, String[] paramNames, Object[] values);

}
