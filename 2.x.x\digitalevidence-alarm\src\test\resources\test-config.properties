# mysql
jdbc.database=Mysql
jdbc.driver=com.mysql.jdbc.Driver

#jdbc.url=*********************************************************************************************************************************************************************
#jdbc.username=root
#jdbc.password=root
jdbc.url=***************************************************************************************************************************************************************************
#jdbc.url=**********************************************************************************************************************************************************************
jdbc.username=root
jdbc.password=jsdz1234

#c3p0 settings
c3p0.acquireIncrement=3
c3p0.acquireRetryAttempts=60
c3p0.acquireRetryDelay=1000
c3p0.initialPoolSize=10
c3p0.maxPoolSize=30
c3p0.minPoolSize=10
c3p0.maxIdleTime=60
c3p0.numHelperThreads=3
c3p0.maxStatementsPerConnection=100

#document##############
document.storage.rootpath=e:/repo/
document.storage.rootpath_log=e:/repo/log
fileserver.rootpath=http://************:8900/media/
#
document.expireddays=3650
#dynsql template
document.dynsql.template=dynsql/
#1024*1024*10
document.extract.writelimit=10485760
# 
document.pathstrategy.id.segs=3
document.pathstrategy.id.seglen=3
#
report.template.path=report
#
document.delete.batchsize = 10
#
document.jobgroup = Document
document.clear.jobname = DocumentClearJob
document.clear.jobtype = documentClearJob
document.clear.jobdesc = \u8FC7\u671F\u6E05\u7406\u4F5C\u4E1A

######geo########
# jedis cluster
redis.maxIdle=100  
jedis.maxActive=300  
jedis.timeout=100000  
jedis.hosts=localhost:7001,localhost:7002
# for test
jedis.host=************:7001
#\u6700\u5927\u5EFA\u7ACB\u8FDE\u63A5\u7B49\u5F85\u65F6\u95F4  
# Object Pool config
jedis.maxWait=1000  
redis.maxTotal=1000  
redis.minIdle=8  
jedis.maxAttemps=5  
jedis.testOnBorrow=true 
redis.dbIndex=0
#
lbs.geoKey=GEO_KEY

#red5
red5.host=************
red5.port=1935
red5.app=live
#
# Assessment
assessment.start.day=5
assessment.frozen.day=10
assessment.rule.gfyq.threshold=0.2
assessment.rule.quality.threshold=0.2
assessment.assessRate=0.33334
#
# assessment job
assessment.jobgroup = Assessment
#
assessment.dailyreportjob.name = AssessmentReportDailyJob
assessment.dailyreportjob.type = assessmentReportDailyJob
assessment.dailyreportjob.desc = \u65E5\u8003\u6838\u62A5\u544A\u4F5C\u4E1A
#
assessment.pushjob.name = AssessmentPushJob
assessment.pushjob.type = assessmentPushJob
assessment.pushjob.desc = \u8003\u6838\u89C6\u9891\u63A8\u9001\u4F5C\u4E1A
#
assessment.weeklyalertjob.name = AssessmentWeeklyAlertJob
assessment.weeklyalertjob.type = assessmentWeeklyAlertJob
assessment.weeklyalertjob.desc = \u6708\u8003\u6838\u9884\u8B66\u4F5C\u4E1A
#
assessment.monthlyreportjob.name = AssessmentMonthlyReportJob
assessment.monthlyreportjob.type = assessmentMonthlyReportJob
assessment.monthlyreportjob.desc = \u6708\u8003\u6838\u62A5\u544A\u4F5C\u4E1A
#
assessment.alertjob.name = AssessmentAlertJob
assessment.alertjob.type = assessmentAlertJob
assessment.alertjob.desc = \u8003\u6838\u9884\u8B66\u4F5C\u4E1A
#
assessment.freezejob.name = AssessmentFreezeJob
assessment.freezejob.type = assessmentFreezeJob
assessment.freezejob.desc = \u8003\u6838\u62A5\u544A\u51BB\u7ED3\u4F5C\u4E1A

#gueiyang
#gueiyang.api.url=http://*************:9000/
gueiyang.api.url=http://localhost:8080/web-site/simulate/
gueiyang.api.un=jingshengUser
gueiyang.api.pwd=20dfcf8ba380ba5b164193424a1b329d

#activeMQ
activemq.brokerurl=tcp://localhost:61616

#download center
dc.storage.rootpath=e:/repo/
dc.fileserver.rootpath=http://localhost:8080/media/
#notify
notify.day=-8