package com.jsdz.digitalevidence.attendance.bean.vomodel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AttendanceTaskBean implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 序号
     */
    private Long id;

    /**
     * 单位编号
     */
    private Long attendanceOrgId;

    /**
     * 考勤任务名称
     */
    private String attendanceName;

    /**
     * 考勤时间
     */
//    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private String attendanceTime;

    /**
     * 可提前多少分钟打卡
     */
    private Integer advanceByMinutes;

    /**
     * 是否允许自动打卡（0允许，1不允许）
     */
    private Integer isAutomatic;

    /**
     * 离考勤范围多少米可打卡
     */
    private Double attendanceScopeRice;

    private String attendanceDescribe;

    /**
     * 创建时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date updateTime;





    /**
     * 单位相关
     */
    private String orgName;
    private String orgPath;
    private String orgCode;
    private String orgId;




}
