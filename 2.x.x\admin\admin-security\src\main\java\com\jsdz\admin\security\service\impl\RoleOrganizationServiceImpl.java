package com.jsdz.admin.security.service.impl;
/**
 * 
 * @类名: RoleOrganizationServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-05-15 16:42:50
 * 修改记录：
 *
 * @see
*/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;
import com.jsdz.admin.org.dao.OrganizationDao;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.bean.RoleMenuBean;
import com.jsdz.admin.security.dao.RoleDao;
import com.jsdz.admin.security.dao.RoleOrganizationDao;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleOrganization;
import com.jsdz.admin.security.model.SystemMenu;
import com.jsdz.admin.security.service.RoleOrganizationService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.reportquery.ReportQueryDao;

@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("RoleOrganizationServiceImpl")
public class RoleOrganizationServiceImpl implements RoleOrganizationService {
	private final Logger logger = Logger.getLogger(this.getClass());
	@Autowired
	private RoleOrganizationDao roleOrganizationDao;
	@Autowired
	private RoleDao roleDao;
	@Autowired
	private OrganizationDao organizationDao;
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<SystemMenu> reportQueryDao;	
	

	//新增
	public AjaxResult addRoleOrganization(RoleOrganization roleOrganization) {
		AjaxResult result = new AjaxResult();
		roleOrganizationDao.addRoleOrganization(roleOrganization);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateRoleOrganization(RoleOrganization roleOrganization) {
		AjaxResult result = new AjaxResult();
		roleOrganizationDao.updateRoleOrganization(roleOrganization);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteRoleOrganization(RoleOrganization roleOrganization) {
		AjaxResult result = new AjaxResult();
		roleOrganizationDao.deleteRoleOrganization(roleOrganization);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public RoleOrganization findRoleOrganizationById(Long id){

		return roleOrganizationDao.findRoleOrganizationById(id);
	}

	//按 id 查询
	public RoleOrganization locateRoleOrganizationById(Long id) {
		return roleOrganizationDao.locateRoleOrganizationById(id);
	}

	//单个查询
	public RoleOrganization findRoleOrganizationByParam(String queryStr, String[] paramNames, Object[] values) {
		return roleOrganizationDao.findRoleOrganizationByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<RoleOrganization> findAllRoleOrganizations() {
		return roleOrganizationDao.findAllRoleOrganizations();
	}

	//列表查询
	public List<RoleOrganization> findRoleOrganizationsByParam(String queryStr, String[] paramNames, Object[] values) {
		return roleOrganizationDao.findRoleOrganizationsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<RoleOrganization> findRoleOrganizationsOnPage(Page<RoleOrganization> page, String queryStr, String[] paramNames, Object[] values) {
		Page<RoleOrganization> pos = roleOrganizationDao.findRoleOrganizationsOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
    //设置角色单位权限
    @Transactional
    public AjaxResult setRoleOrganization(String orgIds,Long roleId){
    	AjaxResult result = new AjaxResult(0,false,"",null);
   	 try{
   		//删除原来数据
   		String queryStr = "from RoleOrganization r where r.role.id = :id";
   		List<RoleOrganization> list = roleOrganizationDao.findRoleOrganizationsByCondition(
   				queryStr, new String[]{"id"},new Object[]{roleId});
   		if (list != null && list.size() > 0){
   			roleOrganizationDao.batchDelete(list);
   		}
   		
   		if (orgIds == null || orgIds.equals("")){
   			result.setSuccess(true);
   			result.setMsg("操作成功");
   			return result;
   		}
   		
   		//写入
   		Role role = roleDao.get(roleId);
   		String orgNames = "";
  	   	String[] orgArray = orgIds.split(",");
  	   	List<RoleOrganization> insertList = new ArrayList<RoleOrganization>();
  	   	for (String sorgId:orgArray){
  	   		Long orgId = Long.valueOf(sorgId);
  	   		Organization org = organizationDao.get(orgId);
  	   		
  	   	    RoleOrganization ro = new RoleOrganization();
  	   	    ro.setRole(role);
  	   	    ro.setOrganization(org);
  	   	    insertList.add(ro);
  	   	    
  	   	    orgNames = orgNames + ";" + org.getOrgName();
  	   	}
  	   	roleOrganizationDao.batchInsert(insertList);
  	   	role.setOrgNames(orgNames);
  	   	roleDao.updateRole(role);
   	 }catch(Exception e){
   		logger.error("内部错误：" + e);
   		 result.setMsg("内部错误：" + e);
   		 result.setSuccess(false);
   	 }
   	 result.setSuccess(true);
   	 return result;
    }
    
	@Override
	public List<RoleMenuBean> findAllOrganitionForRoleSet(Long roleId) throws Exception{
	
		Map<String,Object> map = new HashMap<String,Object>();
    	if (roleId != null)
    		map.put("roleId", roleId);
    	
    	String[] paramNames = map.keySet().toArray(new String[0]);
		Object[] values = map.values().toArray(new Object[0]);
		List<RoleMenuBean> result = new ArrayList<RoleMenuBean>(); 
		List<Object> list= reportQueryDao.QueryNamedAllDataSQL("findAllOranizationForRoleSet",paramNames,values);
		for(Object o : list ){
			result.add((RoleMenuBean)o);
		}
		return result;
	}
	//
	@Override
  public String getRoleOrgPath(Long userId){
	  String result="";
	  List<Object> list = roleOrganizationDao.getRoleOrgPath(userId);
	  if (list != null && list.size() > 0){
		  for(Object obj : list){
			  result = result + (String)obj + ",";
			  
		  }
	  }
	  return result;
  }

}
