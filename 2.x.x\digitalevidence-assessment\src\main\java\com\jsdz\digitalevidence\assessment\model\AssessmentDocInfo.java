package com.jsdz.digitalevidence.assessment.model;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * @Package: com.jsdz.digitalevidence.assessment.model
 * @ClassName: AssessmentDocInfo
 * @Author: 周志强
 * @Date: 2023/2/20 15:45
 */
public class
AssessmentDocInfo {
    /**考核编号*/
    private String assessmentId;
    /**考核文档编号*/
    private Long assessmentDocId;
    /**考核员*/
    private String assessmentPoliceId;
    /**考核评分*/
    private Integer assessmentScore;
    /**考核意见*/
    private String assessmentRemarks;
    /**考核时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date assessmentTime;
    /**考核更新时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date assessmentUpdateTime;
    /**考核考核添加时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date assessmentCreateTime;


	public String getAssessmentId() {
		return assessmentId;
	}
	public void setAssessmentId(String assessmentId) {
		this.assessmentId = assessmentId;
	}
	public Long getAssessmentDocId() {
		return assessmentDocId;
	}
	public void setAssessmentDocId(Long assessmentDocId) {
		this.assessmentDocId = assessmentDocId;
	}
	public String getAssessmentPoliceId() {
		return assessmentPoliceId;
	}
	public void setAssessmentPoliceId(String assessmentPoliceId) {
		this.assessmentPoliceId = assessmentPoliceId;
	}
	public Integer getAssessmentScore() {
		return assessmentScore;
	}
	public void setAssessmentScore(Integer assessmentScore) {
		this.assessmentScore = assessmentScore;
	}
	public String getAssessmentRemarks() {
		return assessmentRemarks;
	}
	public void setAssessmentRemarks(String assessmentRemarks) {
		this.assessmentRemarks = assessmentRemarks;
	}
	public Date getAssessmentTime() {
		return assessmentTime;
	}
	public void setAssessmentTime(Date assessmentTime) {
		this.assessmentTime = assessmentTime;
	}
	public Date getAssessmentUpdateTime() {
		return assessmentUpdateTime;
	}
	public void setAssessmentUpdateTime(Date assessmentUpdateTime) {
		this.assessmentUpdateTime = assessmentUpdateTime;
	}
	public Date getAssessmentCreateTime() {
		return assessmentCreateTime;
	}
	public void setAssessmentCreateTime(Date assessmentCreateTime) {
		this.assessmentCreateTime = assessmentCreateTime;
	}
    
    
}
