package com.jsdz.digitalevidence.archives.bean.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.archives.bean.ArchivesDataBean;
import com.jsdz.digitalevidence.archives.model.DocEnfor;
import com.jsdz.utils.DateTimeUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
public class DocEnforParam implements Serializable{


    Page<DocEnfor> page;
    private String orgPath;

    //是否包含下级 （0不，1包含）
    private String includeSub;
    private String alarmCode;
    //执法仪Id
    private Long recorderId;

    //拍摄时间
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date shotTimeStart;
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date shotTimeEnd;

    //关联时间
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date relationTimeStart;
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date relationTimeEnd;

    public String getAlarmCode() {
        return alarmCode;
    }

    public void setAlarmCode(String alarmCode) {
        this.alarmCode = alarmCode;
    }

    public Page<DocEnfor> getPage() {
        return page;
    }

    public void setPage(Page<DocEnfor> page) {
        this.page = page;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public String getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(String includeSub) {
        this.includeSub = includeSub;
    }

    public Long getRecorderId() {
        return recorderId;
    }

    public void setRecorderId(Long recorderId) {
        this.recorderId = recorderId;
    }

    public Date getShotTimeStart() {
        return shotTimeStart;
    }

    public void setShotTimeStart(Date shotTimeStart) {
        this.shotTimeStart = shotTimeStart;
    }

    public Date getShotTimeEnd() {
        return shotTimeEnd;
    }

    public void setShotTimeEnd(Date shotTimeEnd) {
        this.shotTimeEnd = shotTimeEnd;
    }

    public Date getRelationTimeStart() {
        return relationTimeStart;
    }

    public void setRelationTimeStart(Date relationTimeStart) {
        this.relationTimeStart = relationTimeStart;
    }

    public Date getRelationTimeEnd() {
        return relationTimeEnd;
    }

    public void setRelationTimeEnd(Date relationTimeEnd) {
        this.relationTimeEnd = relationTimeEnd;
    }
}
