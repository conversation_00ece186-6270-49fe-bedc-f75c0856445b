package com.jsdz.digitalevidence.analysis.openapi.dao.impl;

/**
 *
 * @类名: DsjInfoDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-06-09 15:15:00
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.analysis.openapi.dao.DsjInfoDao;
import com.jsdz.digitalevidence.analysis.openapi.model.DsjInfo;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class DsjInfoDaoImpl extends GenericEntityDaoHibernateImpl<DsjInfo,Long> implements DsjInfoDao{

	//新增
	public void addDsjInfo(DsjInfo dsjInfo) {
		this.saveOrUpdate(dsjInfo);
	}

	//删除
	public void deleteDsjInfo(DsjInfo dsjInfo) {
		this.delete(dsjInfo);
	}

	//修改
	public void updateDsjInfo(DsjInfo dsjInfo) {
		this.merge(dsjInfo);
	}

	//按id查询(游离状态)
	public DsjInfo findDsjInfoById(Long id){

		final String  hql = "from DsjInfo d where d.id = :id";
		final Long oid = id;
		DsjInfo data = getHibernateTemplate().execute(new HibernateCallback<DsjInfo>() {
			public DsjInfo doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DsjInfo> list = query.list();
				DsjInfo rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public DsjInfo locateDsjInfoById(Long id){

		final String  hql = "from DsjInfo d where d.id = :id";
		final Long oid = id;
		DsjInfo data = getHibernateTemplate().execute(new HibernateCallback<DsjInfo>() {
			public DsjInfo doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DsjInfo> list = query.list();
				DsjInfo rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public DsjInfo findDsjInfoByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		DsjInfo data = getHibernateTemplate().execute(new HibernateCallback<DsjInfo>() {
		public DsjInfo doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<DsjInfo> list = query.list();
			DsjInfo rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<DsjInfo> findAllDsjInfos(){
		return this.find("from DsjInfo dsjInfo ");
	}

	//列表查询
	public List<DsjInfo> findDsjInfosByCondition(String queryStr,String[] paramNames,Object[] values){
		List<DsjInfo> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<DsjInfo> findDsjInfosOnPage(Page<DsjInfo> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<DsjInfo>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
