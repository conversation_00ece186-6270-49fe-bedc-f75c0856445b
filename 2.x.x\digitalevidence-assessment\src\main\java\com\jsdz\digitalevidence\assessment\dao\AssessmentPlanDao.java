package com.jsdz.digitalevidence.assessment.dao;

/**
 *
 * @类名: AssessmentPlanDao
 * @说明:
 * @author: kenny
 * @Date 2019-06-19 15:40:26
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlan;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface AssessmentPlanDao extends GenericORMEntityDAO<AssessmentPlan,Long> {

	/** 
 	 * 新增
	 */ 
 	public void addAssessmentPlan(AssessmentPlan assessmentPlan);

	/** 
 	 * 修改
	 */ 
 	public void updateAssessmentPlan(AssessmentPlan assessmentPlan);

	/** 
 	 * 删除
	 */ 
 	public void deleteAssessmentPlan(AssessmentPlan assessmentPlan);

	/** 
 	 * 按id查询,结果是游离状态的数据
	 */ 
 	public AssessmentPlan findAssessmentPlanById(Long id);

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlan locateAssessmentPlanById(Long id);

	/* 
 	 * 单个查询
	 */ 
 	public AssessmentPlan findAssessmentPlanByCondition(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlan> findAllAssessmentPlans();

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlan> findAssessmentPlansByCondition(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlan> findAssessmentPlansOnPage(Page<AssessmentPlan> page,String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 执行指定的HQL文件
	 */ 
 	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);

}

