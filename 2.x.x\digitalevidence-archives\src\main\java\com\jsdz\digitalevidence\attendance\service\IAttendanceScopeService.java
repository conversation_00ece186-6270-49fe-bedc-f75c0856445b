package com.jsdz.digitalevidence.attendance.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.digitalevidence.attendance.model.AttendanceCheckModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceScopeModel;
import com.jsdz.digitalevidence.utils.response.Result;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface IAttendanceScopeService  extends IService<AttendanceScopeModel> {

    Result getDatas(String taskId, PerLvlBean permLvl);
    List<AttendanceScopeModel> getDatas(Long taskId);

    void saveData(AttendanceScopeModel model, PerLvlBean permLvl);

    void updateDataById(AttendanceScopeModel model, PerLvlBean permLvl);

    AttendanceScopeModel getById(AttendanceScopeModel model, PerLvlBean permLvl);

    void deleteById(AttendanceScopeModel model, PerLvlBean permLvl);

    void deleteByTaskId(AttendanceScopeModel model, PerLvlBean permLvl);
    void deleteByTaskId(Long taskId, PerLvlBean permLvl);
}
