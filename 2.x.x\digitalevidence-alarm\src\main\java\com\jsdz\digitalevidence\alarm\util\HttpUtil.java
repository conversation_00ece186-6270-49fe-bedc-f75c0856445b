package com.jsdz.digitalevidence.alarm.util;
/**
 * 
 * @类名: HttpUtil
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年10月26日上午11:19:42
 * 修改记录：
 *
 * @see
 */
import java.io.IOException;
import java.util.List;

import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;


public class HttpUtil {
	public static final int METHOD_GET=0;
	public static final int METHOD_POST=1;
	static HttpResponse resp=null;

	public static HttpResponse send(int method, String uri, List<BasicNameValuePair> pairs) throws ClientProtocolException, IOException{
		
		//CloseableHttpResponse response = null;
	    //CloseableHttpClient client = null;

		//try {
		   DefaultHttpClient httpClient = new DefaultHttpClient(); 
		
			//client = HttpClient.createDefault();
			HttpUriRequest request=null;
			switch (method) {
			case METHOD_GET:
				request=new HttpGet(uri);
				break;
			case METHOD_POST:
				HttpPost post=new HttpPost(uri);
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(pairs, "utf-8");
				post.setEntity(entity);
				post.setHeader("Content-Type", "application/x-www-form-urlencoded");
				request=post;
				break;
			} 
			resp=httpClient.execute(request);
		//}catch (Exception e) {
		//	e.printStackTrace();
		//}
		return resp;
	}
	
	//带json参数
	public static HttpResponse sendWithJson(String uri, String jsonStr) throws ClientProtocolException, IOException{
		DefaultHttpClient httpClient = new DefaultHttpClient(); 
		HttpPost post = new HttpPost(uri);
		
		StringEntity entity = new StringEntity(jsonStr,"utf-8");//解决中文乱码问题    
        entity.setContentEncoding("UTF-8");    
        entity.setContentType("application/json");    
        post.setEntity(entity);    
        resp=httpClient.execute(post);
		
		return resp;
	}
}
