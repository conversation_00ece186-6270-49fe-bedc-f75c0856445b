package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: DictionaryValuesDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-10-31 11:44:06
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.DictionaryValuesDao;
import com.jsdz.admin.security.model.DictionaryValues;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class DictionaryValuesDaoImpl extends GenericEntityDaoHibernateImpl<DictionaryValues,Long> implements DictionaryValuesDao{

	//新增
	public void addDictionaryValues(DictionaryValues dictionaryValues) {
		this.saveOrUpdate(dictionaryValues);
	}

	//删除
	public void deleteDictionaryValues(DictionaryValues dictionaryValues) {
		this.delete(dictionaryValues);
	}

	//修改
	public void updateDictionaryValues(DictionaryValues dictionaryValues) {
		this.merge(dictionaryValues);
	}

	//按id查询(游离状态)
	public DictionaryValues findDictionaryValuesById(Long id){

		final String  hql = "from DictionaryValues d where d.id = :id";
		final Long oid = id;
		DictionaryValues data = getHibernateTemplate().execute(new HibernateCallback<DictionaryValues>() {
			public DictionaryValues doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DictionaryValues> list = query.list();
				DictionaryValues rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public DictionaryValues locateDictionaryValuesById(Long id){

		final String  hql = "from DictionaryValues v "
				+ " left join fetch v.dictionary d"
				+ " where v.id = :id";
		final Long oid = id;
		DictionaryValues data = getHibernateTemplate().execute(new HibernateCallback<DictionaryValues>() {
			public DictionaryValues doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DictionaryValues> list = query.list();
				DictionaryValues rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public DictionaryValues findDictionaryValuesByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		DictionaryValues data = getHibernateTemplate().execute(new HibernateCallback<DictionaryValues>() {
		public DictionaryValues doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<DictionaryValues> list = query.list();
			DictionaryValues rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<DictionaryValues> findAllDictionaryValuess(){
		return this.find("from DictionaryValues dictionaryValues ");
	}

	//列表查询
	public List<DictionaryValues> findDictionaryValuessByCondition(String queryStr,String[] paramNames,Object[] values){
		List<DictionaryValues> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<DictionaryValues> findDictionaryValuessOnPage(Page<DictionaryValues> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<DictionaryValues>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
