package com.jsdz.admin.org.dao;

/**
 *
 * @类名: RegionDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-08 17:08:54
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.org.model.Region;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface RegionDao extends GenericORMEntityDAO<Region,Long> {

	//新增
	public void addRegion(Region region);

	//修改
	public void updateRegion(Region region);

	//删除
	public void deleteRegion(Region region);

	//按id查询
		public Region locateRegionById(Long id);

	//单个查询
	public Region findRegionByCondition(String queryStr,String[] paramNames,Object[] values);

	//列表查询
	public List<Region> findRegionsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Region> findRegionsOnPage(Page<Region> page,String queryStr,String[] paramNames,Object[] values);

}

