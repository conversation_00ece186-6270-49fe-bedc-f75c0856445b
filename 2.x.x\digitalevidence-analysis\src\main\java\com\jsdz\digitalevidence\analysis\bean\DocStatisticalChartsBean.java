package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: DocStatisticalChartsBean
 * @说明: 处理成图表显示数据
 *
 * @author: kenny
 * @Date	2017年6月14日下午3:46:19
 * 修改记录：
 *
 * @see
 */

import java.util.ArrayList;
import java.util.List;

public class DocStatisticalChartsBean {
	List<String> codes;
	List<Double> vedioDatas ;
	List<Double> audioDatas;
	List<Double> picDatas;
	List<Double> logDatas;
	
	public DocStatisticalChartsBean(){
		codes = new ArrayList<String>();
		vedioDatas = new ArrayList<Double>();
		audioDatas = new ArrayList<Double>();
		picDatas = new ArrayList<Double>();
		logDatas = new ArrayList<Double>();
	}
	
	public void assign(DocStatisicalBean src){
		//this.getCodes().add(src.getCode());
		this.getCodes().add(src.getName());
		this.getVedioDatas().add(src.getVedioCount());
		this.getAudioDatas().add(src.getAudioCount());
		this.getPicDatas().add(src.getPicCount());
		this.getLogDatas().add(src.getLogCount());
/*		double douValue = 0;
		douValue = src.getVedioLen()==null?0:Math.round(src.getVedioLen()/(1000*60.0)*100.00)/100.00; //分钟 
		this.getVedioLens().add(douValue);
		douValue = src.getFileSize()==null?0:Math.round(src.getFileSize()/(1000*1000*1000.00) * 100.00)/100.00; //M 
		this.getFileSizes().add(douValue);
		this.getFileCounts().add(src.getFileCount()==null?0:src.getFileCount().intValue());
*/		
	}

	public List<String> getCodes() {
		return codes;
	}

	public void setCodes(List<String> codes) {
		this.codes = codes;
	}

	public List<Double> getVedioDatas() {
		return vedioDatas;
	}

	public void setVedioDatas(List<Double> vedioDatas) {
		this.vedioDatas = vedioDatas;
	}

	public List<Double> getAudioDatas() {
		return audioDatas;
	}

	public void setAudioDatas(List<Double> audioDatas) {
		this.audioDatas = audioDatas;
	}

	public List<Double> getPicDatas() {
		return picDatas;
	}

	public void setPicDatas(List<Double> picDatas) {
		this.picDatas = picDatas;
	}

	public List<Double> getLogDatas() {
		return logDatas;
	}

	public void setLogDatas(List<Double> logDatas) {
		this.logDatas = logDatas;
	}
	
	
}
