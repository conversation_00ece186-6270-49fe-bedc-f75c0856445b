/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: ShootingAssessmentSumBean
 * @说明: 拍摄考核概况Bean，区域，组织
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingAssessmentSumBean extends AbstractDTO {

	private Long policeId;
	/** 警号*/
	private String policeCode;
	/** 职位*/
	private String position;
	/** 姓名*/
	private String name;
	/** 性别*/
	private String sex;
	/** 年龄*/
	private Integer age;
	/** 视频考核视频数*/
	private Integer assessDocs;
	/** 考核期间视频上传数*/
	private Integer uploadDocs;
	/** 考评率*/
	private Float assessRate;
	
	public void addAssessDoc(int c) {
		assessDocs = assessDocs + c;
		assessRate =  ((float)assessDocs/(float)uploadDocs);
	}
	
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public Integer getAge() {
		return age;
	}
	public void setAge(Integer age) {
		this.age = age;
	}
	public Float getAssessRate() {
		return assessRate;
	}
	public void setAssessRate(Float assessRate) {
		this.assessRate = assessRate;
	}
	public String getPosition() {
		return position;
	}
	public void setPosition(String position) {
		this.position = position;
	}
	public Integer getAssessDocs() {
		return assessDocs;
	}
	public void setAssessDocs(Integer assessDocs) {
		this.assessDocs = assessDocs;
	}
	public Integer getUploadDocs() {
		return uploadDocs;
	}
	public void setUploadDocs(Integer uploadDocs) {
		this.uploadDocs = uploadDocs;
	}
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public Long getPoliceId() {
		return policeId;
	}
	public void setPoliceId(Long policeId) {
		this.policeId = policeId;
	}
	
}
