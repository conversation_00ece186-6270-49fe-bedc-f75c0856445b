package junit;

import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.dao.DepartmentDao;
import com.jsdz.admin.org.dao.OrganizationDao;
import com.jsdz.admin.org.dao.PositionDao;
import com.jsdz.admin.org.dao.RegionDao;
import com.jsdz.admin.org.model.Department;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.model.Position;
import com.jsdz.admin.org.model.Region;

/**
 * 
 * @类名: TestOprator.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年4月26日下午2:32:45
 * 修改记录：
 *
 * @see
 */
@RunWith(SpringJUnit4ClassRunner.class)
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations={"classpath:/testApplicationContext-common.xml"})

public class TestOprator {
	@Autowired
	public OrganizationDao organizationDao;
	@Autowired
	public RegionDao regionDao;
	@Autowired
	public DepartmentDao departmentDao;
	@Autowired
	public PositionDao positionDao;


	
	
	
	//SimpleDateFormat sdf =   new SimpleDateFormat( " yyyy-MM-dd HH:mm:ss " )
	SimpleDateFormat sdf =   new SimpleDateFormat( "yyyy-MM-dd" );
	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}
	
	/*
	@Test
	@Transactional
	public void testAddEmployees() throws UnsupportedEncodingException {
		//String personName,String personMale,Date personBirthDate,String personIdNum
		try {
			Employees em1 = new Employees("王洪军","男",sdf.parse("1990-07-10"),"452365879521236545");
			Employees em2 = new Employees("赵宏","男",sdf.parse("1986-08-10"),"452365198508106545");
			Department d1 = departmentDao.findById((long)2);
			em1.setDepartment(d1);
			Department d2 = departmentDao.findById((long)3);
			em2.setDepartment(d2);
			
			employeesDao.saveOrUpdate(em1);
			employeesDao.saveOrUpdate(em2);
			System.out.println(em1.getName());
			System.out.println(em2.getName());
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        
	}
	*/

	
	/*
	@Test
	@Transactional
	public void testAddPerson() throws UnsupportedEncodingException{
		try {
			Person em1 = new Person("王洪军","男",sdf.parse("1990-07-10"),"452365879521236545");
			Person em2 = new Person("赵宏","男",sdf.parse("1986-08-10"),"452365198508106545");
			PersonDao.saveOrUpdate(em1);
			PersonDao.saveOrUpdate(em2);
			System.out.println(em1.getPersonName());
			System.out.println(em2.getPersonName());
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        
	}
	*/
	
	
	@Test
	@Transactional
	public void testAddOrganization() throws UnsupportedEncodingException {
		//String personName,String personMale,Date personBirthDate,String personIdNum
		try {
			Organization org1 = new Organization("广东省公安厅","省厅");
			Organization org11 = new Organization("广州市公安局","市局");
			Organization org12 = new Organization("深圳市公安局","市局");
			Region region = regionDao.locateRegionById((long)1);
			org1.setRegion(region);
			region = regionDao.locateRegionById((long)3);
			org11.setRegion(region);
			org12.setRegion(region);
			
			org11.setParentOrg(org1);
			org12.setParentOrg(org1);
			
			
			organizationDao.saveOrUpdate(org1);
			organizationDao.saveOrUpdate(org11);
			organizationDao.saveOrUpdate(org12);
			
			System.out.println(org1.getOrgName());
			System.out.println(org11.getOrgName());
			System.out.println(org12.getOrgName());
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
        
	}
	
	@Test
	@Transactional
	public void testAddDepartment(){
		Department d1 = new Department("局长办公室");
		Department d2 = new Department("财务科");
		Department d3 = new Department("业务科");
		
		departmentDao.saveOrUpdate(d1);
		departmentDao.saveOrUpdate(d2);
		departmentDao.saveOrUpdate(d3);
		
	}
	
	
	
	@Test
	@Transactional
	public void testAddPosition(){
		Position p1 = new Position("局长");
		Position p2 = new Position("科长");
		Position p3 = new Position("队长");
		
		positionDao.saveOrUpdate(p1);
		positionDao.saveOrUpdate(p2);
		positionDao.saveOrUpdate(p3);
		
	}	
	
}
