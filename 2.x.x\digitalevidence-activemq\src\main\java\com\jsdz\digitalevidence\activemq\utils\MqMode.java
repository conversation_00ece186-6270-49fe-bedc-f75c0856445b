package com.jsdz.digitalevidence.activemq.utils;
/**
 * 
 * @类名: MqMode
 * @说明: JMS消息传递模式
 *
 * @author: kenny
 * @Date	2018年1月25日下午2:30:11
 * 修改记录：
 *
 * @see
 */
public enum MqMode {
	/**/
	QUEUE(1, "队列模式"), TOPIC(2, "主题模式");
	
	public int index;
	public String name;

	private MqMode(int index, String name) {
		this.index=index;
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	

}
