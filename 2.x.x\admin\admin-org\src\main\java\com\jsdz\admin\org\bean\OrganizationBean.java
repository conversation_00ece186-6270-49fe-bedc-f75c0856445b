package com.jsdz.admin.org.bean;
/**
 * 
 * @类名: OrganizationBean.java
 * @说明: 单位DTO
 *
 * @author: kenny
 * @Date	2017年5月8日下午4:21:23
 * 修改记录：
 *
 * @see
 */

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.model.Region;
import com.jsdz.core.AbstractDTO;

public class OrganizationBean extends AbstractDTO{
	
	private Long id;
	private String orgCode;//单位编号
	private String orgName;//机构名称
	private String orgType;//机构类型(厅/局/县局/派出所);
	private Long parentId; //上级ID
	private String parentCode; //上级代码
	private String parentName; //上级名称
	private Long regionId; //区域id
	private String regionName; //上级名称
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTime; //创建日期
	private String path; //上级名称
	private String state = "open";// open,closed
	
	public void assign(Organization src) {
		this.setId(src.getId());
		this.setPath(src.getPath());
		this.setOrgCode(src.getOrgCode());
		this.setOrgName(src.getOrgName());
		this.setOrgType(src.getOrgType());
		this.setCreateTime(src.getCreateTime());
		if (src.getParentOrg()!=null){
			this.setParentId(src.getParentOrg().getId()==null?null:src.getParentOrg().getId());
			this.setParentName(src.getParentOrg().getOrgName()==null?null:src.getParentOrg().getOrgName());
			this.setParentCode(src.getParentOrg().getOrgCode()==null?null:src.getParentOrg().getOrgCode());			
		}
		if (src.getRegion()!=null){
			this.setRegionId(src.getRegion().getId()==null?null:src.getRegion().getId());
			this.setRegionName(src.getRegion().getRegionName()==null?null:src.getRegion().getRegionName());
			
		}

	}

	public void assignTo(Organization dest) {
		dest.setId(this.id);
		dest.setPath(this.getPath());
		dest.setOrgCode(this.getOrgCode());
		dest.setOrgName(this.getOrgName());
		dest.setOrgType(this.getOrgType());
		dest.setCreateTime(this.getCreateTime());
		if (this.getParentId() != null)
		{
			Organization pog = new Organization();
			pog.setId(this.getParentId());
			dest.setParentOrg(pog);
		}
		if (this.getRegionId()!= null)
		{
			Region rgn = new Region();
			rgn.setId(this.getRegionId());
			dest.setRegion(rgn);
		}
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getOrgType() {
		return orgType;
	}
	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}
	public Long getParentId() {
		return parentId;
	}
	public void setParentId(Long parentId) {
		this.parentId = parentId;
	}
	public String getParentName() {
		return parentName;
	}
	public void setParentName(String parentName) {
		this.parentName = parentName;
	}
	public Long getRegionId() {
		return regionId;
	}
	public void setRegionId(Long regionId) {
		this.regionId = regionId;
	}
	public String getRegionName() {
		return regionName;
	}
	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}
	
	public String getParentCode() {
		return parentCode;
	}

	public void setParentCode(String parentCode) {
		this.parentCode = parentCode;
	}

}
