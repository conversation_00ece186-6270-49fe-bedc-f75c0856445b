package com.jsdz.digitalevidence.assessment.test;

import java.util.Date;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.admin.security.service.OperatorService;
import com.jsdz.admin.security.utils.SessionUtils;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAssessorSumBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentPushDocBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentPushDocQueryBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentOfPushDocBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.model.AssessmentCate;
import com.jsdz.digitalevidence.assessment.model.ShootingAngleLevel;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessment;
import com.jsdz.digitalevidence.assessment.model.ShootingContentLevel;
import com.jsdz.digitalevidence.assessment.model.ShootingResolutionLevel;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocAssessorStrategy;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocDocStrategy;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocPoliceStrategy;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocStrategy;
import com.jsdz.digitalevidence.assessment.service.AssessmentPushService;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.utils.BeanUtils;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: AssessmentPushDocTest
 * @说明: 拍摄考核推送视频测试
 *        推送视频服务，推送策略
 *
 * <AUTHOR>
 * @Date	 2017年4月24日 下午2:00:03
 * 修改记录：
 *
 * @see 	 
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations={
		"/testApplicationContext-common.xml",
		"/testApplicationContext-cycle.xml",
		"/testApplicationContext-dynsql.xml",
		"/testApplicationContext-scheduler.xml",
		"/testApplicationContext-reportengine.xml",
		"/testApplicationContext-documenttype.xml"})
public class AssessmentPushDocTest {

	/** 推送策略*/
	@Autowired
	private PushDocPoliceStrategy policeStrategy;
	@Autowired
	private PushDocDocStrategy docStrategy;
	@Autowired
	private PushDocAssessorStrategy assessorStrategy;
	@Autowired
	private PushDocStrategy pushDocStrategy;
	@Autowired
	private AssessmentPushService pushDocService;
	@Autowired
	private EmployeesService empService;
	
	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
	}
	
	// 
	@Test
	@Transactional
	public void testPushDocPoliceStrategy() throws Exception {
		//
		Date startDate = DateTimeUtils.StringToDate("2017-08-05", DateTimeUtils.defaultDatePatten2);
		Date endDate = DateTimeUtils.StringToDate("2017-09-05", DateTimeUtils.defaultDatePatten2);
		int topN = 10; 
		//
		List<ShootingAssessmentSumBean> emps = policeStrategy.findPushingPolice(startDate, endDate, 0.3333f, topN);
		//
		BeanUtils.printBean(emps);
		
	}	
	
	@Test
	@Transactional
	public void testPushDocDocStrategy() throws Exception {
		//
		Long policeId = 4L;
		Date startDate = DateTimeUtils.StringToDate("2017-08-05", DateTimeUtils.defaultDatePatten2);
		Date endDate = DateTimeUtils.StringToDate("2017-09-05", DateTimeUtils.defaultDatePatten2);
		int topN = 10; 
		//
		List<Document> emps = docStrategy.findPushDoc(policeId, startDate, endDate, topN);
		//
		BeanUtils.printBean(emps);
		
	}	
	
	@Test
	@Transactional
	public void testPushDocAssessorStrategy() throws Exception {
		//
		Date startDate = DateTimeUtils.StringToDate("2017-09-05", DateTimeUtils.defaultDatePatten2);
		Date endDate = DateTimeUtils.StringToDate("2017-10-05", DateTimeUtils.defaultDatePatten2);
		int topN = 10; 
		//
		List<AssessmentAssessorSumBean> emps = assessorStrategy.findAssessor(startDate, endDate, topN);
		//
		BeanUtils.printBean(emps);
		
	}

	// 
	@Test
	@Transactional
	public void testPushDoc() throws Exception {
		//
		Date startDate = DateTimeUtils.StringToDate("2017-08-05", DateTimeUtils.defaultDatePatten2);
		Date endDate = DateTimeUtils.StringToDate("2017-09-05", DateTimeUtils.defaultDatePatten2);
		//
		Cycle cycle = new Cycle(startDate, endDate);
		// 
		pushDocStrategy.push(cycle);
		
	}	
	
	// 搜索推送到考核员文档测试
	@Autowired
	private OperatorService operatorService;
	
	/** 警号*//*
	private String policeCode;
	*//** 警员姓名*//*
	private String policeName;
	*//** 组织*//*
	private TreeField orgId;
	*//** 考核周期*//*
	*//** 当前周期日*//*
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date cycleDate;
	*//** 周期选择，上一个/下一个*//*
	private CycleSelector cycleSelector;
	*//** 是否已考核*//*
	private Boolean hasAssessed;
	*//** 是否未考核*//*
	private Boolean notAssessed;*/
	@Test
	@Transactional
	public void testSearchPushingDoc() throws Exception {
		//
		Operator operator = operatorService.locateOperatorById(71L);
		// 设置当前用户
		SessionUtils.setCurrentOperator(operator);
		//
		Page<AssessmentPushDocBean> page = new Page<AssessmentPushDocBean>();
		page.setPage(1);
		page.setPageSize(2);
		//
		AssessmentPushDocQueryBean qb = new AssessmentPushDocQueryBean();
		// 周期
		Date cycleDate = DateTimeUtils.StringToDate("2017-09-28", DateTimeUtils.defaultDatePatten2);
		qb.setCycleDate(cycleDate);
		// 上一个，下一个
		// ...
		// 
		// qb.setHasAssessed(true);
		// qb.setNotAssessed(false);
		//
		page = pushDocService.searchPushDoc(page, qb);
		System.out.println(page.getRows());
		
	}	
	
	@Test
	@Transactional
	public void testOpenPushingDoc() {
		ShootingAssessmentOfPushDocBean saBean = pushDocService.openPushDocAssessment(3L, new Operator(), "");
		System.out.println(saBean);
	}
	
	// 提交推送文档拍摄考核测试
	@Test
	@Transactional
	public void testSubmitAssessmentOfPushingDoc() {
		Operator operator = operatorService.locateOperatorById(71L);
		// 考核员
		Employees inspector = empService.locateEmployeesById(10L);
		//
		SessionUtils.setCurrentOperator(operator);
		// 考核资料
		Document doc = new Document();
		doc.setId(932L);
		//
		ShootingAssessment sa = new ShootingAssessment();
		sa.setInspector(inspector);
		// 考核日期
		sa.setAssessDate(new Date());
		// 按要求拍摄
		sa.setAYQPS(true);
		// 考核类型
		sa.setCate(AssessmentCate.NORMAL);
		// 考核资料
		sa.setDocument(doc);
		// 没有违法违纪
		sa.setNoWFWJ(true);
		// 文明规范
		sa.setWMGF(false);
		// 按要求运用语言
		sa.setYYYY(true);
		// 拍摄角度
		sa.setShootingAngle(ShootingAngleLevel.ACCURATE);
		sa.setShootingContent(ShootingContentLevel.GENERAL);
		sa.setShootingResolution(ShootingResolutionLevel.HIGH);
		//
		this.pushDocService.submitPushDocAssessment(31L, sa);
	}
	
}
