package com.jsdz.digitalevidence.task.dao.mapper;

import com.jsdz.digitalevidence.task.model.TTaskProcessDoc;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 分发任务附件表(TTaskProcessDoc)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-28 11:43:30
 */
public interface TTaskProcessDocMapper {

    /**
     * 新增数据
     *
     * @param tTaskProcessDoc 实例对象
     * @return 影响行数
     */
    int insert(TTaskProcessDoc tTaskProcessDoc);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TTaskProcessDoc> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TTaskProcessDoc> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     *
     * @param entity TTaskProcessDoc 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertNew(@Param("entity") TTaskProcessDoc entity);

    /**
     * 修改数据
     *
     * @param tTaskProcessDoc 实例对象
     * @return 影响行数
     */
    int update(TTaskProcessDoc tTaskProcessDoc);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

