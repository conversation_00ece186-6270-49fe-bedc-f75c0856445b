package com.jsdz.digitalevidence.alarm.alarm110.bean;

/**
 * 给情指行推送接警
 */
public class AlarmToQZHBean {

    public AlarmToQZHBean(DeviceAlarmDto dto) {
        this.id = dto.getCode();
        this.gljjdbh = dto.getAlarmCode();
    }

    /**
     * 国标id，必填
     */
    private String id;

    /**
     * 关联接警单编号
     */
    private String gljjdbh;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGljjdbh() {
        return gljjdbh;
    }

    public void setGljjdbh(String gljjdbh) {
        this.gljjdbh = gljjdbh;
    }

}
