package com.jsdz.digitalevidence.analysis.model;
/**
 * 
 * @类名: DailyType
 * @说明: 日结类型
 *
 * @author: kenny
 * @Date	2017年5月31日下午3:12:27
 * 修改记录：
 *
 * @see
 */
public enum DailyType {
	TYPE_DAY(1,"日结"),TYPE_MONTH(2,"月结"),TYPE_YEAR(3,"年结");
	
	public int index;
	public String name;
	
	private DailyType(int index, String name) {
		this.name = name;
	}
	
	public int getIndex() {
		return index;
	}
	public void setIndex(int index) {
		this.index = index;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	public DailyType getDailyType(int ordinal){
		if (ordinal==0)
			return TYPE_DAY;
		else if (ordinal==0)
			return TYPE_MONTH;
	    else if (ordinal==0)
	    	return TYPE_YEAR;
		else
			return TYPE_DAY;
	}	
}
