package com.jsdz.digitalevidence.analysis.dao.impl;
/**
 * 
 * @类名: DocStatisicalDaoImpl.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年6月13日下午5:09:16
 * 修改记录：
 *
 * @see
 */

import java.util.Collection;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import org.springframework.stereotype.Repository;

import com.jsdz.digitalevidence.analysis.bean.DocStatisicalDto;
import com.jsdz.digitalevidence.analysis.dao.DocStatisicalDao;

@Repository
public class DocStatisicalDaoImpl extends HibernateDaoSupport implements DocStatisicalDao {
	//通过SQL查询所有数据不分页
	@Override
	public List<DocStatisicalDto> QueryNamedAllDataSQL(final String sqlStr, final String[] paramNames, final Object[] values) {

		List<DocStatisicalDto> data = getHibernateTemplate().execute(new HibernateCallback<List<DocStatisicalDto>>() {
			@SuppressWarnings("unchecked")
			public List<DocStatisicalDto> doInHibernate(Session session) throws HibernateException {
				Query queryObject = session.createSQLQuery(sqlStr);
				// 设置实体转换
				SQLQuery sqlQuery = (SQLQuery) queryObject;
				//sqlQuery.addEntity(template.getItemClass());
				sqlQuery.addEntity(DocStatisicalDto.class);
				if (values != null) {
					for (int i = 0; i < values.length; i++) {
						applyNamedParameterToQuery(queryObject, paramNames[i], values[i]);
					}
				}
				return queryObject.list();
			}
		});
		return data;
	}
	
	protected void applyNamedParameterToQuery(Query queryObject, String paramName, Object value)
			throws HibernateException {

		if (value instanceof Collection) {
			queryObject.setParameterList(paramName, (Collection<?>) value);
		} else if (value instanceof Object[]) {
			queryObject.setParameterList(paramName, (Object[]) value);
		} else if (value instanceof Enum) {
			int ordinal = ((Enum<?>) value).ordinal();
			queryObject.setParameter(paramName, ordinal);
		} else {
			queryObject.setParameter(paramName, value);
		}
	}	
}
