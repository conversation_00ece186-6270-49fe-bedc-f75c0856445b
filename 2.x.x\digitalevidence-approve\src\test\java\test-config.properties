# \u6570\u636e\u5e93\u8fde\u63a5\u914d\u7f6e-oracle
#jdbc.database=Oracle
#jdbc.driver=oracle.jdbc.driver.OracleDriver
#jdbc.url=******************************************
#jdbc.username=etl
#jdbc.password=casking

# mysql
jdbc.database=Mysql
jdbc.driver=com.mysql.jdbc.Driver

jdbc.url=*********************************************************************************************************************************************************************
jdbc.username=root
jdbc.password=jsdz1234

#c3p0 settings
c3p0.acquireIncrement=3
c3p0.acquireRetryAttempts=60
c3p0.acquireRetryDelay=1000
c3p0.initialPoolSize=10
c3p0.maxPoolSize=30
c3p0.minPoolSize=10
c3p0.maxIdleTime=60
c3p0.numHelperThreads=3
c3p0.maxStatementsPerConnection=100

#document##############
# smb 
#document.storage.rootpath=smb://root:jsdz1234@*************/public/
document.storage.rootpath=e:/repo/
fileserver.rootpath=http://localhost:8081/media/
# èµææ¶éï¼ä¸ä¼ å¤å°å¤©åè¿æ
document.expireddays=3650
#dynsql template
document.dynsql.template=dynsql/
#1024*1024*10
document.extract.writelimit=10485760
# 
document.pathstrategy.id.segs=3
document.pathstrategy.id.seglen=3
#
report.template.path=report
#
document.delete.batchsize = 50
#
document.jobgroup = Document
document.clear.jobname = DocumentClearJob
document.clear.jobtype = documentClearJob
document.clear.jobdesc = \u8FC7\u671F\u6E05\u7406\u4F5C\u4E1A