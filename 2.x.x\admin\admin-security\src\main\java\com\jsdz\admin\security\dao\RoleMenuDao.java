package com.jsdz.admin.security.dao;

/**
 *
 * @类名: RoleMenuDao
 * @说明:
 * @author: kenny
 * @Date 2017-09-08 12:03:22
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleMenu;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface RoleMenuDao extends GenericORMEntityDAO<RoleMenu,Long> {

	//新增
	public void addRoleMenu(RoleMenu roleMenu);

	//修改
	public void updateRoleMenu(RoleMenu roleMenu);

	//删除
	public void deleteRoleMenu(RoleMenu roleMenu);

	//按id查询,结果是游离状态的数据
	public RoleMenu findRoleMenuById(Long id);

	//按id查询
	public RoleMenu locateRoleMenuById(Long id);

	//单个查询
	public RoleMenu findRoleMenuByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<RoleMenu> findAllRoleMenus();

	//列表查询
	public List<RoleMenu> findRoleMenusByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RoleMenu> findRoleMenusOnPage(Page<RoleMenu> page,String queryStr,String[] paramNames,Object[] values);
	
	//删除该角色关联的所有权限
	public void deleteRoleMenuByRole(Role role);

}

