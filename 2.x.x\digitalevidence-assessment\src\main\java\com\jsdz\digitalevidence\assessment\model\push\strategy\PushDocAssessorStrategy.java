package com.jsdz.digitalevidence.assessment.model.push.strategy;

import java.util.Date;
import java.util.List;

import com.jsdz.digitalevidence.assessment.bean.AssessmentAssessorSumBean;

/**
 * @类名: PushDocAssessorStrategy
 * @说明: 拍摄考核视频推送考核员策略
 *        推给哪个考核员
 *
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 修改记录：
 *
 * @see
 */
public interface PushDocAssessorStrategy {

	public List<AssessmentAssessorSumBean> findAssessor(Date startDate, Date endDate, int topN);
}
