/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.model.push.strategy.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocDocStrategy;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.reportquery.ReportQueryDao;

/**
 * @类名: PushDocDocStrategyImpl
 * @说明: 视频推送查找视频文档策略
 *
 * <AUTHOR>
 * @Date	 2017年10月17日 下午6:59:43
 * 修改记录：
 *
 * @see 	 
 */
@Component
public class PushDocDocStrategyImpl implements PushDocDocStrategy {

	@Autowired
	private ReportQueryDao rqDao;

	// 未考核过，未推送过，考核期内的文档, top n	
	@SuppressWarnings("unchecked")
	@Override
	public List<Document> findPushDoc(Long policeId, Date startDate, Date endDate, int topN) {
		List<Document> docs = (List<Document>)rqDao.queryNamedSQL(
				"findPushingDocTopN", 
				new String[]{"policeId", "startDate","endDate", "topN"}, 
				new Object[]{policeId, startDate, endDate,  topN});
		return docs;
	}

}
