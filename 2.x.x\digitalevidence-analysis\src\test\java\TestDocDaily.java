	/**
	 * 
	 * @类名: TestDocDaily.java
	 * @说明: 
	 *
	 * @author: kenny
	 * @Date	2017年5月31日下午12:58:51
	 * 修改记录：
	 *
	 * @see
	 */

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.analysis.bean.SiteDocDailyDto;
import com.jsdz.digitalevidence.analysis.model.DailyObject;
import com.jsdz.digitalevidence.analysis.model.DailyType;
import com.jsdz.digitalevidence.analysis.service.DocDailyService;

@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { 
		"classpath:/testApplicationContext-common.xml",
		"classpath:/testApplicationContext-dynsql.xml"
		})

public class TestDocDaily {

	@Autowired
	private DocDailyService docDailyService;
	
	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}

	@Test
	@Transactional
	public void testDailyExecute(){
		List<String> dateList= new ArrayList<String>();
		dateList.add("2016-10-25");
		dateList.add("2016-10-26");
		dateList.add("2016-12-09");
		dateList.add("2017-04-08");
		dateList.add("2017-04-24");
		
		String date = "2016-10-25";
		for (int i=0;i<dateList.size();i++){
			date = dateList.get(i);
			docDailyService.dailyExecute(DailyType.TYPE_DAY, DailyObject.DIALY_SITE,date,date);
			docDailyService.dailyExecute(DailyType.TYPE_DAY, DailyObject.DIALY_RECORDER,date,date);
			docDailyService.dailyExecute(DailyType.TYPE_DAY, DailyObject.DIALY_POLICE,date,date);
		}
	}

	//@Test
	public void testFindSiteAnalysis(){
		Page<SiteDocDailyDto> page = new Page<SiteDocDailyDto>();
		page.setOffset(0);
		page.setPageSize(100);
		Date dateFrom;
		Date dateTo;
		String siteNo = "%123%";
		
		
		try{
			dateFrom = sdf.parse("2016-10-01");
			dateTo = sdf.parse("2017-10-01");
		}catch(Exception e){
			System.out.println(e);
			return;
		}
		String[] paramNames = new String[]{"dateFrom","dateTo","siteNo"}; 
		Object[] values = new Object[]{dateFrom,dateTo,siteNo};
		
		
/*		page = docDailyService.findSiteAnalysis(page, paramNames, values);
		System.out.println("*** " + page.getRows().size());
		for (SiteDocDailyDto sdd : page.getRows()){
			SiteDoxDailyBean sddb = new SiteDoxDailyBean();
			sddb.assign(sdd);
			System.out.println("*** " + sddb.toString());
		}*/
	}

}
