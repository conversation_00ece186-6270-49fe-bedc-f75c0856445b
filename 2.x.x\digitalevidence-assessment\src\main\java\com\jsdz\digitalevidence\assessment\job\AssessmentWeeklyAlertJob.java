/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.job;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.digitalevidence.assessment.service.AssessmentAlertService;
import com.sun.xml.bind.v2.TODO;

/**
 * @类名: AssessmentWeeklyAlertJob
 * @说明: 考核预警作业，周
 *        每周生成，当前周期
 *
 * <AUTHOR>
 * @Date	 2017年6月5日 下午4:10:36
 * 修改记录：
 *
 * @see 	 
 * 
 * TODO
 *  *. 分块
 *  
 */
//public class AssessmentWeeklyAlertJob extends BaseAssessmentJob {
public class AssessmentWeeklyAlertJob {
	
	public static final String KEY_PARAM_REPORT_DATA = "_reportData";
	
	@Autowired
	private AssessmentAlertService alertService;
	
	/* (non-Javadoc)
	 * @see com.jsdz.scheduler.job.AbstractJob#execute(org.quartz.JobExecutionContext)
	 */
/*	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
		// 传入参数
		// 警员
		List<Employees> emps = this.getPolice();
		// 报告日期
		Map<String, Object> params = this.getParams();
		Date reportData = (Date)params.get(KEY_PARAM_REPORT_DATA);
		if(reportData==null)
			reportData = new Date();
		//
		try {
			for(Employees emp : emps) {
				alertService.genAssessmentWeeklyAlertOfPolice(emp.getId(), new Date());
			}
		} catch (Exception e) {
			throw new JobExecutionException(e.getMessage());
		}
	}*/

}
