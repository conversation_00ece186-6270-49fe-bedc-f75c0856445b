<?xml version="1.0" encoding="utf-8"?>
<!-- 考核监督-视频推送动态sql -->
<sqlTemplates>
  <entry>
  	<!-- 查找推送警员 -->
    <string>findPushingPoliceTopN</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
		select policeId,
		   a.uploadDocs,
		   a.assessDocs,
		   IFNULL(assessDocs/uploadDocs, 0) assessRate
		   from (
				SELECT 
				    e._id policeId,
				    SUM(CASE
				        WHEN d.doc_id IS NOT NULL THEN 1
				        ELSE 0
				    	END) uploadDocs,
				    SUM(CASE
				        WHEN s.ID IS NOT NULL THEN 1
				        ELSE 0
				    	END) 
				    	+ IFNULL((SELECT count(*) 
				    	FROM t_assessment_push_document p
					    LEFT JOIN t_doc d ON p.doc_id=d.doc_id
				        where p.POLICE_ID=e._id 
				             and p.has_assessed = false {0} {1}), 0) assessDocs
				FROM admin_employees e 
				    LEFT JOIN t_doc d ON e._id=d.POLICE_ID 
				    LEFT JOIN t_assessment_shooting s ON s.DOC_ID = d.DOC_ID			
				WHERE 1=1 {0} {1}
				GROUP BY e._id) a
			where 1=1 and a.uploadDocs>0 {2}
			order by assessDocs/uploadDocs asc
		    limit 0, :topN
	  </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.admin.security.model.Employees</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!-- 1, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and d.upload_time &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and d.upload_time &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      	<!-- 3, assessRate -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>assessRate</paramName>
                <t>and assessDocs/uploadDocs &lt; :assessRate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 查找(可)推送文档 -->
    <string>findPushingDocTopN</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
        select d.* 
          from 
        	t_doc d
        WHERE 1=1 
        and not exists (SELECT * FROM t_assessment_push_document p WHERE p.doc_id = d.doc_id)
        and not exists (SELECT * FROM t_assessment_shooting s WHERE s.doc_id = d.doc_id)
        {0} {1} {2}
        limit 0, :topN
	  </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.document.model.Document</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, police  -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeId</paramName>
                <t>and d.police_id = :policeId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      	<!-- 1, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and d.upload_time &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and d.upload_time &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 查找推送考核员 -->
    <string>findPushingAssessorTopN</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
        select a._id policeId,
            sum(assessedDocs) assessedDocs,
            sum(totalPushDocs) totalPushDocs,
            sum(notAssessPushDocs) notAssessPushDocs
			from (
				select e._id,
				    SUM(CASE
						WHEN s.ID IS NOT NULL {0} {1}
						THEN 1 ELSE 0 END) assessedDocs,
						0 totalPushDocs,
						0 notAssessPushDocs
					from admin_employees e
					left join t_assessment_shooting s on e._id = s.police_id
					LEFT JOIN t_doc d ON s.doc_id=d.doc_id
					WHERE 1=1 and e._positionId = 19 
				    group by e._id
				  union all
				select e._id,
				    SUM(CASE
						WHEN p.ID IS NOT NULL and p.HAS_ASSESSED = false  
							{0} {1}
						THEN 1 ELSE 0 END) assessedDocs,
					SUM(CASE
						WHEN p.ID IS NOT NULL 
						     {0} {1}
              				THEN 1 ELSE 0 END) totalPushDocs,
					SUM(CASE
						WHEN p.ID IS NOT NULL 
                           {0} {1} 
                           THEN 1 ELSE 0 END) -
					  SUM(CASE
						 WHEN p.ID IS NOT NULL and p.HAS_ASSESSED = false 
                         {0} {1} 
                         THEN 1 ELSE 0 END) notAssessPushDocs
					from admin_employees e
				    left join t_assessment_push_document p on e._id = p.police_id 
				    LEFT JOIN t_doc d ON p.doc_id=d.doc_id
					WHERE 1=1 and e._positionId = 19 
				    group by e._id
			    ) a
			group by a._id
			order by sum(assessedDocs) asc
			limit 0, :topN
	  </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentAssessorSumBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
      	<!--0, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and d.upload_time &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 1, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and d.upload_time &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 考核员查找推送文档 -->
    <string>searchAssessmentPushingDoc</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
        select
				p.id pushDocId,
				b._workNumber policeCode,
				b._name policeName,
				o._orgName orgName,
				d1._name deptName,
				c._position position,
				d.doc_id docId,
				d.doc_name docName,
				a._workNumber assessorPoliceCode,
				a._name assessorName,
				p.has_assessed hasAssessed,
				p.create_time createTime
				from
				t_assessment_push_document p
			    left join t_doc d on p.doc_id = d.doc_id 
				left join admin_employees b on b._id = d.police_id
				left join admin_position c on b._positionId = c._id
				left join admin_organization o on o._id = b._organizationId
				left join admin_region r on o._regionId = r._id
				left join admin_department d1 on d1._id = b._departmentId
				left join admin_employees a on a._id = p.assessor_id
				where 1=1 {0} {1} {2} {3} {4} {5} {6} {7}
	  </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentPushDocBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, police  -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>assessorId</paramName>
                <t>and a._id = :assessorId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      	<!-- 1, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and d.upload_time &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and d.upload_time &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, 警号 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeCode</paramName>
                <t>and b._workNumber = :policeCode</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4, 警员姓名 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeName</paramName>
                <t>and  b._name like :policeName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 5, 组织 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>orgId</paramName>
                <t>and o._path like :orgId</t>
                <a class="IsTree"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 6, 是否已考核 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>hasAssessed</paramName>
                <t>and p.has_assessed = :hasAssessed</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 7, 是否未考核 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>notAssessed</paramName>
                <t>and (p.has_assessed is null or p.has_assessed = :notAssessed)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
      
    <!-- 考核员查找推送给自己的文档总页数 -->
    <entry>
    <string>searchAssessmentPushingDocTotalPage</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
        select
				count(*) total
				from
				t_assessment_push_document p
			    left join t_doc d on p.doc_id = d.doc_id 
				left join admin_employees b on b._id = d.police_id
				left join admin_position c on b._positionId = c._id
				left join admin_organization o on o._id = b._organizationId
				left join admin_region r on o._regionId = r._id
				left join admin_department d1 on d1._id = b._departmentId
				left join admin_employees a on a._id = p.assessor_id
				where 1=1 {0} {1} {2} {3} {4} {5} {6} {7}
	  </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.AssessmentPushDocBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, police  -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>assessorId</paramName>
                <t>and a._id = :assessorId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      	<!-- 1, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>startDate</paramName>
                <t>and d.upload_time &gt;= :startDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 2, endTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>endDate</paramName>
                <t>and d.upload_time &lt; :endDate</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 3, 警号 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeCode</paramName>
                <t>and b._workNumber like :policeCode</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 4, 警员姓名 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>policeName</paramName>
                <t>and  b._name like :policeName</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 5, 组织 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>orgId</paramName>
                <t>and o._path like :orgId</t>
                <a class="IsTree"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 6, 是否已考核 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>hasAssessed</paramName>
                <t>and p.has_assessed = :hasAssessed</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
        <!-- 7, 是否未考核 -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>notAssessed</paramName>
                <t>and (p.has_assessed is null or p.has_assessed = :notAssessed)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 
  	  考核推送视频
  	-->
    <string>openAssessmentPushing</string>
    <sqlTemplate>
      <!-- sql/hql模式 -->
      <sqlPattern>
		 select
		 		p.id pushDocId,
				b._workNumber policeCode,
				b._name name,
				c._position position,
				b._male sex,
				b._age age,
				p.doc_id docId,
				f.alarm_code alarmcode,
				f.alarm_name alarmname,
				f.alarm_tel alarmtel,
				f.id id,
				f.alarm_context alarmcontext,
				f.is_relation isrelation,
				d.content_type contentType
				from
				t_assessment_push_document p
				left join admin_employees b on b._id = p.police_id
				left join admin_position c on b._positionId = c._id
				left join admin_organization o on o._id = b._organizationId
				left join admin_region r on o._regionId = r._id
				left join admin_department d1 on d1._id = b._departmentId
				left join t_alarm_relation e on e.document_id=p.DOC_ID
				left join t_alarm_info f on f.id=e.alarminfo_id
				left join t_doc d on p.doc_id = d.doc_id
				where 1=1 {0}
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentOfPushDocBean</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, startTime -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>pushingDocId</paramName>
                <t>and p.id = :pushingDocId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
     </sqlTemplate>
  	</entry>
  
</sqlTemplates>