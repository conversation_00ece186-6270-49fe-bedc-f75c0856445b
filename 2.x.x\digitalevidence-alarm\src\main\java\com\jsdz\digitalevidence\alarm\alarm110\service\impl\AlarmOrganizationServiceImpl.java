package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

/**
 * 
 * @类名: AlarmOrganizationServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-08-29 21:08:18
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmOrganizationDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmOrganization;
import com.jsdz.digitalevidence.alarm.alarm110.service.AlarmOrganizationService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

@Service("AlarmOrganizationServiceImpl")
public class AlarmOrganizationServiceImpl implements AlarmOrganizationService {

	@Autowired
	private AlarmOrganizationDao alarmOrganizationDao;

	//新增
	public AjaxResult addAlarmOrganization(AlarmOrganization alarmOrganization) {
		AjaxResult result = new AjaxResult();
		alarmOrganizationDao.addAlarmOrganization(alarmOrganization);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateAlarmOrganization(AlarmOrganization alarmOrganization) {
		AjaxResult result = new AjaxResult();
		alarmOrganizationDao.updateAlarmOrganization(alarmOrganization);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteAlarmOrganization(AlarmOrganization alarmOrganization) {
		AjaxResult result = new AjaxResult();
		alarmOrganizationDao.deleteAlarmOrganization(alarmOrganization);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public AlarmOrganization findAlarmOrganizationById(Long id){

		return alarmOrganizationDao.findAlarmOrganizationById(id);
	}

	//按 id 查询
	public AlarmOrganization locateAlarmOrganizationById(Long id) {
		return alarmOrganizationDao.locateAlarmOrganizationById(id);
	}

	//单个查询
	public AlarmOrganization findAlarmOrganizationByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmOrganizationDao.findAlarmOrganizationByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<AlarmOrganization> findAllAlarmOrganizations() {
		return alarmOrganizationDao.findAllAlarmOrganizations();
	}

	//列表查询
	public List<AlarmOrganization> findAlarmOrganizationsByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmOrganizationDao.findAlarmOrganizationsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<AlarmOrganization> findAlarmOrganizationsOnPage(Page<AlarmOrganization> page, String queryStr, String[] paramNames, Object[] values) {
		Page<AlarmOrganization> pos = alarmOrganizationDao.findAlarmOrganizationsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
