package com.jsdz.digitalevidence.task.model;

import java.io.Serializable;

/**
 * 任务附件表(TTaskDoc)实体类
 *
 * <AUTHOR>
 * @since 2023-09-26 15:42:45
 */
public class TTaskDoc implements Serializable {
    private static final long serialVersionUID = -30041397435642120L;

    public TTaskDoc() {

    }

    public TTaskDoc(Long taskId, Long siteId, String docUrl) {
        this.taskId = taskId;
        this.siteId = siteId;
        this.docUrl = docUrl;
    }

/**
     * 任务id
     */
    private Long taskId;
/**
     * 文档id
     */
    private String docUrl;
/**
     * 存储id
     */
    private Long siteId;
/**
     * 是否删除 0、未删除 1、已删除
     */
    private Byte isDeleted;


    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getDocUrl() {
        return docUrl;
    }

    public void setDocUrl(String docUrl) {
        this.docUrl = docUrl;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Byte getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Byte isDeleted) {
        this.isDeleted = isDeleted;
    }

}

