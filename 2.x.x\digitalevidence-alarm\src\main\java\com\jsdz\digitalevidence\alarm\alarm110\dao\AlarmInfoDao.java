package com.jsdz.digitalevidence.alarm.alarm110.dao;

/**
 *
 * @类名: AlarmInfoDao
 * @说明:
 * @author: kenny
 * @Date 2017-08-29 19:34:11
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;

public interface AlarmInfoDao extends GenericORMEntityDAO<AlarmInfo,Long> {

	//新增
	public void addAlarmInfo(AlarmInfo alarmInfo);

	//修改
	public void updateAlarmInfo(AlarmInfo alarmInfo);

	//删除
	public void deleteAlarmInfo(AlarmInfo alarmInfo);

	//按id查询,结果是游离状态的数据
	public AlarmInfo findAlarmInfoById(Long id);

	//按id查询
	public AlarmInfo locateAlarmInfoById(Long id);

	//单个查询
	public AlarmInfo findAlarmInfoByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmInfo> findAllAlarmInfos();

	//列表查询
	public List<AlarmInfo> findAlarmInfosByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmInfo> findAlarmInfosOnPage(Page<AlarmInfo> page,String queryStr,String[] paramNames,Object[] values);

	
	// 多个查询（根据其他字段查询）
	public List<AlarmInfo> findAllAlarmInfosByCondition(String queryStr, String[] paramNames, Object[] values);
	
	// 得到警情信息中的最大id值
	public Long findMaxId(String queryStr);

}

