package com.jsdz.digitalevidence.task.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;

import java.util.Date;
import java.util.List;

/**
 * 任务管理表(TTask)表实体类
 *
 * <AUTHOR>
 * @since 2023-09-25 16:37:32
 */
@SuppressWarnings("serial")
public class TTask {

    private Long id;
    //标题
    private String title;
    //类别
    private Integer cate;
    //内容
    private String content;
    //等级
    private int level;

    //地点
    private String address;

    //经度
    private String longitude;

    //纬度
    private String latitude;

    //任务开始时间
    @JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date startTime;
    //任务结束时间
    @JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date endTime;
    //任务延期时间
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date delayTime;
    //是否延期 0、未延期 1、已延期
    private Integer isDelay;
    //机构id
    private Long orgId;
    //是否包含下级单位 0、不包含 1、包含
    private Byte includeSub;
    //创建者
    private Long createBy;
    //创建时间
    @JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTime;
    //更新者
    private Long updateBy;
    //更新时间
    @JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date updateTime;
    //是否删除 0、未删除 1、已删除
    private Byte isDeleted;

    private String libIdNum;

    private String libPersonName;

    private String libLabel;

    private String libPersonSex;

    private List<String> urlList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getCate() {
        return cate;
    }

    public void setCate(Integer cate) {
        this.cate = cate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(Date delayTime) {
        this.delayTime = delayTime;
    }

    public Integer getIsDelay() {
        return isDelay;
    }

    public void setIsDelay(Integer isDelay) {
        this.isDelay = isDelay;
    }

    public Long getOrgId() {
        return orgId;
    }

    public void setOrgId(Long orgId) {
        this.orgId = orgId;
    }

    public Byte getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(Byte includeSub) {
        this.includeSub = includeSub;
    }

    public Long getCreateBy() {
        return createBy;
    }

    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Byte isDeleted) {
        this.isDeleted = isDeleted;
    }

    public List<String> getUrlList() {
        return urlList;
    }

    public void setUrlList(List<String> urlList) {
        this.urlList = urlList;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLibIdNum() {
        return libIdNum;
    }

    public void setLibIdNum(String libIdNum) {
        this.libIdNum = libIdNum;
    }

    public String getLibPersonName() {
        return libPersonName;
    }

    public void setLibPersonName(String libPersonName) {
        this.libPersonName = libPersonName;
    }

    public String getLibLabel() {
        return libLabel;
    }

    public void setLibLabel(String libLabel) {
        this.libLabel = libLabel;
    }

    public String getLibPersonSex() {
        return libPersonSex;
    }

    public void setLibPersonSex(String libPersonSex) {
        this.libPersonSex = libPersonSex;
    }
}

