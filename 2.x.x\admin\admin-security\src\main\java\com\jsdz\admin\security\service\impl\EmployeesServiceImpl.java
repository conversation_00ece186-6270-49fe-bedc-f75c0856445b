package com.jsdz.admin.security.service.impl;

import java.util.Date;

/**
 * 
 * @绫诲悕: EmployeesServiceImpl
 * @璇存槑: 
 *
 * @author: kenny
 * @Date 2017-05-13 13:48:18
 * 淇敼璁板綍锛�
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.SecurityConsts;
import com.jsdz.admin.security.dao.EmployeesDao;
import com.jsdz.admin.security.dao.OperatorDao;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.PoliceOrganization;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.admin.security.service.OperatorService;
import com.jsdz.admin.security.service.PoliceOrganizationService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.cache.encrypt.MD5;
import com.jsdz.digitalevidence.cache.utils.Constant;
import com.jsdz.reportquery.ReportQueryDao;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("EmployeesServiceImpl")
public class EmployeesServiceImpl implements EmployeesService {

		@Autowired
		private EmployeesDao employeesDao;
		@Autowired
		private OperatorDao operatorDao;
		@Autowired
		private ReportQueryDao rqDao;
		@Autowired
		private  PoliceOrganizationService policeOrganizationService;
		@Autowired
		private OperatorService operatorService;
		
		

		//新增
		@Transactional
		public AjaxResult addEmployees(Employees employees) {
			AjaxResult result = new AjaxResult();
			result.setSuccess(false);
			if (employees.getName() == null || "".equals(employees.getName())){
				result.setMsg("姓名必须输入。");
				return result;
			}
			
			if ( !(employees.getIdNum()==null || "".equals(employees.getIdNum()))  ){
				if (null!=findEmployeesByParam(
						"from Employees e where e.isDeleted = false and e.idNum = :idNum",
						new String[]{"idNum"},
						new Object[]{employees.getIdNum()})){
					result.setMsg("此身份证号已存在。");
					return result;
				}
			}
			
			if (employees.getWorkNumber() == null || "".equals(employees.getWorkNumber())){
				result.setMsg("警号必须输入。");
				return result;
			}
			

			
			if (null!=findEmployeesByParam(
					"from Employees e where e.isDeleted = false and e.workNumber = :workNumber",
					new String[]{"workNumber"},
					new Object[]{employees.getWorkNumber()})){
				result.setMsg("警号重复。");
				return result;
			}

			employees.setUpdateTime(new Date());
			employees.setCreateTime(new Date());
			employees.setIsDeleted(false);
			employees.setId(null);//没加这句总是不成功
			employeesDao.addEmployees(employees);
			
			//同时新增操作员帐号
			Operator operator = operatorDao.findOperatorByWorkNum(employees.getWorkNumber());
			if (operator != null){
				operator.setUpdateTime(new Date());
				operator.setEmployees(employees);
				operator.setDescription(employees.getName());
				operatorDao.updateOperator(operator);
			}else{
				operator = new Operator();
				operator.setEmployees(employees);
				operator.setDescription(employees.getName());
				operator.setLoginName(employees.getWorkNumber());
				operator.setState("0");
				operator.setCreateDate(new Date());
				operator.setUpdateTime(new Date());
				operator.setIsDeleted(false);
				operator.setPassword(MD5.getMD5Str(Constant.DEFALUT_PASSWORD));
				operatorDao.addOperator(operator);
			}
			//更新记录到PoliceOrganization中
			policeOrganizationService.changeOrgPolice(
					null, 
					new PoliceOrganization(employees,employees.getOrganization()));
			
			result.setSuccess(true);
			result.setMsg("新增保存成功。");
			result.setData(employees);
			return result; 
		}

		//修改
		@Transactional
		public AjaxResult updateEmployees(Employees employees) {
			AjaxResult result = new AjaxResult();
			result.setSuccess(false);
			if (employees.getName() == null || "".equals(employees.getName())){
				result.setMsg("姓名必须输入。");
				return result;
			}
			if (employees.getWorkNumber() == null || "".equals(employees.getWorkNumber())){
				result.setMsg("警号必须输入。");
				return result;
			}
			
			Employees employees1=employeesDao.locateEmployeesById(employees.getId());
			if (null == employees1){
				result.setMsg("不存在的员工资料");
				return result;
			}
			
			Organization originalOrg = employees1.getOrganization();

			if ( !(employees.getIdNum()==null || "".equals(employees.getIdNum()))  ){
				if (null!=findEmployeesByParam(
						"from Employees e where e.isDeleted = false and e.idNum = :idNum and e.id != :id",
						new String[]{"idNum","id"},
						new Object[]{employees.getIdNum(),employees.getId()})){
					result.setMsg("此身份证号已存在。");
					return result;
				}
				
			}
		
			if (employees.getWorkNumber() != null || "".equals(employees.getWorkNumber())){
				if (null!=findEmployeesByParam(
						"from Employees e where e.isDeleted = false and e.workNumber = :workNumber and e.id != :id",
						new String[]{"workNumber","id"},
						new Object[]{employees.getWorkNumber(),employees.getId()})){
					result.setMsg("警号重复。");
					return result;
				}
			}	
			employees1.setName(employees.getName());
			employees1.setWorkNumber(employees.getWorkNumber());
			employees1.setBirthDate(employees.getBirthDate());
			employees1.setMale(employees.getMale());
			employees1.setIdNum(employees.getIdNum());
			employees1.setMobile(employees.getMobile());
			employees1.setOrganization(employees.getOrganization());
			employees1.setPosition(employees.getPosition());
			employees1.setDepartment(employees.getDepartment());
			employees1.setPost(employees.getPost());
			employees1.setIsPolice(employees.getIsPolice());
			employees1.setFaceFeatures(employees.getFaceFeatures());
			employees1.setUpdateTime(new Date());
			
			
			employeesDao.updateEmployees(employees1);
			
			//同时修改操作员帐号
			Operator operator = operatorDao.findOperatorByWorkNum(employees.getWorkNumber());
			if (operator != null){
				operator.setEmployees(employees);
				operator.setDescription(employees.getName());
				operator.setUpdateTime(new Date());
				operatorDao.updateOperator(operator);
			}else{
				operator = new Operator();
				operator.setEmployees(employees);
				operator.setDescription(employees.getName());
				operator.setLoginName(employees.getWorkNumber());
				operator.setState("0");
				operator.setCreateDate(new Date());
				operator.setUpdateTime(new Date());
				operator.setIsDeleted(false);
				operator.setPassword(MD5.getMD5Str(Constant.DEFALUT_PASSWORD));
				operatorDao.addOperator(operator);
			}
			//更新记录到PoliceOrganization中
			policeOrganizationService.changeOrgPolice(
					new PoliceOrganization(employees,originalOrg), 
					new PoliceOrganization(employees,employees1.getOrganization()));

			result.setSuccess(true);
			result.setMsg("修改成功。");
			return result; 
		}
		
		@Override
		public Employees locateEmployeesByWorkNumber(String workNumber) {
			return employeesDao.locateEmployeesByWorkNumber(workNumber);
		}

		//删除
		@Transactional
		public AjaxResult deleteEmployees(Employees employees) {
			AjaxResult result = new AjaxResult();
			result.setSuccess(false);
			
			
			
			//先删除帐号
			List<Operator> list = operatorDao.findByCondition(
					"from Operator o where o.isDeleted = false and o.employees.id = :id", 
					new String[]{"id"}, 
					new Object[]{employees.getId()});
			for (Operator o : list){
				o.setDeleteBy(employees.getDeleteBy());
				operatorService.deleteOperator(o);
			}
			
			Employees employees1 = employeesDao.setDeleteFlag(employees);
			Organization originalOrg = employees1.getOrganization();
			//更新记录到PoliceOrganization中
			policeOrganizationService.changeOrgPolice(
					new PoliceOrganization(employees,originalOrg), 
					null);

			
			result.setSuccess(true);
			result.setMsg("删除成功。");
			return result; 
		}

		//按 id 查询
		@Transactional
		public Employees locateEmployeesById(Long id) {
			return employeesDao.findEmployeesById(id);
		}

		//单个查询
		public Employees findEmployeesByParam(String queryStr, String[] paramNames, Object[] values) {
			Employees result = employeesDao.findEmployeesByCondition(queryStr,paramNames,values);
			
			return result;
		}

		//查询全部
		@Transactional
		public List<Employees> findAllEmployeess() {
			return employeesDao.findAllEmployeess();
		}


	@Transactional
	public List<Employees> findAllEmployeess(long orgId) {
		return employeesDao.findAllEmployeess(orgId);
	}


	//列表查询
		public List<Employees> findEmployeessByParam(String queryStr, String[] paramNames, Object[] values) {
			return employeesDao.findEmployeessByCondition(queryStr,paramNames,values);
		}

		//分页查询
		public Page<Employees> findEmployeessOnPage(Page<Employees> page, String queryStr, String[] paramNames, Object[] values) {
/*			StringBuffer key = new StringBuffer();
			key.append("Employeess_findEmployeessOnPage");
			key.append("(");
			for(int i = 0; i < values.length; i++){
				key.append(paramNames[i]+"=" + values[i] + "|");
			}
			key.append("offest="+page.getOffset() + "|");
			key.append("pageSize="+page.getPageSize());
			key.append(")");
*/			Page<Employees> pos = null;
			
/*			 pos = (Page<Employees>)redisCache.get(key.toString());
			if (pos != null){
				return pos;
			}
*/			
			pos = employeesDao.findEmployeessOnPage(page, queryStr, paramNames, values);
			return pos;
		}

		@SuppressWarnings("unchecked")
		@Override
		public Employees findEmployeesBean(Long empId) {
			List<Employees> emps = (List<Employees>)rqDao.queryNamedHQL(
					SecurityConsts.SQL_KEY_Find_Employees_Bean,
					new String[] { "empId" }, 
					new Object[] { empId });
			return (emps!=null && emps.size()>0) ? emps.get(0) : null;
		}

		@Override
		public Employees locateEmployeesByIdNum(String idNum) {
			List<Employees> emps = (List<Employees>)rqDao.queryNamedHQL(
					SecurityConsts.SQL_KEY_Find_Employees_Bean,
					new String[] { "idNum" },
					new Object[] { idNum });
			return (emps!=null && emps.size()>0) ? emps.get(0) : null;
		}

		/**
		 * 改变警员单位，登录后切换单位
		 * @param employees
		 */
		public void changeEmployeesOrganization(Employees employees){
			Employees employees1=employeesDao.locateEmployeesById(employees.getId());
			employees1.setOrganization(employees.getOrganization());
			employeesDao.updateEmployees(employees1);
		}
}
