package com.jsdz.digitalevidence.statistics.mapper;

import com.jsdz.digitalevidence.statistics.bean.param.VEvidenceStatisticsParam;
import com.jsdz.digitalevidence.statistics.model.VEvidenceStatisticsModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * VIEW Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface VEvidenceStatisticsMapper extends BaseMapper<VEvidenceStatisticsModel> {

    List<VEvidenceStatisticsModel> selectModels(@Param("param")VEvidenceStatisticsParam param);

    Integer selectModelCount(@Param("param")VEvidenceStatisticsParam param);
}
