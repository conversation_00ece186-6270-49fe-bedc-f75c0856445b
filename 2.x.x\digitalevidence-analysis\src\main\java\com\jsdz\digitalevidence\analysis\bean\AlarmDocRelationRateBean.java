package com.jsdz.digitalevidence.analysis.bean;

import java.io.Serializable;

/**
 * 
 * @类名: AlarmDocRelationRateBean
 * @说明: 警情视频关联率报表
 *
 * @author: kenny
 * @Date  2018年6月21日上午11:15:54
 * 修改记录：
 *
 * @see
 */
public class AlarmDocRelationRateBean implements Serializable{

	private static final long serialVersionUID = -7524018954816361709L;
	
	private Long id;
	private String orgCode;//单位编码
	private String orgName;//单位名称
	private String timeRange;//时间范围
	private Long fileCount;//文件数量(排除日志)
	private Long alarmCount;//警情数量
	private Long alarmRelateCount;//警情关联数
	private Double alarmRelateRate;//警情关联率
	private Long docRelationCount;//文档关联数
	private Double docRelationRate;//文档关联率
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getTimeRange() {
		return timeRange;
	}
	public void setTimeRange(String timeRange) {
		this.timeRange = timeRange;
	}
	public Long getFileCount() {
		return fileCount;
	}
	public void setFileCount(Long fileCount) {
		this.fileCount = fileCount;
	}
	public Long getAlarmCount() {
		return alarmCount;
	}
	public void setAlarmCount(Long alarmCount) {
		this.alarmCount = alarmCount;
	}
	public Double getAlarmRelateRate() {
		return alarmRelateRate;
	}
	public void setAlarmRelateRate(Double alarmRelateRate) {
		this.alarmRelateRate = alarmRelateRate;
	}
	public Long getDocRelationCount() {
		return docRelationCount;
	}
	public void setDocRelationCount(Long docRelationCount) {
		this.docRelationCount = docRelationCount;
	}
	public Double getDocRelationRate() {
		return docRelationRate;
	}
	public void setDocRelationRate(Double docRelationRate) {
		this.docRelationRate = docRelationRate;
	}
	public Long getAlarmRelateCount() {
		return alarmRelateCount;
	}
	public void setAlarmRelateCount(Long alarmRelateCount) {
		this.alarmRelateCount = alarmRelateCount;
	}
}
