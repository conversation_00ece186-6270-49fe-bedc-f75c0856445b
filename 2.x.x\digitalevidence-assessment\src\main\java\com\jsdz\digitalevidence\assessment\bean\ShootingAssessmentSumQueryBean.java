/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.core.SortOrder;
import com.jsdz.digitalevidence.assessment.cycle.CycleSelector;
import com.jsdz.reportquery.dynsql.fork.support.tree.TreeField;

/**
 * @类名: AssessmentSumQueryBean
 * @说明: 考核概况查询Bean
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingAssessmentSumQueryBean extends AbstractDTO {

	/** 市/县/区Id*/
	private Long regionId;
	/** 组织*/
	private TreeField orgId;
	/** 部门Id*/
	private Long deptId;
	/** 警号*/
	private String policeCode;
	/** 警员姓名*/
	private String policeName;
	/** 考核率排序*/
	private SortOrder assessRateOrder;
	/** 考核周期*/
	/** 当前周期日*/
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date cycleDate;
	/** 周期选择，上一个/下一个*/
	private CycleSelector cycleSelector;

	public Long getRegionId() {
		return regionId;
	}
	public void setRegionId(Long regionId) {
		this.regionId = regionId;
	}
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public Date getCycleDate() {
		return cycleDate;
	}
	public void setCycleDate(Date cycleDate) {
		this.cycleDate = cycleDate;
	}
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	public CycleSelector getCycleSelector() {
		return cycleSelector;
	}
	public void setCycleSelector(CycleSelector cycleSelector) {
		this.cycleSelector = cycleSelector;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public TreeField getOrgId() {
		return orgId;
	}
	public void setOrgId(TreeField orgId) {
		this.orgId = orgId;
	}
	public SortOrder getAssessRateOrder() {
		return assessRateOrder;
	}
	public void setAssessRateOrder(SortOrder assessRateOrder) {
		this.assessRateOrder = assessRateOrder;
	}

}
