package com.jsdz.digitalevidence.activemq.utils;

import com.jsdz.core.Env;

/**
 * 
 * @类名: FunctionUtils
 * @说明: 
 *
 * @author: kenny
 * @Date	2018年2月7日上午10:10:18
 * 修改记录：
 *
 * @see
 */
public class FunctionUtils {
	//是否启用MQ
	public static boolean activemqActive(){
		String activemqActive = Env.getProperty("activemq.active");
		activemqActive=activemqActive==null || activemqActive.equals("")?"0":activemqActive;
		return activemqActive.equals("1");
	}
	//是否保存到转换数据
	public static boolean exchangeActive(){
		String exchangeActive = Env.getProperty("exchange.active");
		exchangeActive=exchangeActive==null || exchangeActive.equals("")?"0":exchangeActive;
		return exchangeActive.equals("1");
	}
	
}
