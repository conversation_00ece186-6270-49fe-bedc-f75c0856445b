<?xml version="1.0" encoding="UTF-8"?>
<beans  xmlns="http://www.springframework.org/schema/beans"
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
		xmlns:aop="http://www.springframework.org/schema/aop"
		xmlns:tx="http://www.springframework.org/schema/tx"
		xmlns:context="http://www.springframework.org/schema/context"
		xsi:schemaLocation="http://www.springframework.org/schema/beans
	                        http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
	                        http://www.springframework.org/schema/context
			                http://www.springframework.org/schema/context/spring-context-3.1.xsd
			                http://www.springframework.org/schema/tx
			                http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
			                http://www.springframework.org/schema/aop 
			                http://www.springframework.org/schema/aop/spring-aop-3.1.xsd" default-autowire="byName">

	<!-- spring bean自动扫描 -->
	<context:component-scan base-package="com.digitalevidence.analysis.dao" />
	<context:component-scan base-package="com.digitalevidence.analysis.service" />
	
	<!-- 全局配置属性 -->
	<bean id="propertyConfigurer"
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:testanalysis.properties</value>
			</list>
		</property>
	</bean>

	<!-- 数据源配置 -->
	<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource"
		destroy-method="close">
		<property name="driverClass" value="${jdbc.driver}" />
		<property name="jdbcUrl" value="${jdbc.url}" />
		<property name="user" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />
		<property name="acquireIncrement" value="${c3p0.acquireIncrement}" />
		<property name="acquireRetryAttempts" value="${c3p0.acquireRetryAttempts}" />
		<property name="acquireRetryDelay" value="${c3p0.acquireRetryDelay}" />
		<property name="initialPoolSize" value="${c3p0.initialPoolSize}" />
		<property name="maxPoolSize" value="${c3p0.maxPoolSize}" />
		<property name="minPoolSize" value="${c3p0.minPoolSize}" />
		<property name="maxIdleTime" value="${c3p0.maxIdleTime}" />
		<property name="numHelperThreads" value="${c3p0.numHelperThreads}" />
		<property name="maxStatementsPerConnection" value="${c3p0.maxStatementsPerConnection}" />
	</bean>

	<!-- hibernate session -->
	<bean id="sessionFactory"
		class="org.springframework.orm.hibernate3.LocalSessionFactoryBean">
		<property name="dataSource" ref="dataSource" />
		<property name="configLocation">
			<value>classpath:test-hibernate.cfg.xml</value>
		</property>
	</bean>
	
	<!-- hibernate事务管理器 -->
	<bean id="transactionManager"
		class="org.springframework.orm.hibernate3.HibernateTransactionManager">
		<property name="sessionFactory">
			<ref local="sessionFactory" />
		</property>
	</bean>
	
	<!-- 注解事务 -->
	<tx:annotation-driven transaction-manager="transactionManager"/>
</beans>

