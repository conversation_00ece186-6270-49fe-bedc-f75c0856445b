package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

/**
 * 
 * @类名: AlarmPoliceServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-03-25 13:43:48
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmPoliceDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmPolice;
import com.jsdz.digitalevidence.alarm.alarm110.service.AlarmPoliceService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("AlarmPoliceServiceImpl")
public class AlarmPoliceServiceImpl implements AlarmPoliceService {

	@Autowired
	private AlarmPoliceDao alarmPoliceDao;

	//新增
	public AjaxResult addAlarmPolice(AlarmPolice alarmPolice) {
		AjaxResult result = new AjaxResult();
		alarmPoliceDao.addAlarmPolice(alarmPolice);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateAlarmPolice(AlarmPolice alarmPolice) {
		AjaxResult result = new AjaxResult();
		alarmPoliceDao.updateAlarmPolice(alarmPolice);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteAlarmPolice(AlarmPolice alarmPolice) {
		AjaxResult result = new AjaxResult();
		alarmPoliceDao.deleteAlarmPolice(alarmPolice);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public AlarmPolice findAlarmPoliceById(Long id){

		return alarmPoliceDao.findAlarmPoliceById(id);
	}

	//按 id 查询
	public AlarmPolice locateAlarmPoliceById(Long id) {
		return alarmPoliceDao.locateAlarmPoliceById(id);
	}

	//单个查询
	public AlarmPolice findAlarmPoliceByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmPoliceDao.findAlarmPoliceByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<AlarmPolice> findAllAlarmPolices() {
		return alarmPoliceDao.findAllAlarmPolices();
	}

	//列表查询
	public List<AlarmPolice> findAlarmPolicesByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmPoliceDao.findAlarmPolicesByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<AlarmPolice> findAlarmPolicesOnPage(Page<AlarmPolice> page, String queryStr, String[] paramNames, Object[] values) {
		Page<AlarmPolice> pos = alarmPoliceDao.findAlarmPolicesOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
