package com.jsdz.digitalevidence.archives.mapper;

import java.util.List;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.digitalevidence.archives.model.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface EquipmentCountMapper {
	
	@Select("select o.* from admin_organization a, v_equipment_count o where (o.id = a.id or o.parentId = a.id) and a.path = #{path}")
	List<EquipmentCount> findEquipmentCount(String path);

	@Select("select o.* from admin_organization a, v_equipment_count o where (o.id = a.id or o.parentId = a.id) and a.path = #{path}  ORDER BY recorderNum desc")
	List<EquipmentCount> lawpblCount(String path);

	List<VRecorderOrgCount> lawslCount(@Param("permLevel") PerLvlBean permLevel);

	List<VSiteOrgCount> siteCount(@Param("permLevel") PerLvlBean permLevel);

	List<TDocOrgCount> docCount(@Param("permLevel") PerLvlBean permLevel);

	List<RelationCount> relationCount(@Param("permLevel") PerLvlBean permLevel);

	List<ArchivesCount> archivesCount(@Param("permLevel") PerLvlBean permLevel);
}
