import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.Map;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.jsdz.digitalevidence.cache.encrypt.RSAHelper;
import com.jsdz.digitalevidence.cache.utils.Constant;
import com.jsdz.digitalevidence.cache.utils.JsonUtil;
import com.jsdz.digitalevidence.cache.utils.SysSource;
/**
	 * 
	 * @类名: Test
	 * @说明: 
	 *
	 * @author: kenny
	 * @Date	2018年1月20日下午2:40:12
	 * 修改记录：
	 *
	 * @see
	 */
public class Test {
	public static void main(String[] args) throws Exception {
		testFastJson();
		//TestEncryptDecrypt();
	}
	
	private static void TestEncryptDecrypt(){
		SysSource.getInstance().setAttribute(Constant.VAR_PUBLIC_KEY,
			"MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQClL9urKZAz35nbo/5mr4Ic408L9lcz+veZxVHGVoDRVTDpxiYNXwq3jcB1htvG+XkSwqaxylB8C5E2cNJzGxQGSyea9Ecv3453hyRWU9gTGdpk71p1hxmremro8AqkVnqCCs0SmoXVBapTjos48EUqBVG59WvhGNg0FwsC1KLBUQIDAQAB"	
		);
		SysSource.getInstance().setAttribute(Constant.VAR_PRIVATE_KEY,
				"MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKUv26spkDPfmduj/mavghzjTwv2VzP695nFUcZWgNFVMOnGJg1fCreNwHWG28b5eRLCprHKUHwLkTZw0nMbFAZLJ5r0Ry/fjneHJFZT2BMZ2mTvWnWHGat6aujwCqRWeoIKzRKahdUFqlOOizjwRSoFUbn1a+EY2DQXCwLUosFRAgMBAAECgYAU8q/B0gCGg4mob/CYJxOyBCWHF9NRNTdnGb3YECo/4bTVQJqcwXvDcUeB3/tJDWQMPAEVtNlmEbV1mPMnQ7zTJtbK8IAnUeq5yXz0W6o7lxfsiq2fMmPXV4MBviNmzc5Y+KEO9O3fkQ0GnV/sWbCN5GYUoXFIWGSt/VLCyGeDfQJBAOwgonMsDfW4kAzD4wbEsAXUFLU6uNPBvDZUk0SHCa1VaxMH4jbecZgA0rA1tMyQST4cf3OZdCJTgq0MHpXdpecCQQCzFs9ftpFS9phHMN19O5OVCmvegxwoRNCDn0ZjL2wsW+zUp4n2phw3F7HFRmaM1q5q3NrpIutTM9MUxc+p/AgHAkEA02AHbEhUmtHECdlVItrxo9YNT4qop883F/+v8Vlc/VCI0HlvNKT+VQ7vjBQXrEPBfRtvsGD/SDcvtI8z1f6bowJAJblfU20UraIuL4jWjyO2kcWRNVzu2HiuZ5tsa0Y5BhsTBzSHdkJFXUGLe9+5iF9MfEwrfMKTSYJ4Kz8D4G2rjwJBAKln8ty+FhOWwbCGFE8MMkhcu/fRf6GB2btyMhf+SOcHKLZ+rt+eEP9TNFsjooveTaoThd0TQfXYMZaCFzMbSkU="	
			);
		
		
		//
		
		RSAHelper RSAHelper = new RSAHelper();
		String time = String.valueOf(new Date().getTime()) + "-LIMITTIME";
		System.err.println("time:"+time);
		try {
			String decStr = RSAHelper.RsaEncrypt(time);
			System.err.println("decStr:"+decStr);
			decStr = decStr;
			String origin = RSAHelper.RsaDecrypt(decStr);
			System.err.println("解密后:"+origin);
			origin = origin.substring(0, origin.indexOf("-LIMITTIME"));
			System.err.println("原时间:"+origin);
			
		} catch (Exception e) {
			System.err.println(e.toString());
		}
	}
	private static void testDate(){
		Date d=new Date();   
		SimpleDateFormat df=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");   
		System.out.println("今天的日期："+df.format(d));
		System.out.println("五分钟后的日期：" + df.format(new Date(d.getTime() + 5 * 60 * 1000)));  
		
		System.out.println("两天前的日期：" + df.format(new Date(d.getTime() - 2 * 24 * 60 * 60 * 1000)));  
		System.out.println("三天后的日期：" + df.format(new Date(d.getTime() + 3 * 24 * 60 * 60 * 1000)));
	
		GregorianCalendar gc=new GregorianCalendar(); 
		gc.setTime(d); 
		gc.add(1,-1);
		System.out.println("去年：" + df.format(gc.getTime()));
		gc.setTime(d); 
		gc.add(2,1);
		System.out.println("下个月：" + df.format(gc.getTime()));
		gc.setTime(d); 
		gc.add(3,2);
		System.out.println("下2周：" + df.format(gc.getTime()));
		gc.setTime(d); 
		gc.add(5,1);
		System.out.println("明天：" + df.format(gc.getTime()));
		gc.add(6,1);
		System.out.println("下一个小时：" + df.format(gc.getTime()));
		
		
		String s = "1/10/23/43/";  
        String s1 = "/";  
          
        String[] arr = s.split(s1);  
        System.out.println(arr.length);  
        
        
/*		*gc.add(2,-1)表示月份减一.
		*gc.add(3.-1)表示周减一.
		*gc.add(5,-1)表示天减一
		gc.add(field,value);
*/		
	}
	
	private static void testFastJson(){
		Map<String,Object> map = new HashMap<String,Object>();
		map.put("key1", 100);
		map.put("key2", 100.22);
		map.put("key3", "100");
		map.put("key4", new Date().getTime());
		System.out.println(map);
		String json = JSON.toJSONString(map);
		System.out.println(json);
		map = JSONObject.parseObject(json, Map.class);
		System.out.println(map);
		Map<String,Object> mapgson = JsonUtil.fromJson(json, Map.class);
		System.err.println(mapgson);
		
	}
}
