/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.assessment.cycle.CycleSelector;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.model.ImportantLevel;
import com.jsdz.digitalevidence.document.model.VideoClarity;

/**
 * @类名: AssessmentDocumentQueryBean
 * @说明: 考核视频查询Bean
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentDocumentQueryBean extends AbstractDTO {

	/** 警号*/
	private Long policeId;
	/** 资料文件名称*/
	private String docName;
	/** 清晰度*/
	private VideoClarity clarity;
	/** 上传时间/拍摄时间，起始/结束*/
	/*@JsonFormat(pattern="yyyy-MM-dd HH:mm")
	private Date startUT;
	@JsonFormat(pattern="yyyy-MM-dd HH:mm")
	private Date endUT;
	@JsonFormat(pattern="yyyy-MM-dd HH:mm")
	private Date startCT;
	@JsonFormat(pattern="yyyy-MM-dd HH:mm")
	private Date endCT;*/
	/** 重要级别*/
	private ImportantLevel impLevel;
	/** 文档分类*/
	private DocumentCate cate;
	/** 文档描述*/
	private String comments;
	/** 执法类型*/
	private String enforceTypeName;
	/** 是否已考核*/
	private Boolean isAssessed;
	/** 考核周期*/
	/** 当前所选周期日*/
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date cycleDate;
	/** 周期选择，上一个/下一个*/
	private CycleSelector cycleSelector;
	
	public Date getCycleDate() {
		return cycleDate;
	}
	public void setCycleDate(Date cycleDate) {
		this.cycleDate = cycleDate;
	}
	public CycleSelector getCycleSelector() {
		return cycleSelector;
	}
	public void setCycleSelector(CycleSelector cycleSelector) {
		this.cycleSelector = cycleSelector;
	}
	public Long getPoliceId() {
		return policeId;
	}
	public void setPoliceId(Long policeId) {
		this.policeId = policeId;
	}
	public String getDocName() {
		return docName;
	}
	public void setDocName(String docName) {
		this.docName = docName;
	}
	public VideoClarity getClarity() {
		return clarity;
	}
	public void setClarity(VideoClarity clarity) {
		this.clarity = clarity;
	}
	public ImportantLevel getImpLevel() {
		return impLevel;
	}
	public void setImpLevel(ImportantLevel impLevel) {
		this.impLevel = impLevel;
	}
	public DocumentCate getCate() {
		return cate;
	}
	public void setCate(DocumentCate cate) {
		this.cate = cate;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getEnforceTypeName() {
		return enforceTypeName;
	}
	public void setEnforceTypeName(String enforceTypeName) {
		this.enforceTypeName = enforceTypeName;
	}
	public Boolean getIsAssessed() {
		return isAssessed;
	}
	public void setIsAssessed(Boolean isAssessed) {
		this.isAssessed = isAssessed;
	}
	
}
