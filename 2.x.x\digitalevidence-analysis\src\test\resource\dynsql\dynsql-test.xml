<?xml version="1.0" encoding="utf-8"?>
<!-- 日结综合查询 -->
<sqlTemplates>
  <entry>
  	<!-- sql名称 -->
    <string>docQuerySiteForDialy</string>
    <sqlTemplate>
      <!-- 按站点查询统计日结 -->
      <sqlPattern>
      SELECT max(DOC_ID) as id,
      		IMP_LEVEL as impLevel, 
      		CATE as cate,  
      		sum(DOC_SIZE) as fileSize,
      		sum(DURATION) as vedioLen,
      		COUNT(*) as fileCount,
      		SITE_ID as objectId
      	FROM t_doc
      	{0} {1}
      	GROUP BY SITE_ID,IMP_LEVEL,CATE
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.digitalevidence.analysis.bean.DocDailyDto</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, dataFrom -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataFrom</paramName>
                <t>WHERE CREATE_TIME &gt;=:dataFrom</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 1, site_no -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataTo</paramName>
                <t>AND CREATE_TIME &lt;= :dataTo</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- sql名称  按站点查询 总数 -->
    <string>docQuerySiteForDialyTotalPage</string>
    <sqlTemplate>
      <!-- sql模式 -->
      <sqlPattern>
      SELECT COUNT(*) as Total FROM
      	(SELECT SITE_ID FROM t_doc {0} {1} GROUP BY SITE_ID,IMP_LEVEL,CATE) A
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.digitalevidence.analysis.bean.DocDailyDto</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, dataFrom -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataFrom</paramName>
                <t>WHERE CREATE_TIME &gt;=:dataFrom</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 1, dataTo -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataTo</paramName>
                <t>AND CREATE_TIME &lt;= :dataTo</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>

  <entry>
  	<!-- sql名称  按执法仪查询统计日结-->
    <string>docQueryRecorderForDialy</string>
    <sqlTemplate>
      <sqlPattern>
      SELECT max(DOC_ID) as id,
      		IMP_LEVEL as impLevel, 
      		CATE as cate,  
      		sum(DOC_SIZE) as fileSize,
      		sum(DURATION) as vedioLen,
      		COUNT(*) as fileCount,
      		RECORDER_ID as objectId
      	FROM t_doc
      	{0} {1}
      	GROUP BY RECORDER_ID,IMP_LEVEL,CATE
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.digitalevidence.analysis.bean.DocDailyDto</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, dataFrom -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataFrom</paramName>
                <t>WHERE CREATE_TIME &gt;=:dataFrom</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 1, site_no -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataTo</paramName>
                <t>AND CREATE_TIME &lt;= :dataTo</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- sql名称 按执法仪查询统计日结 总数-->
    <string>docQueryRecorderForDialyTotalPage</string>
    <sqlTemplate>
      <!-- sql模式 -->
      <sqlPattern>
      SELECT COUNT(*) as Total FROM
      	(SELECT RECORDER_ID FROM t_doc {0} {1} GROUP BY RECORDER_ID,IMP_LEVEL,CATE) A
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.digitalevidence.analysis.bean.DocDailyDto</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, dataFrom -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataFrom</paramName>
                <t>WHERE CREATE_TIME &gt;=:dataFrom</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 1, dataTo -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataTo</paramName>
                <t>AND CREATE_TIME &lt;= :dataTo</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
   <entry>
  	<!-- sql名称  按警员查询统计日结-->
    <string>docQueryPoliceForDialy</string>
    <sqlTemplate>
      <sqlPattern>
      SELECT max(DOC_ID) as id,
      		IMP_LEVEL as impLevel, 
      		CATE as cate,  
      		sum(DOC_SIZE) as fileSize,
      		sum(DURATION) as vedioLen,
      		COUNT(*) as fileCount,
      		POLICE_ID as objectId
      	FROM t_doc
      	{0} {1}
      	GROUP BY POLICE_ID,IMP_LEVEL,CATE
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.digitalevidence.analysis.bean.DocDailyDto</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, dataFrom -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataFrom</paramName>
                <t>WHERE CREATE_TIME &gt;=:dataFrom</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 1, site_no -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataTo</paramName>
                <t>AND CREATE_TIME &lt;= :dataTo</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- sql名称 按警员查询统计日结 总数-->
    <string>docQueryPoliceForDialyTotalPage</string>
    <sqlTemplate>
      <!-- sql模式 -->
      <sqlPattern>
      SELECT COUNT(*) as Total FROM
      	(SELECT POLICE_ID FROM t_doc {0} {1} GROUP BY POLICE_ID,IMP_LEVEL,CATE) A
     </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.digitalevidence.analysis.bean.DocDailyDto</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, dataFrom -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataFrom</paramName>
                <t>WHERE CREATE_TIME &gt;=:dataFrom</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      <!-- 1, dataTo -->
      <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dataTo</paramName>
                <t>AND CREATE_TIME &lt;= :dataTo</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry> 
  
  <!--**拍摄统计*********************************************************************************-->
  <entry>
  	<!-- 按站点统计-->
    <string>siteDocDailyAnalysis</string>
    <sqlTemplate>
      <sqlPattern>
      	SELECT 
      		MAX(d.id) as id,
      		min(d.dateFrom) as dateFrom,
			max(d.dateTo) as dateTo,
		 	d.objectId as objectId,
		 	MAX(s.SITE_NO) as siteNo,
		 	MAX(s.SITE_NAME) as siteName,
		 	SUM(d.fileSize) as fileSize,
		 	SUM(d.vedioLen) as vedioLen,
		 	SUM(d.fileCount) as fileCount
		FROM analysis_docdaily d
		LEFT JOIN t_site s on d.objectId = s.ID
		WHERE dailyObject = 0 {0} {1} {2}
		GROUP BY d.objectId
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.digitalevidence.analysis.bean.SiteDocDailyDto</itemClass>
      <!-- sql可变块 -->
      <pieces>
        <!-- 0, dataFrom -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dateFrom</paramName>
                <t>AND d.dateFrom &gt;=:dateFrom</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      	<!-- 1, dataTo -->
      	<sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dateTo</paramName>
                <t>AND d.dateFrom &lt;= :dateTo</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
       	<!-- 2, siteNo -->
      	<sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>siteNo</paramName>
                <t>AND (s.SITE_NO like :siteNo OR s.SITE_NAME like :siteNo)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
       </pieces>     
    </sqlTemplate>
  </entry>
  
  <entry>
  	<!-- 按站点统计总数-->
    <string>siteDocDailyAnalysisTotalPage</string>
    <sqlTemplate>
      <!-- sql模式 -->
      <sqlPattern>
      	SELECT COUNT(*) as Total FROM
      		(SELECT MAX(d.id) FROM analysis_docdaily d
			LEFT JOIN t_site s on d.objectId = s.ID
			WHERE dailyObject = 0 {0} {1} {2}
			GROUP BY d.objectId) A
     </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.digitalevidence.analysis.bean.SiteDocDailyDto</itemClass>
      <!-- sql可变块 -->
       <pieces>
        <!-- 0, dataFrom -->
        <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dateFrom</paramName>
                <t>AND d.dateFrom &gt;=:dateFrom</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      	<!-- 1, dataTo -->
      	<sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>dateTo</paramName>
                <t>AND d.dateFrom &lt;= :dateTo</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
       	<!-- 2, siteNo -->
      	<sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>siteNo</paramName>
                <t>AND (s.SITE_NO like :siteNo OR s.SITE_NAME like :siteNo)</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces> 
    </sqlTemplate>
  </entry>   
  
</sqlTemplates>