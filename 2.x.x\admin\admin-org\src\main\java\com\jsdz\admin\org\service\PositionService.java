package com.jsdz.admin.org.service;

/**
 * 
 * @类名: PositionService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-09 21:50:03
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.org.model.Position;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface PositionService {

	//新增
	public AjaxResult addPosition(Position position);

	//修改
	public AjaxResult updatePosition(Position position);

	//删除
	public AjaxResult deletePosition(Position position);

	//按id查询
	public Position locatePositionById(Long id);

	//单个查询
	public Position findPositionByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<Position> findAllPositions();

	//列表查询
	public List<Position> findPositionsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Position> findPositionsOnPage(Page<Position> page, String queryStr,String[] paramNames,Object[] values);

}

