package com.jsdz.digitalevidence.assessment.dao;

/**
*
* @类名: OrgAssPeriodDao
* @说明:
* @author: kenny
* @Date 2018-01-11 17:27:52
* 修改记录：
* @see
* @see
*/

import java.util.List;
import com.jsdz.digitalevidence.assessment.model.org.OrgAssPeriod;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface OrgAssPeriodDao extends GenericORMEntityDAO<OrgAssPeriod,Long> {

	//新增
	public void addOrgAssPeriod(OrgAssPeriod orgAssPeriod);

	//修改
	public void updateOrgAssPeriod(OrgAssPeriod orgAssPeriod);

	//删除
	public void deleteOrgAssPeriod(OrgAssPeriod orgAssPeriod);

	//按id查询,结果是游离状态的数据
	public OrgAssPeriod findOrgAssPeriodById(Long id);

	//按id查询
	public OrgAssPeriod locateOrgAssPeriodById(Long id);

	//单个查询
	public OrgAssPeriod findOrgAssPeriodByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<OrgAssPeriod> findAllOrgAssPeriods();

	//列表查询
	public List<OrgAssPeriod> findOrgAssPeriodsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<OrgAssPeriod> findOrgAssPeriodsOnPage(Page<OrgAssPeriod> page,String queryStr,String[] paramNames,Object[] values);

	//执行指定的HQL文件
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);

}