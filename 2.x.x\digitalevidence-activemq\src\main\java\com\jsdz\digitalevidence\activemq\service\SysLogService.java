package com.jsdz.digitalevidence.activemq.service;

/**
 * 
 * @类名: SysLogService
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-01-31 15:36:14
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.activemq.model.SysLog;
import com.jsdz.digitalevidence.activemq.utils.LogType;

public interface SysLogService {

	//新增
	public AjaxResult addSysLog(SysLog sysLog);

	//修改
	public AjaxResult updateSysLog(SysLog sysLog);

	//删除
	public AjaxResult deleteSysLog(SysLog sysLog);

	//按id查询,结果是游离状态的数据
	public SysLog findSysLogById(Long id);

	//按id查询
	public SysLog locateSysLogById(Long id);

	//单个查询
	public SysLog findSysLogByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<SysLog> findAllSysLogs();

	//列表查询
	public List<SysLog> findSysLogsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<SysLog> findSysLogsOnPage(Page<SysLog> page, String queryStr,String[] paramNames,Object[] values);

	//登记提示或错误信息
    void setInfoLog(SysLog sysLog,String msg);
    void setErrorLog(SysLog sysLog,String msg);
    
   	void setInfoLog(SysLog sysLog,String keyWord,String msg);
  	void setErrorLog(SysLog sysLog,String keyWord,String msg);

  	public void setLog(SysLog sysLog,LogType logType,String msg);
	public void setLog(SysLog sysLog,LogType logType,String keyWord,String msg);
	public void setLog(LogType logType,String topic,String clazz,String keyWord,String msg);
	
	
	//按指定字段分级查询
	public List<String> findGroupBy(String groupByName);
	
	//根据系统参数设置定时删除时间过得太久的系统日志
	public Integer deleteSysLogBySetAnyDays();
	
	//注册日志主题
	public void registeredTopic(String topic);

}

