/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.model;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: BusinessObject
 * @说明: 业务对象
 *
 * <AUTHOR>
 * @Date	 2017年11月17日 上午10:20:05
 * 修改记录：
 *
 * @see 	 
 */
public class BusinessObject extends AbstractDTO {
	
	/** 对象Id*/
	private Object objId;
	/** */
	private BusinessObjectType objType;
	
	public Object getObjId() {
		return objId;
	}
	public void setObjId(Object objId) {
		this.objId = objId;
	}
	public BusinessObjectType getObjType() {
		return objType;
	}
	public void setObjType(BusinessObjectType objType) {
		this.objType = objType;
	}
	
}
