/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.job;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import com.jsdz.digitalevidence.assessment.service.AssessmentPushService;

/**
 * @类名: AssessmentPushJob
 * @说明: 推送考核视频作业作业
 *
 * <AUTHOR>
 * @Date	 2017年6月5日 下午4:10:36
 * 修改记录：
 *
 * @see 	 
 */
//public class AssessmentPushJob extends BaseAssessmentJob {
public class AssessmentPushJob {
	
	@Autowired
	private AssessmentPushService pushDocService;

	/* (non-Javadoc)
	 * @see com.jsdz.scheduler.job.AbstractJob#execute(org.quartz.JobExecutionContext)
	 */
/*	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
		try {
			pushDocService.pushDoc();
		} catch (Exception e) {
			throw new JobExecutionException(e.getMessage());
		}

	}
*/


}
