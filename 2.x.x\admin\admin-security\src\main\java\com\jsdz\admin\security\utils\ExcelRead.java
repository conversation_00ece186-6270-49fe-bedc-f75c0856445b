package com.jsdz.admin.security.utils;
/**
 * 
 * @类名: ExcelRead
 * @说明: Excel导入
 *
 * @author: kenny
 * @Date	2017年11月4日上午9:39:43
 * 修改记录：
 *
 * @see
 */
import java.io.IOException;  
import java.io.InputStream;  
import java.util.ArrayList;  
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFCell;  
import org.apache.poi.hssf.usermodel.HSSFRow;  
import org.apache.poi.hssf.usermodel.HSSFSheet;  
import org.apache.poi.hssf.usermodel.HSSFWorkbook;  
import org.apache.poi.xssf.usermodel.XSSFCell;  
import org.apache.poi.xssf.usermodel.XSSFRow;  
import org.apache.poi.xssf.usermodel.XSSFSheet;  
import org.apache.poi.xssf.usermodel.XSSFWorkbook;  
import org.springframework.web.multipart.MultipartFile;

import com.jsdz.core.AjaxResult; 

public class ExcelRead {
	   private final Logger logger = Logger.getLogger(this.getClass());
	
	   private Integer totalRows=0; //sheet中总行数  
	   private Integer totalCells=0; //每一行总单元格数  
	    /** 
	     * read the Excel .xlsx,.xls 
	     * @param file jsp中的上传文件 
	     * @return 
	     * @throws IOException  
	     */  
	    public AjaxResult readExcel(MultipartFile file) throws IOException {
	    	try{
		    	AjaxResult result = new AjaxResult(0,false,"非EXCEL格式文件",null);
		        if(file==null||ExcelUtils.EMPTY.equals(file.getOriginalFilename().trim())){  
		            return result;  
		        }else{ 
		        	List<ArrayList<String>> list = null;
		            String postfix = ExcelUtils.getPostfix(file.getOriginalFilename());  
		            if(!ExcelUtils.EMPTY.equals(postfix)){  
		                if(ExcelUtils.OFFICE_EXCEL_2003_POSTFIX.equals(postfix)){  
		                	list = readXls(file);  
		                }else if(ExcelUtils.OFFICE_EXCEL_2010_POSTFIX.equals(postfix)){  
		                	list = readXlsx(file);  
		                }else{                    
		                    //return null;  
		                }  
		            }
		            result.setData(list);
		            return result;
		        }  
	    		
	    	}catch(Exception e){
	    		logger.error(">>>>>>: 2.1、readExcel: " + e.toString()  );
	    		throw e;
	    	}
	    }  
	    /** 
	     * read the Excel 2010 .xlsx 
	     * @param file 
	     * @param beanclazz 
	     * @param titleExist 
	     * @return 
	     * @throws IOException  
	     */  
	    @SuppressWarnings("deprecation")  
	    public List<ArrayList<String>> readXlsx(MultipartFile file){  
	        List<ArrayList<String>> list = new ArrayList<ArrayList<String>>();  
	        // IO流读取文件  
	        InputStream input = null;  
	        XSSFWorkbook wb = null;  
	        ArrayList<String> rowList = null;  
	        try {  
	            input = file.getInputStream();  
	            // 创建文档  
	            wb = new XSSFWorkbook(input);                         
	            //读取sheet(页)  

	            for(int numSheet=0;numSheet<wb.getNumberOfSheets();numSheet++){  
	            	//只导入sheet1
	            	if (numSheet > 0)
	            		break;
	            	
	                XSSFSheet xssfSheet = wb.getSheetAt(numSheet);  
	                if(xssfSheet == null){  
	                    continue;  
	                }  
	                if (totalRows == 0)
	                	totalRows = xssfSheet.getLastRowNum(); 
	                
	                /** 判断是否空行 */
	                boolean isEmptyRow = false;
	                //读取Row,从第二行开始  
	                for(int rowNum = 1;rowNum <= totalRows;rowNum++){  
	                    XSSFRow xssfRow = xssfSheet.getRow(rowNum);  
	                    if(xssfRow!=null){  
	                        rowList = new ArrayList<String>();  
	                        if (totalCells == 0)
	                        	totalCells = Integer.valueOf(xssfRow.getLastCellNum());  
	                        
	                        //读取列，从第一列开始  
	                        for(int c=0;c<=totalCells+1;c++){  
	                            XSSFCell cell = xssfRow.getCell(c);  
	                            if(cell==null){  
	                                rowList.add(ExcelUtils.EMPTY);  
	                                continue;  
	                            }
	                            rowList.add(ExcelUtils.getXValue(cell).trim());  
	                        }
	                        
	                        /** 判断是否是空行  */
	                        isEmptyRow = true;
	                        for (String value :rowList){
	                        	if (!StringUtils.isEmpty(value)){
	                        		isEmptyRow = false;
	                        		break;
	                        	}
	                        }
	                        
	                        if (isEmptyRow){
	                        	break;
	                        }
	                        list.add(rowList);
	                    }  
	                }  
	            }  
	            return list;  
	        } catch (IOException e) {             
	            e.printStackTrace();  
	        } finally{  
	            try {  
	                input.close();  
	            } catch (IOException e) {  
	                e.printStackTrace();  
	            }  
	        }  
	        return null;  
	          
	    }  
	    /** 
	     * read the Excel 2003-2007 .xls 
	     * @param file 
	     * @param beanclazz 
	     * @param titleExist 
	     * @return 
	     * @throws IOException  
	     */  
	    public List<ArrayList<String>> readXls(MultipartFile file){   
	        List<ArrayList<String>> list = new ArrayList<ArrayList<String>>();  
	        // IO流读取文件  
	        InputStream input = null;  
	        HSSFWorkbook wb = null;  
	        ArrayList<String> rowList = null;  
	        try {  
	            input = file.getInputStream();  
	            // 创建文档  
	            wb = new HSSFWorkbook(input);                         
	            //读取sheet(页)  
	            for(int numSheet=0;numSheet<wb.getNumberOfSheets();numSheet++){  
	                HSSFSheet hssfSheet = wb.getSheetAt(numSheet);  
	                if(hssfSheet == null){  
	                    continue;  
	                }  
	                if (totalRows == 0)
	                	totalRows = hssfSheet.getLastRowNum(); 
               
	                //读取Row,从第二行开始  
	                for(int rowNum = 1;rowNum <= totalRows;rowNum++){  
	                    HSSFRow hssfRow = hssfSheet.getRow(rowNum);  
	                    if(hssfRow!=null){  
	                        rowList = new ArrayList<String>();  
	                        if (totalCells == 0)
	                        	totalCells = Integer.valueOf(hssfRow.getLastCellNum());  
	                        //读取列，从第一列开始  
	                        for(short c=0;c<=totalCells+1;c++){  
	                            HSSFCell cell = hssfRow.getCell(c);  
	                            if(cell==null){  
	                                rowList.add(ExcelUtils.EMPTY);  
	                                continue;  
	                            }                             
	                            rowList.add(ExcelUtils.getHValue(cell).trim());  
	                        }          
	                        list.add(rowList);  
	                    }                     
	                }  
	            }  
	            return list;  
	        } catch (IOException e) {             
	            e.printStackTrace();  
	        } finally{  
	            try {  
	                input.close();  
	            } catch (IOException e) {  
	                e.printStackTrace();  
	            }  
	        }  
	        return null;  
	    }
		public Integer getTotalRows() {
			return totalRows;
		}
		public void setTotalRows(Integer totalRows) {
			this.totalRows = totalRows;
		}
		public Integer getTotalCells() {
			return totalCells;
		}
		public void setTotalCells(Integer totalCells) {
			this.totalCells = totalCells;
		}  
	    
	
}
