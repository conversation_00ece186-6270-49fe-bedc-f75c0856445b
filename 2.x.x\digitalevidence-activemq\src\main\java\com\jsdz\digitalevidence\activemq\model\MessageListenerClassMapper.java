package com.jsdz.digitalevidence.activemq.model;
/**
 * 
 * @类名: MessageListenerClassMapper
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年10月27日下午2:49:34
 * 修改记录：
 *
 * @see
 */

import java.util.List;
import java.util.Map;
import javax.jms.MessageListener;

public class MessageListenerClassMapper {

	private Map<String, Integer> listenersName;
	private List<TopicMapper> topicMapper;
	
	public Map<String, Integer> getListenersName() {
		return listenersName;
	}
	public void setListenersName(Map<String, Integer> listenersName) {
		this.listenersName = listenersName;
	}
	public List<TopicMapper> getTopicMapper() {
		return topicMapper;
	}
	public void setTopicMapper(List<TopicMapper> topicMapper) {
		this.topicMapper = topicMapper;
	}

	
	

}
