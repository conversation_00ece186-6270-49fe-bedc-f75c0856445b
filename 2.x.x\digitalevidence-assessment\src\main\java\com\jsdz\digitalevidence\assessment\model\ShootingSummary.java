/**
 * 
 */
package com.jsdz.digitalevidence.assessment.model;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: ShootingSummary
 * @说明: 拍摄统计
 *        
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午5:46:40
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingSummary extends AbstractDTO {

	/** Id*/
	private Long id;	
	/** 视频数*/
	private Integer countOfVedio;
	/** 视频时长，单位毫秒*/
	private Long lengthOfVedio;
	/** 视频大小, 单位: G*/
	private Long sizeOfVedio;
	/** 音频数*/
	private Integer countOfAudio;
	/** 音频时长*/
	private Long lengthOfAudio;
	/** 音频大小, 单位: G*/
	private Long sizeOfAudio;
	/** 图片数*/
	private Integer countOfPic;
	/** 图片大小，单位：byte*/
	private Long sizeOfPic;
	/** 日志数*/
	private Integer countOfLog;
	/** 日志大小*/
	private Long sizeOfLog;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Integer getCountOfVedio() {
		return countOfVedio;
	}
	public void setCountOfVedio(Integer countOfVedio) {
		this.countOfVedio = countOfVedio;
	}
	public Long getLengthOfVedio() {
		return lengthOfVedio;
	}
	public void setLengthOfVedio(Long lengthOfVedio) {
		this.lengthOfVedio = lengthOfVedio;
	}
	public Long getSizeOfVedio() {
		return sizeOfVedio;
	}
	public void setSizeOfVedio(Long sizeOfVedio) {
		this.sizeOfVedio = sizeOfVedio;
	}
	public Integer getCountOfAudio() {
		return countOfAudio;
	}
	public void setCountOfAudio(Integer countOfAudio) {
		this.countOfAudio = countOfAudio;
	}
	public Long getLengthOfAudio() {
		return lengthOfAudio;
	}
	public void setLengthOfAudio(Long lengthOfAudio) {
		this.lengthOfAudio = lengthOfAudio;
	}
	public Long getSizeOfAudio() {
		return sizeOfAudio;
	}
	public void setSizeOfAudio(Long sizeOfAudio) {
		this.sizeOfAudio = sizeOfAudio;
	}
	public Integer getCountOfPic() {
		return countOfPic;
	}
	public void setCountOfPic(Integer countOfPic) {
		this.countOfPic = countOfPic;
	}
	public Long getSizeOfPic() {
		return sizeOfPic;
	}
	public void setSizeOfPic(Long sizeOfPic) {
		this.sizeOfPic = sizeOfPic;
	}
	public Integer getCountOfLog() {
		return countOfLog;
	}
	public void setCountOfLog(Integer countOfLog) {
		this.countOfLog = countOfLog;
	}
	public Long getSizeOfLog() {
		return sizeOfLog;
	}
	public void setSizeOfLog(Long sizeOfLog) {
		this.sizeOfLog = sizeOfLog;
	}
	
}
