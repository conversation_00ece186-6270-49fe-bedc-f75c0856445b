package com.jsdz.digitalevidence.alarm.alarm110.dao.mapper;

import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmInfoVo;
import com.jsdz.digitalevidence.alarm.alarm110.bean.DeviceAlarmDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AlarmMapper {

    void insertAlamSendRecord(@Param("id") Long id, @Param("deviceId") Long deviceId, @Param("status") Integer status);

    List<AlarmInfoVo> getAlarmListByRecorderCode(@Param("code") String code);

    AlarmInfoVo findRecorderAlarm(@Param("code") String code, @Param("alarmCode") String alarmCode);

    /***
     * 获取执法仪警情状态
     * @param dto
     * @return
     */
    Integer alarmStatus(DeviceAlarmDto dto);
}
