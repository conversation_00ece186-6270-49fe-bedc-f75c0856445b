package com.jsdz.digitalevidence.alarm.common.bean;

/**
 * 
 * @类名: GetRecoderViewParam
 * @说明: 
 *
 * @author: kenny
 * @Date	2018年3月2日上午10:06:58
 * 修改记录：
 *
 * @see
 */
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.jsdz.admin.security.bean.ParamGenerateHql;
import com.jsdz.core.Page;
import com.jsdz.core.SortBean;
import com.jsdz.digitalevidence.alarm.common.model.RecoderView;

public class GetRecoderViewParam extends ParamGenerateHql{
	private Page<RecoderView> page;
	private Map<String, Object> kvs = new HashMap<String, Object>();
	private List<String> parLst = new ArrayList<String>();
	private Long docId;
	private String querySql;
	private String totalSql;
	
	public GetRecoderViewParam(){
		page = new Page<RecoderView>();
		page.setOffset(0);
		page.setPageSize(10);
	}
	
	public String queryBeanToHql(){
		parLst.clear();
		
		querySql=" from RecoderView r ";
		totalSql = " select count(*) from RecoderView r ";
		
		String p1 = " r.doId = :docId ";
		parLst.add(p1);
		kvs.put("docId", this.getDocId());
		querySql = this.generateHql(page, parLst, querySql, totalSql);
		
		StringBuilder sb = new StringBuilder();
		if (page.getSort() != null && page.getSort().size() > 0 ){
			sb.append(" order by ");
			List<SortBean> list = page.getSort();
			for (SortBean sortBean : list){
				sb.append(sortBean);
				sb.append(" ");
				sb.append(sortBean.getOrder().toString());
			}
			
		}else{
			sb.append(" order by r.id asc ");
		}
		
		return querySql + sb;
	}
	
	public Page<RecoderView> getPage() {
		return page;
	}
	public void setPage(Page<RecoderView> page) {
		this.page = page;
	}
	public Map<String, Object> getKvs() {
		return kvs;
	}
	public void setKvs(Map<String, Object> kvs) {
		this.kvs = kvs;
	}

	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}

	public String getQuerySql() {
		return querySql;
	}

	public void setQuerySql(String querySql) {
		this.querySql = querySql;
	}

	public String getTotalSql() {
		return totalSql;
	}

	public void setTotalSql(String totalSql) {
		this.totalSql = totalSql;
	}
	
	

}
