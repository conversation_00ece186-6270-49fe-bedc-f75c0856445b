package com.jsdz.digitalevidence.attendance.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *   打卡信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_attendance_check")
public class AttendanceCheckModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 维度
     */
    private String latitude;

    /**
     * 人员设备考勤配置表
     */
    private Long attendanceInfoId;

    /**
     * 考勤时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date attendanceTime;

    /**
     * 打卡描述
     */
    private String attendanceDescribe;


    /**
     * 是否自动打卡（0自动，1手动）
     */
    private Integer isAutomatic;

    /**
     * 创建时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date updateTime;

}
