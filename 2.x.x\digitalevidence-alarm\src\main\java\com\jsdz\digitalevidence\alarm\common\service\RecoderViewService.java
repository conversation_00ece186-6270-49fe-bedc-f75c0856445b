package com.jsdz.digitalevidence.alarm.common.service;
/**
 * 
 * @类名: RecoderViewService
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年10月26日上午10:50:09
 * 修改记录：
 *
 * @see
 */
import java.util.List;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.common.model.RecoderView;

public interface RecoderViewService {
	//新增
	public AjaxResult addRecoderView(RecoderView recoderView);

	//修改
	public AjaxResult updateRecoderView(RecoderView recoderView);

	//删除
	public AjaxResult deleteRecoderView(RecoderView recoderView);

	//按id查询,结果是游离状态的数据
	public RecoderView findRecoderViewById(Long id);

	//按id查询
	public RecoderView locateRecoderViewById(Long id);

	//单个查询
	public RecoderView findRecoderViewByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<RecoderView> findAllRecoderViews();

	//列表查询
	public List<RecoderView> findRecoderViewsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RecoderView> findRecoderViewsOnPage(Page<RecoderView> page, String queryStr,String[] paramNames,Object[] values);

}
