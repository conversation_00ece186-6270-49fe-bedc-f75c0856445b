/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.cycle;

import java.util.Date;

import org.joda.time.Interval;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

/**
 * @类名: Cycle
 * @说明: 周期
 *
 * <AUTHOR>
 * @Date	 2017年8月18日 下午4:33:08
 * 修改记录：
 *
 * @see 	 
 */
public class Cycle {

	/** 周期开始日、结束日*/
	private Date start;
	private Date end;
	
	public Cycle() {
		super();
	}

	public Cycle(Date start, Date end) {
		super();
		this.start = start;
		this.end = end;
	}

	/**
	 * @说明：某日是否在周期内
	 *
	 * <AUTHOR>
	 * @param date
	 * @return
	 * 
	 */
	public boolean inCycle(Date date) {
		return (date.equals(start)||date.after(start)) && date.before(end);
	}
	
	/**
	 * @说明：日期是否在周期范围内
	 *
	 * <AUTHOR>
	 * @param date
	 * @return
	 * 
	 */
	public boolean within(Cycle cycle) {
		return false;
	}
	
	/**
	 * @说明：周期交集
	 *
	 * <AUTHOR>
	 * @param startDate
	 * @param endDate
	 * @return
	 * 
	 */
	public Interval intersect(Cycle cycle) {
		return null;
	}
	
	@JsonSerialize(using=JsonDateSerializer.class)
	public Date getStart() {
		return start;
	}

	@JsonSerialize(using=JsonDateSerializer.class)
	public Date getEnd() {
		return end;
	}

	public void setStart(Date start) {
		this.start = start;
	}

	public void setEnd(Date end) {
		this.end = end;
	}

	
}
