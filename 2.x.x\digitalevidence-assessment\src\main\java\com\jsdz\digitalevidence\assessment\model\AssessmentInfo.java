package com.jsdz.digitalevidence.assessment.model;


import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.core.Env;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocBean;
import com.jsdz.digitalevidence.document.bean.DocumentBean;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.model.EnforceType;
import com.jsdz.digitalevidence.document.model.ImportantLevel;
import com.jsdz.digitalevidence.document.model.VideoClarity;
import com.jsdz.utils.DateTimeUtils;

/**
 * @Package: com.jsdz.digitalevidence.assessment.model
 * @ClassName: AssessmentInfo
 * @Author: 周志强
 * @Date: 2023/2/20 15:45
 */
public class
AssessmentInfo  extends AbstractDTO {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 3384290028536630066L;
    /**考核编号*/
    private String assessmentId;
    /**考核单位*/
    private Integer assessmentOrg;
    /**考核员*/
    private Integer assessmentPoliceId;
    /**考核评分*/
    private Double assessmentScore;
    /**考核意见*/
    private String assessmentRemarks;
    /**考核时间*/
    private Date assessmentTime;
    /**考核更新时间*/
    private Date assessmentUpdateTime;
    /**考核考核添加时间*/
    private Date assessmentCreateTime;
    
	private Long id;
	/** 被考核警员编号*/
	private Integer policeCode;
	private String policeName;
	private String siteCode;
	/** 被考核属单位*/
	private String orgName;
	private String orgPath;
	private String orgCode;
	/** 类型，计算于ContentType*/
	private String type;
	private Long enforceTypeId;
	private String enforceTypeName;
	/** 重要级别*/
	private ImportantLevel impLevelRec;
	private ImportantLevel impLevel;
	/** 资料文件名称*/
	private String docName;
	private String thumbnail;
	/**上传开始时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
	private Date uploadStrTime;
	/**上传结束时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
	private Date uploadEndTime;
	/** 拍摄开始时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
	private Date createStrTime;
	/** 拍摄结束时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
	private Date createEndTime;
	/** 执法类型*/
	private EnforceType enforceType;
	private Long duration;

	private DocumentCate cate;
	private VideoClarity clarity;
	private String equimentCode;
	/** 文件大少，单位：M*/ 
	private Long fileM;
	/** 站点地址*/
	private String siteAddr;
	/** 说明*/
	private String comments;
	
	private Integer storageType = 1;
	
	/**拍摄结束时间**/
	@JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
	private Date endTime;
	
	/**过期时间**/
	@JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
	private Date expiryTime;


	/** 2小时内点击次数，超过10次点击量 是热点视频 */
	private Integer playCount2Hour = 0;

	/**页数*/
	private Integer page;
	

	/**考核级别 1,考核个人 2，考核单位 3，本单位考核*/
	private Integer aeeseeImpLevel;
	
	
	/** 页大小/窗口大小 */
	private Integer pageSize;
	/** 偏移量 */
	private Integer offset;
	
	
	public Integer getOffset() {
		return offset;
	}

	public void setOffset(Integer offset) {
		this.offset = offset;
	}

	
	public Integer getAeeseeImpLevel() {
		return aeeseeImpLevel;
	}

	public void setAeeseeImpLevel(Integer aeeseeImpLevel) {
		this.aeeseeImpLevel = aeeseeImpLevel;
	}

	/**过期预警 1 预警  0 不预警*/
	public Integer getExpiryAlarm(){
		
		if (this.expiryTime != null){
			Integer days = Env.getProperty("document.expiry.waring.days")==null?
					7:Integer.valueOf(Env.getProperty("document.expiry.waring.days"));
			Date alamTime = new Date(expiryTime.getTime() - days * 24 * 60 * 60 *1000L);
			if (alamTime.getTime() < new Date().getTime())
				return 1;
		}
		return 0;
	}

	
	public String getDurationStr() {
		return DateTimeUtils.durationToStr(duration);
	}
	
	public String getFileSizeStr() {
		if(fileM==null)
			return "-";
		long k = fileM / 1024;
		if(k==0)
			return "<1K";
		long m = k / 1024;
		k = k % 1024;
		if(m==0)
			return k+"K";
		long g = m / 1024;
		m = m % 1024;
		if(g==0)
			return m+"M"+k+"K";
		return g+"G"+ m+"M"+k+"K";
		    
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public String getAssessmentId() {
		return assessmentId;
	}

	public void setAssessmentId(String assessmentId) {
		this.assessmentId = assessmentId;
	}

	public Integer getAssessmentOrg() {
		return assessmentOrg;
	}

	public void setAssessmentOrg(Integer assessmentOrg) {
		this.assessmentOrg = assessmentOrg;
	}

	public Integer getAssessmentPoliceId() {
		return assessmentPoliceId;
	}

	public void setAssessmentPoliceId(Integer assessmentPolice) {
		this.assessmentPoliceId = assessmentPolice;
	}

	public Double getAssessmentScore() {
		return assessmentScore;
	}

	public void setAssessmentScore(Double assessmentScore) {
		this.assessmentScore = assessmentScore;
	}

	public String getAssessmentRemarks() {
		return assessmentRemarks;
	}

	public void setAssessmentRemarks(String assessmentRemarks) {
		this.assessmentRemarks = assessmentRemarks;
	}

	public Date getAssessmentTime() {
		return assessmentTime;
	}

	public void setAssessmentTime(Date assessmentTime) {
		this.assessmentTime = assessmentTime;
	}

	public Date getAssessmentUpdateTime() {
		return assessmentUpdateTime;
	}

	public void setAssessmentUpdateTime(Date assessmentUpdateTime) {
		this.assessmentUpdateTime = assessmentUpdateTime;
	}

	public Date getAssessmentCreateTime() {
		return assessmentCreateTime;
	}

	public void setAssessmentCreateTime(Date assessmentCreateTime) {
		this.assessmentCreateTime = assessmentCreateTime;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getPoliceCode() {
		return policeCode;
	}

	public void setPoliceCode(Integer policeCode) {
		this.policeCode = policeCode;
	}

	public String getPoliceName() {
		return policeName;
	}

	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}

	public String getSiteCode() {
		return siteCode;
	}

	public void setSiteCode(String siteCode) {
		this.siteCode = siteCode;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgPath() {
		return orgPath;
	}

	public void setOrgPath(String orgPath) {
		this.orgPath = orgPath;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Long getEnforceTypeId() {
		return enforceTypeId;
	}

	public void setEnforceTypeId(Long enforceTypeId) {
		this.enforceTypeId = enforceTypeId;
	}

	public String getEnforceTypeName() {
		return enforceTypeName;
	}

	public void setEnforceTypeName(String enforceTypeName) {
		this.enforceTypeName = enforceTypeName;
	}

	public ImportantLevel getImpLevelRec() {
		return impLevelRec;
	}

	public void setImpLevelRec(ImportantLevel impLevelRec) {
		this.impLevelRec = impLevelRec;
	}

	public ImportantLevel getImpLevel() {
		return impLevel;
	}

	public void setImpLevel(ImportantLevel impLevel) {
		this.impLevel = impLevel;
	}

	public String getDocName() {
		return docName;
	}

	public void setDocName(String docName) {
		this.docName = docName;
	}

	public String getThumbnail() {
		return thumbnail;
	}

	public void setThumbnail(String thumbnail) {
		this.thumbnail = thumbnail;
	}

	public EnforceType getEnforceType() {
		return enforceType;
	}

	public void setEnforceType(EnforceType enforceType) {
		this.enforceType = enforceType;
	}

	public Long getDuration() {
		return duration;
	}

	public void setDuration(Long duration) {
		this.duration = duration;
	}

	public DocumentCate getCate() {
		return cate;
	}

	public void setCate(DocumentCate cate) {
		this.cate = cate;
	}

	public VideoClarity getClarity() {
		return clarity;
	}

	public void setClarity(VideoClarity clarity) {
		this.clarity = clarity;
	}

	public String getEquimentCode() {
		return equimentCode;
	}

	public void setEquimentCode(String equimentCode) {
		this.equimentCode = equimentCode;
	}

	public Long getFileM() {
		return fileM;
	}

	public void setFileM(Long fileM) {
		this.fileM = fileM;
	}

	public String getSiteAddr() {
		return siteAddr;
	}

	public void setSiteAddr(String siteAddr) {
		this.siteAddr = siteAddr;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public Integer getStorageType() {
		return storageType;
	}

	public void setStorageType(Integer storageType) {
		this.storageType = storageType;
	}

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getExpiryTime() {
		return expiryTime;
	}

	public void setExpiryTime(Date expiryTime) {
		this.expiryTime = expiryTime;
	}

	public Integer getPlayCount2Hour() {
		return playCount2Hour;
	}

	public void setPlayCount2Hour(Integer playCount2Hour) {
		this.playCount2Hour = playCount2Hour;
	}

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Date getUploadStrTime() {
		return uploadStrTime;
	}

	public void setUploadStrTime(Date uploadStrTime) {
		this.uploadStrTime = uploadStrTime;
	}

	public Date getUploadEndTime() {
		return uploadEndTime;
	}

	public void setUploadEndTime(Date uploadEndTime) {
		this.uploadEndTime = uploadEndTime;
	}

	public Date getCreateStrTime() {
		return createStrTime;
	}

	public void setCreateStrTime(Date createStrTime) {
		this.createStrTime = createStrTime;
	}

	public Date getCreateEndTime() {
		return createEndTime;
	}

	public void setCreateEndTime(Date createEndTime) {
		this.createEndTime = createEndTime;
	}
}
