package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmStamp;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmPoliceDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmRelationDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmStampDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.CaseRelationDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmPolice;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;
import com.jsdz.digitalevidence.alarm.alarm110.model.CaseAlarmRelation;
import com.jsdz.digitalevidence.alarm.alarm110.model.CaseRelation;
import com.jsdz.digitalevidence.alarm.alarm110.service.CaseRelevanceService;
import com.jsdz.digitalevidence.cache.utils.SysSource;
import com.jsdz.digitalevidence.document.dao.CaseDao;
import com.jsdz.digitalevidence.document.model.Case;


/**
 * 案件和警情关联，从而达到案件和视频关联
 * <AUTHOR>
 *
 */
@Service("CaseRelevanceServiceImpl")
public class CaseRelevanceServiceImpl implements CaseRelevanceService{
	
	private final Logger logger = Logger.getLogger(this.getClass());
	
	@Autowired
	private CaseDao caseDao;
	
	@Autowired
	private AlarmStampDao alarmStampDao;
	
	@Autowired
	private AlarmPoliceDao alarmPoliceDao;
	
	@Autowired
	private AlarmRelationDao alarmRelationDao;
	
	@Autowired
	private CaseRelationDao caseRelationDao;
	
	//案件和视频自动关联
	public void caseBouAlarBack() {
		
//		System.out.println("====================案件开始运行了");
		
		// 保存的条件
		String stamp = " from AlarmStamp  ala order by ala.stampTime asc  ";
		AlarmStamp alarmStamp01 = alarmStampDao.findAlarmStamp(stamp);
		
		if(alarmStamp01.getCasId() == null){
			alarmStamp01.setCasId(1l);
		}
		// 按条件查询案件信息
		String queryStr01 = " from Case ca left join fetch ca.procOrg "
					+ " where ca.id > :casId order by ca.procTime asc ";
		String[] paramNames01 = new String[]{"casId"};
		Object[] values01 = new Object[]{alarmStamp01.getCasId()};
		List<Case> cases = caseDao.findDocumentsByCondition(queryStr01,paramNames01,values01);
			
		// 查询带条件的警情信息
		String queryStr02 = " from AlarmPolice ap left join fetch ap.alarmInfo ai "
		 			  + " left join fetch ap.police "
		 			  + " left join fetch ai.reveiceOrg "
		 			  + "where  ai.id > :alaId order by ai.alarmTime asc " ;
		String[] paramNames02 = new String[]{"alaId"};
		Object[] values02 = new Object[]{alarmStamp01.getAlaId()};
		List<AlarmPolice> alarmPolices = alarmPoliceDao.findAlarmPolicesByCondition(queryStr02, paramNames02, values02);
	
		List<CaseAlarmRelation> lists = new ArrayList<>();
		CaseAlarmRelation caseAlarmRelation = null ;
		for (AlarmPolice alarmPolice : alarmPolices) {
			for (Case caseInfo : cases) {
				if(alarmPolice.getPolice() !=null && caseInfo.getWorkNumber1() != null && caseInfo.getWorkNumber2() != null){
					
//					System.out.println("到达了第1步=========");
					// 判断警情信息的出警人工号是否和案件信息中的出警人工号是否相等
					if(alarmPolice.getPolice().getWorkNumber().equals(caseInfo.getWorkNumber1()) || alarmPolice.getPolice().getWorkNumber().equals(caseInfo.getWorkNumber2())){
//						System.out.println("到达了第2步=========");
						// 判断警情信息中的单位代码和案件信息中的
						//if(alarmPolice.getAlarmInfo().getReveiceOrg().getOrgCode().equals(caseInfo.getProcOrg().getOrgCode())){
//							System.out.println("到达了第3步=========");
							// 比较警情中的报警时间和案件信息中的时间不能相差一个小时
							Date alarmTime = alarmPolice.getAlarmInfo().getAlarmTime();
							Date caseTime = caseInfo.getProcTime();
							long nd = 1000 * 24 * 60 * 60;
						    long nh = 1000 * 60 * 60;
						    long hour =(alarmTime.getTime() - caseTime.getTime()) % nd / nh;
						    long times = Math.abs(hour);
						    if(times <= 1){
//						    	System.out.println("警情id为"+alarmPolice.getAlarmInfo().getId());
//						    	System.out.println("案件id为"+caseInfo.getId());
						    	caseAlarmRelation = new CaseAlarmRelation();
						    	caseAlarmRelation.setCas(caseInfo);
						    	caseAlarmRelation.setAlas(alarmPolice.getAlarmInfo());
						    }else{
						    	break;
						    }
						//}
					}
				}
				
			}
			
			// 保存警情和案件的id
			if(caseAlarmRelation != null){
				
				// 设置案件信息中与视频的已关联
				lists.add(caseAlarmRelation);
				caseAlarmRelation = null;
			}
		}
		
		CaseRelation caseRelation = null;
		for (CaseAlarmRelation casAlRel : lists) {
			
			// 从t_alarm_relation找出关联警情的视频信息
			String queryStr03 = " from AlarmRelation ar left join fetch ar.alarmInfo al "
								+ "	left join fetch ar.mediaInfo  me "
								+ " where al.id = :alarmIds ";
			String[] paramNames03 = new String[]{"alarmIds"};
			Object[] values03 = new Object[]{casAlRel.getAlas().getId()};
			List<AlarmRelation> alarmRelations = alarmRelationDao.findAlarmRelationsByCondition(queryStr03, paramNames03, values03);
			for (AlarmRelation alarmRela: alarmRelations) {
				
				// 更新案件信息中与视频的已关联
				casAlRel.getCas().setIsRelation(1);
				caseDao.update(casAlRel.getCas());
				
				caseRelation = new CaseRelation();
				caseRelation.setCas(casAlRel.getCas());
				caseRelation.setMediaInfo(alarmRela.getMediaInfo());
				caseRelation.setRelationTime(new Date());
				CaseRelation mutipleAlarmRelation = caseRelationDao.findMutipleAlarmRelation(caseRelation);
				
				// 数据表t_case_relation中有重复数据
				if(mutipleAlarmRelation != null){
					// 删除重复数据
					caseRelationDao.deleteCaseRelation(mutipleAlarmRelation);
				}
				//向数据表中t_case_relation新增数据
				caseRelationDao.addCaseRelation(caseRelation);
				caseRelation = null;
			}
		}
		
		// 查出案件信息中的最大id值
		String queryStr04 = " SELECT MAX(id) from Case " ;
		Long maxCasId = caseDao.finaMaxId(queryStr04);
		
		alarmStamp01.setCasId(maxCasId);
		alarmStampDao.updateStamp(alarmStamp01);
	}
	
	// 获取到锁执行目标方法
	public void timingMethod(){
		try {
			
			 String identifier = UUID.randomUUID().toString();
			 // 获取锁值
			 Object attribute01 = SysSource.getInstance().get("CaseRelevanceServiceImpl_CaseBouAlar_XYZ");
			 
			 if(attribute01 == null){
				 // 设置锁值
				 SysSource.getInstance().set("CaseRelevanceServiceImpl_CaseBouAlar_XYZ", identifier,3l);
			 }
			 Object attribute02 = SysSource.getInstance().get("CaseRelevanceServiceImpl_CaseBouAlar_XYZ");
			 if(identifier.equals(attribute02)){
				// 执行目标方法
				// caseBouAlar();
				//删除锁
				 SysSource.getInstance().remove("CaseRelevanceServiceImpl_CaseBouAlar_XYZ");
			 }
		} catch (Exception e) {
			
			//删除锁
			SysSource.getInstance().remove("CaseRelevanceServiceImpl_CaseBouAlar_XYZ");
			logger.error("*** 错误：" + e);
		}
	}

}
