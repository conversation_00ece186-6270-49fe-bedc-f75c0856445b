package com.jsdz.admin.org.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.Entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 
 * @类名: Region
 * @说明: 区域类
 *
 * @author: kenny
 * @Date	2017年4月24日下午4:07:32
 * 修改记录：
 *
 * @see
 */
 //@Entity
 //@Table(name="T_region")
public class Region implements Serializable {
	private Long id;
	private String regionName;//行政区域名称
	//@JsonIgnore
	private RegionType regionType;//类型(省(直辖市)/市/县(区)/镇(乡));
	//@JsonIgnore
	private Region parentRegion; //上级区域
	@JsonIgnore
	private Set<Region> subRegion = new HashSet<Region>(); //下级区域
	private Date createTime; //创建日期
	
	public Region(){
		super();
	}
	public Region(String regionName,RegionType regionType,Region parentRegion,Date createTime){
		this.regionName=regionName;
		//this.regionType=regionType;
		this.parentRegion=parentRegion;
		this.createTime=createTime;
	}
	
	 //@Id  
	 //@GeneratedValue(strategy = GenerationType.IDENTITY)   //auto  
	    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	

	public String getRegionName() {
		return regionName;
	}
	public void setRegionName(String regionName) {
		this.regionName = regionName;
	}
	public void regionType(String regionName) {
		this.regionName = regionName;
	}
	
	public RegionType getRegionType() {
		return regionType;
	}
	public void setRegionType(RegionType regionType) {
		this.regionType = regionType;
	}
	
	//@ManyToOne  
	//@JoinColumn(name="_parentId")
	
	public Region getParentRegion() {
		return parentRegion;
	}
	public void setParentRegion(Region parentRegion) {
		this.parentRegion = parentRegion;
	}
		
	
	//@OneToMany(mappedBy="parent",cascade=CascadeType.ALL,fetch=FetchType.EAGER)  
	public Set<Region> getSubRegion() {
		return subRegion;
	}
	public void setSubRegion(Set<Region> subRegion) {
		this.subRegion = subRegion;
	}
	
	public Date getCreateTime() {
		return createTime;
	}
	
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}	

	@Override
	public String toString() {
		return "Case [ID=" + id + ", regionName=" + regionName + ", regionType=" + "fds" + ", parentRegion="
				+ "11111" + ", createTime=" + createTime + "]";
	}	
}
