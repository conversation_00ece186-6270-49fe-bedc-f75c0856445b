package com.jsdz.digitalevidence.assessment.test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlert;
import com.jsdz.digitalevidence.assessment.model.alert.rule.ShootingAssessmentAYQPSRule;
import com.jsdz.digitalevidence.assessment.model.alert.rule.ShootingAssessmentAngleRule;
import com.jsdz.digitalevidence.assessment.model.alert.rule.ShootingAssessmentAssessingRateRule;
import com.jsdz.digitalevidence.assessment.model.alert.rule.ShootingAssessmentContentRule;
import com.jsdz.digitalevidence.assessment.model.alert.rule.ShootingAssessmentNoWFWJRule;
import com.jsdz.digitalevidence.assessment.model.alert.rule.ShootingAssessmentResolutionRule;
import com.jsdz.digitalevidence.assessment.model.alert.rule.ShootingAssessmentTotalDocsRule;
import com.jsdz.digitalevidence.assessment.model.alert.rule.ShootingAssessmentWMGFRule;
import com.jsdz.digitalevidence.assessment.model.alert.rule.ShootingAssessmentYYYYRule;
import com.jsdz.digitalevidence.assessment.service.AssessmentService;
import com.jsdz.ruleengine.MapWrapper;
import com.jsdz.ruleengine.NameValueReferableMap;
import com.jsdz.ruleengine.Result;
import com.jsdz.ruleengine.converter.JavaType;
import com.jsdz.ruleengine.dao.ChainRuleTopologyDao;
import com.jsdz.ruleengine.dao.NodeDao;
import com.jsdz.ruleengine.dao.ParamDao;
import com.jsdz.ruleengine.model.RuleBook;
import com.jsdz.ruleengine.model.param.RuleParam;
import com.jsdz.ruleengine.model.topo.Node;
import com.jsdz.ruleengine.model.topo.chain.ChainRuleTopology;
import com.jsdz.ruleengine.service.RuleService;
import com.jsdz.utils.BeanUtils;

/**
 * @类名: AlertRuleTopologyTest
 * @说明: 考核预警规则构建测试
 *
 * <AUTHOR>
 * @Date	 2017年9月7日 上午10:36:44
 * 修改记录：
 *
 * @see 	 
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { 
		"/testApplicationContext-common.xml",
		"/testApplicationContext-cycle.xml",
		"/testApplicationContext-dynsql.xml",
		"/testApplicationContext-scheduler.xml",
		"/testApplicationContext-reportengine.xml",
		"/testApplicationContext-documenttype.xml",
		"/testApplicationContext-storage.xml"
									
									})
public class AlertRuleTopologyTest {

	private Logger log = LoggerFactory.getLogger(this.getClass());

	@Autowired
    private NodeDao nodeDao;
	@Autowired
	private ParamDao paramDao;
	@Autowired
	private ChainRuleTopologyDao topoDao;
	@Autowired
	private RuleService ruleService;
	@Autowired
	private AssessmentService assessmentService;
    
	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
		
	}
	
	// 创建考核预警规则拓扑
	@Test
	@Transactional
	public void testCreatTopology() {
		//
		ChainRuleTopology topo = new ChainRuleTopology();
		topo.setKey("assessmentAlert");
		// 
		Node n1 = newTotalDocNode();
		Node n2 = newAssessRate();
		Node n3 = newAYQPS();
		Node n4 = newNoWFWJ();
		Node n5 = newYYYY();
		Node n6 = newWMGF();
		//
		nodeDao.saveOrUpdate(n1);
		nodeDao.saveOrUpdate(n2);
		nodeDao.saveOrUpdate(n3);
		nodeDao.saveOrUpdate(n4);
		nodeDao.saveOrUpdate(n5);
		nodeDao.saveOrUpdate(n6);
		//
		topo.setRules(Arrays.asList(n1, n2, n3, n4, n5, n6));
		//
		topoDao.saveOrUpdate(topo);
	}
	
	// 创建周考核预警规则拓扑
	@Test
	@Transactional
	public void testCreatWeeklyAlertTopology() {
		//
		ChainRuleTopology topo = new ChainRuleTopology();
		topo.setKey("assessmentWeeklyAlert");
		//
		Node n2 = newAssessRate();
		Node n3 = newAYQPS();
		Node n4 = newNoWFWJ();
		Node n5 = newYYYY();
		Node n6 = newWMGF();
		//
		nodeDao.saveOrUpdate(n2);
		nodeDao.saveOrUpdate(n3);
		nodeDao.saveOrUpdate(n4);
		nodeDao.saveOrUpdate(n5);
		nodeDao.saveOrUpdate(n6);
		//
		topo.setRules(Arrays.asList(n2, n3, n4, n5, n6));
		//
		topoDao.saveOrUpdate(topo);
	}
	
	// 增加考核规则
	@Test
	@Transactional
	public void testTopologyAddNode() {
		//
		ChainRuleTopology topo = topoDao.get("assessmentAlert");
		// 
		Node n1 = newAngle();
		//
		nodeDao.saveOrUpdate(n1);
		//
		topo.getRules().add(n1);
		//
		topoDao.saveOrUpdate(topo);
	}
	
	// 预警规则测试
	// 上传总数预警，考核率，
	// 拍摄质量  
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Test
	@Transactional
	public void testExecAlertRule() throws Exception {
		
		ShootingAssessmentReport report = assessmentService.getShootingAccessmentReport(4L);
		//
		NameValueReferableMap<ShootingAssessmentSummary> facts = new MapWrapper<ShootingAssessmentSummary>();
		ShootingAssessmentSummary sum = report.getSumOfShootingAssessment();
		facts.setValue("sum", sum);
		//
		RuleBook ruleBook = ruleService.run("assessmentAlert", facts);
		//
		Result r = (Result)ruleBook.getResult().get();
		List<AssessmentAlert> alerts = (List<AssessmentAlert>)r.getValue();
		//
		BeanUtils.printBean(alerts);
			
	}
	
	/********************/
	// 获取拓扑
	@Test
	@Transactional
	public void testGetTopo() {
		//
		ChainRuleTopology topo = topoDao.get("assessmentAlert");
		//
		List<Node> nodes = topo.getRules();
		for(Node node : nodes) {
			System.out.println(node.getName()+","+node.getDescription());
		}
	}
	
	@Test
	public void testGetRuleParam() {
		//
		List<RuleParam> ruleParams = ruleService.getParamsOfRule(3L);
		//
		for(RuleParam param : ruleParams) {
			System.out.println(param.getName());
			System.out.println(param.getValue().toString());
			System.out.println(param.getValue().getClass().getName());
		}
	}
	
	@Test
	@Transactional
	public void testParam() {
		RuleParam param = paramDao.get(12L);
		param.setValue((float)1/3);
		paramDao.saveOrUpdate(param);
	}
	
	// 上传文档总数
	public Node newTotalDocNode() {
		//
		Node node = new Node();
		node.setRuleClazz(ShootingAssessmentTotalDocsRule.class);
		// 参数
		// 总数阀值
		RuleParam p1 = new RuleParam();
		p1.setName("threshold");
		p1.setValue(3);
		p1.setType(JavaType.INT);
		p1.setComments("文档上传总数阀值");
		//
		Map<String, RuleParam> params = new HashMap<String, RuleParam>();
		params.put("threshold", p1);
		//
		node.setParams(params);
		//
		return node;
		
	}
	
	// 考核率
	public Node newAssessRate() {
		//
		Node node = new Node();
		node.setRuleClazz(ShootingAssessmentAssessingRateRule.class);
		// 参数
		// 总数阀值
		RuleParam p1 = new RuleParam();
		p1.setName("threshold");
		float threshold = (float)1/3;
		p1.setValue(threshold);
		p1.setType(JavaType.FLOATPRIMITIVE);
		p1.setComments("考核率阀值");
		//
		Map<String, RuleParam> params = new HashMap<String, RuleParam>();
		params.put("threshold", p1);
		//
		node.setParams(params);
		//
		return node;
		
	}
	
	// 按要求拍摄百分比
	public Node newAYQPS() {
		//
		Node node = new Node();
		node.setRuleClazz(ShootingAssessmentAYQPSRule.class);
		// 参数
		// 总数阀值
		RuleParam p1 = new RuleParam();
		p1.setName("ayqpsPercent");
		float threshold = 0.9f;
		p1.setValue(threshold);
		p1.setType(JavaType.FLOATPRIMITIVE);
		p1.setComments("按要求拍摄百分比阀值");
		//
		Map<String, RuleParam> params = new HashMap<String, RuleParam>();
		params.put("ayqpsPercent", p1);
		//
		node.setParams(params);
		//
		return node;
		
	}
	
	// 没有违规违纪
	public Node newNoWFWJ() {
		//
		Node node = new Node();
		node.setRuleClazz(ShootingAssessmentNoWFWJRule.class);
		// 参数
		// 总数阀值
		RuleParam p1 = new RuleParam();
		p1.setName("noWFWJPercent");
		float threshold = 1.0f;
		p1.setValue(threshold);
		p1.setType(JavaType.FLOATPRIMITIVE);
		p1.setComments("没有违规违纪百分比阀值");
		//
		Map<String, RuleParam> params = new HashMap<String, RuleParam>();
		params.put("noWFWJPercent", p1);
		//
		node.setParams(params);
		//
		return node;
		
	}
	
	// 文明规范
	public Node newWMGF() {
		//
		Node node = new Node();
		node.setRuleClazz(ShootingAssessmentWMGFRule.class);
		// 参数
		// 总数阀值
		RuleParam p1 = new RuleParam();
		p1.setName("wmgfPercent");
		float threshold = 0.9f;
		p1.setValue(threshold);
		p1.setType(JavaType.FLOATPRIMITIVE);
		p1.setComments("文明规范百分比阀值");
		//
		Map<String, RuleParam> params = new HashMap<String, RuleParam>();
		params.put("wmgfPercent", p1);
		//
		node.setParams(params);
		//
		return node;
		
	}
	
	// 运用语言
	public Node newYYYY() {
		//
		Node node = new Node();
		node.setRuleClazz(ShootingAssessmentYYYYRule.class);
		// 参数
		// 总数阀值
		RuleParam p1 = new RuleParam();
		p1.setName("yyyyPercent");
		float threshold = 0.9f;
		p1.setValue(threshold);
		p1.setType(JavaType.FLOATPRIMITIVE);
		p1.setComments("正确运用语言百分比阀值");
		//
		Map<String, RuleParam> params = new HashMap<String, RuleParam>();
		params.put("yyyyPercent", p1);
		//
		node.setParams(params);
		//
		return node;
		
	}
	
	// 拍摄分辨率规则
	public Node newResolution() {
		//
		Node node = new Node();
		node.setRuleClazz(ShootingAssessmentResolutionRule.class);
		// 参数
		// 总数阀值
		RuleParam p1 = new RuleParam();
		p1.setName("psResolutionHighPercent");
		float threshold = 0.75f;
		p1.setValue(threshold);
		//
		Map<String, RuleParam> params = new HashMap<String, RuleParam>();
		params.put("psResolutionHighPercent", p1);
		//
		node.setParams(params);
		//
		return node;
		
	}
	
	// 拍摄要素准确性
	public Node newContent() {
		//
		Node node = new Node();
		node.setRuleClazz(ShootingAssessmentContentRule.class);
		// 参数
		// 阀值
		RuleParam p1 = new RuleParam();
		p1.setName("psContentAcuratePercent");
		float threshold = 0.75f;
		p1.setValue(threshold);
		//
		Map<String, RuleParam> params = new HashMap<String, RuleParam>();
		params.put("psContentAcuratePercent", p1);
		//
		node.setParams(params);
		//
		return node;
		
	}
	
	// 拍摄角度准确性
	public Node newAngle() {
		//
		Node node = new Node();
		node.setRuleClazz(ShootingAssessmentAngleRule.class);
		// 参数
		// 阀值
		RuleParam p1 = new RuleParam();
		p1.setName("psAngleAcuratePercent");
		float threshold = 0.75f;
		p1.setValue(threshold);
		//
		Map<String, RuleParam> params = new HashMap<String, RuleParam>();
		params.put("psAngleAcuratePercent", p1);
		//
		node.setParams(params);
		//
		return node;
		
	}
	
	// 删除拓扑
	@Test
	@Transactional
	public void testTopu() {
		ChainRuleTopology topu = topoDao.get("assessmentWeeklyAlert");
		//
		this.topoDao.delete(topu);
	
	}
	
	// 删除节点
	@Test
	@Transactional
	public void testDelNode() {
		//
		Node node = nodeDao.get(4L);
		nodeDao.delete(node);
	}
	
	// 浮点数
	@Test
	@Transactional
	public void testFloatDiv() {
		//
		float f = (float)1/3;
		//
		System.out.println(f);
	}
	
}
