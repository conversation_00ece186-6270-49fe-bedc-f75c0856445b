package com.jsdz.digitalevidence.task.service.impl;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.cache.utils.JsonUtil;
import com.jsdz.digitalevidence.cache.utils.ResultUtil;
import com.jsdz.digitalevidence.cache.utils.SysSource;
import com.jsdz.digitalevidence.graphensemble.bean.HeartBeatBean;
import com.jsdz.digitalevidence.graphensemble.utils.CacheUtils;
import com.jsdz.digitalevidence.site.constant.*;
import com.jsdz.digitalevidence.task.bean.*;
import com.jsdz.digitalevidence.task.constant.*;
import com.jsdz.digitalevidence.task.dao.mapper.TTaskDocMapper;
import com.jsdz.digitalevidence.task.dao.mapper.TTaskMapper;
import com.jsdz.digitalevidence.task.dao.mapper.TTaskProcessDocMapper;
import com.jsdz.digitalevidence.task.dao.mapper.TTaskProcessMapper;
import com.jsdz.digitalevidence.site.model.*;
import com.jsdz.digitalevidence.site.service.RecorderService;
import com.jsdz.digitalevidence.site.service.SiteService;
import com.jsdz.digitalevidence.site.util.HttpUtils;
import com.jsdz.digitalevidence.task.model.*;
import com.jsdz.digitalevidence.task.service.TTaskService;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;

/**
 * 任务管理表(TTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-25 16:37:32
 */
@Service("tTaskService")
public class TTaskServiceImpl implements TTaskService {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private TTaskMapper taskMapper;

    @Resource
    private SiteService siteService;

    @Resource
    private RecorderService recorderService;

    @Resource
    private TTaskDocMapper taskDocMapper;

    @Resource
    private TTaskProcessMapper tTaskProcessMapper;

    @Resource
    private TTaskProcessDocMapper processDocMapper;

    @Resource(name = "taskExecutor")
    private Executor executor;

    @Value("${ftp.webPath:/upload/web}")
    private String webPath;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AjaxResult add(TTask task, MultipartFile[] files) {
        // 新增任务
//        if(!StringUtils.hasText(task.getTitle())) {
//            return ResultUtil.error("任务标题不能为空！");
//        }
//        if(!StringUtils.hasText(task.getContent())) {
//            return ResultUtil.error("任务内容不能为空！");
//        }
        if(task.getCate() == null) {
            return ResultUtil.error("任务类别不能为空！");
        } else {
            if(!TaskCateMenu.check(task.getCate())) {
                return ResultUtil.error("不支持该类别！");
            }
        }
        taskMapper.insert(task);
        // 附件上传
        Site site = getStorage();
        String upUrl = site.getHttp() + webPath;
//        String upUrl = "http://localhost:8022/upload/web";

        ArrayList<TTaskDoc> taskDocs = new ArrayList<>();
        Map<String, Object> param = new HashMap<>();
        param.put("type", BusinessType.TASK_PUB_TYPE);
        if(files != null && files.length > 0) {
            for (MultipartFile file : files) {
                if(file != null) {
                    try {
                        String result = HttpUtils.postFile(upUrl, param, file);
                        AjaxResult ajaxResult = JsonUtil.fromJson(result, AjaxResult.class);
                        taskDocs.add(new TTaskDoc(task.getId(), site.getId(), ajaxResult.getMsg()));
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            if(!CollectionUtils.isEmpty(taskDocs)) {
                // 新增任务跟附件关联数据
                taskDocMapper.insertBatch(taskDocs);
            }
        }

        return ResultUtil.ok("新增成功！");
    }

    /**
     * 上传文件至存储
     * @param file 文件
     * @param type 类型
     * @return url
     */
    public String uploadFileToStorage(Site site, MultipartFile file, int type) {

        String upUrl = site.getHttp() + webPath;
//        String upUrl = "http://localhost:8022/upload/web";

        ArrayList<TTaskDoc> taskDocs = new ArrayList<>();
        Map<String, Object> param = new HashMap<>();
        param.put("type", type);
        if(file != null) {
            try {
                String result = HttpUtils.postFile(upUrl, param, file);
                AjaxResult ajaxResult = JsonUtil.fromJson(result, AjaxResult.class);
                if(ajaxResult.isSuccess()) {
                    return ajaxResult.getMsg();
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    /**
     * 获取本地存储
     * @return
     */
    private Site getStorage() {
        String hql = " FROM Site s WHERE s.siteType = 2";
        List<Site> sites = siteService.findSitesByParam(hql, new String[]{}, new Object[]{});
        if (CollectionUtils.isEmpty(sites)){
            throw new RuntimeException("没有注册存储服务器");
        }

        Site site = sites.get(0);
        if (org.apache.commons.lang3.StringUtils.isEmpty(site.getHttp())){
            throw new RuntimeException("存储服务器没有设置HTTP地址");
        }

        return site;
    }

    @Override
    public Page<TTask> pageQuery(TaskPageBean pageBean) {
        Page<TTask> result = new Page<>();
        try {
            List<TTask> rows = taskMapper.pageQuery(pageBean);
            int total = taskMapper.totalCount(pageBean);
            result.setTotal(total);
            result.setRows(rows);
            result.setOffset(pageBean.getPage().getOffset());
            result.setPageSize(pageBean.getPage().getPageSize());
        } catch (Exception e) {
            logger.error("任务管理分页查询错误", e);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult send(Long[] taskIds, Integer sendType, Long[] devIds, PerLvlBean perLvlBean) {
        if(taskIds ==null || taskIds.length == 0) {
            return ResultUtil.error("任务id不能为空！");
        }
        List<TaskRecorderBean> recorders = null;
        // 1、查询待发送执法仪
        // 发送指定人员
        if(TaskSendType.SEND_SOME == sendType) {
            // 根据执法仪id查找执法仪
            if(devIds != null && devIds.length > 0) {
                recorders = taskMapper.queryRecorders(devIds, null);
            }
        }
        // 发送所有人
        else if (TaskSendType.SEND_ALL == sendType) {
            recorders = taskMapper.queryRecorders(null, perLvlBean);
        }

        if(CollectionUtils.isEmpty(recorders)) {
            return ResultUtil.error("没有符合的执法仪！");
        }
        // 2、查询任务
        List<TTask> tasks = queryByIds(taskIds);
        if(CollectionUtils.isEmpty(tasks)) {
            return ResultUtil.error("任务不存在！");
        }
        // 分批异步保存执法仪任务数据
        int step = 500;
        List<CompletableFuture<Integer>> completableFutures = new ArrayList<>();
        List<TTaskProcess> taskProcessList = new ArrayList<>();
        for(TTask task : tasks) {
            Long taskId = task.getId();
//            SysSource.getInstance().hset(Consts.TASK_SEND_CHACHE_KEY, String.valueOf(taskId), task);
            for(TaskRecorderBean r : recorders) {
                TTaskProcess tTaskProcess = new TTaskProcess(task, r, perLvlBean == null ? 0L : perLvlBean.getOperatorId());
                // 3、 发送任务到执法仪
                sendTask(tTaskProcess, task, r.getCode());
                taskProcessList.add(tTaskProcess);
            }
        }
        // 保存/更新发送记录
        for (int i = 0; i < taskProcessList.size(); i += step) {
            List<TTaskProcess> processList = taskProcessList.subList(i, Math.min(i + step, taskProcessList.size()));
            completableFutures.add(CompletableFuture.supplyAsync(() -> {
                return tTaskProcessMapper.insertOrUpdateBatch(processList);
            }, executor));
        }
        completableFutures.forEach(c -> {
            try {
                c.get();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } catch (ExecutionException e) {
                throw new RuntimeException(e);
            }
        });
        return ResultUtil.ok("发送成功！");
    }

    /**
     * 根据id查找任务列表
     * @param taskIds
     * @return
     */
    private List<TTask> queryByIds(Long[] taskIds) {
        return taskMapper.queryByIds(taskIds);
    }

    /**
     * 接收到任务后给平台响应
     * @param taskId 任务id
     * @param code 执法仪编号
     * @return
     */
    @Override
    public AjaxResult received(Long taskId, String code) {
        // 根据执法仪编号查询执法仪
        /*Recorder recorder = recorderService.findRecorderByCode(code);
        if(recorder == null) {
          ResultUtil.error("执法仪不存在！");
        }*/
        // 根据taskId查找任务
        TTaskProcess taskProcess = tTaskProcessMapper.getTaskInfo(taskId, code);
        if(taskProcess == null) {
            return ResultUtil.error("任务不存在！");
        }
        // 修改任务状态
        if(TaskProcessStatus.WAIT == taskProcess.getStatus()) {
            TTaskProcess updateProcess = new TTaskProcess(taskProcess.getId(), TaskProcessStatus.RECEIVED);
            tTaskProcessMapper.update(updateProcess);
        }
        return ResultUtil.ok("更新成功！");
    }

    @Transactional
    @Override
    public AjaxResult feedback(TaskFeedbackDto dto) {
        if(dto.getTaskId() == null) {
            return ResultUtil.error("任务id不能为空！");
        }
        if(!StringUtils.hasText(dto.getCode())) {
            return ResultUtil.error("执法仪编号不能为空！");
        }
        /*if(!StringUtils.hasText(dto.getStorageNo())) {
            return ResultUtil.error("存储编号不能为空！");
        }*/
        List<String> fileNames = dto.getFileNames();
        String result = dto.getResult();
        if(!StringUtils.hasText(result) && CollectionUtils.isEmpty(fileNames)) {
            return ResultUtil.error("反馈内容或文件不能为空！");
        }
        // 根据存储编号获取存储id
        /*Long storageId = taskMapper.getStorageIdByCode(dto.getStorageNo());
        if(storageId == null) {
            return ResultUtil.error("存储未注册或不存在！");
        }*/
        // 设置任务反馈内容
        TTaskProcess taskProcess = tTaskProcessMapper.findTaskProcess(dto.getTaskId(), dto.getCode());
        if(taskProcess == null) {
            return ResultUtil.error("任务不存在！");
        }
        if(taskProcess.getStatus() == TaskProcessStatus.FILE) {
            return ResultUtil.error("任务已结束！");
        }
        taskProcess.setResult(result);
        taskProcess.setStatus(TaskProcessStatus.IN_REVIEW);
        tTaskProcessMapper.update(taskProcess);
        // 查询附件
        List<Long> docIds = taskMapper.getDocByName(fileNames);
        // 保存上传附件
        List<TTaskProcessDoc> processDocs = new ArrayList<>();
        if(!CollectionUtils.isEmpty(docIds)) {
            docIds.forEach(docId -> {
                if(docId != null) {
                    TTaskProcessDoc processDoc = new TTaskProcessDoc();
                    processDoc.setProcessId(taskProcess.getId());
//                    processDoc.setDocId(docId);
                    processDocs.add(processDoc);
                }
            });
        }
//        processDocMapper.insertOrUpdateBatch(processDocs);
        return ResultUtil.ok("操作成功！");
    }

    /**
     * 任务审核
     *
     * @param processId 任务分发id
     * @param isApprove 是否审核通过
     * @param advice    审核意见
     * @param operator  操作人员
     * @return
     */
    @Override
    public AjaxResult examine(Long processId, int isApprove, String advice, Operator operator) {
        TTaskProcess process = tTaskProcessMapper.queryById(processId);
        if (process == null) {
            return ResultUtil.error("任务不存在！");
        }
        if(process.getStatus() != TaskProcessStatus.IN_REVIEW) {
            return ResultUtil.error("该任务不在审核中！");
        }
        TTaskProcess taskProcess = new TTaskProcess();
        taskProcess.setId(processId);
        taskProcess.setAdvice(advice);
        taskProcess.setExamineBy(operator == null ? 0L : operator.getId());
        taskProcess.setStatus(isApprove == 0 ? TaskProcessStatus.REPULSE : TaskProcessStatus.FILE);
        tTaskProcessMapper.update(taskProcess);

        // 审核不通过重新发送任务
        if(isApprove == 0) {
            String code = tTaskProcessMapper.getRecorderByProcess(processId);
            process.setAdvice(taskProcess.getAdvice());
            process.setStatus(taskProcess.getStatus());
            sendTask(process, code);
        }

        return ResultUtil.error("操作成功！");
    }

    /**
     * 任务延期
     * @param processId 分发任务id
     * @param endIme    延期时间
     * @return
     */
    @Transactional
    @Override
    public AjaxResult delay(Long processId, Date endIme) {
        // 根据任务分发id查询
        TTaskProcess process = tTaskProcessMapper.queryById(processId);
        if (process == null) {
            return ResultUtil.error("任务不存在！");
        }
        if(process.getEndTime() != null && !process.getEndTime().before(endIme)) {
            return ResultUtil.error("延期后时间不能早于当前时间！");
        }
        if(TaskProcessStatus.IN_REVIEW == process.getStatus() || TaskProcessStatus.FILE == process.getStatus()) {
            return ResultUtil.error("该任务在审核中或已完成无法延期！");
        }
        // 查询执法仪信息
        Long deviceId = process.getDeviceId();
        Recorder recorder = recorderService.findRecorderById(deviceId);
        if (recorder == null) {
            return ResultUtil.error("任务对应设备不存在！");
        }
        // 修改结束时间
        TTaskProcess taskProcess = new TTaskProcess();
        taskProcess.setId(processId);
        taskProcess.setEndTime(endIme);
        taskProcess.setStatus(TaskProcessStatus.DELAY);
        tTaskProcessMapper.update(taskProcess);
        // 发送更新后任务至执法仪
        process.setEndTime(taskProcess.getEndTime());
        process.setStatus(taskProcess.getStatus());
        sendTask(process, recorder.getCode());
        // 任务延期
        return ResultUtil.ok("操作成功！");
    }

    /**
     * 查询任务后发送任务
     * @param taskProcess 任务分发表
     * @param code  执法仪编码
     */
    private void sendTask(TTaskProcess taskProcess, String code) {
        // 查询任务
        TTask task = taskMapper.queryById(taskProcess.getTaskId());
        if(task == null) {
            throw new RuntimeException("任务不存在！");
        }
        sendTask(taskProcess, task, code);
//        SysSource.getInstance().hset(Consts.TASK_SEND_CHACHE_KEY, String.valueOf(task.getId()), task);
    }

    /**
     * 发送任务
     * @param taskProcess 任务分发表
     * @param task 任务表
     * @param code 执法仪编号
     */
    private void sendTask(TTaskProcess taskProcess, TTask task, String code) {
        // 重新发布任务更新任务结束时间
        String lockKey = Consts.TASK_SEND_LOCK_PRE + code;
        SysSource.getInstance().tryLock(lockKey);
        try {
            // 缓存待发布执法仪编号及任务id
            Object taskListObj = SysSource.getInstance().hget(Consts.TASK_SEND_DEVICE_CHACHE_KEY, code);
            List<TaskApiVo> taskProcessList = new ArrayList<>();
            if (taskListObj != null) {
                taskProcessList = (List<TaskApiVo>)taskListObj;
            }
            TaskApiVo taskApiVo = new TaskApiVo(task, taskProcess);
            taskProcessList.add(taskApiVo);
            SysSource.getInstance().hset(Consts.TASK_SEND_DEVICE_CHACHE_KEY, code, taskProcessList);
        } catch (Exception e) {
            logger.error("任务发布错误："+code, e);
        } finally {
            SysSource.getInstance().unLock(lockKey);
        }
    }

    /**
     * 任务转交
     * @param processId 分发任务id
     * @param deviceId 设备id
     * @return
     */
    @Transactional
    @Override
    public AjaxResult transmit(Long processId, Long deviceId) {
        // 根据设备id查询设备及人员信息
        Recorder recorder = recorderService.findRecorderById(deviceId);
        if (recorder == null) {
            return ResultUtil.error("设备不存在！");
        }
        // 查询任务
        TTaskProcess tTaskProcess = tTaskProcessMapper.queryById(processId);
        if (tTaskProcess == null) {
            return ResultUtil.error("任务不存在！");
        }
        if(deviceId.equals(tTaskProcess.getDeviceId())) {
            return ResultUtil.error("不能转发相同人员！");
        }
        if(tTaskProcess.getStatus() == TaskProcessStatus.IN_REVIEW || tTaskProcess.getStatus() == TaskProcessStatus.FILE) {
            return ResultUtil.error("任务已提交，无法转交！");
        }
        // 任务转交
        TTaskProcess taskProcess = new TTaskProcess();
        taskProcess.setId(processId);
        taskProcess.setDeviceId(deviceId);
        taskProcess.setStatus(TaskProcessStatus.WAIT);
        if(recorder.getEmployees() != null) {
            taskProcess.setPoliceId(recorder.getEmployees().getId());
        }
        tTaskProcessMapper.update(taskProcess);

        // 重新发布任务更新任务结束时间
        tTaskProcess.setStatus(taskProcess.getStatus());
        sendTask(tTaskProcess, recorder.getCode());

        return ResultUtil.ok("操作成功！");
    }

    @Override
    public AjaxResult apiPage(TaskApiDto dto) {
        if (!StringUtils.hasText(dto.getCode())) {
            return ResultUtil.error("执法仪编号不能为空！");
        }
        if(dto.getPage() != null) {
            Page<TaskApiVo> result = new Page<>();
            List<TaskApiVo> rows = taskMapper.apiPage(dto);
            int total = taskMapper.apiPageCount(dto);
            result.setTotal(total);
            result.setRows(rows);
            result.setOffset(dto.getPage().getOffset());
            result.setPageSize(dto.getPage().getPageSize());
            return ResultUtil.ok(result);
        } else {
            return ResultUtil.ok(taskMapper.apiPage(dto));
        }
    }

    /**
     * 根据任务id获取任务详情
     * @param id
     * @return
     */
    @Override
    public AjaxResult getProcessInfo(Long id) {
        // 查询分发任务
        TTaskProcess taskProcess = tTaskProcessMapper.getProcessInfo(id);
        if (taskProcess == null) {
            return ResultUtil.error("任务不存在！");
        }
        // 查询任务
        TTask task = taskMapper.queryById(taskProcess.getTaskId());
        if(task == null) {
            return ResultUtil.error("任务不存在！");
        }
        return ResultUtil.ok(new TaskInfoVo(task, taskProcess));
    }

    @Override
    public AjaxResult getTaskInfo(Long taskId, String code) {
        // 查询分发任务
        TTaskProcess taskProcess = tTaskProcessMapper.getTaskInfo(taskId, code);
        if (taskProcess == null) {
            return ResultUtil.error("任务不存在！");
        }
        // 查询任务
        TTask task = taskMapper.queryById(taskId);
        if(task == null) {
            return ResultUtil.error("任务不存在！");
        }
        return ResultUtil.ok(new TaskInfoVo(task, taskProcess));
    }

    @Override
    public Page<TaskProcessVo> processPage(TaskProcessPageBean pageBean) {
        if(pageBean.getTaskId() == null) {
            throw new RuntimeException("任务id不能为空！");
        }
        Page<TaskProcessVo> result = new Page<>();
        List<TaskProcessVo> rows = taskMapper.processPage(pageBean);
        int total = taskMapper.processPageCount(pageBean);
        result.setTotal(total);
        result.setRows(rows);
        result.setOffset(pageBean.getPage().getOffset());
        result.setPageSize(pageBean.getPage().getPageSize());
        return result;
    }

    @Override
    public AjaxResult feedback(FeedbackDto dto) {
        Assert.notNull(dto.getCate());
        String param = dto.getParam();
        // 人脸抓拍
        if(BusinessCate.PHOTO_SHOT.equals(dto.getCate())) {
            // 小图查询时替换后缀生成
            if(param.endsWith(FaceShot.SMALL)) return ResultUtil.ok();
        }
        Assert.notNull(dto.getId());
        Assert.hasText(dto.getCode());
        Assert.hasText(param);
        // 根据任务id和设备号获取下发任务
        TTaskProcess taskProcess = tTaskProcessMapper.findTaskProcess(Long.valueOf(dto.getId()), dto.getCode());
        // 根据文件名获取文件
        Long docId = getDocIdByName(param, System.currentTimeMillis() + 1000);
        if(docId == null) return ResultUtil.error("文件不存在！");
        // 保存上传附件
        TTaskProcessDoc processDoc = new TTaskProcessDoc();
        processDoc.setProcessId(taskProcess.getId());
//        processDoc.setDocId(docId);
//        processDoc.setSceneMark(dto.getParam());
//        processDocMapper.insertOrUpdate(processDoc);
        return ResultUtil.ok("关联成功");
    }

    @Override
    public Page<TaskDocVo> taskDocPage(TaskPageBean pageBean) {
        Page<TaskDocVo> result = new Page<>();
        List<TaskDocVo> rows = taskMapper.taskDocPage(pageBean);
        int total = taskMapper.taskDocPageCount(pageBean);
        // 处理场景图访问路径
        if(!CollectionUtils.isEmpty(rows)) {
            rows.forEach(row -> {
                String url = row.getUrl();
                if (StringUtils.hasText(url)) {
                    int indexOfPoint = url.lastIndexOf(FaceShot.LARGE);
                    if(indexOfPoint != -1) {
                        String sceneMark = url.substring(0, indexOfPoint) + FaceShot.SMALL + url.substring(indexOfPoint+FaceShot.LARGE.length());
                        row.setSceneMark(sceneMark);
                    }
                }
            });
        }
        result.setTotal(total);
        result.setRows(rows);
        result.setOffset(pageBean.getPage().getOffset());
        result.setPageSize(pageBean.getPage().getPageSize());
        return result;
    }

    @Override
    public void deleteByIds(Long[] ids) {
        taskMapper.deleteByIds(ids);
    }

    /**
     * 待办已办分页查询
     * @param pageBean
     * @return
     */
    @Override
    public Page<AffairVo> affairPage(TaskPageBean pageBean) {
        Page<AffairVo> result = new Page<>();
        try {
            List<AffairVo> rows = taskMapper.affairList(pageBean);
            int total = taskMapper.affairCount(pageBean);
            result.setTotal(total);
            result.setRows(rows);
            result.setOffset(pageBean.getPage().getOffset());
            result.setPageSize(pageBean.getPage().getPageSize());
        } catch (Exception e) {
            logger.error("事件待办已办分页查询错误", e);
        }
        return result;
    }

    /**
     * 查询事件详情
     *
     * @param id      事件id
     * @param type    事件类型
     * @param permLvl 用户权限
     * @return
     */
    @Override
    public AffairVo affairInfo(Long id, Integer type, PerLvlBean permLvl) {
        AffairVo affairVo = taskMapper.affairInfo(id, type, permLvl);
        if(affairVo == null) return null;
        List<AffairRecorderBean> recorderBeans = affairVo.getRecorderBeans();
        if(!CollectionUtils.isEmpty(recorderBeans)) {
            recorderBeans.forEach(bean -> {
                HeartBeatBean hb = CacheUtils.getHeartbeat(bean.getCode());
                if (hb != null) {
                    BeanUtils.copyProperties(hb, bean);
                }
            });
        }
        return affairVo;
    }

    @Override
    public AjaxResult feedbackWithFile(MultipartFile file, MultipartFile sceneFile, FeedbackDto dto) {

        // 检查参数
        dto.check();

        // 上传文件
        Site site = getStorage();
        String url = uploadFileToStorage(site, file, BusinessType.TASK_PUB_DOC_TYPE);
        String sceneFileUrl = uploadFileToStorage(site, sceneFile, BusinessType.TASK_PUB_DOC_TYPE);

        // 根据任务id和设备号获取下发任务
        TTaskProcess taskProcess = tTaskProcessMapper.findTaskProcess(Long.valueOf(dto.getId()), dto.getCode());
        if(taskProcess == null) {
            return ResultUtil.error("任务不存在");
        }

        // 保存上传附件
        TTaskProcessDoc processDoc = new TTaskProcessDoc();
        processDoc.setProcessId(taskProcess.getId());
        processDoc.setEmployeesId(taskProcess.getPoliceId());
        processDoc.setRecorderId(taskProcess.getDeviceId());
        processDoc.setStorageId(site.getId());
        processDoc.setDocUri(url);
        processDoc.setSceneMark(sceneFileUrl);
        processDoc.setCaptureTime(dto.getCaptureTime());
        processDoc.setLongitude(dto.getLongitude());
        processDoc.setLatitude(dto.getLatitude());
        processDocMapper.insertNew(processDoc);
        return ResultUtil.ok("人脸数据上传成功！");
    }

    /**
     * 获取任务详情
     * @param taskId
     * @return
     */
    private TTask queryById(Long taskId) {
        return taskMapper.queryById(taskId);
    }

    /**
     * 根据文件名获取文件id
     * @param docName
     * @return
     */
    private Long getDocIdByName(String docName, long delayTime) {
        Long docId = taskMapper.getDocIdByName(docName);
        if(System.currentTimeMillis() > delayTime) return docId;
        if(docId == null) {
            docId = getDocIdByName(docName, delayTime);
        }
        return docId;
    }

}

