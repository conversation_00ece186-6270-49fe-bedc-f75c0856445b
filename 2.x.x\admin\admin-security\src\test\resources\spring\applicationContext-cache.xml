<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
                        http://www.springframework.org/schema/context
                        http://www.springframework.org/schema/context/spring-context-3.1.xsd
                        http://www.springframework.org/schema/tx
                        http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
                        http://www.springframework.org/schema/aop 
                        http://www.springframework.org/schema/aop/spring-aop-3.1.xsd" default-autowire="byName">


	 <!-- 取得ApplicationContext 对象-->
	 <bean id="springContextUtil" class="com.jsdz.digitalevidence.cache.redis.SpringContextUtil"></bean>

     <bean id="redisCache" class="com.jsdz.digitalevidence.cache.redis.RedisCache"></bean>

	
	<!-- 加载properties文件 -->
 <!--    <bean id="redisconfigProperties"
        class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:config.properties</value>
                <value>classpath:redis.properties</value>
            </list>
        </property>
    </bean>
 --> 
 	<!-- 全局配置属性 -->
	<bean id="propertyConfigurer" class="com.jsdz.core.Env">
		<property name="locations">
			<list>
				<value>classpath:config.properties</value>
				<value>classpath:redis.properties</value>
			</list>
		</property>
	</bean>
  
    <!-- Session 共享使用 -->
	<!-- RedisHttpSessionConfiguration -->
<!--     
	<bean
        class="org.springframework.session.data.redis.config.annotation.web.http.RedisHttpSessionConfiguration">
        <property name="maxInactiveIntervalInSeconds" value="${redis.session.timeout}" />    
    </bean>    
 -->
     <!-- session过期时间,单位是秒-->
    
    <!-- redis 连接池 -->
	<bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
	    <!--连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true --> 
		<property name="blockWhenExhausted" value="true" />
    	<!-- 是否启用pool的jmx管理功能, 默认true -->
    	<property name="jmxEnabled" value="true" />
     	<!--jedis调用returnObject方法时，是否进行有效检查  -->
    	<property name="testOnReturn" value="true" />
    	<!--是否启用后进先出, 默认true  -->
    	<property name="lifo" value="true" />
    	<!-- 最大空闲连接数 -->
    	<property name="maxIdle" value="${redis.maxIdle}" />
    	<!-- 最大连接数 -->
    	<property name="maxTotal" value="${redis.maxTotal}" />
    	<!-- 获取连接时的最大等待毫秒数(如果设置为阻塞时BlockWhenExhausted),如果超时就抛异常, 小于零:阻塞不确定的时间,  默认-1 -->
    	<property name="maxWaitMillis" value="-1" />
    	<!-- 逐出连接的最小空闲时间 默认1800000毫秒(30分钟) -->
    	<property name="minEvictableIdleTimeMillis" value="1800000" />
    	<!--最小空闲连接数  -->
    	<property name="minIdle" value="${redis.minIdle}" />
    	<!-- 每次逐出检查时 逐出的最大数目 如果为负数就是 : 1/abs(n), 默认3 -->
    	<property name="numTestsPerEvictionRun" value="3" />
    	<!--对象空闲多久后逐出, 当空闲时间>该值 且 空闲连接>最大空闲数 时直接逐出,不再根据MinEvictableIdleTimeMillis判断  (默认逐出策略)  -->
    	<property name="softMinEvictableIdleTimeMillis" value="1800000" />
    	<!-- 在获取连接的时候检查有效性, 默认false -->
    	<property name="testOnBorrow" value="${redis.testOnBorrow}" /> 
    	<!-- 在空闲时检查有效性, 默认false --> 
    	<property name="testWhileIdle" value="false" />
    	<!--逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1  -->  
    	<property name="timeBetweenEvictionRunsMillis" value="-1" /> 	        
	 </bean>

	<!--********************* redis单例 standalone模式 **********************************-->
	<!-- JedisConnectionFactory -->
<!-- 
 	<bean id="jedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
       <constructor-arg name="poolConfig" ref="jedisPoolConfig" />
        <property name="password" value="${redis.pwd}"/>
        <property name="hostName" value="${redis.host}"/>
        <property name="port" value="${redis.port}"/>
		<property name="usePool" value="true"/>
		<property name="timeout" value="${redis.timeout}"/>
	</bean>
 -->
	<!--/redis单例 standalone模式结束-->	 
	 
	<!--********************* redis主从sentinel模式 **********************************-->
<!--  	<bean id="jedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
		 <constructor-arg name="sentinelConfig" >
            <bean class="org.springframework.data.redis.connection.RedisSentinelConfiguration"> 
            	<constructor-arg name="master" value="${spring.redis.redis.sentinel.masterName}" />
            	<constructor-arg name="sentinelHostAndPorts">
            		<set>
            			<value>${spring.redis.sentinel.node1}</value>
            			<value>${spring.redis.sentinel.node2}</value>
            			<value>${spring.redis.sentinel.node3}</value>
    				</set>  
            	</constructor-arg>
            </bean>
        </constructor-arg>
        
        <property  name="poolConfig" ref="jedisPoolConfig" />

		<property name="password" value="${spring.redis.redis.sentinel.password}"/>
		<property name="usePool" value="true"/>
		<property name="timeout" value="${redis.timeout}"/>
	</bean>  
-->
	<!--/redis主从sentinel模式结束-->
	
 	<!--********************* redis Cluster集群模式 **********************************-->
	<!-- JedisConnectionFactory -->
<!-- 	<bean id="jedisConnectionFactory" class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
		 <constructor-arg name="clusterConfig" >
-->			<!--redisSentinel配置-->  
<!--             <bean class="org.springframework.data.redis.connection.RedisClusterConfiguration"> 
            	<constructor-arg name="sentinelHostAndPorts">
            		<set>
            			<value>${spring.redis.sentinel.node1}</value>
            			<value>${spring.redis.sentinel.node2}</value>
            			<value>${spring.redis.sentinel.node3}</value>
    				</set>  
            	</constructor-arg>
            	<property name="maxRedirects" value="${spring.redis.cluster.max-redirects}"/>
            </bean>
        </constructor-arg>
-->        <!-- JedisPoolConfig  -->
<!--        <property name="poolConfig" ref="jedisPoolConfig" >

		<property name="usePool" value="true"/>
		<property name="timeout" value="${redis.timeout}"/>
	</bean>
-->	
	<!--/redis Cluster集群模式结束-->
  	
</beans>