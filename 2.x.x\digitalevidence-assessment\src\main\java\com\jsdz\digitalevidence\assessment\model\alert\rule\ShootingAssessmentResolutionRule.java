package com.jsdz.digitalevidence.assessment.model.alert.rule;

import java.util.ArrayList;
import java.util.List;

import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.ruleengine.RuleState;
import com.jsdz.ruleengine.annotation.Given;
import com.jsdz.ruleengine.annotation.Param;
import com.jsdz.ruleengine.annotation.Result;
import com.jsdz.ruleengine.annotation.Rule;
import com.jsdz.ruleengine.annotation.Then;
import com.jsdz.ruleengine.annotation.When;

/**
 * @类名: ShootingAssessmentAssessingRateRule
 * @说明: 拍摄考核拍摄分辨率规则
 *
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 修改记录：
 *
 * @see
 */
@Rule(name="psResolutionHighPercent", description="拍摄分辨率高清晰占比")
public class ShootingAssessmentResolutionRule {

	public static final String key = "拍摄分辨率高清晰占比";

	@Param(name="psResolutionHighPercent", description="拍摄分辨率高清晰占比")
	private float psResolutionHighPercent;
	
	/** */
	@Result
	private List<AssessmentAlertItem<?>> _result;
	
	@Given
	private ShootingAssessmentSummary sum;
	
	@When
	public boolean when() {
		if((float)sum.getCountOfShootingResolutionH()/sum.getTotalAssessingDoc()<psResolutionHighPercent)
			return true;
		return false;
	}

	@Then
	public RuleState then() {
		AssessmentAlertItem<Float> item = new AssessmentAlertItem<Float>();
		item.setItem(key);
		item.setV((float)sum.getCountOfShootingResolutionH()/sum.getTotalAssessingDoc());
		item.setRef(psResolutionHighPercent);
		if(_result==null)
			_result = new ArrayList<AssessmentAlertItem<?>>();
		_result.add(item);
		return RuleState.NEXT;
	}

}
