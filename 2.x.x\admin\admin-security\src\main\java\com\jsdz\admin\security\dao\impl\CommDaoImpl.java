package com.jsdz.admin.security.dao.impl;
/**
 * 
 * @类名: CommDaoImpl.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月3日下午5:32:49
 * 修改记录：
 *
 * @see
 */

import java.io.Serializable;
import java.util.List;

import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;

import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

public class CommDaoImpl extends GenericEntityDaoHibernateImpl<Object,Long>{
	
	//查询单个
	//@SuppressWarnings("deprecation")
	public Object findObjectByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		List<OperatorRole> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}	
}
