package com.jsdz.digitalevidence.assessment.dao.mapper;

import java.util.List;

import java.util.Map;

import com.jsdz.digitalevidence.assessment.bean.AssessmentDocBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentInfoDocBean;

import com.jsdz.digitalevidence.assessment.bean.DocBean;
import com.jsdz.digitalevidence.assessment.model.AssessmentDocInfo;
import com.jsdz.digitalevidence.assessment.model.AssessmentInfo;

import org.apache.ibatis.annotations.Param;

public interface AssessmentInfoMapper {
//						 getAssessmentInfo
	List<AssessmentInfo> getAssessmentInfo(Map<String, Object> params);
	List<AssessmentInfo> getAssessmentInfos();

    List<DocBean> findAssessmetDocuments(Map<String, Object> paramMap);
    //保存视频考核信息
    Integer saveAssessmentDocInfos(@Param("assessmentInfoList") List<AssessmentDocInfo> assessmentInfoList);
    Integer saveAssessmentDocInfos1(@Param("assessmentInfoList1") List<AssessmentDocInfo> assessmentInfoList);
    //保存视频考核得分信息
    Integer saveAssessmentInfo(AssessmentInfo paramMap);

    Integer saveAssessmentDocInfo(AssessmentDocInfo assessmentDocInfo);

    Map<String, Object>   getEmpId(@Param("empId")Integer empId);

    Integer updateAssessmentDocInfo(AssessmentDocInfo assessmentDocInfo);

    List<AssessmentInfoDocBean> getAssessmentInfosByAssess(Map<String, Object> assessmentInfo);

    List<AssessmentDocBean> getAssessmentInfoByAssessId(Map<String, Object> assessmentInfo);

    Integer getAssessmentInfosCounts(Map<String, Object> paramMap);

    Integer updateAssessmentInfo(AssessmentInfo assessmentInfo);
    
	int getAssessmentInfoCount(Map<String, Object> paramMap);
}
