package com.jsdz.digitalevidence.analysis.bean;

import java.util.ArrayList;
import java.util.List;

/**
 * 
 * @类名: analysisChartsBean
 * @说明: 处理成图表显示数据
 *
 * @author: kenny
 * @Date	2017年6月6日下午4:53:16
 * 修改记录：
 *
 * @see
 */
public class AnalysisChartsBean {
	List<String> codes;
	List<Double> vedioLens ;
	List<Double> fileSizes;
	List<Integer> fileCounts;
	
	public AnalysisChartsBean(){
		codes = new ArrayList<String>();
		vedioLens = new ArrayList<Double>();
		fileSizes = new ArrayList<Double>();
		fileCounts = new ArrayList<Integer>();
	}
	
	public void assign(SiteDocDailyDto src){
		//this.getCodes().add(src.getCode());
		this.getCodes().add(src.getName());
		double douValue = 0;
		douValue = src.getVedioLen()==null?0:Math.round(src.getVedioLen()/(1000*60.0)*100.00)/100.00; //分钟 
		this.getVedioLens().add(douValue);
		douValue = src.getFileSize()==null?0:Math.round(src.getFileSize()/(1000*1000*1000.00) * 100.00)/100.00; //M 
		this.getFileSizes().add(douValue);
		this.getFileCounts().add(src.getFileCount()==null?0:src.getFileCount().intValue());
		
	}

	public List<String> getCodes() {
		return codes;
	}

	public void setCodes(List<String> codes) {
		this.codes = codes;
	}

	public List<Integer> getFileCounts() {
		return fileCounts;
	}

	public void setFileCounts(List<Integer> fileCounts) {
		this.fileCounts = fileCounts;
	}

	public List<Double> getVedioLens() {
		return vedioLens;
	}

	public void setVedioLens(List<Double> vedioLens) {
		this.vedioLens = vedioLens;
	}

	public List<Double> getFileSizes() {
		return fileSizes;
	}

	public void setFileSizes(List<Double> fileSizes) {
		this.fileSizes = fileSizes;
	}
}