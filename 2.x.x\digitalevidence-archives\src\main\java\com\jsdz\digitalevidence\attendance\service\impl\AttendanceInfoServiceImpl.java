package com.jsdz.digitalevidence.attendance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.param.AttendanceInfoParam;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceInfoBean;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceInfoTaskBean;
import com.jsdz.digitalevidence.attendance.mapper.AttendanceCheckMapper;
import com.jsdz.digitalevidence.attendance.mapper.AttendanceInfoMapper;
import com.jsdz.digitalevidence.attendance.model.AttendanceCheckModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceInfoModel;
import com.jsdz.digitalevidence.attendance.model.AttendanceTaskModel;
import com.jsdz.digitalevidence.attendance.service.IAttendanceInfoService;
import com.jsdz.digitalevidence.utils.DateUtil;
import com.jsdz.digitalevidence.utils.SqlUtils;
import com.jsdz.digitalevidence.utils.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Service
public class AttendanceInfoServiceImpl  extends ServiceImpl<AttendanceInfoMapper, AttendanceInfoModel> implements IAttendanceInfoService {

    private final Logger logger = Logger.getLogger(this.getClass());
    @Resource
    private AttendanceInfoMapper attendanceInfoMapper;
    //设置表名
    private String tableName = "sys_attendance_info";


    SqlUtils sqlUtils = new SqlUtils<AttendanceInfoModel>();
    @Override
    public Page<AttendanceInfoBean> getDatas(AttendanceInfoParam param, PerLvlBean permLvl) {
        Page<AttendanceInfoBean> page = param.getPage();
        String countSql = " select count(1) countSum ";
        String dataSql  = " select t.*,o.orgName,o.path as orgPath,o.orgCode,o.Id as orgId ";
        String whereSql = " from "+tableName+" t LEFT JOIN sys_attendance_task n on t.attendanceTaskId = n.id LEFT JOIN admin_organization o on t.attendanceOrgId = o.id where  t.isDelete = 0 ";
        //分页
        String limitSql = " limit "+param.getPage().getOffset()+" ,"+param.getPage().getPageSize();
        //排序条件，暂时无需求
        String orderSql = " order by t.createTime desc";
        //页面没指定单位权限，需使用用户权限
        if(StringUtils.isBlank(param.getOrgPath())){
            whereSql = sqlUtils.userLevel( whereSql,permLvl);
        }
        if(StringUtils.isNoBlank(param.getOrgPath()) & StringUtils.isNoBlank(param.getIncludeSub())){
            String includeSub = param.getIncludeSub();
            whereSql+=" and o.path like '"+param.getOrgPath() + ("1".equals(includeSub)?"%":"")  +"'";
        }
        if(StringUtils.isNoBlank(param.getTaskId()) ){
            whereSql+=" and n.id =  '"+param.getTaskId()+"'";
        }
        //其他调教查询补充
        logger.info(dataSql + whereSql + orderSql + limitSql );
        List<AttendanceInfoBean> datas = attendanceInfoMapper.getDatas(dataSql + whereSql + orderSql + limitSql );
        Integer count = attendanceInfoMapper.getCount(countSql + whereSql  + orderSql);
        page.setRows(datas);
        page.setTotal(count);
        return page;
    }

    @Override
    public void saveData(AttendanceInfoModel model, PerLvlBean permLvl) {
        attendanceInfoMapper.insert(model);
    }


    @Override
    public void updateDataById(AttendanceInfoModel model, PerLvlBean permLvl) {
        attendanceInfoMapper.updateById(model);
    }

    @Override
    public AttendanceInfoModel getById(AttendanceInfoModel model, PerLvlBean permLvl) {
        return attendanceInfoMapper.selectById(model.getId());
    }

    @Override
    public void deleteById(AttendanceInfoModel model, PerLvlBean permLvl) {
        model.setIsDelete(1);
        attendanceInfoMapper.updateById(model);
    //    attendanceInfoMapper.deleteById(sqlUtils.deleteByIdSql(model,tableName,"id"));
    }
    @Override
    public List<AttendanceInfoModel> getInfos(String deviceCode ) {
        QueryWrapper<AttendanceInfoModel> wrapper = new QueryWrapper<>();
        wrapper.eq("deviceCode",deviceCode);//设备ID
        wrapper.eq("isAutomatic",0);//允许自动打卡
        wrapper.eq("isDelete",0);
//        wrapper.isNotNull("lastAattendanceTime");
//        wrapper.lt("lastAattendanceTime", DateUtil.getDate000());//今天为打卡的
        return attendanceInfoMapper.selectList(wrapper);
    }

    @Override
    public Integer findisSave(Long taskId, Long deviceId) {
        return attendanceInfoMapper.findisSave( taskId, deviceId);
    }

    @Override
    public List<AttendanceInfoTaskBean> getInfoTaskAll(String code) {
        return attendanceInfoMapper.getInfoTaskAll(code);
    }

    @Override
    public List<AttendanceInfoModel> getInfosByCode(String deviceCode) {
        QueryWrapper<AttendanceInfoModel> wrapper = new QueryWrapper<>();
        wrapper.eq("deviceCode",deviceCode);//设备ID
        wrapper.eq("isDelete",0);//允许自动打卡
        return attendanceInfoMapper.selectList(wrapper);
    }

}
