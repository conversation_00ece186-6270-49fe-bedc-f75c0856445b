/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;

/**
 * @类名: AssessmentAlertBean
 * @说明: 考核预警报告Bean
 *        
 *      
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentAlertBean extends AbstractDTO {

	/********警员资料********/
	/** 考核预警Id*/
	private Long alertId;
	/** 警号*/
	private String policeCode;
	/** 姓名*/
	private String policeName;
	/** 性别*/
	private String sex;
	/** 年龄*/
	private Integer age;
	/** 局*/
	private String orgName;
	/** 部门*/
	private String deptName;
	/** 职位*/
	private String position;
    /** 报告生成日期*/
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime; 
    /** 考核周期*/
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date startDate;
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endDate;
    /** 冻结状态*/
    private Boolean frozen;
    
    /** 预警项*/
    @SuppressWarnings("rawtypes")
	private List<AssessmentAlertItem> items;

    /** 反馈*/
    private String feedback;

	public String getPoliceCode() {
		return policeCode;
	}

	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}

	public String getPoliceName() {
		return policeName;
	}

	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}

	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	public Integer getAge() {
		return age;
	}

	public void setAge(Integer age) {
		this.age = age;
	}

	public String getDeptName() {
		if(deptName==null)
			return "-";
		return deptName;
	}

	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	public String getPosition() {
		if(position==null)
			return "-";
		return position;
	}

	public void setPosition(String position) {
		this.position = position;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public List<AssessmentAlertItem> getItems() {
		return items;
	}

	public void setItems(List<AssessmentAlertItem> items) {
		this.items = items;
	}

	public String getFeedback() {
		return feedback;
	}

	public void setFeedback(String feedback) {
		this.feedback = feedback;
	}

	public Long getAlertId() {
		return alertId;
	}

	public void setAlertId(Long alertId) {
		this.alertId = alertId;
	}

	public String getOrgName() {
		if(orgName==null)
			return "-";
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public Boolean getFrozen() {
		return frozen;
	}

	public void setFrozen(Boolean frozen) {
		this.frozen = frozen;
	}

}
