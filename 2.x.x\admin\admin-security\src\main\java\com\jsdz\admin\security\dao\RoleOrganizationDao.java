package com.jsdz.admin.security.dao;

/**
 *
 * @类名: RoleOrganizationDao
 * @说明:
 * @author: kenny
 * @Date 2018-05-15 16:42:50
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.RoleOrganization;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface RoleOrganizationDao extends GenericORMEntityDAO<RoleOrganization,Long> {

	//新增
	public void addRoleOrganization(RoleOrganization roleOrganization);

	//修改
	public void updateRoleOrganization(RoleOrganization roleOrganization);

	//删除
	public void deleteRoleOrganization(RoleOrganization roleOrganization);

	//按id查询,结果是游离状态的数据
	public RoleOrganization findRoleOrganizationById(Long id);

	//按id查询
	public RoleOrganization locateRoleOrganizationById(Long id);

	//单个查询
	public RoleOrganization findRoleOrganizationByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<RoleOrganization> findAllRoleOrganizations();

	//列表查询
	public List<RoleOrganization> findRoleOrganizationsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RoleOrganization> findRoleOrganizationsOnPage(Page<RoleOrganization> page,String queryStr,String[] paramNames,Object[] values);

	//执行指定的HQL文件
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);
	
	//取权限单位
	public List<Object> getRoleOrgPath(Long userId);

}

