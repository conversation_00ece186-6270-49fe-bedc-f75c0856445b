package com.jsdz.digitalevidence.assessment.bean;
/**
 * 
 * @类名: FiveRateBean
 * @说明: 
 *
 * @author: kenny
 * @Date	2018年1月15日下午3:05:04
 * 修改记录：
 *
 * @see
 */
public class FiveRateBean {
	private Long id;
	private String orgCode;
	private String orgName;
	private Double numerator;//分子
	private Double total;//总数(分母)
	private Double perRate;//百分比
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public Double getTotal() {
		return total;
	}
	public void setTotal(Double total) {
		this.total = total;
	}
	public Double getNumerator() {
		return numerator;
	}
	public void setNumerator(Double numerator) {
		this.numerator = numerator;
	}
	public Double getPerRate() {
		return perRate;
	}
	public void setPerRate(Double perRate) {
		this.perRate = perRate;
	}
	
	
}
