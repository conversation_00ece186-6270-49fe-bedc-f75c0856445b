package com.jsdz.digitalevidence.activemq.dao.impl;

/**
 *
 * @类名: SysLogTopicDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2018-03-15 14:58:15
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.activemq.dao.SysLogTopicDao;
import com.jsdz.digitalevidence.activemq.model.SysLogTopic;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class SysLogTopicDaoImpl extends GenericEntityDaoHibernateImpl<SysLogTopic,Long> implements SysLogTopicDao{

	//新增
	public void addSysLogTopic(SysLogTopic sysLogTopic) {
		this.saveOrUpdate(sysLogTopic);
	}

	//删除
	public void deleteSysLogTopic(SysLogTopic sysLogTopic) {
		this.delete(sysLogTopic);
	}

	//修改
	public void updateSysLogTopic(SysLogTopic sysLogTopic) {
		this.merge(sysLogTopic);
	}

	//按id查询(游离状态)
	public SysLogTopic findSysLogTopicById(Long id){

		final String  hql = "from SysLogTopic s where s.id = :id";
		final Long oid = id;
		SysLogTopic data = getHibernateTemplate().execute(new HibernateCallback<SysLogTopic>() {
			public SysLogTopic doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<SysLogTopic> list = query.list();
				SysLogTopic rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public SysLogTopic locateSysLogTopicById(Long id){

		final String  hql = "from SysLogTopic s where s.id = :id";
		final Long oid = id;
		SysLogTopic data = getHibernateTemplate().execute(new HibernateCallback<SysLogTopic>() {
			public SysLogTopic doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<SysLogTopic> list = query.list();
				SysLogTopic rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public SysLogTopic findSysLogTopicByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		SysLogTopic data = getHibernateTemplate().execute(new HibernateCallback<SysLogTopic>() {
		public SysLogTopic doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<SysLogTopic> list = query.list();
			SysLogTopic rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<SysLogTopic> findAllSysLogTopics(){
		return this.find("from SysLogTopic sysLogTopic ");
	}

	//列表查询
	public List<SysLogTopic> findSysLogTopicsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<SysLogTopic> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<SysLogTopic> findSysLogTopicsOnPage(Page<SysLogTopic> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<SysLogTopic>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//执行自定义的hql
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
}
