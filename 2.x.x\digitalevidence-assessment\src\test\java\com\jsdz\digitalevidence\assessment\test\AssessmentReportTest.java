/**
 * %项目描述%
 * %版本信息%
 */
package com.jsdz.digitalevidence.assessment.test;

import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.core.Env;
import com.jsdz.reportengine.REConsts;
import com.jsdz.reportengine.engine.ReportEngine;
import com.jsdz.reportengine.engine.dao.ReportDao;
import com.jsdz.reportengine.engine.model.Param;
import com.jsdz.reportengine.engine.model.QueryBean;
import com.jsdz.reportengine.engine.model.Report;
import com.jsdz.reportengine.service.REService;

/**
 * @类名: AssessmentReportTest
 * @说明: 考核报表测试
 * 
 * <AUTHOR>
 * @Date 2012-6-27 下午5:21:18
 * @修改记录：
 * 
 * @see
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(
		locations={
				"/testApplicationContext-common.xml",
				"/testApplicationContext-reportengine.xml",
				"/testApplicationContext-dynsql.xml",
				"/testApplicationContext-documenttype.xml",
				"/testApplicationContext-scheduler.xml",
				"/testApplicationContext-cycle.xml"
				})
public class AssessmentReportTest {
	
	@Autowired
	private ReportEngine xlsReportEngine;
	@Autowired
	private ReportEngine xlsxReportEngine;
	@Autowired
	private ReportEngine rtfReportEngine;
	@Autowired
	private ReportEngine docxReportEngine;
	@Autowired
	private ReportDao reportDao;
	@Autowired
	private REService reService;
	@Autowired
	private Env env;
	
	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
	}
	
	/*******************报表服务测试*********************/
	// 考核报告报表测试
	@Test
	public void testAssessmentReport() throws Exception {
		OutputStream os1 = new FileOutputStream("shootingAssessmentReport.rtf");
		OutputStream os2 = new FileOutputStream("shootingAssessmentReport.xlsx");
		OutputStream os3 = new FileOutputStream("shootingAssessmentAlertReport.docx");
		String reportName = "shootingAssessmentReport";
		// 查询参数
		Map<String, String> params = new HashMap<String, String>();
		// 上传几天过期
		params.put("reportId", "6");
		//
		reService.exportReport(REConsts.CONTENT_TYPE_RTF, reportName, params, os1);
		reService.exportReport(REConsts.CONTENT_TYPE_XLSX, reportName, params, os2);
		reService.exportReport(REConsts.CONTENT_TYPE_DOCX, reportName, params, os3);
		os1.close();
		os2.close();
		os3.close();

	}
	
	// 考核报告报表测试
	@Test
	public void testAssessmentDailyReport() throws Exception {
		OutputStream os1 = new FileOutputStream("shootingAssessmentDailyReport.rtf");
		OutputStream os2 = new FileOutputStream("shootingAssessmentDailyReport.xlsx");
		OutputStream os3 = new FileOutputStream("shootingAssessmentDailyReport.rtf");
		String reportName = "shootingAssessmentDailyReport";
		// 查询参数
		Map<String, String> params = new HashMap<String, String>();
		// 上传几天过期
		params.put("reportId", "6");
		//
		reService.exportReport(REConsts.CONTENT_TYPE_RTF, reportName, params, os1);
		reService.exportReport(REConsts.CONTENT_TYPE_XLSX, reportName, params, os2);
		reService.exportReport(REConsts.CONTENT_TYPE_RTF, reportName, params, os3);
		os1.close();
		os2.close();
		os3.close();

	}
	
	// 考核预警测试
	@Test
	public void testAlertReport() throws Exception {
		OutputStream os1 = new FileOutputStream("shootingAssessmentAlertReport.docx");
		OutputStream os2 = new FileOutputStream("shootingAssessmentAlertReport.xlsx");
		OutputStream os3 = new FileOutputStream("shootingAssessmentAlertReport.rtf");
		String reportName = "shootingAssessmentAlertReport";
		// 查询参数
		Map<String, String> params = new HashMap<String, String>();
		// 上传几天过期
		params.put("alertId", "9");
		//
		reService.exportReport(REConsts.CONTENT_TYPE_DOCX, reportName, params, os1);
		reService.exportReport(REConsts.CONTENT_TYPE_XLSX, reportName, params, os2);
		reService.exportReport(REConsts.CONTENT_TYPE_RTF, reportName, params, os3);
		os1.close();
		os2.close();
		os3.close();
	}
	
	// 周考核预警测试
	@Test
	public void testAlertWeeklyReport() throws Exception {
		OutputStream os1 = new FileOutputStream("shootingAssessmentAlertWeeklyReport.docx");
		OutputStream os2 = new FileOutputStream("shootingAssessmentAlertWeeklyReport.xlsx");
		OutputStream os3 = new FileOutputStream("shootingAssessmentAlertWeeklyReport.rtf");
		String reportName = "shootingAssessmentAlertWeeklyReport";
		// 查询参数
		Map<String, String> params = new HashMap<String, String>();
		// 上传几天过期
		params.put("alertId", "9");
		//
		reService.exportReport(REConsts.CONTENT_TYPE_DOCX, reportName, params, os1);
		reService.exportReport(REConsts.CONTENT_TYPE_XLSX, reportName, params, os2);
		reService.exportReport(REConsts.CONTENT_TYPE_RTF, reportName, params, os3);
		os1.close();
		os2.close();
		os3.close();
	}
		
	/************考核report模型写入************/
	// 完整考核报告
	@Test
	@Transactional
	public void testAddReport_ShootingAssessmentReport() {
		// 报表定义
		Report r = new Report();
		r.setReportName("shootingAssessmentReport");
		// 报表模板
		r.setTemplate("report/ShootingAssessmentReport.jasper");
		// 报表查询参数
		Map<String, Param> queryParams = new HashMap<String, Param>();
		Param p1 = new Param();
		p1.setReportName(r.getReportName());
		p1.setIsContext(false);
		p1.setName("reportId");
		queryParams.put("reportId", p1);
		//
		r.setQueryParams(queryParams);
		// 报表参数
		// 没有
		//
		r.setQueryStrType(REConsts.QueryStr_Type_NamedSQL);
		r.setQueryStr("getCompleteShootingAccessmentReport");
		//
		reportDao.saveOrUpdate(r);
		
	}
	
	// 日考核报告
	@Test
	@Transactional
	public void testAddReport_ShootingAssessmentDailyReport() {
		// 报表定义
		Report r = new Report();
		r.setReportName("shootingAssessmentDailyReport");
		// 报表模板
		r.setTemplate("assessment/ShootingDailyAssessmentReport.jasper");
		// 报表查询参数
		Map<String, Param> queryParams = new HashMap<String, Param>();
		Param p1 = new Param();
		p1.setReportName(r.getReportName());
		p1.setIsContext(false);
		p1.setName("reportId");
		queryParams.put("reportId", p1);
		//
		r.setQueryParams(queryParams);
		// 报表参数
		// 没有
		//
		r.setQueryStrType(REConsts.QueryStr_Type_NamedSQL);
		r.setQueryStr("getCompleteShootingAccessmentReport");
		//
		reportDao.saveOrUpdate(r);
		
	}	
	
	// 考核预警报告
	@Test
	@Transactional
	public void testAddReport_ShootingAssessmentAlertReport() {
		// 报表定义
		Report r = new Report();
		r.setReportName("shootingAssessmentAlertReport");
		// 报表模板
		r.setTemplate("report/ShootingAssessmentAlert.jasper");
		// 报表查询参数
		Map<String, Param> queryParams = new HashMap<String, Param>();
		Param p1 = new Param();
		p1.setReportName(r.getReportName());
		p1.setIsContext(false);
		p1.setName("alertId");
		queryParams.put("alertId", p1);
		//
		r.setQueryParams(queryParams);
		// 报表参数
		// 没有
		//
		r.setQueryStrType(REConsts.QueryStr_Type_NamedSQL);
		r.setQueryStr("getShootingAccessmentAlertReport");
		//
		reportDao.saveOrUpdate(r);

	}
	
	// 周考核预警报告
	@Test
	@Transactional
	public void testAddReport_ShootingAssessmentAlertWeeklyReport() {
		// 报表定义
		Report r = new Report();
		r.setReportName("shootingAssessmentAlertWeeklyReport");
		// 报表模板
		r.setTemplate("assessment/ShootingWeeklyAssessmentAlert.jasper");
		// 报表查询参数
		Map<String, Param> queryParams = new HashMap<String, Param>();
		Param p1 = new Param();
		p1.setReportName(r.getReportName());
		p1.setIsContext(false);
		p1.setName("alertId");
		queryParams.put("alertId", p1);
		//
		r.setQueryParams(queryParams);
		// 报表参数
		// 没有
		//
		r.setQueryStrType(REConsts.QueryStr_Type_NamedSQL);
		r.setQueryStr("getShootingAccessmentAlertItem");
		r.setSumQueryStr("getShootingAccessmentAlertBean");
		//
		reportDao.saveOrUpdate(r);

	}
	
	// 增加参数
	@Test
	@Transactional
	public void testAddReportParams() {
		// 报表
		Report r = reportDao.get("documentExpiredReport");
		/*r.setQueryStr("documentExpiredReport");
		r.setQueryStrType(REConsts.QueryStr_Type_NamedSQL);*/
		Map<String, Param> rps = r.getQueryParams();
		Param p1 = new Param();
		p1.setReportName(r.getReportName());
		p1.setName("withinDays");
		p1.setValue(3);
		p1.setType(Integer.TYPE);
		rps.put(p1.getName(), p1);
		reportDao.saveOrUpdate(r);
		
	}
	
	public Object toObject( Type type, String value ) {
	    if( Boolean.class == type ) 
	    	return Boolean.parseBoolean( value );
	    if( Byte.class == type ) 
	    	return Byte.parseByte( value );
	    if( Short.class == type ) 
	    	return Short.parseShort( value );
	    if( Integer.class == type || int.class == type ) 
	    	return Integer.parseInt( value );
	    if( Long.class == type ) 
	    	return Long.parseLong( value );
	    if( Float.class == type ) 
	    	return Float.parseFloat( value );
	    if( Double.class == type ) 
	    	return Double.parseDouble( value );
	    return value;
	}

	public Env getEnv() {
		return env;
	}

	public void setEnv(Env env) {
		this.env = env;
	}
	
}
