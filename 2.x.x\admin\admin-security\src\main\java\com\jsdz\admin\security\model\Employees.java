package com.jsdz.admin.security.model;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Department;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.model.Position;
import com.jsdz.utils.DateTimeUtils;

/**
 * 
 * @类名: Employees
 * @说明: 员工类
 *
 * @author: kenny
 * @Date	2017年4月26日上午11:47:10
 * 修改记录：
 *
 * @see
 */
public class Employees implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 6437139583471176695L;
	
	private Long id;
	private String name; //姓名
	private String male;//性别
	@JsonFormat(pattern=DateTimeUtils.defaultDatePatten2,timezone="GMT+8")
	private Date   birthDate;//生日
	private String idNum;   //身份证号
	private String workNumber;   //工号
	private String mail;    //e_mail
	private String mobile;  //移动电话
	private Integer age;    //年龄    
	private Date createTime;  //创建时间
	//@JsonIgnore
	private Department department; //部门
	//@JsonIgnore
	private Organization organization; //单位（行政单位）
	//@JsonIgnore
	private Position position; //职位

	private String post;//岗位
	
	private Integer isPolice=1;//1 干警  0协警
	
	/** 更新时间*/
	private Date updateTime=new Date();
	/** 是否已删除*/
	private Boolean isDeleted=false;
	/** 原code,为防止加入新编号重复 */
	private String deleteCode;//
	/** 删除时间 */
	private Date deleteTime;//
	/** 删除人 */
	private String deleteBy;//
	/** 人脸特征 值*/
	private String faceFeatures;
	
	

	public Employees(){
		super();
	}
	public Employees(String name,String male,Date   birthDate,String idNum){
		this.name = name;
		this.male = male;
		this.birthDate = birthDate;
		this.idNum = idNum;
		this.createTime = new Date();
	}
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getMale() {
		return male;
	}
	public void setMale(String male) {
		this.male = male;
	}
	public Date getBirthDate() {
		return birthDate;
	}
	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}
	public String getIdNum() {
		return idNum;
	}
	public void setIdNum(String idNum) {
		this.idNum = idNum;
	}
	public String getMail() {
		return mail;
	}
	public void setMail(String mail) {
		this.mail = mail;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public Integer getAge() {
		if (birthDate != null){
			long diff = new Date().getTime() - birthDate.getTime();  
			int years = (int)Math.round((long) diff / (1000 * 60 * 60 * 24 * 365 * 21.45)); 
			age = years;
				return age;
		}else
			return 0;

	}

	public void setAge(Integer age) {
		this.age = age;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Department getDepartment() {
		return department;
	}
	public void setDepartment(Department department) {
		this.department = department;
	}
	public Organization getOrganization() {
		return organization;
	}
	public void setOrganization(Organization organization) {
		this.organization = organization;
	}
	public Position getPosition() {
		return position;
	}
	public void setPosition(Position position) {
		this.position = position;
	}

	public String getWorkNumber() {
		return workNumber;
	}
	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}
	public String getPost() {
		return post;
	}
	public void setPost(String post) {
		this.post = post;
	}
	public Integer getIsPolice() {
		return isPolice;
	}
	public void setIsPolice(Integer isPolice) {
		this.isPolice = isPolice;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public Boolean getIsDeleted() {
		return isDeleted;
	}
	public void setIsDeleted(Boolean isDeleted) {
		this.isDeleted = isDeleted;
	}
	public String getDeleteCode() {
		return deleteCode;
	}
	public void setDeleteCode(String deleteCode) {
		this.deleteCode = deleteCode;
	}
	public Date getDeleteTime() {
		return deleteTime;
	}
	public void setDeleteTime(Date deleteTime) {
		this.deleteTime = deleteTime;
	}
	public String getDeleteBy() {
		return deleteBy;
	}
	public void setDeleteBy(String deleteBy) {
		this.deleteBy = deleteBy;
	}
	public String getFaceFeatures() {
		return faceFeatures;
	}
	public void setFaceFeatures(String faceFeatures) {
		this.faceFeatures = faceFeatures;
	}


	

}
