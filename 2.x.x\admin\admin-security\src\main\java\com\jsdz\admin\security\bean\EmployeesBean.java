package com.jsdz.admin.security.bean;

import java.util.Date;

import org.apache.commons.beanutils.BeanUtils;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Department;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.model.Position;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.AbstractDTO;
import com.jsdz.utils.DateTimeUtils;
/**
 * 
 * @类名: EmployeesBean
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月13日下午3:00:17
 * 修改记录：
 *
 * @see
 */
public class EmployeesBean extends AbstractDTO{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1385068081245557860L;
	
	private Long id;
	private String name; //姓名
	private String male;//性别
	@JsonFormat(pattern=DateTimeUtils.defaultDatePatten2,timezone="GMT+8")
	private Date   birthDate;//性别
	private String idNum;   //身份证号
	private String workNumber;   //工号
	private String mail;    //e_mail
	private String mobile;  //移动电话
	private Integer age;    //年龄    
	private Long departmentId; //部门
	private String departmentName;  //部门名称
	private Long organizationId; //单位（行政单位）
	private String organizationName;  //单位名称
	private Long positionId; //职位ID	
	private String positionName;  
	private Date createTime;  //创建时间
	private String post;  //岗位
	private Integer isPolice=1;//1 干警  0协警
	/** 人脸特征 值*/
	private String faceFeatures;
	
	public void assign(Employees src) {
		try {
			BeanUtils.copyProperties(this, src);

			if (src.getOrganization() != null){
				this.setOrganizationId(src.getOrganization().getId());
				this.setOrganizationName(src.getOrganization().getOrgName());
			}
			
			if (src.getPosition() != null){
				this.setPositionId(src.getPosition().getId());
				this.setPositionName(src.getPosition().getPosition());
			}
			
			if (src.getDepartment()!=null){
				this.setDepartmentId(src.getDepartment().getId());
				this.setDepartmentName(src.getDepartment().getName());
			}
		} catch (Exception e) {
			throw new RuntimeException("Assign Error.", e);
			//System.out.println("Assign Error:", e);
		}
	}	
	
	public void assignTo(Employees dest) {
		try {
			BeanUtils.copyProperties(dest,this);

			if (this.getOrganizationId()!=null){
				Organization organization = new Organization();
				organization.setId(this.getOrganizationId());
				dest.setOrganization(organization);
			}
			
			if (this.getDepartmentId()!=null){
				Department department = new Department();
				department.setId(this.getDepartmentId());
				dest.setDepartment(department);
			}
			
			if (this.getPositionId()!=null){
				Position position = new Position();
				position.setId(this.getPositionId());
				dest.setPosition(position);
			}
		} catch (Exception e) {
			throw new RuntimeException("AssignTo Error.", e);
			//System.out.println("Assign Error:", e);
		}
	}	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getMale() {
		return male;
	}
	public void setMale(String male) {
		this.male = male;
	}
	public Date getBirthDate() {
		return birthDate;
	}
	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}
	public String getIdNum() {
		return idNum;
	}
	public void setIdNum(String idNum) {
		this.idNum = idNum;
	}
	public String getWorkNumber() {
		return workNumber;
	}
	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}
	public String getMail() {
		return mail;
	}
	public void setMail(String mail) {
		this.mail = mail;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public Integer getAge() {
		return age;
	}
	public void setAge(Integer age) {
		this.age = age;
	}
	public Long getDepartmentId() {
		return departmentId;
	}
	public void setDepartmentId(Long departmentId) {
		this.departmentId = departmentId;
	}
	public String getDepartmentName() {
		return departmentName;
	}
	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	
	public Long getOrganizationId() {
		return organizationId;
	}

	public void setOrganizationId(Long organizationId) {
		this.organizationId = organizationId;
	}

	public String getOrganizationName() {
		return organizationName;
	}
	public void setOrganizationName(String organizationName) {
		this.organizationName = organizationName;
	}
	public Long getPositionId() {
		return positionId;
	}
	public void setPositionId(Long positionId) {
		this.positionId = positionId;
	}
	public String getPositionName() {
		return positionName;
	}
	public void setPositionName(String positionName) {
		this.positionName = positionName;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getPost() {
		return post;
	}

	public void setPost(String post) {
		this.post = post;
	}

	public Integer getIsPolice() {
		return isPolice;
	}

	public void setIsPolice(Integer isPolice) {
		this.isPolice = isPolice;
	}

	public String getFaceFeatures() {
		return faceFeatures;
	}

	public void setFaceFeatures(String faceFeatures) {
		this.faceFeatures = faceFeatures;
	}
	
	
}
