package com.jsdz.digitalevidence.archives.service;

import java.util.List;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.digitalevidence.archives.model.*;

public interface EquipmentCountService{

	List<EquipmentCount> findEquipmentCount(String path);

    List<EquipmentCount> lawpblCount(String path);

    List<VRecorderOrgCount> lawslCount(PerLvlBean path);

    List<VSiteOrgCount> siteCount(PerLvlBean p);

    List<TDocOrgCount> docCount(PerLvlBean path);

    List<ArchivesCount> archivesCount(PerLvlBean p);

    List<RelationCount> relationCount(PerLvlBean p);
}
