package com.jsdz.admin.security.dao.impl;

import java.sql.SQLException;

/**
 *
 * @类名: ActionDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-03 19:35:10
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.ActionDao;
import com.jsdz.admin.security.model.Action;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class ActionDaoImpl extends GenericEntityDaoHibernateImpl<Action,Long> implements ActionDao{

	//新增
	public void addAction(Action action) {
		
		
		this.saveOrUpdate(action);
		reorderIndex();
	}

	//删除
	public void deleteAction(Action action) {
		this.delete(action);
		reorderIndex();
	}

	//修改
	public void updateAction(Action action) {
		this.saveOrUpdate(action);
	}

	//单个查询
	public Action findActionByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		};
		List<Action> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//列表查询
	public List<Action> findActionsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<Action> ops = this.findByNamedParam(queryStr,paramNames,values);
		return ops;
	}
	
	//查询所有
	public List<Action> findALlActions(String queryStr){
		List<Action> ops = this.find(queryStr);
		return ops;
	}

	//分页查询
	public Page<Action> findActionsOnPage(Page<Action> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<Action>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	public List<Action> findAll() {
		return this.find("from Action a order by index");
	}
	
	//重排Index,index须按顺序递增，中间不能跳
	private int reorderIndex(){
		int index = 0;
		boolean updateflag = false;
		List<Action> psList = findAll();
		if (null != psList){
			for (Action ps : psList){
				index ++;
				if (ps.getIndex() != index){
					ps.setIndex(index);
					updateflag = true;
				}
			}
			if (updateflag==true) {
				//若有更改则重新设置index
				this.batchUpdate(psList);
			}
		}
		return index;
	}		

	//
	public void Count(final String hql,final String[] paramNames,final Object[] values){
			Long data = getHibernateTemplate().execute(new HibernateCallback<Long>() {      	 
    	    public Long doInHibernate(Session session)throws HibernateException, SQLException {  
					Query queryObject = session.createQuery("slect ");
					for (int i=0;i<values.length;i++){
						queryObject.setParameter(paramNames[i], values[i]);
					}
					queryObject.executeUpdate();
					return null;
	    	    }  
			});
	}
}
