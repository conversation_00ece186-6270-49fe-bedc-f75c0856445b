package com.jsdz.admin.security.model;
/**
 * 
 * @类名: SystemMenu
 * @说明: 系统菜单
 *
 * @author: kenny
 * @Date	2017年4月26日上午9:48:15
 * 修改记录：
 *
 * @see
 */

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

public class SystemMenu implements Serializable{
	private static final long serialVersionUID = -4841982949044751096L;
	private Long id;
	private String menuName;//菜单描述
	private String menuUrl; //url
	private Integer menuType; //类型 1 菜单 2按钮 3资源
	private SystemMenu parentMenu; //上级菜单
	//private Set<SystemMenu> subMenu = new HashSet<SystemMenu>(); //下级菜单
	private Integer isShow; //是否显示 0/1
	private Integer sort;   //顺序号
	private String imageUrl;  //显示图片
	private Date createTime;//创建时间
	
	public SystemMenu(){
		super();
	}
	public SystemMenu(String menuName,String menuUrl,Integer sort){
		this.menuName=menuName;
		this.menuUrl=menuUrl;
		this.isShow=1;
		this.sort=sort;
		this.createTime = new Date();
	}


	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getMenuName() {
		return menuName;
	}
	public void setMenuName(String menuName) {
		this.menuName = menuName;
	}
	public String getMenuUrl() {
		return menuUrl;
	}
	public void setMenuUrl(String menuUrl) {
		this.menuUrl = menuUrl;
	}
	public Integer getMenuType() {
		return menuType;
	}
	public void setMenuType(Integer menuType) {
		this.menuType = menuType;
	}
	public SystemMenu getParentMenu() {
		return parentMenu;
	}
	public void setParentMenu(SystemMenu parentMenu) {
		this.parentMenu = parentMenu;
	}
	public Integer getIsShow() {
		return isShow;
	}
	public void setIsShow(Integer isShow) {
		this.isShow = isShow;
	}
	public Integer getSort() {
		return sort;
	}
	public void setSort(Integer sort) {
		this.sort = sort;
	}
	public String getImageUrl() {
		return imageUrl;
	}
	public void setImageUrl(String imageUrl) {
		this.imageUrl = imageUrl;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	
	
}
