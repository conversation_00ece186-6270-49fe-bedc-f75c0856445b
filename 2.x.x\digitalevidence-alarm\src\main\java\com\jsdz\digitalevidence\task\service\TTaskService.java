package com.jsdz.digitalevidence.task.service;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.task.model.TTask;
import com.jsdz.digitalevidence.task.bean.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

/**
 * 任务管理表(TTask)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-25 16:37:32
 */
public interface TTaskService {

    /**
     * 新增任务
     * @param task
     * @param files
     * @return
     */
    AjaxResult add(TTask task, MultipartFile[] files);

    /**
     * 分页查询
     * @param pageBean
     * @return
     */
    Page<TTask> pageQuery(TaskPageBean pageBean);

    /**
     * 发布任务
     *
     * @param taskIds
     * @param sendType
     * @param devIds
     * @param perLvlBean
     * @return
     */
    AjaxResult send(Long[] taskIds, Integer sendType, Long[] devIds, PerLvlBean perLvlBean);

    /**
     * 接收到任务后给平台响应
     * @param taskId
     * @param code
     * @return
     */
    AjaxResult received(Long taskId, String code);

    /**
     * 任务反馈
     * @param dto
     * @return
     */
    AjaxResult feedback(TaskFeedbackDto dto);

    /**
     * 任务审批
     *
     * @param processId
     * @param isApprove
     * @param advice
     * @param operator
     * @return
     */
    AjaxResult examine(Long processId, int isApprove, String advice, Operator operator);

    /**
     * 任务延期
     * @param processId 分发任务id
     * @param endIme    延期时间
     * @return
     */
    AjaxResult delay(Long processId, Date endIme);

    /**
     * 任务转交
     * @param processId 分发任务id
     * @param deviceId 设备id
     * @return
     */
    AjaxResult transmit(Long processId, Long deviceId);

    /**
     * 执法仪任务列表查询
     * @param dto
     * @return
     */
    AjaxResult apiPage(TaskApiDto dto);

    /**
     * 根据任务分发id获取任务详情
     * @param id
     * @return
     */
    AjaxResult getProcessInfo(Long id);

    /**
     * 根据任务id获取任务详情
     * @param taskId
     * @param code
     * @return
     */
    AjaxResult getTaskInfo(Long taskId, String code);

    /**
     * 任务分发列表查询
     * @param pageBean
     * @return
     */
    Page<TaskProcessVo> processPage(TaskProcessPageBean pageBean);

    /**
     * 文档与任务关联
     * @param dto
     * @return
     */
    AjaxResult feedback(FeedbackDto dto);

    /**
     * 查询任务结果
     * @param pageBean
     * @return
     */
    Page<TaskDocVo> taskDocPage(TaskPageBean pageBean);

    /**
     * 任务id
     * @param ids
     */
    void deleteByIds(Long[] ids);

    /**
     * 待办已办分页查询
     * @param pageBean
     * @return
     */
    Page<AffairVo> affairPage(TaskPageBean pageBean);

    /**
     * 查询事件详情
     *
     * @param id      事件id
     * @param type    事件类型
     * @param permLvl 用户权限
     * @return
     */
    AffairVo affairInfo(Long id, Integer type, PerLvlBean permLvl);

    /**
     * 人脸抓拍反馈带文件
     *
     * @param file 人脸
     * @param sceneFile 场景
     * @param feedbackDto 反馈
     * @return
     */
    AjaxResult feedbackWithFile(MultipartFile file, MultipartFile sceneFile, FeedbackDto feedbackDto);
}

