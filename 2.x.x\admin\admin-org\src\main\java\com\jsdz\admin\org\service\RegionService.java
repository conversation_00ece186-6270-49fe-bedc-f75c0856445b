package com.jsdz.admin.org.service;

/**
 * 
 * @类名: RegionService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-08 17:08:54
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import com.jsdz.admin.org.bean.OrganizationBean;
import com.jsdz.admin.org.bean.RegionBean;
import com.jsdz.admin.org.model.Region;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.core.TreeBean;

public interface RegionService {

	//新增
	public AjaxResult addRegion(Region region);

	//修改
	public AjaxResult updateRegion(Region region);

	//删除
	public AjaxResult deleteRegion(Region region);

	//按id查询
	public Region locateRegionById(Long id);

	//单个查询
	public Region findRegionByParam(String queryStr,String[] paramNames,Object[] values);

	//列表查询
	public List<Region> findRegionsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Region> findRegionsOnPage(Page<Region> page, String queryStr,String[] paramNames,Object[] values);
	
	//查询所有的区域并生成tree
	public List<TreeBean> findRegionOnTree();
	
	//分页查询数据给前端easyUi datagrid使用
	public Page<RegionBean> findReginOnPageForSy(Page<RegionBean> page, String[] paramNames, Object[] values);


}

