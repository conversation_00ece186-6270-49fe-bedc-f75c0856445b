package com.jsdz.digitalevidence.analysis.openapi.service.impl;

/**
 * 
 * @类名: StationInfoServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-06-09 16:03:51
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.digitalevidence.analysis.openapi.dao.StationInfoDao;
import com.jsdz.digitalevidence.analysis.openapi.model.StationInfo;
import com.jsdz.digitalevidence.analysis.openapi.service.StationInfoService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("StationInfoServiceImpl")
public class StationInfoServiceImpl implements StationInfoService {

	@Autowired
	private StationInfoDao stationInfoDao;

	//新增
	public AjaxResult addStationInfo(StationInfo stationInfo) {
		AjaxResult result = new AjaxResult();
		stationInfoDao.addStationInfo(stationInfo);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateStationInfo(StationInfo stationInfo) {
		AjaxResult result = new AjaxResult();
		stationInfoDao.updateStationInfo(stationInfo);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteStationInfo(StationInfo stationInfo) {
		AjaxResult result = new AjaxResult();
		stationInfoDao.deleteStationInfo(stationInfo);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public StationInfo findStationInfoById(Long id){

		return stationInfoDao.findStationInfoById(id);
	}

	//按 id 查询
	public StationInfo locateStationInfoById(Long id) {
		return stationInfoDao.locateStationInfoById(id);
	}

	//单个查询
	public StationInfo findStationInfoByParam(String queryStr, String[] paramNames, Object[] values) {
		return stationInfoDao.findStationInfoByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<StationInfo> findAllStationInfos() {
		return stationInfoDao.findAllStationInfos();
	}

	//列表查询
	public List<StationInfo> findStationInfosByParam(String queryStr, String[] paramNames, Object[] values) {
		return stationInfoDao.findStationInfosByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<StationInfo> findStationInfosOnPage(Page<StationInfo> page, String queryStr, String[] paramNames, Object[] values) {
		Page<StationInfo> pos = stationInfoDao.findStationInfosOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
