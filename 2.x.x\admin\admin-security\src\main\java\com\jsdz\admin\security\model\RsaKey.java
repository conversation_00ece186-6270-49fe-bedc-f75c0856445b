package com.jsdz.admin.security.model;

import java.util.Date;

/**
 * 
 * @类名: RsaKey
 * @说明: 保存公钥和私钥 
 *
 * @author: kenny
 * @Date	2017年6月2日上午11:28:32
 * 修改记录：
 *
 * @see
 */
public class RsaKey {
   private Long id;
   private Integer uniqueKey; 
   private String publicKey;  //公钥
   private String privateKey;//私钥
   private Date createTime;
   private String aesKey;  //Aes密钥
   
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getPublicKey() {
		return publicKey;
	}
	public void setPublicKey(String publicKey) {
		this.publicKey = publicKey;
	}
	public String getPrivateKey() {
		return privateKey;
	}
	public void setPrivateKey(String privateKey) {
		this.privateKey = privateKey;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Integer getUniqueKey() {
		return uniqueKey;
	}
	public void setUniqueKey(Integer uniqueKey) {
		this.uniqueKey = uniqueKey;
	}
	public String getAesKey() {
		return aesKey;
	}
	public void setAesKey(String aesKey) {
		this.aesKey = aesKey;
	} 
   
   
}
