package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.alarm110.bean.AlarmStamp;
import com.jsdz.digitalevidence.alarm.alarm110.bean.docAlarmCaseInfoBean;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmPoliceDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmReleDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmStampDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlaBiDoc;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmPolice;
import com.jsdz.digitalevidence.alarm.alarm110.service.AlarmReleService;
import com.jsdz.digitalevidence.cache.utils.SysSource;
import com.jsdz.digitalevidence.document.dao.DocumentDao;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.reportquery.ReportQueryDao;

/**
 * 
 * @类名: AlarmReleServiceImpl
 * @说明: 警情和视频自动关联(吉林)
 *
 * <AUTHOR>
 * @Date	 2018年4月23日 下午2:02:33
 * 修改记录：
 *
 * @see
 */
@Service(value="AlarmReleServiceImpl")
public class AlarmReleServiceImpl implements AlarmReleService{
	
	private final Logger logger = Logger.getLogger(this.getClass());
	
	@Autowired
	private DocumentDao documentDao;

	@Autowired
	private AlarmStampDao alarmStampDao;

	@Autowired
	private AlarmPoliceDao alarmPoliceDao;

	@Autowired
	private AlarmReleDao alarmReleDao;
	
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao drqDao;

	// 警情和视频关联，从而案件和视频关联
	public void alaBouVidBack() {
		
		// 计算开始时间
		long startTime = System.currentTimeMillis();
//		System.out.println("自动关联开始运行了==========================");
		// 保存的条件
		String stamp = " from AlarmStamp  ala order by ala.stampTime asc";
		AlarmStamp alarmStamp01 = alarmStampDao.findAlarmStamp(stamp);
		
		if(alarmStamp01 == null || alarmStamp01.getAlaId() == null || alarmStamp01.getDocId() == null){
			alarmStamp01 = new AlarmStamp(null,0l,0l,0l,null);
		}
		
		// 查询带条件的视频信息
		String queryStr01 = "from Document doc left join fetch doc.police emp "
							+ "where doc.id > :docId  and doc.cate < 3 ";
		String[] paramNames01 = new String[]{"docId"};
		Object[] values01 = new Object[]{alarmStamp01.getDocId()};
		List<Document> documents = documentDao.findDocumentsByCondition(queryStr01, paramNames01, values01);
		// 查询带条件的警情信息
		String queryStr02 = "from AlarmPolice ap left join fetch ap.alarmInfo ai "
						+ " left join fetch ap.police po "
						+ " left join fetch ai.reveiceOrg org "
						+ "where  ai.id > :alaId order by ai.alarmTime asc " ;
		String[] paramNames02 = new String[]{"alaId"};
		Object[] values02 = new Object[]{alarmStamp01.getAlaId()};
		List<AlarmPolice> alarmPolices = alarmPoliceDao.findAlarmPolicesByCondition(queryStr02, paramNames02, values02);
		
		// 日期格式
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
		List<AlaBiDoc> list = new ArrayList<>();
		AlaBiDoc alaBiDoc = null ;
		AlarmInfo alarmInfo=null;
		for (Document document : documents) {
			for (AlarmPolice alarmPolice : alarmPolices) {
				if(document.getPolice() != null && alarmPolice.getPolice() != null){
					// 判断视频信息中的工号和警情信息中的工号是否相等
					if((document.getPolice().getWorkNumber()).equals(alarmPolice.getPolice().getWorkNumber())){
//						System.out.println("来到了第1步");
						// 用来判断警情创建时间比视频创建时间早
						int time1 = (document.getCreateTime()).compareTo(alarmPolice.getAlarmInfo().getAlarmTime());
						if(time1 > 0){
							alarmInfo = alarmPolice.getAlarmInfo();
//							System.out.println("来到了第2步");
						}else{
							break;
						}
					}
				}
			}
			// 用来保存AlaBiDoc 信息
			if(alarmInfo != null){
				alaBiDoc = new AlaBiDoc();
				alaBiDoc.setAlarmCode(alarmInfo.getAlarmCode());
				alaBiDoc.setCaseCode(alarmInfo.getCaseId());
				alaBiDoc.setDocId(String.valueOf(document.getId()));
				switch (document.getCate().getName()) {
				case "图片":
					alaBiDoc.setType("0");
					break;
				case "音频":
					alaBiDoc.setType("1");
					break;
				case "视频":
					alaBiDoc.setType("2");
					break;
				case "日志":
					alaBiDoc.setType("3");
					break;
				}
//				alaBiDoc.setType(String.valueOf(document.getCate().getIndex()));
//				System.out.println("==========="+alaBiDoc.getType());
				// 资料创建时间
				alaBiDoc.setCollTime(formatter.format(document.getCreateTime()).toString());
				// 上传时间
				alaBiDoc.setUplTime(formatter.format(document.getUploadTime()).toString());
				// 设置关联时间
				alaBiDoc.setRelationTime(formatter.format(new Date()).toString());
/*				if(alarmInfo.getReveiceOrg() != null){
					alaBiDoc.setOrgCode(alarmInfo.getReveiceOrg().getOrgCode());
				}
*///				System.out.println("日期时间为"+formatter.format(alarmInfo.getAlarmTime()).toString());
				// 保存到集合中
				list.add(alaBiDoc);
				alarmInfo = null;
			}
		}
		 
		// 插入关联数据
		for (AlaBiDoc alaBi : list) {
			// 是否有重复数据
			AlaBiDoc mutipleAlaBiDoc = alarmReleDao.findMutipleAlaBiDoc(alaBi);
			if(mutipleAlaBiDoc != null){
				// 删除重复数据
				alarmReleDao.deleteAlaBiDoc(mutipleAlaBiDoc);
			}
			// 新增数据
			alarmReleDao.addAlaBiDoc(alaBi);
		}
		// 计算结束时间
		long endTime = System.currentTimeMillis();
		System.out.println("程序运行时间："+(endTime-startTime)+"ms");
	}
	
	// 获取到锁执行目标方法
	public void executeAla(){
		try {
			 String identifier = UUID.randomUUID().toString();
			 // 获取锁值
			 Object attribute01 = SysSource.getInstance().get("AlarmReleServiceImpl_alaBouVid_resource_xyz");
			 if(attribute01 == null){
				 // 设置锁值
				 SysSource.getInstance().set("AlarmReleServiceImpl_alaBouVid_resource_xyz", identifier,3l);
			 }
			 Object attribute02 = SysSource.getInstance().get("AlarmReleServiceImpl_alaBouVid_resource_xyz");
			 if(identifier.equals(attribute02)){
				// 执行目标方法
				// alaBouVid();
				//删除锁
				 SysSource.getInstance().remove("AlarmReleServiceImpl_alaBouVid_resource_xyz");
			 }
		} catch (Exception e) {
			//删除锁
			SysSource.getInstance().remove("AlarmReleServiceImpl_alaBouVid_resource_xyz");
			logger.error("*** 错误：" + e);
		}
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public Page<docAlarmCaseInfoBean> searchdocAlarmCaseInfoBean(Page<docAlarmCaseInfoBean> page, String[] paramNames,
			Object[] values) {
		page =  drqDao.pageQueryNamedSQL(page, "searchdocAlarmCaseInfoBeanQuery", paramNames, values);
		return page;
	}
}
