#redis for spring session,spring cache
#redis model:0=none redis
#			 1=single server model
#            2=master-slave model (sentinal)
#            3=cluster model (cluster)
redis.model=0
redis.host=127.0.0.1
redis.port=6379
redis.pwd=
redis.maxIdle=8
redis.maxActive=300
redis.maxWait=100000
redis.timeout=1000
redis.maxTotal=50
redis.minIdle=0
redis.testOnBorrow=true

#cluster model
spring.redis.cluster.nodes=*************:7001,*************:7002,*************:7003
spring.redis.cluster.max-redirects=3

#sentinel model
spring.redis.redis.sentinel.masterName=mymaster  
spring.redis.redis.sentinel.password=
#the first string is master node
spring.redis.sentinel.nodes=*************:7100,*************:7101
