package com.jsdz.digitalevidence.assessment.dao.impl;

/**
 *
 * @类名: AssessmentPlanPoliceDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2019-06-21 16:28:56
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanPoliceDao;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPolice;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class AssessmentPlanPoliceDaoImpl extends GenericEntityDaoHibernateImpl<AssessmentPlanPolice,Long> implements AssessmentPlanPoliceDao{

	/** 
 	 * 新增
	 */ 
 	public void addAssessmentPlanPolice(AssessmentPlanPolice assessmentPlanPolice) {
		this.saveOrUpdate(assessmentPlanPolice);
	}

	/** 
 	 * 删除
	 */ 
 	public void deleteAssessmentPlanPolice(AssessmentPlanPolice assessmentPlanPolice) {
		this.delete(assessmentPlanPolice);
	}

	/** 
 	 * 修改
	 */ 
 	public void updateAssessmentPlanPolice(AssessmentPlanPolice assessmentPlanPolice) {
		this.merge(assessmentPlanPolice);
	}

	/** 
 	 * 按id查询(游离状态)
	 */ 
 	public AssessmentPlanPolice findAssessmentPlanPoliceById(Long id){

		final String  hql = "from AssessmentPlanPolice p where p.id = :id";
		final Long oid = id;
		AssessmentPlanPolice data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlanPolice>() {
			public AssessmentPlanPolice doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AssessmentPlanPolice> list = query.list();
				AssessmentPlanPolice rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlanPolice locateAssessmentPlanPoliceById(Long id){

		final String  hql = "from AssessmentPlanPolice p where p.id = :id";
		final Long oid = id;
		AssessmentPlanPolice data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlanPolice>() {
			public AssessmentPlanPolice doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AssessmentPlanPolice> list = query.list();
				AssessmentPlanPolice rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 单个查询(根据其它字段查询)
	 */ 
 	public AssessmentPlanPolice findAssessmentPlanPoliceByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		AssessmentPlanPolice data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlanPolice>() {
		public AssessmentPlanPolice doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<AssessmentPlanPolice> list = query.list();
			AssessmentPlanPolice rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanPolice> findAllAssessmentPlanPolices(){
		return this.find("from AssessmentPlanPolice assessmentPlanPolice ");
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanPolice> findAssessmentPlanPolicesByCondition(String queryStr,String[] paramNames,Object[] values){
		List<AssessmentPlanPolice> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanPolice> findAssessmentPlanPolicesOnPage(Page<AssessmentPlanPolice> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<AssessmentPlanPolice>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	/** 
 	 * 执行自定义的hql
	 */ 
 	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
}
