package com.jsdz.digitalevidence.analysis.openapi.model;
/**
 * 
 * @类名: StationLog
 * @说明: 执法数据采集设备日志信息实体类
 *
 * @author: kenny
 * @Date	2017年6月9日下午3:56:34
 */

import java.util.Date;
public class StationLog {

	private Long id;//
	private String gzz_xh;//执法数据采集设备产品编码
	private String rzlx;// 日志类型
	private String dxbh;//对象编号
	private Date rzrq;//日志时间(yy-MM-dd HH:mm:ss)
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getGzz_xh() {
		return gzz_xh;
	}
	public void setGzz_xh(String gzz_xh) {
		this.gzz_xh = gzz_xh;
	}
	public String getRzlx() {
		return rzlx;
	}
	public void setRzlx(String rzlx) {
		this.rzlx = rzlx;
	}
	public String getDxbh() {
		return dxbh;
	}
	public void setDxbh(String dxbh) {
		this.dxbh = dxbh;
	}
	public Date getRzrq() {
		return rzrq;
	}
	public void setRzrq(Date rzrq) {
		this.rzrq = rzrq;
	}
}
