package com.jsdz.digitalevidence.assessment.dao.impl;

/**
 *
 * @类名: AssessmentPlanDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2019-06-19 15:40:26
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPlanDao;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlan;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class AssessmentPlanDaoImpl extends GenericEntityDaoHibernateImpl<AssessmentPlan,Long> implements AssessmentPlanDao{

	/** 
 	 * 新增
	 */ 
 	public void addAssessmentPlan(AssessmentPlan assessmentPlan) {
		this.saveOrUpdate(assessmentPlan);
	}

	/** 
 	 * 删除
	 */ 
 	public void deleteAssessmentPlan(AssessmentPlan assessmentPlan) {
		this.delete(assessmentPlan);
	}

	/** 
 	 * 修改
	 */ 
 	public void updateAssessmentPlan(AssessmentPlan assessmentPlan) {
		this.merge(assessmentPlan);
	}

	/** 
 	 * 按id查询(游离状态)
	 */ 
 	public AssessmentPlan findAssessmentPlanById(Long id){

		final String  hql = "from AssessmentPlan a where a.id = :id";
		final Long oid = id;
		AssessmentPlan data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlan>() {
			public AssessmentPlan doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AssessmentPlan> list = query.list();
				AssessmentPlan rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlan locateAssessmentPlanById(Long id){

		final String  hql = "from AssessmentPlan a where a.id = :id";
		final Long oid = id;
		AssessmentPlan data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlan>() {
			public AssessmentPlan doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AssessmentPlan> list = query.list();
				AssessmentPlan rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 单个查询(根据其它字段查询)
	 */ 
 	public AssessmentPlan findAssessmentPlanByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		AssessmentPlan data = getHibernateTemplate().execute(new HibernateCallback<AssessmentPlan>() {
		public AssessmentPlan doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<AssessmentPlan> list = query.list();
			AssessmentPlan rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlan> findAllAssessmentPlans(){
		return this.find("from AssessmentPlan assessmentPlan ");
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlan> findAssessmentPlansByCondition(String queryStr,String[] paramNames,Object[] values){
		List<AssessmentPlan> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlan> findAssessmentPlansOnPage(Page<AssessmentPlan> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<AssessmentPlan>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	/** 
 	 * 执行自定义的hql
	 */ 
 	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
}
