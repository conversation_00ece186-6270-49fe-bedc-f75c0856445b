package com.jsdz.digitalevidence.statistics.bean.param;

import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.model.VEquipmentOnlineStatisticsModel;

public class VEquipmentOnlineStatisticsParam {

    Page<VEquipmentOnlineStatisticsModel> page;

    private String orgPath;

    private String sortName;

    private String sortOrder;
    //是否包含下级 （0不，1包含）
    private String includeSub;



    public VEquipmentOnlineStatisticsParam(){
        page = new Page<VEquipmentOnlineStatisticsModel>();
        page.setOffset(0);
        page.setPageSize(20);
    }

    public Page<VEquipmentOnlineStatisticsModel> getPage() {
        return page;
    }

    public void setPage(Page<VEquipmentOnlineStatisticsModel> page) {
        this.page = page;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public String getSortName() {
        return sortName;
    }

    public void setSortName(String sortName) {
        this.sortName = sortName;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(String includeSub) {
        this.includeSub = includeSub;
    }
}
