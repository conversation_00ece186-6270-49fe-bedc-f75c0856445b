package com.jsdz.digitalevidence.archives.dao;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.archives.bean.ArchivesDataBean;
import com.jsdz.digitalevidence.archives.bean.param.ArchivesParam;

import java.util.List;


/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
public interface ArchivesDataDao extends GenericORMEntityDAO<ArchivesDataBean,Long> {
    ArchivesDataBean archivesById(Long archivesDataBean);

    boolean getArchivesCode(String archivesCode);

    List<ArchivesDataBean> getArchivesByCode(String archivesCode);

    // Page<ArchivesDataBean> findArchivesByPage(ArchivesParam archivesParam, PerLvlBean p);
}
