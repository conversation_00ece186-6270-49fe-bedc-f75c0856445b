package com.jsdz.admin.security.model;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 
 * @类名: Resource
 * @说明: 资源类
 *
 * @author: kenny
 * @Date	2017年4月26日上午9:29:28
 * 修改记录：
 *
 * @see
 */
public class Resource implements Serializable {
	private static final long serialVersionUID = -7662197911926078220L;
	private Long id;
	private String resourceName;  //资源名称
	private String resourceUrl;   //资源URL
	@JsonIgnore
	private ResourcesCategory resourcesCategory; //资源类型
	private Integer resourceIsUsed; //是否可用0/1
	private Integer resourceSort;   //顺序
	private String resourceImgUrl;  //显示图片
	private Date createTime;//创建时间
	
	public Resource(){
		super();
	}
	public Resource(String resourceName,String resourceUrl,Integer resourceIsUsed,Integer resourceSort){
		this.resourceName=resourceName;
		this.resourceUrl=resourceUrl;
		this.resourceIsUsed=resourceIsUsed;
		this.resourceSort=resourceSort;
		this.createTime = new Date();
		this.resourceIsUsed = 1;
	}

	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getResourceName() {
		return resourceName;
	}
	public void setResourceName(String resourceName) {
		this.resourceName = resourceName;
	}
	public String getResourceUrl() {
		return resourceUrl;
	}
	public void setResourceUrl(String resourceUrl) {
		this.resourceUrl = resourceUrl;
	}

	public ResourcesCategory getResourcesCategory() {
		return resourcesCategory;
	}
	public void setResourcesCategory(ResourcesCategory resourcesCategory) {
		this.resourcesCategory = resourcesCategory;
	}
	public Integer getResourceIsUsed() {
		return resourceIsUsed;
	}
	public void setResourceIsUsed(Integer resourceIsUsed) {
		this.resourceIsUsed = resourceIsUsed;
	}
	public Integer getResourceSort() {
		return resourceSort;
	}
	public void setResourceSort(Integer resourceSort) {
		this.resourceSort = resourceSort;
	}
	public String getResourceImgUrl() {
		return resourceImgUrl;
	}
	public void setResourceImgUrl(String resourceImgUrl) {
		this.resourceImgUrl = resourceImgUrl;
	}
	
	
}
