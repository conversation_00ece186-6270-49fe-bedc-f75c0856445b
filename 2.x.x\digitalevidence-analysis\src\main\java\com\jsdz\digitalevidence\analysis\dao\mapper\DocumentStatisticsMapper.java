package com.jsdz.digitalevidence.analysis.dao.mapper;

import java.util.List;
import java.util.Map;

public interface DocumentStatisticsMapper {
	/** 统计某日，某月的 文件上传数量，文件总大小，总视频 */
	Map<String,Object> findStatistics(Map<String,Object> param);
	
	/** 线型图，每日每月的文件具传数量*/
	List<Map<String,Object>> findStatisticsOfHours(Map<String,Object> param);
	List<Map<String,Object>> findStatisticsOfDays(Map<String,Object> param);
	List<Map<String,Object>> findStatisticsOfOrgs(Map<String,Object> param);
	/** 饼图，按部门*/
	List<Map<String,Object>> findStatisticsOfOrg(Map<String,Object> param);
	/** 饼图，按类别*/
	List<Map<String,Object>> findStatisticsOfCate(Map<String,Object> param);
	
	
	
	
}
