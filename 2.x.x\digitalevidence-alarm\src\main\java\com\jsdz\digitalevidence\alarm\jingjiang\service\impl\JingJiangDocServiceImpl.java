package com.jsdz.digitalevidence.alarm.jingjiang.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.jingjiang.dao.JingJiangDocDao;
import com.jsdz.digitalevidence.alarm.jingjiang.model.JingJiangDoc;
import com.jsdz.digitalevidence.alarm.jingjiang.service.JingJiangDocService;
import com.jsdz.digitalevidence.document.dao.DocumentDao;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.digitalevidence.document.model.DocumentCate;

/**
 * 
 * @类名: JingJiangDocServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年10月25日下午4:43:07
 * 修改记录：
 *
 * @see
 */
//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("JingJiangDocServiceImpl")

public class JingJiangDocServiceImpl implements JingJiangDocService{
	
	@Autowired
	private JingJiangDocDao jingJiangDocDao;
	@Autowired
	private DocumentDao documentDao;
	@Value("${document.storage.rootpath}")
	private String rootPath;
	@Value("${fileserver.rootpath}")
	private String fsRootPath;
	
	
	public static final Logger logger = Logger.getLogger(JingJiangDocServiceImpl.class);
	//新增
	public AjaxResult addJingJiangDoc(JingJiangDoc jingJiangDoc) {
		AjaxResult result = new AjaxResult();
		jingJiangDocDao.addJingJiangDoc(jingJiangDoc);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateJingJiangDoc(JingJiangDoc jingJiangDoc) {
		AjaxResult result = new AjaxResult();
		jingJiangDocDao.updateJingJiangDoc(jingJiangDoc);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteJingJiangDoc(JingJiangDoc jingJiangDoc) {
		AjaxResult result = new AjaxResult();
		jingJiangDocDao.deleteJingJiangDoc(jingJiangDoc);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public JingJiangDoc findJingJiangDocById(Long id){

		return jingJiangDocDao.findJingJiangDocById(id);
	}

	//按 id 查询
	public JingJiangDoc locateJingJiangDocById(Long id) {
		return jingJiangDocDao.locateJingJiangDocById(id);
	}

	//单个查询
	public JingJiangDoc findJingJiangDocByParam(String queryStr, String[] paramNames, Object[] values) {
		return jingJiangDocDao.findJingJiangDocByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<JingJiangDoc> findAllJingJiangDocs() {
		return jingJiangDocDao.findAllJingJiangDocs();
	}

	//列表查询
	public List<JingJiangDoc> findJingJiangDocsByParam(String queryStr, String[] paramNames, Object[] values) {
		return jingJiangDocDao.findJingJiangDocsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<JingJiangDoc> findJingJiangDocsOnPage(Page<JingJiangDoc> page, String queryStr, String[] paramNames, Object[] values) {
		Page<JingJiangDoc> pos = jingJiangDocDao.findJingJiangDocsOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
	//保存到靖江数据交换表
	public void toJingJiang(Long id){
		String queryStr = " from Document d "
				+ " left join fetch d.police p "
				+ " left join fetch d.site s "
				+ " left join fetch d.organization o "
				+ " where d.id = :id";
		try{
/*			Document doc = documentDao.findDocumentByCondition(queryStr, new String[]{"id"}, new Object[]{id});


			if (doc == null){
				logger.error("*** toJingJiang():不存在的Document id=" + id);
				return;
			}
			if (doc.getCate() != DocumentCate.VEDIO){
				logger.error("*** toJingJiang():非视频数据 id=" + id);
				return;
			}
			//取播放地址
			String mediaUrl = null;
			//分布式存储
			if (doc.getStorageType() == 2){
				String uri = doc.getUri();
				uri = uri.replace('\\', '/');
				mediaUrl =  doc.getSite().getHttp() + "/" + uri + doc.getName();
			}else{ //中心服务器存储
				String uri = doc.getUri();
				uri = uri.replace('\\', '/');
				mediaUrl =  this.fsRootPath+uri+doc.getName();
			}
			
			JingJiangDoc j = this.jingJiangDocDao.findJingJiangDocByCondition(
					" from JingJiangDoc j "
					+ " where j.docId=:docid", 
					new String[]{"docid"},new Object[]{id});
			if (j != null){
				logger.error("*** toJingJiang():重复的数据 docid=" + id);
				return;
			}
			
			j = new JingJiangDoc();
			j.setPoliceCode(doc.getPolice()==null?null:doc.getPolice().getWorkNumber());
			j.setPoliceName(doc.getPolice()==null?null:doc.getPolice().getName());
			j.setCaptureTime(doc.getCreateTime());
			j.setUploadTime(doc.getUploadTime());
			if (doc.getSite() != null){
				if (doc.getSite().getOrganization() != null)
					j.setOrgCode(doc.getSite().getOrganization().getOrgCode());
			}
			j.setFileSize(doc.getSize());
			j.setDocId(doc.getId());
			j.setCreateTime(new Date());
			j.setReadFlag(0);
			j.setMediaUrl(mediaUrl);
			j.setAlarmCode(doc.getAlarmCode());
			this.jingJiangDocDao.addJingJiangDoc(j);
		*/
		}catch(Exception e){
			logger.error("*** 错误toJingJiang():" + e);
			return;
		}
	}

	
}
