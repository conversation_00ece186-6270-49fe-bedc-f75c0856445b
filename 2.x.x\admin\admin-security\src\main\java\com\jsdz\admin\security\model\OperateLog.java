package com.jsdz.admin.security.model;
/**
 * 
 * @类名: OperateLog
 * @说明: 操作日志
 *
 * @author: kenny
 * @Date	2017年4月28日上午10:26:32
 * 修改记录：
 *
 * @see
 */

import com.jsdz.admin.org.model.Organization;

import java.io.Serializable;
import java.util.Date;


public class OperateLog implements Serializable{
	private static final long serialVersionUID = 8744028682258532275L;
	

	private Long id;
	private String logContent;
	//@JsonIgnore
	//private Operator logCreator;
	private String loginName;
	private String workNumber;
	private String name;
	private Date logCreateTime;
	private Integer logType;
	private String queryBeginTime;
	private String queryEndTime;
	private String logTypeStr;
	private String fromIp;
	private Long operatorId;
	private Organization organization;

	
	public OperateLog(){
		super();
	}
	public OperateLog(String logContent,Operator logCreator){
		this.logContent = logContent;
		if (logCreator != null){
			this.loginName = logCreator.getLoginName();
			this.setOperatorId(logCreator.getId());
			if (logCreator.getEmployees() != null){
				this.workNumber = logCreator.getEmployees().getWorkNumber();
				this.name = logCreator.getEmployees().getName();
				Organization organization = logCreator.getEmployees().getOrganization();
				this.setOrganization(organization);
			}
		}
		
		this.logCreateTime = new Date();
	}

	public String getLogTypeStr() {
		if(logType !=null){
			if(logType==1){
				return"登录";
			}else if(logType==2){
				return"注销";
			}else if(logType==3){
				return"新增";
			}else if(logType==4){
				return"删除";
			}else if(logType==5){
				return"修改";
			}else if(logType==6){
				return"查询";
			}else if(logType==7){
				return"导出";
			}else if(logType==8){
				return"下载";
			}else if(logType==9){
				return"参数配置";
			}else if(logType==10){
				return"时间校正";
			}else if(logType==13){
				return"5G执法仪拉流";
			}else if(logType==14){
				return"5G执法仪日志";
			}else if(logType==15){
				return"情指行接警日志";
			}else if(logType==16){
				return"播放";
			}

		}
		return logTypeStr;
	}
	public void setLogTypeStr(String logTypeStr) {
		this.logTypeStr = logTypeStr;
	}
	public String getQueryBeginTime() {
		return queryBeginTime;
	}
	public void setQueryBeginTime(String queryBeginTime) {
		this.queryBeginTime = queryBeginTime;
	}
	public String getQueryEndTime() {
		return queryEndTime;
	}
	public void setQueryEndTime(String queryEndTime) {
		this.queryEndTime = queryEndTime;
	}
	public String getLogContent() {
		return logContent;
	}
	public void setLogContent(String logContent) {
		this.logContent = logContent;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	public Integer getLogType() {
		return logType;
	}
	public void setLogType(Integer logType) {
		this.logType = logType;
	}
	public Date getLogCreateTime() {
		return logCreateTime;
	}
	public void setLogCreateTime(Date logCreateTime) {
		this.logCreateTime = logCreateTime;
	}
	public String getFromIp() {
		return fromIp;
	}
	public void setFromIp(String fromIp) {
		this.fromIp = fromIp;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public String getWorkNumber() {
		return workNumber;
	}
	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public Long getOperatorId() {
		return operatorId;
	}

	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}

	public Organization getOrganization() {
		return organization;
	}

	public void setOrganization(Organization organization) {
		this.organization = organization;
	}
}
