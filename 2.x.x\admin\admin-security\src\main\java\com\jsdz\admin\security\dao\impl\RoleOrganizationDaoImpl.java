package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: RoleOrganizationDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2018-05-15 16:42:50
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.RoleOrganizationDao;
import com.jsdz.admin.security.model.RoleOrganization;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class RoleOrganizationDaoImpl extends GenericEntityDaoHibernateImpl<RoleOrganization,Long> implements RoleOrganizationDao{

	//新增
	public void addRoleOrganization(RoleOrganization roleOrganization) {
		this.saveOrUpdate(roleOrganization);
	}

	//删除
	public void deleteRoleOrganization(RoleOrganization roleOrganization) {
		this.delete(roleOrganization);
	}

	//修改
	public void updateRoleOrganization(RoleOrganization roleOrganization) {
		this.merge(roleOrganization);
	}

	//按id查询(游离状态)
	public RoleOrganization findRoleOrganizationById(Long id){

		final String  hql = "from RoleOrganization r where r.id = :id";
		final Long oid = id;
		RoleOrganization data = getHibernateTemplate().execute(new HibernateCallback<RoleOrganization>() {
			public RoleOrganization doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<RoleOrganization> list = query.list();
				RoleOrganization rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public RoleOrganization locateRoleOrganizationById(Long id){

		final String  hql = "from RoleOrganization r where r.id = :id";
		final Long oid = id;
		RoleOrganization data = getHibernateTemplate().execute(new HibernateCallback<RoleOrganization>() {
			public RoleOrganization doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<RoleOrganization> list = query.list();
				RoleOrganization rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public RoleOrganization findRoleOrganizationByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		RoleOrganization data = getHibernateTemplate().execute(new HibernateCallback<RoleOrganization>() {
		public RoleOrganization doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<RoleOrganization> list = query.list();
			RoleOrganization rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<RoleOrganization> findAllRoleOrganizations(){
		return this.find("from RoleOrganization roleOrganization ");
	}

	//列表查询
	public List<RoleOrganization> findRoleOrganizationsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<RoleOrganization> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<RoleOrganization> findRoleOrganizationsOnPage(Page<RoleOrganization> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<RoleOrganization>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//执行自定义的hql
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
	
	//取权限单位
	@Override
	public List<Object> getRoleOrgPath(Long userId){
		final String sql = 
				" select path from "
				+ " ("
				+ " select org_id from admin_roles_organization t "
				+ " left join admin_operatorroles t1 on t.role_id = t1.roleId "
				+ " where t1.operatorId = :id "
				+ " group by org_id "
				+ " )t "
				+ " left join admin_organization t1 on t.org_id = t1.id ";
		
		List<Object> data = getHibernateTemplate().execute(new HibernateCallback<List<Object>>() {
			@SuppressWarnings("unchecked")
			@Override
			public List<Object> doInHibernate(Session session) throws HibernateException, SQLException {
				// 获取查询对象
				Query queryObject = session.createSQLQuery(sql);
				// 置入参数
				applyNamedParameterToQuery(queryObject, "id", userId);
				return queryObject.list();
			}
		});
		
		return data;
		
	}
}
