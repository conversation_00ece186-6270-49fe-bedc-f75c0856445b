package com.jsdz.admin.security.service;

/**
 * 
 * @类名: DictionaryValuesService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-10-31 11:44:06
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.DictionaryValues;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface DictionaryValuesService {

	//新增
	public AjaxResult addDictionaryValues(DictionaryValues dictionaryValues);

	//修改
	public AjaxResult updateDictionaryValues(DictionaryValues dictionaryValues);

	//删除
	public AjaxResult deleteDictionaryValues(DictionaryValues dictionaryValues);

	//按id查询,结果是游离状态的数据
	public DictionaryValues findDictionaryValuesById(Long id);

	//按id查询
	public DictionaryValues locateDictionaryValuesById(Long id);

	//单个查询
	public DictionaryValues findDictionaryValuesByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DictionaryValues> findAllDictionaryValuess();

	//列表查询
	public List<DictionaryValues> findDictionaryValuessByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DictionaryValues> findDictionaryValuessOnPage(Page<DictionaryValues> page, String queryStr,String[] paramNames,Object[] values);

}

