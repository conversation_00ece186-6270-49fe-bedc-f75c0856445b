package com.jsdz.admin.security.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class JSDateFormatUtils {

    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_FORMAT_SLASH = "yyyy/MM/dd";
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_HOUR_FORMAT = "yyyy-MM-dd HH:mm";
    
    
    /**
     * 根据特定format格式化Date
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Date date, String pattern){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(date);
//    	DateTimeFormatter dtf = DateTimeFormatter.ofPattern(pattern);
//    	LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
//    	String dateStr = localDateTime.format(dtf);
//        return dateStr;
    }

    /**
     * 根据特定的格式解析Date
     * @param dateStr
     * @param pattern
     * @return
     */
    public static Date parse(String dateStr, String pattern){
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
            return simpleDateFormat.parse(dateStr);
//            DateTimeFormatter dtf = DateTimeFormatter.ofPattern(pattern);
//        	LocalDateTime localDateTime = LocalDateTime.parse(dateStr, dtf);
//        	Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
//            return date;
        } catch (Exception e) {
            throw new RuntimeException("时间格式错误, 请输入正确时间格式："+pattern);
        }
    }

    /**
     * 时间格式化为：yyyy-MM-dd HH:mm:ss
     * @param date
     * @return
     */
    public static String formatDateTime(Date date){
        return format(date, DATE_TIME_FORMAT);
    }

    /**
     * 时间格式化为：yyyy-MM-dd yyyy/MM/dd
     * @param date
     * @return
     */
    public static String formatDateSlash(Date date){
        return format(date, DATE_FORMAT_SLASH);
    }

    /**
     * 时间格式化为：yyyy-MM-dd
     * @param date
     * @return
     */
    public static String formatDate(Date date){
        return format(date, DATE_FORMAT);
    }

    /**
     * 时间格式化为：yyyy-MM-dd HH:mm
     * @param date
     * @return
     */
    public static String formatDateHour(Date date){
        return format(date, DATE_HOUR_FORMAT);
    }

    /**
     * yyyy-MM-dd解析为日期
     * @param dateStr
     * @return
     */
    public static Date parseDate(String dateStr){
        return parse(dateStr, DATE_FORMAT);
    }

    /**
     * yyyy/MM/dd解析为日期
     * @param dateStr
     * @return
     */
    public static Date parseDateSlash(String dateStr){
        return parse(dateStr, DATE_FORMAT_SLASH);
    }

    /**
     * yyyy-MM-dd HH:MM:ss解析为时间
     * @param dateStr
     * @return
     */
    public static Date parseDateTime(String dateStr){
        return parse(dateStr, DATE_TIME_FORMAT);
    }

    /**
     * yyyy-MM-dd HH:mm解析为时间
     * @param dateStr
     * @return
     */
    public static Date parseDateHour(String dateStr){
        return parse(dateStr, DATE_HOUR_FORMAT);
    }

    public static void main(String[] args) {
      /*  String dateTime = formatDateTime(new Date());
        String date = formatDate(new Date());
        String dateSlash = formatDateSlash(new Date());

        Date date1 = parseDateTime(dateTime);
        Date date2 = parseDate(date);
        Date date3 = parseDateSlash(dateSlash);

        System.out.println(dateTime);
        System.out.println(date);
        System.out.println(dateSlash);
        System.out.println(date1);
        System.out.println(date2);
        System.out.println(date3);

        String s = formatDateTime(date1);
        System.out.println(s);
        System.out.println(parseDateTime(s));*/
    	
    	
    	Date parseDateTime = JSDateFormatUtils.parseDateTime("2023-07-03 16:24:14");
    	System.out.println(parseDateTime);
    	
    	
    	for (int i = 0; i < 10; i++) {
			Thread thread =  new Thread(()->{
				System.out.println(Thread.currentThread().getName() + "--" + JSDateFormatUtils.parseDateTime("2023-07-03 16:24:14") );
			},"thread--" + i);
    	
			thread.start();	
		}
    	
    }

}
