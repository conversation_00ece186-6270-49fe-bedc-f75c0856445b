package com.jsdz.admin.security.dao;

/**
 *
 * @类名: OperateLogDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-11 08:56:21
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.OperateLog;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface OperateLogDao extends GenericORMEntityDAO<OperateLog,Long> {

	//新增
	public void addOperateLog(OperateLog operateLog);

	//修改
	public void updateOperateLog(OperateLog operateLog);

	//删除
	public void deleteOperateLog(OperateLog operateLog);

	//按id查询
	public OperateLog locateOperateLogById(Long id);

	//单个查询
	public OperateLog findOperateLogByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<OperateLog> findAllOperateLogs();

	//列表查询
	public List<OperateLog> findOperateLogsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<OperateLog> findOperateLogsOnPage(Page<OperateLog> page,String queryStr,String[] paramNames,Object[] values);

	//按条件执行HQL（非查询）
	public Integer execHqlByCondition(final String hql,final String[] params,final Object[] values );
}

