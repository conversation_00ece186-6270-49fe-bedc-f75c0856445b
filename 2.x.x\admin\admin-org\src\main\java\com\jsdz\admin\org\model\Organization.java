package com.jsdz.admin.org.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 
 * @类名: Organization
 * @说明: 组织机构类
 *
 * @author: kenny
 * @Date	2017年4月24日
 * 修改记录：
 *
 * @see
 */
public class Organization implements Serializable{
	private Long id;
	private String orgCode;//单位编号
	private String orgName;//单位名称
	private String orgType;//单位类型(厅/局/县局/派出所);
	@JsonIgnore
	private Organization parentOrg; //上级单位
	@JsonIgnore
	private Region region;
	private String path;//路径
	
	private Date createTime; //创建日期
	private String orgNameJC; //简

	public String getOrgNameJC() {
		return orgNameJC;
	}

	public void setOrgNameJC(String orgNameJC) {
		this.orgNameJC = orgNameJC;
	}

	public Organization(){
		super();
	}
	public Organization(String orgName,String orgType){
		this.orgName=orgName;
		this.orgType=orgType;
		this.createTime=new Date();
	}
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getOrgType() {
		return orgType;
	}
	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}
	
	public Organization getParentOrg() {
		return parentOrg;
	}
	public void setParentOrg(Organization parentOrg) {
		this.parentOrg = parentOrg;
	}
/*	
	public Set<Organization> getSubOrg() {
		return subOrg;
	}
	public void setSubOrg(Set<Organization> subOrg) {
		this.subOrg = subOrg;
	}
	
*/	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	public Region getRegion() {
		return region;
	}
	public void setRegion(Region region) {
		this.region = region;
	}
	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
}
