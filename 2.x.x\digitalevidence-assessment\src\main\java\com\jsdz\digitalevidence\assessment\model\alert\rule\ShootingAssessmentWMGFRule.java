package com.jsdz.digitalevidence.assessment.model.alert.rule;

import java.util.ArrayList;
import java.util.List;

import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.ruleengine.RuleState;
import com.jsdz.ruleengine.annotation.Given;
import com.jsdz.ruleengine.annotation.Param;
import com.jsdz.ruleengine.annotation.Result;
import com.jsdz.ruleengine.annotation.Rule;
import com.jsdz.ruleengine.annotation.Then;
import com.jsdz.ruleengine.annotation.When;

/**
 * @类名: ShootingAssessmentWMGFRule
 * @说明: 拍摄考核文明规范百分比规则
                   文明规范要求百分比                
 *        
 *        
 *
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 
 * 修改记录：
 *
 * @see
 */
@Rule(name="wmgfPercent", description="文明规范要求百分比规则")
public class ShootingAssessmentWMGFRule extends ShootingAssessmentRuleBase {
	
	public static final String key = "文明规范要求百分比";

	@Param(name="wmgfPercent", description="文明规范要求百分比")
	private float wmgfPercent;
	
	/** */
	@Result
	private List<AssessmentAlertItem<?>> _result;
	
	@Given
	private ShootingAssessmentSummary sum;
	
	@When
	public boolean when() {
		Integer countOfWMGF = sum.getCountOfWMGF();
		if(countOfWMGF==null)
			return true;
	    if((float)countOfWMGF/sum.getTotalDoc()<wmgfPercent)
	    	return true;
		return false;
	}

	@Then
	public RuleState then() {
		AssessmentAlertItem<Float> item = new AssessmentAlertItem<Float>();
		item.setItem(key);
		item.setV((float)sum.getCountOfWMGF()/sum.getTotalAssessingDoc());
		item.setRef(wmgfPercent);
		if(_result==null)
			_result = new ArrayList<AssessmentAlertItem<?>>();
		_result.add(item);
		return RuleState.NEXT;
	}
	
}
