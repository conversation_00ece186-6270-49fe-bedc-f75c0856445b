/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.service;

import java.util.List;
import java.util.Map;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.AjaxResult;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentInfoDocBean;
import com.jsdz.digitalevidence.assessment.bean.DocBean;
import com.jsdz.digitalevidence.assessment.model.AssessmentDocInfo;
import com.jsdz.digitalevidence.assessment.model.AssessmentInfo;

/**
 * @类名: AssessmentInfoService
 * @说明: 拍摄考核应用服务
 *        
 *
 * <AUTHOR>
	 
 */
public interface AssessmentInfoService {
	
	
	List<DocBean> getAssessmentInfo(AssessmentInfo param, PerLvlBean paramMap)throws Exception;

	AjaxResult saveAssessmentInfo(List<AssessmentDocInfo> assessmentInfoList, Map<String, Object> paramMap);

    List<AssessmentInfoDocBean> getAssessmentInfosByAssess(AssessmentInfo param, Map<String, Object> paramMap);

	AssessmentInfoDocBean getAssessmentInfoByAssessId(AssessmentInfo param, Map<String, Object> paramMap);

	Integer getAssessmentInfosCounts(AssessmentInfo param, Map<String, Object> paramMap);

	int getAssessmentInfoCount(AssessmentInfo param, PerLvlBean perLvlBean);
}
