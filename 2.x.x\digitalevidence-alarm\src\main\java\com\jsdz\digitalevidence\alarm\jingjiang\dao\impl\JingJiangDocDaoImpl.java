package com.jsdz.digitalevidence.alarm.jingjiang.dao.impl;

import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;
import org.hibernate.Session;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.alarm.jingjiang.dao.JingJiangDocDao;
import com.jsdz.digitalevidence.alarm.jingjiang.model.JingJiangDoc;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import java.util.List;

import org.hibernate.Query;

/**
 * 
 * @类名: JingJiangDocDaoImpl
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年10月25日下午3:59:45
 * 修改记录：
 *
 * @see
 */
@Repository
public class JingJiangDocDaoImpl extends GenericEntityDaoHibernateImpl<JingJiangDoc,Long> implements JingJiangDocDao{

	//新增
	public void addJingJiangDoc(JingJiangDoc jingJiangDoc) {
		this.saveOrUpdate(jingJiangDoc);
	}

	//删除
	public void deleteJingJiangDoc(JingJiangDoc jingJiangDoc) {
		this.delete(jingJiangDoc);
	}

	//修改
	public void updateJingJiangDoc(JingJiangDoc jingJiangDoc) {
		this.merge(jingJiangDoc);
	}

	//按id查询(游离状态)
	public JingJiangDoc findJingJiangDocById(Long id){

		final String  hql = "from JingJiangDataExchange j where j.id = :id";
		final Long oid = id;
		JingJiangDoc data = getHibernateTemplate().execute(new HibernateCallback<JingJiangDoc>() {
			public JingJiangDoc doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<JingJiangDoc> list = query.list();
				JingJiangDoc rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public JingJiangDoc locateJingJiangDocById(Long id){

		final String  hql = "from JingJiangDataExchange j where j.id = :id";
		final Long oid = id;
		JingJiangDoc data = getHibernateTemplate().execute(new HibernateCallback<JingJiangDoc>() {
			public JingJiangDoc doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<JingJiangDoc> list = query.list();
				JingJiangDoc rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public JingJiangDoc findJingJiangDocByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		JingJiangDoc data = getHibernateTemplate().execute(new HibernateCallback<JingJiangDoc>() {
		public JingJiangDoc doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<JingJiangDoc> list = query.list();
			JingJiangDoc rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<JingJiangDoc> findAllJingJiangDocs(){
		return this.find("from JingJiangDataExchange jingJiangDataExchange ");
	}

	//列表查询
	public List<JingJiangDoc> findJingJiangDocsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<JingJiangDoc> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<JingJiangDoc> findJingJiangDocsOnPage(Page<JingJiangDoc> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<JingJiangDoc>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}