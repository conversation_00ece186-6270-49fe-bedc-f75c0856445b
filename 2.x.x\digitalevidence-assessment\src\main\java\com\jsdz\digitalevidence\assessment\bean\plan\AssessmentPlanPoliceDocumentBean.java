package com.jsdz.digitalevidence.assessment.bean.plan;

import java.text.SimpleDateFormat;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPoliceDocument;
import com.jsdz.digitalevidence.document.model.Document;
/**
 * @类名: AssessmentPlanPoliceDocumentBean
 * @说明: 
 *
 * <AUTHOR>
 * @Date	 2019年6月24日上午11:11:10
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentPlanPoliceDocumentBean {
	private Long id;
	private Long planId;
	private Long policeId;//考评员id
	private String policeCode;//考评员警号
	private String policeName;//考评员姓名
	private Long docId;
	private String docName;
	private String updateTime;
	private String createTime;
	private String docPoliceCode;//拍摄警员
	private String docPoliceName;
	private String orgName;
	
	public AssessmentPlanPoliceDocument assignTo(){
		AssessmentPlanPoliceDocument dest = new AssessmentPlanPoliceDocument();
		assign(dest);
		return dest;
	}
	
	public void assign(AssessmentPlanPoliceDocument sur){
		this.setId(sur.getId());
		this.setPlanId(sur.getPlanId());
		if (null != sur.getPolice()){
			this.setPlanId(sur.getPolice().getId());
			this.setPoliceCode(sur.getPolice().getWorkNumber());
			this.setPoliceName(sur.getPolice().getName());
		}
		if (null != sur.getDoc()){
			this.setDocId(sur.getDoc().getId());
			this.setDocName(sur.getDoc().getName());
			this.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(sur.getDoc().getCreateTime()));
			this.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(sur.getDoc().getUploadTime()));
			if (null!=sur.getDoc().getPolice()){
				this.setDocPoliceCode(sur.getDoc().getPolice().getWorkNumber());
				this.setDocPoliceName(sur.getDoc().getPolice().getName());
				if (null!=sur.getDoc().getPolice().getOrganization()){
					this.setOrgName(sur.getDoc().getPolice().getOrganization().getOrgName());
				}
			}
			
		}
	}
	
	public void assignTo(AssessmentPlanPoliceDocument dest){
		dest.setId(this.getId());
		dest.setPlanId(this.getPlanId());
		if (null!=this.getPoliceId()){
			Employees emp = new Employees();
			emp.setId(this.getPoliceId());
			dest.setPolice(emp);
		}
		
		if (null!=this.getDocId()){
			Document doc =new Document();
			doc.setId(this.getDocId());
			dest.setDoc(doc);
		}
			
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getPlanId() {
		return planId;
	}
	public void setPlanId(Long planId) {
		this.planId = planId;
	}
	public Long getPoliceId() {
		return policeId;
	}
	public void setPoliceId(Long policeId) {
		this.policeId = policeId;
	}
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public Long getDocId() {
		return docId;
	}
	public void setDocId(Long docId) {
		this.docId = docId;
	}
	public String getDocName() {
		return docName;
	}
	public void setDocName(String docName) {
		this.docName = docName;
	}
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	public String getDocPoliceCode() {
		return docPoliceCode;
	}
	public void setDocPoliceCode(String docPoliceCode) {
		this.docPoliceCode = docPoliceCode;
	}
	public String getDocPoliceName() {
		return docPoliceName;
	}
	public void setDocPoliceName(String docPoliceName) {
		this.docPoliceName = docPoliceName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	
}
