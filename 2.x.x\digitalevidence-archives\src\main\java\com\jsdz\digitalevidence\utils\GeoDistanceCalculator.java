package com.jsdz.digitalevidence.utils;


import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.operation.buffer.validate.PointPairDistance;

public class GeoDistanceCalculator  {


//    public public static void main(String[] args) {
//
//
//    }

    /**
     * 点是否在面内
     * @param dot
     * @param dots
     * @return
     */
    public static boolean geometryContainsPoint(Point dot,Geometry dots){
        return dots.contains(dot);
    }


    public static boolean  geometryContainsPoint(double[] dot,double[][] dots){
        return geometryContainsPoint(getPoint(dot),getGeometry(dots));
    }

    /**
     * 点是否在面内（包含缓冲区）
     * @param dot 点
     * @param dots 面经纬度
     * @param bufferSize 缓冲区大小
     * @return
     */
    public static boolean  geometryContainsPointWithBuffer(double[] dot,double[][] dots, Double bufferSize){
        return geometryContainsPoint(getPoint(dot),getBufferedGeometry(dots, bufferSize));
    }

    public static Point getPoint(double[] dot){
        return new GeometryFactory().createPoint(new Coordinate(dot[0], dot[1]));
    }

    public static Geometry getGeometry(double[][] dots){
        try {
            StringBuilder dotStr= new StringBuilder();
            for (double[] dot : dots) {
                dotStr.append(dot[0]).append(" ").append(dot[1]).append(",");
            }
            if(dotStr.toString().contains(",")){
                dotStr = new StringBuilder(dotStr.substring(0, dotStr.length() - 1));
            }
            return  new WKTReader().read( "POLYGON((" + dotStr + "))");
        }catch (ParseException e){
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 生成带缓冲区的面
     * @param dots 面经纬度
     * @param bufferSize 缓冲区
     * @return
     */
    public static Geometry getBufferedGeometry(double[][] dots, Double bufferSize){
        try {
            StringBuilder dotStr= new StringBuilder();
            for (double[] dot : dots) {
                dotStr.append(dot[0]).append(" ").append(dot[1]).append(",");
            }
            if(dotStr.toString().contains(",")){
                dotStr = new StringBuilder(dotStr.substring(0, dotStr.length() - 1));
            }
            Geometry geometry = new WKTReader().read("POLYGON((" + dotStr + "))");
            // 设置缓冲区
            if(bufferSize != null) {
                geometry = geometry.buffer(bufferSize * 0.00001); //单位转为米
            }
            return geometry;
        }catch (ParseException e){
            e.printStackTrace();
        }
        return null;
    }

    public static void main(String[] args) {
//        double[] point = new double[]{116.007487, 39.891374};
//        double[] point = new double[]{116.167000, 39.950251};
//        double[] point = new double[]{116.168000, 39.950251};
        double[] point = new double[]{116.169000, 39.950251};
        double[][] positions = new double[][]{
                new double[]{115.871232, 40.008636},
                new double[]{115.740151, 39.877643},
                new double[]{115.88618, 39.730418},
                new double[]{116.139143, 39.741072},
                new double[]{116.166738, 39.950251},
                new double[]{115.871232, 40.008636}
        };
//        System.out.println(geometryContainsPointWithBuffer(point, positions, 0.00027));
//        System.out.println(geometryContainsPointWithBuffer(point, positions, 0.00127));

    }


}
