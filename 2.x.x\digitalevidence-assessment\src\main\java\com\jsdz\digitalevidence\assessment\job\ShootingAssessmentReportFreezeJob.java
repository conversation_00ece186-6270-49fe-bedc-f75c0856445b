/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.job;

import java.util.List;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;
import com.jsdz.digitalevidence.assessment.service.AssessmentQueryService;
import com.jsdz.digitalevidence.assessment.service.AssessmentService;

/**
 * @类名: ShootingAssessmentReportFreezeJob
 * @说明: 考核报告冻结作业
 *
 * <AUTHOR>
 * @Date	 2017年6月5日 下午4:10:36
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingAssessmentReportFreezeJob extends BaseAssessmentJob {
	
	@Autowired
	private AssessmentService assessmentService;
	@Autowired
	private AssessmentQueryService assessmentQueryService;

	/* (non-Javadoc)
	 * @see com.jsdz.scheduler.job.AbstractJob#execute(org.quartz.JobExecutionContext)
	 */
/*	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
		//
		try {
			// 传入参数
			Cycle c = this.getCycle();
			// 上一个周期
			if (c == null) {
				c = cc.getCurrentCycle();
				c = cc.prevCycle(c);
			}
			// 获取警员
			List<Employees> emps = this.getPolice();
			if (emps == null)
				empService.findAllEmployeess();
			for (Employees emp : emps) {
				ShootingAssessmentReport report = 
						assessmentQueryService.getAssessmentReport(emp.getId(), c.getStart());
				assessmentService.frozenAccessmentReport(report.getReportId());
			}
		} catch (Exception e) {
			throw new JobExecutionException(e.getMessage());
		}
	}*/
	
}
