package com.jsdz.digitalevidence.attendance.mapper;

import com.jsdz.admin.security.model.Operator;
import com.jsdz.digitalevidence.archives.bean.param.DocEnforParam;
import com.jsdz.digitalevidence.archives.model.DocEnfor;
import com.jsdz.digitalevidence.attendance.bean.param.AttendanceAllParam;
import com.jsdz.digitalevidence.attendance.model.AttendanceMonthModel;
import com.jsdz.digitalevidence.attendance.model.VAttendanceAllModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * VIEW Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface VAttendanceAllMapper extends BaseMapper<VAttendanceAllModel> {

    List<VAttendanceAllModel> selectModels(@Param("param")AttendanceAllParam param);

    List<AttendanceMonthModel> selectAttendanceCountsOfMonth(@Param("param")AttendanceAllParam param);

    Integer selectModelCount(@Param("param")AttendanceAllParam param);

    Integer selectAttendanceCountsOfMonthCount(@Param("param")AttendanceAllParam param);


    /**
     * 归档借用下mapper，懒得写了
     * @param param
     * @return
     */
    List<DocEnfor> selectEnfor(@Param("param") DocEnforParam param);


    Integer selectEnforCount(@Param("param") DocEnforParam param);

    Integer getTaskCount(@Param("time")  Date time,@Param("taskId")  Long taskId);

    Integer getDeviceCount(@Param("time")  Date time,@Param("deviceId") Long deviceId);

    @Select("select o.id as id from admin_employees e left join admin_operators o on e.id = o.employeesId where e.idnum =#{idnum}")
    List<Map> getOperatorByEid(@Param("idnum")String idnum);

    @Insert("INSERT INTO T_ALARM_ORGANIZATION (ALARM_ID,ORG_ID) VALUES (#{alarmId} , #{orgId})")
    void saveAlarmOrg(@Param("alarmId")Long alarmId,@Param("orgId") Long orgId);

    @Update("UPDATE T_CASE SET IS_RELATION = #{isRelation}  WHERE CASE_ID = #{id}")
    void updateCase(@Param("id")long id,@Param("isRelation") Integer isRelation);
}
