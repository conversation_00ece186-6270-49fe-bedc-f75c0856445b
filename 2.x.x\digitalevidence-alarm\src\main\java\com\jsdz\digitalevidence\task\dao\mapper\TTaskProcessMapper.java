package com.jsdz.digitalevidence.task.dao.mapper;

import com.jsdz.digitalevidence.site.model.Recorder;
import com.jsdz.digitalevidence.task.model.TTaskProcess;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 任务分发表(TTaskProcess)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-27 11:29:37
 */
public interface TTaskProcessMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    TTaskProcess queryById(Long id);

    /**
     * 查询指定行数据
     *
     * @param tTaskProcess 查询条件
     * @param pageable         分页对象
     * @return 对象列表
     */
    List<TTaskProcess> queryAllByLimit(TTaskProcess tTaskProcess, @Param("pageable") Pageable pageable);

    /**
     * 统计总行数
     *
     * @param tTaskProcess 查询条件
     * @return 总行数
     */
    long count(TTaskProcess tTaskProcess);

    /**
     * 新增数据
     *
     * @param tTaskProcess 实例对象
     * @return 影响行数
     */
    int insert(TTaskProcess tTaskProcess);

    /**
     * 批量新增数据（MyBatis原生foreach方法）
     *
     * @param entities List<TTaskProcess> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<TTaskProcess> entities);

    /**
     * 批量新增或按主键更新数据（MyBatis原生foreach方法）
     * 该方法兼容达梦数据库批量插入，采用注入的方式有sql注入的风险，不可直接传入前端的参数
     *
     * @param entities List<TTaskProcess> 实例对象列表
     * @return 影响行数
     * @throws org.springframework.jdbc.BadSqlGrammarException 入参是空List的时候会抛SQL语句错误的异常，请自行校验入参
     */
    int insertOrUpdateBatch(@Param("entities") List<TTaskProcess> entities);

    /**
     * 修改数据
     *
     * @param tTaskProcess 实例对象
     * @return 影响行数
     */
    int update(TTaskProcess tTaskProcess);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

    /**
     * 根据任务id获取任务发布记录
     *
     * @param taskId
     * @param deviceId
     * @return
     */
    TTaskProcess queryByTaskId(@Param("taskId") Long taskId, @Param("deviceId")  Long deviceId);

    /**
     * 查找分发任务
     * @param taskId 任务id
     * @param code  执法仪编号
     * @return
     */
    TTaskProcess findTaskProcess(@Param("taskId") Long taskId, @Param("code") String code);

    /**
     * 根据执法仪编号查询执法仪
     * @param code
     * @return
     */
    Recorder getRecorderByCode(String code);

    /**
     * 根据任务id获取任务详情
     * @param id
     * @return
     */
    TTaskProcess getProcessInfo(@Param("id") Long id);

    /**
     * 根据任务id获取任务详情
     * @param taskId
     * @param code
     * @return
     */
    TTaskProcess getTaskInfo(@Param("taskId") Long taskId, @Param("code") String code);

    /**
     * 根据任务分发id查询执法仪编码
     * @param processId
     * @return
     */
    String getRecorderByProcess(Long processId);
}

