package com.jsdz.digitalevidence.statistics.service.impl;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.bean.param.ApprovedDatasParam;
import com.jsdz.digitalevidence.statistics.model.SysDeleteApprovalModel;
import com.jsdz.digitalevidence.statistics.mapper.SysDeleteApprovalMapper;
import com.jsdz.digitalevidence.statistics.service.ISysDeleteApprovalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Service
public class SysDeleteApprovalServiceImpl extends ServiceImpl<SysDeleteApprovalMapper, SysDeleteApprovalModel> implements ISysDeleteApprovalService {

    @Resource
    private SysDeleteApprovalMapper sysDeleteApprovalMapper;


    @Override
    public Page<SysDeleteApprovalModel> approvedDatas(Long empId, ApprovedDatasParam taskParam) {
        Page<SysDeleteApprovalModel> page = taskParam.getPage();
        List<SysDeleteApprovalModel> approvalModels = sysDeleteApprovalMapper.approvedDatas(empId,taskParam);
        Integer count = sysDeleteApprovalMapper.approvedCount(empId, taskParam);
        page.setRows(approvalModels);
        page.setTotal(count);
        return page;
    }

    @Override
    public List<Employees> findAllEmployeess(Long operatorId) {
        return sysDeleteApprovalMapper.findAllEmployeess(operatorId);
    }

    @Override
    public void deletedocmentbyId(long docid) {
        sysDeleteApprovalMapper.deletedocmentbyId(docid);
    }
}
