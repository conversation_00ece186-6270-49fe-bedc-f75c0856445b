<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
	<!-- *********五率考核********* -->
	<!-- 组织考核周期-->
	<class name="com.jsdz.digitalevidence.assessment.model.org.OrgAssPeriod" table="T_ASSESSMENT_ORG_PERIOD">
		<id name="id" column="id" >
            <generator class="native"/>
        </id>
        <property name="periodName" column="period_name"></property>
        <property name="order" column="show_order"></property>
        <property name="cycleNum" column="cycle_num"></property>
        <!-- 类型 -->
		<property name="periodType" column="period_type">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.assessment.model.org.PeriodType
            		</param>
            	</type>
		</property>        
	</class>
	
	<!-- 五率考核 -->
	<class name="com.jsdz.digitalevidence.assessment.bean.FiveRateBean" >
		<id name="id" type="long">
			<generator class="native" />
		</id>
		<!-- 名称属性 -->
		<property name="orgCode" ></property>
		<property name="orgName" ></property>
		<property name="numerator" ></property>
		<property name="total" ></property>
		<property name="perRate" ></property>
	</class>
	 
	
</hibernate-mapping>