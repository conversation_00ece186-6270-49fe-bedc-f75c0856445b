<project xmlns="http://maven.apache.org/POM/4.0.0" 
		xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	
	<parent> 
		<groupId>com.jsdz</groupId> 
		<artifactId>admin</artifactId> 
		<version>2.4.1</version> 
	</parent>

	<artifactId>admin-security</artifactId>	

	<url>http://maven.apache.org</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
	</properties>
	
	<build>
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.5.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
		</plugins>
		
	</build>
	
   <dependencies>
        <dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-core</artifactId>
				<exclusions>
					<exclusion>
						<groupId>commons-logging</groupId>
						<artifactId>commons-logging</artifactId>
					</exclusion>
				</exclusions>
		</dependency>
		<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-context</artifactId>
		</dependency>
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-context-support</artifactId>
		</dependency>	
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-tx</artifactId>
		</dependency>	
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-orm</artifactId>
		</dependency>		
		<dependency>
		  <groupId>org.springframework</groupId>
		  <artifactId>spring-jdbc</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>jstl</artifactId>
			<version>1.2</version>
		</dependency>
		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>servlet-api</artifactId>
			<version>2.5</version>
			<scope>provided</scope>
		</dependency>
				
		<!-- SPRING basic end -->	
		<!-- AOP -->
		<dependency>
		  <groupId>org.aspectj</groupId>
		  <artifactId>aspectjrt</artifactId>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
		</dependency>
		<dependency>
			<groupId>cglib</groupId>
			<artifactId>cglib</artifactId>
		</dependency>		
		<dependency>
		  <groupId>asm</groupId>
		  <artifactId>asm</artifactId>
		</dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
			<!--<dependency>
				<groupId>com.mchange</groupId>
				<artifactId>c3p0</artifactId>
			</dependency>-->
        <dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
		</dependency>
		<dependency>
			<groupId>org.hibernate</groupId>
			<artifactId>hibernate-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.javassist</groupId>
			<artifactId>javassist</artifactId>
		</dependency>
		
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>
		<!-- db driver -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
		    <groupId>junit</groupId>
		    <artifactId>junit</artifactId>
		    <version>4.10</version>
		    <scope>test</scope>
		</dependency>

		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
		</dependency>
		
		<dependency>
			    <groupId>com.fasterxml.jackson.core</groupId>
			    <artifactId>jackson-core</artifactId>
			</dependency>
			
			<dependency>
			    <groupId>com.fasterxml.jackson.core</groupId>
			    <artifactId>jackson-databind</artifactId>
			</dependency>
			
			<dependency>
			    <groupId>com.fasterxml.jackson.core</groupId>
			    <artifactId>jackson-annotations</artifactId>
			</dependency>
			
			<!-- POI EXCEL导入导出 -->
			<dependency>
    			<groupId>org.apache.poi</groupId>
    			<artifactId>poi</artifactId>
    			<version>3.14</version>
			</dependency>	
			<dependency>
    			<groupId>org.apache.poi</groupId>
    			<artifactId>poi-ooxml</artifactId>
    			<version>3.14</version>
			</dependency>
				 
		<dependency>
			<groupId>com.jsdz</groupId>
			<artifactId>serializer</artifactId>
		</dependency>
		<dependency>
			<groupId>com.jsdz</groupId>
			<artifactId>utils</artifactId>
		</dependency>
			
		<dependency>
			<groupId>com.jsdz</groupId>
			<artifactId>admin-org</artifactId>
			<version>2.4.1</version>
		</dependency>
		

		<dependency>
			<groupId>com.jsdz</groupId>
			<artifactId>commons-reportquery</artifactId>
		</dependency>
		<dependency>
			<groupId>com.jsdz</groupId>
			<artifactId>digitalevidence-cache</artifactId>
			<version>2.4.1</version>
		</dependency>
   </dependencies>


</project>
