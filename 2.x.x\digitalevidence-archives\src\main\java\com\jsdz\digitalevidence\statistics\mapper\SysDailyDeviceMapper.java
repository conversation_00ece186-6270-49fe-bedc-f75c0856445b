package com.jsdz.digitalevidence.statistics.mapper;

import com.jsdz.digitalevidence.statistics.bean.param.SysDailyDeviceParam;
import com.jsdz.digitalevidence.statistics.model.SysDailyDeviceModel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface SysDailyDeviceMapper extends BaseMapper<SysDailyDeviceModel> {

    List<SysDailyDeviceModel> selectModels(@Param("param")SysDailyDeviceParam param);

    Integer selectModelCount(@Param("param")SysDailyDeviceParam param);

    @Select("CALL DAILY_DEVICE_STATISTICS()")
    void startDailyDevice();

    @Update("update t_case set caseFlow = #{caseFlow} where case_id = #{id}" )
    void updateCaseFlow(@Param("id")Long id,@Param("caseFlow") String caseFlow);
}
