package com.jsdz.admin.security.service.impl;

import java.util.Date;

/**
 * 
 * @类名: DictionaryValuesServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-10-31 11:44:06
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
//import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.dao.DictionaryValuesDao;
import com.jsdz.admin.security.model.DataDictionary;
import com.jsdz.admin.security.model.DictionaryValues;
import com.jsdz.admin.security.service.DictionaryValuesService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("DictionaryValuesServiceImpl")
public class DictionaryValuesServiceImpl implements DictionaryValuesService {

	@Autowired
	private DictionaryValuesDao dictionaryValuesDao;

	//新增
	public AjaxResult addDictionaryValues(DictionaryValues dictionaryValues) {
		AjaxResult result = new AjaxResult(0,false,"操作失败",null);
		if (dictionaryValues.getDictionary()==null){
			result.setMsg("请先择字典。");
			return result;
		}

		if (dictionaryValues.getValue()==null || dictionaryValues.getValue().equals("")){
			result.setMsg("字典内容必须提供");
			return result;
		}
		
		String queryStr = " from DictionaryValues v where v.value = :value and v.dictionary.id = :dictionaryId ";
		DictionaryValues d = dictionaryValuesDao.findDictionaryValuesByCondition(
				queryStr, new String[]{"value","dictionaryId"}, new Object[]{dictionaryValues.getValue(),dictionaryValues.getDictionary().getId()});
		if (d != null){
			result.setMsg("字典内容重复");
			return result;
		}
		dictionaryValues.setCreateTime(new Date());
		dictionaryValuesDao.addDictionaryValues(dictionaryValues);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	@Transactional
	public AjaxResult updateDictionaryValues(DictionaryValues dictionaryValues) {
		AjaxResult result = new AjaxResult(0,false,"操作失败",null);
		if (dictionaryValues.getDictionary()==null){
			result.setMsg("请先择字典。");
			return result;
		}

		if (dictionaryValues.getValue()==null || dictionaryValues.getValue().equals("")){
			result.setMsg("字典内容必须提供");
			return result;
		}
		String queryStr = " from DictionaryValues v where v.value = :value and v.dictionary.id = :dictionaryId and v.id != :id";
		DictionaryValues d = dictionaryValuesDao.findDictionaryValuesByCondition(
				queryStr, new String[]{"value","dictionaryId","id"}, 
				new Object[]{dictionaryValues.getValue(),dictionaryValues.getDictionary().getId(),dictionaryValues.getId()});
		if (d != null){
			result.setMsg("字典内容重复");
			return result;
		}
		
		d = locateDictionaryValuesById(dictionaryValues.getId());
		d.setDictionary(dictionaryValues.getDictionary());
		d.setValue(dictionaryValues.getValue());
		d.setOrder(dictionaryValues.getOrder());
		d.setNote(dictionaryValues.getNote());
		
		dictionaryValuesDao.updateDictionaryValues(d);
		
		//同时修改相关资料
		updateUesedTable(d);
		
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteDictionaryValues(DictionaryValues dictionaryValues) {
		AjaxResult result = new AjaxResult(0,false,"操作失败",null);
		DictionaryValues d = locateDictionaryValuesById(dictionaryValues.getId());
		dictionaryValuesDao.deleteDictionaryValues(d);
		
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public DictionaryValues findDictionaryValuesById(Long id){

		return dictionaryValuesDao.findDictionaryValuesById(id);
	}

	//按 id 查询
	public DictionaryValues locateDictionaryValuesById(Long id) {
		return dictionaryValuesDao.locateDictionaryValuesById(id);
	}

	//单个查询
	public DictionaryValues findDictionaryValuesByParam(String queryStr, String[] paramNames, Object[] values) {
		return dictionaryValuesDao.findDictionaryValuesByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<DictionaryValues> findAllDictionaryValuess() {
		return dictionaryValuesDao.findAllDictionaryValuess();
	}

	//列表查询
	public List<DictionaryValues> findDictionaryValuessByParam(String queryStr, String[] paramNames, Object[] values) {
		return dictionaryValuesDao.findDictionaryValuessByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<DictionaryValues> findDictionaryValuessOnPage(Page<DictionaryValues> page, String queryStr, String[] paramNames, Object[] values) {
		Page<DictionaryValues> pos = dictionaryValuesDao.findDictionaryValuessOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
	//同时修改相关资料
	private void updateUesedTable(DictionaryValues dv){
		return;

/*		DataDictionary d = dv.getDictionary();
		if (d == null)
			return;
		
		//岗位
		if (d.getCode().equalsIgnoreCase("POST")){
			
		}

		//文件分类
		if (d.getCode().equalsIgnoreCase("FILECATE")){
			
		}
		
		//典型视频分类
		if (d.getCode().equalsIgnoreCase("VIDEOCATE")){
			
		}
		 
		//审批问题
		if (d.getCode().equalsIgnoreCase("APPROVERESULT")){
			
		}
		
		//审批问题
		if (d.getCode().equalsIgnoreCase("APPROVERESULT")){
			
		}
		
		//事件类型
		if (d.getCode().equalsIgnoreCase("CASETYPE")){
			
		}

		//事件等级
		if (d.getCode().equalsIgnoreCase("CASELEVEL")){
			
		}*/
		
	}

}
