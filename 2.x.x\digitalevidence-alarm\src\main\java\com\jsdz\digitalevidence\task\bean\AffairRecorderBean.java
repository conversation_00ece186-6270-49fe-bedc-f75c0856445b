package com.jsdz.digitalevidence.task.bean;

import com.jsdz.digitalevidence.graphensemble.bean.HeartBeatBean;

public class AffairRecorderBean extends HeartBeatBean {

    /**
     * 执法仪编号
     */
    private String code;
    /**
     * 警员编号
     */
    private String policeName;
    /**
     * 机构编号
     */
    private String orgName;
    /**
     * 设备名
     */
    private String rname;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getPoliceName() {
        return policeName;
    }

    public void setPoliceName(String policeName) {
        this.policeName = policeName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getRname() {
        return rname;
    }

    public void setRname(String rname) {
        this.rname = rname;
    }
}
