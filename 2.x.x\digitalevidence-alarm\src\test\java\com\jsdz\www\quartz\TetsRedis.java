package com.jsdz.www.quartz;

public class TetsRedis extends Thread{
	
	private RedisService redisService;
	
	
	public TetsRedis(RedisService redisService) {
		this.redisService = redisService;
	}



	@Override
	public void run() {
		redisService.seckill();
	}
	
	public static void main(String[] args) {
		
		RedisService redisService = new RedisService();
		
		for (int i = 0; i < 1; i++) {
			
			TetsRedis tetsRedis = new TetsRedis(redisService);
			
			tetsRedis.start();
		}
	}
}
