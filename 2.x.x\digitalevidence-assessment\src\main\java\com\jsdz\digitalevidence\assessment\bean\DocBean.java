/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.model.ImportantLevel;

import java.util.Date;

/**
 * @类名: AssessmentDocBean
 * @说明: 考核文档Bean
 * <AUTHOR>
 */
public class DocBean extends AbstractDTO {

	//录入视频民警信息
	/** 警号*/
	private String policeCode;
	/** 姓名*/
	private String policeName;
	/** 部门名称*/
	private String orgName;
	/** 部门code*/
	private String orgCode;

    /** 录入文档信息文*/
    /** 文档ID*/
    private Long docId;
	/** 视频时长，单位毫秒*/
	private Long lengthOfVedio;
	/** 视频大小, 单位: G*/
	private Long sizeOfVedio;
    /**文档名称*/
    private String docName;
    /**录入时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date turnOnTime;
	/**上传时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date pushTime;
	/** 重要级别*/
	private ImportantLevel impLevel;
	/** 文档分类*/
	private DocumentCate cate;
	/** 文档描述*/
	private String comments;
	/** 文档类型*/
	private String type;
	/** 文档是否归档0未关联，1关联*/
	private Integer writRelation;
	
	private String assessmentld;
	
	public Integer getWritRelation() {
		return writRelation;
	}

	public void setWritRelation(Integer writRelation) {
		this.writRelation = writRelation;
	}

	public String getPoliceCode() {
		return policeCode;
	}

	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}

	public String getPoliceName() {
		return policeName;
	}

	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}

	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}

	public Long getLengthOfVedio() {
		return lengthOfVedio;
	}

	public void setLengthOfVedio(Long lengthOfVedio) {
		this.lengthOfVedio = lengthOfVedio;
	}

	public Long getSizeOfVedio() {
		return sizeOfVedio;
	}

	public void setSizeOfVedio(Long sizeOfVedio) {
		this.sizeOfVedio = sizeOfVedio;
	}

	public String getDocName() {
		return docName;
	}

	public void setDocName(String docName) {
		this.docName = docName;
	}

	public Date getTurnOnTime() {
		return turnOnTime;
	}

	public void setTurnOnTime(Date turnOnTime) {
		this.turnOnTime = turnOnTime;
	}

	public Date getPushTime() {
		return pushTime;
	}

	public void setPushTime(Date pushTime) {
		this.pushTime = pushTime;
	}

	public ImportantLevel getImpLevel() {
		return impLevel;
	}

	public void setImpLevel(ImportantLevel impLevel) {
		this.impLevel = impLevel;
	}

	public DocumentCate getCate() {
		return cate;
	}

	public void setCate(DocumentCate cate) {
		this.cate = cate;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getAssessmentld() {
		return assessmentld;
	}

	public void setAssessmentld(String assessmentld) {
		this.assessmentld = assessmentld;
	}
   
	
}
