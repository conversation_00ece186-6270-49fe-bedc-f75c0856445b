package com.jsdz.admin.security.bean;

import java.util.List;

/**
 * 
 * @类名: MenuTreeBean
 * @说明: 树形菜单封装类
 *
 * @author: kenny
 * @Date	2017年9月8日上午11:21:36
 * 修改记录：
 *
 * @see
 */
public class MenuTreeBean {
	private String id;
	private String text;
	private String state = "open";// open,closed
	private boolean checked = false;
	private Object attributes;
	private List<MenuTreeBean> children;
	private String iconCls;
	private String pid;
	private String menuUrl;
	private Integer menuType;
	private Integer menuSort;
	private Integer menuIsshow;
	
	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getText() {
		return text;
	}
	public void setText(String text) {
		this.text = text;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public boolean isChecked() {
		return checked;
	}
	public void setChecked(boolean checked) {
		this.checked = checked;
	}
	public Object getAttributes() {
		return attributes;
	}
	public void setAttributes(Object attributes) {
		this.attributes = attributes;
	}
	public List<MenuTreeBean> getChildren() {
		return children;
	}
	public void setChildren(List<MenuTreeBean> children) {
		this.children = children;
	}
	public String getIconCls() {
		return iconCls;
	}
	public void setIconCls(String iconCls) {
		this.iconCls = iconCls;
	}

	public String getMenuUrl() {
		return menuUrl;
	}
	public void setMenuUrl(String menuUrl) {
		this.menuUrl = menuUrl;
	}
	public Integer getMenuType() {
		return menuType;
	}
	public void setMenuType(Integer menuType) {
		this.menuType = menuType;
	}
	public Integer getMenuSort() {
		return menuSort;
	}
	public void setMenuSort(Integer menuSort) {
		this.menuSort = menuSort;
	}
	public Integer getMenuIsshow() {
		return menuIsshow;
	}
	public void setMenuIsshow(Integer menuIsshow) {
		this.menuIsshow = menuIsshow;
	}
	public String getPid() {
		return pid;
	}
	public void setPid(String pid) {
		this.pid = pid;
	}
}
