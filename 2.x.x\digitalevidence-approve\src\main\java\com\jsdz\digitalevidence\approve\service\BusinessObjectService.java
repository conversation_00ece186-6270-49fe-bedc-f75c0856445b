/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.service;

import java.util.List;
import java.util.Map;

import com.jsdz.digitalevidence.approve.model.BusinessObject;

/**
 * @类名: BusinessObjectService
 * @说明: 业务对象接口
 *        业务服务模块提供
 *
 * <AUTHOR>
 * @Date	 2017年11月17日 上午11:16:03
 * 修改记录：
 *
 * @see 	 
 */
public interface BusinessObjectService {

	/**
	 * @说明：解释业务请求参数，获取业务操作对象
	 *
	 * <AUTHOR>
	 * @param param
	 * @return
	 * 
	 */
	List<BusinessObject> getBusinessObject(Map<String, Object> param);
		
}
