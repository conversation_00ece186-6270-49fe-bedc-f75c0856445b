package com.jsdz.digitalevidence.archives.dao.impl;

import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.archives.bean.ArchivesDataBean;
import com.jsdz.digitalevidence.archives.dao.ArchivesDataDao;
import com.jsdz.digitalevidence.utils.StringUtils;
import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import java.sql.SQLException;
import java.util.List;


/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-11
 */
@Repository
public class ArchivesDataDaoImpl extends GenericEntityDaoHibernateImpl<ArchivesDataBean,Long> implements ArchivesDataDao {

    @Override
    public ArchivesDataBean archivesById(Long archivesDataBean) {
        ArchivesDataBean data = getHibernateTemplate().execute(new HibernateCallback<ArchivesDataBean>() {
            public ArchivesDataBean doInHibernate(Session session)throws HibernateException, SQLException {
                Query query = session.createQuery(" from ArchivesDataBean s left join fetch s.organization o where s.archivesId =:id " );
                query.setParameter("id", archivesDataBean);
                List<ArchivesDataBean> list = query.list();
                ArchivesDataBean rsult = list == null || list.size() <= 0?null:list.get(0);
                return rsult;
            }
        });
        return data;
    }

    @Override
    public boolean getArchivesCode(String archivesCode) {
        boolean data = getHibernateTemplate().execute(new HibernateCallback<Boolean>() {
            public Boolean doInHibernate(Session session)throws HibernateException, SQLException {
                Query query = session.createQuery(" from ArchivesDataBean s left join fetch s.organization o where s.archivesCode =:archivesCode " );
                query.setParameter("archivesCode", archivesCode);
                List<ArchivesDataBean> list = query.list();
                if(StringUtils.isBlank(list) || list.size()==0)
                    return true;
                return false;
            }
        });
        return !data;
    }

    @Override
    public List<ArchivesDataBean> getArchivesByCode(String archivesCode) {
        List<ArchivesDataBean> data = getHibernateTemplate().execute(new HibernateCallback<List<ArchivesDataBean>>() {
            public  List<ArchivesDataBean> doInHibernate(Session session)throws HibernateException, SQLException {
                Query query = session.createQuery(" from ArchivesDataBean s left join fetch s.organization o where s.archivesCode =:archivesCode " );
                query.setParameter("archivesCode", archivesCode);
                return query.list();
            }
        });
        return data;
    }
}
