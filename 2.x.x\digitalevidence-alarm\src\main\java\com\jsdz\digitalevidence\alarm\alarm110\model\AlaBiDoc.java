package com.jsdz.digitalevidence.alarm.alarm110.model;

import java.util.Date;

import com.jsdz.digitalevidence.document.model.Case;
import com.jsdz.digitalevidence.document.model.Document;

/**
 *  吉林版，警情绑定视频
 * @类名: AlaBiDoc
 * @说明: 
 *
 * <AUTHOR>
 * @Date	 2018年4月23日 上午10:02:59
 * 修改记录：
 *
 * @see
 */
public class AlaBiDoc {
	
	private Long id;
	
	private String docId; // 音视频编号
	
	private String alarmCode; // 警情编号
	
	private String caseCode; // 案件编号
	
	private String type; // 音视频类别
	
	private String collTime; // 采集时间
	
	private String uplTime ; // 上传时间
	
	private String relationTime; // 关联时间
	
	private String orgCode ; //单位代码
	
	private String msg ; //备注信息
	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDocId() {
		return docId;
	}

	public void setDocId(String docId) {
		this.docId = docId;
	}

	public String getAlarmCode() {
		return alarmCode;
	}

	public void setAlarmCode(String alarmCode) {
		this.alarmCode = alarmCode;
	}

	public String getCaseCode() {
		return caseCode;
	}

	public void setCaseCode(String caseCode) {
		this.caseCode = caseCode;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getCollTime() {
		return collTime;
	}

	public void setCollTime(String collTime) {
		this.collTime = collTime;
	}

	public String getUplTime() {
		return uplTime;
	}

	public void setUplTime(String uplTime) {
		this.uplTime = uplTime;
	}

	public String getRelationTime() {
		return relationTime;
	}

	public void setRelationTime(String relationTime) {
		this.relationTime = relationTime;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	
}
