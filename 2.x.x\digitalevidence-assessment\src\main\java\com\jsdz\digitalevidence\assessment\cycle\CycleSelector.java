package com.jsdz.digitalevidence.assessment.cycle;

/**
 * 
 * @类名: CycleSelector
 * @说明: 周期选择
 *
 * <AUTHOR>
 * @Date	 2017年4月13日 下午5:46:34
 * 修改记录：
 *
 * @see
 */
public enum CycleSelector {
	
	PREV(1, "上一个周期"), 
	NEXT(2, "下一个周期");
	
	public int index;
	public String name;
	
	private CycleSelector(int index, String name) {
		this.name = name;
	}
	public int getIndex() {
		return index;
	}
	public void setIndex(int index) {
		this.index = index;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
}
