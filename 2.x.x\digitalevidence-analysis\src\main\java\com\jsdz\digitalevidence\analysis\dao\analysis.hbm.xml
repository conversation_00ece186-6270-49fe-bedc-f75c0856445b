<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN" 
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

	<!-- 文档日结表 -->
	<class name="com.jsdz.digitalevidence.analysis.model.DocDaily" table="analysis_docdaily">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<!-- 日期从 -->
		<property name="dateFrom" column="dateFrom"></property>
		<!-- 日期 到-->
		<property name="dateTo" column="dateTo"></property>
		
		<!-- 项目ID-->
		<property name="objectId" column="objectId"></property>
		<!-- 文件长度 -->
		<property name="fileSize" column="fileSize"></property>
		<!-- 视频时长 -->
		<property name="duration" column="vedioLen"></property>
		<!-- 文件数量 -->
		<property name="fileCount" column="fileCount"></property>
		<!-- 重要级别 -->
		<property name="impLevel" column="impLevel">
            	<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.document.model.ImportantLevel
            		</param>
            	</type>
         </property>
		<property name="cate" column="cate">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.document.model.DocumentCate
            		</param>
            	</type>
		</property>
		<property name="dailyObject" column="dailyObject">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.analysis.model.DailyObject
            		</param>
            	</type>
		</property>	
		<!--日结类型 -->
		<property name="dailyType" column="dailyType">
			<type name="org.hibernate.type.EnumType">
            		<param name="enumClass"> 
            			com.jsdz.digitalevidence.analysis.model.DailyType
            		</param>
            	</type>
		</property>				
	</class>
	<!-- 日志-->
	<class name="com.jsdz.digitalevidence.analysis.model.DailyLog" table="analysis_dailylog">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="objectDesc" column="objectDesc"></property>
		<property name="dailyType" column="dailyType"></property>
		<property name="dateFrom" column="dateFrom"></property>
		<property name="dateTo" column="dateTo"></property>
		<property name="createTime" column="createTime"></property>
	</class>
	
	<!-- 查询文档的结果实体类 DocDialyDTO-->
	<class name="com.jsdz.digitalevidence.analysis.bean.DocDailyDto" table="analysis_temp_docdialydto">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="objectId" ></property>
		<property name="fileSize" ></property>
		<property name="fileCount" ></property>
		<property name="cate" ></property>
		<property name="impLevel" ></property>
		<property name="vedioLen" ></property>
	</class>
	
	
		
	<!-- 按站点统计 SiteDocDailyDto-->
	<class name="com.jsdz.digitalevidence.analysis.bean.SiteDocDailyDto" table="analysis_temp_sitedocdialydto">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="dateFrom" ></property>
		<property name="dateTo" ></property>
		<property name="objectId" ></property>
		<property name="code" ></property>
		<property name="name" ></property>
		<property name="vedioLen" ></property>
		<property name="fileCount" ></property>
		<property name="fileSize" ></property>
		<property name="vedeoSize" ></property>
		<property name="audioSize" ></property>
		<property name="picCount" ></property>
		<property name="txtCount" ></property>
		<property name="importanceCount" ></property>
		<property name="generalCount" ></property>
	</class>
	
	<!-- 接口:　执法记录仪日志信息查询结果参数表  CxdsjLogResultBean-->
	<class name="com.jsdz.digitalevidence.analysis.openapi.CxdsjLogResultBean" table="openapi_temp_dxdsjlogresultbean">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="cpxh" column="cpxh"></property>
		<property name="rzlx" column="rzlx"></property>
		<property name="wjmc" column="wjmc"></property>
		<property name="rzrq" column="rzrq"></property>
	</class>

	<!-- 接口:　执法数据采集设备基本信息查询结果 响应类  StationInfoResultBean-->
	<class name="com.jsdz.digitalevidence.analysis.openapi.StationInfoResultBean" table="openapi_temp_stationinfoResultbean">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="gzz_xh" column="gzz_xh"></property>
		<property name="gzz_ipdz" column="gzz_ipdz"></property>
		<property name="zxzt" column="zxzt"></property>
		<property name="qyzt" column="qyzt"></property>
		<property name="ybkjdx" column="ybkjdx"></property>
		<property name="jssykjdx" column="jssykjdx"></property>
	</class>
	<!-- 接口:　执法数据采集设备日志信息查询结果响应类  StationLogResultBean-->
	<class name="com.jsdz.digitalevidence.analysis.openapi.StationLogResultBean" table="openapi_temp_stationlogResultbean">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="gzz_xh" column="gzz_xh"></property>
		<property name="rzlx" column="rzlx"></property>
		<property name="dxbh" column="dxbh"></property>
		<property name="rzrq" column="rzrq"></property>
	</class>

	<!-- 接口:　执法记录仪基本信息上传实体类  DsjInfo-->
	<class name="com.jsdz.digitalevidence.analysis.openapi.model.DsjInfo" table="openapi_dsjinfo">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="wjbh" column="wjbh"></property><!-- 文件编号 -->
		<property name="wjbm" column="wjbm"></property><!-- 文件别名 -->
		<property name="pssj" column="pssj"></property><!-- 拍摄时间 -->
		<property name="wjdx" column="wjdx"></property><!-- 文件大小 -->
		<property name="wjlx" column="wjlx"></property><!-- 文件类型 -->
		<property name="jy_xm" column="jy_xm"></property><!-- 使用者姓名 -->
		<property name="jybh" column="jybh"></property><!-- 警号 -->
		<property name="cpxh" column="cpxh"></property><!-- 产品序号 -->
		<property name="jgdm" column="jgdm"></property><!-- 单位编号 -->
		<property name="dwmc" column="dwmc"></property><!-- 单位名称 -->
		<property name="ccfwq" column="ccfwq"></property><!-- 存储服务器 -->
		<property name="ccwz" column="ccwz"></property><!-- 存储位置 -->
		<property name="bfwz" column="bfwz"></property><!-- 播放位置 -->
		<property name="wlwz" column="wlwz"></property><!-- 物理位置 -->
		<property name="gzz_xh" column="gzz_xh"></property><!-- 执法数据采集设备产品编码 -->
		<property name="scsj" column="scsj"></property><!-- 上传时间	 -->
		<property name="bzlx" column="bzlx"></property><!-- 标注类型 -->
	</class>
		
	<!-- 接口:　执法记录仪日志信息上传  CxDsjLog-->
	<class name="com.jsdz.digitalevidence.analysis.openapi.model.CxDsjLog" table="openapi_csdsjlog">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="gzz_xh" column="gzz_xh"></property><!-- 执法数据采集设备产品编码 -->
		<property name="rzlx" column="rzlx"></property><!-- 日志类型 -->
		<property name="wjmc" column="wjmc"></property><!-- 文件名称 -->
		<property name="rzrq" column="rzrq"></property><!-- 日志时间 -->
	</class>

	<!-- 接口:　执法数据采集设备基本信息实体类   StationInfo -->
	<class name="com.jsdz.digitalevidence.analysis.openapi.model.StationInfo" table="openapi_stationinfo">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="gzz_xh" column="gzz_xh"></property><!-- 执法数据采集设备产品编码 -->
		<property name="gzz_ipdz" column="gzz_ipdz"></property><!-- 执法数据采集设备IP -->
		<property name="zxzt" column="zxzt"></property><!-- 在线状态 0，不在线；1，在线 -->
		<property name="qyzt" column="qyzt"></property><!-- 启用状态 0，未启用；1，启用 -->
		<property name="ybkjdx" column="ybkjdx"></property><!-- 硬盘空间大小 -->
		<property name="jssykjdx" column="jssykjdx"></property><!-- 即时剩余空间大小 -->
	</class>
	
	<!-- 接口:　执法数据采集设备日志信息实体类   StationInfo -->
	<class name="com.jsdz.digitalevidence.analysis.openapi.model.StationLog" table="openapi_stationlog">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="gzz_xh" column="gzz_xh"></property><!-- 执法数据采集设备产品编码 -->
		<property name="rzlx" column="rzlx"></property><!-- 日志类型 -->
		<property name="dxbh" column="dxbh"></property><!-- 对象编号 -->
		<property name="rzrq" column="rzrq"></property><!-- 日志时间(yy-MM-dd HH:mm:ss) -->
	</class>
	
	<!-- 接口:  文档资料综合查询   DocStatitcalDto -->
	<class name="com.jsdz.digitalevidence.analysis.bean.DocStatisicalDto" table="temp_docstatisicaldto">
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="code" column="code"></property><!-- 编号 -->
		<property name="name" column="name"></property><!-- 名称 -->
		<property name="sumValue" column="sumValue"></property><!-- 总计数 -->
	</class>
	
	<!-- 系统简报查询 -->
	<class name="com.jsdz.digitalevidence.analysis.bean.BriefReport" >
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="orgName" ></property><!-- 单位名称 -->
		<property name="fileCount" ></property><!-- 文件数量 -->
		<property name="assessmentCount" ></property><!-- 考核监督数量 -->
		<property name="assessmentRate" ></property><!-- 考核率 -->
		<property name="signCount" ></property><!-- 标注数量 -->
		<property name="signRate" ></property><!-- 标注率 -->
		<property name="alarmCount" ></property><!-- 警情关联数量-->
		<property name="alarmRate" ></property><!-- 警情关联率-->
		<property name="caseCount" ></property><!-- 案件关联数量 -->
		<property name="caseRate" ></property><!-- 案件关联率 -->
	</class>
	
	<!-- 趋势报表查询 -->
	<class name="com.jsdz.digitalevidence.analysis.bean.TrendReport" >
		<id name="id" column="id" type="long">
			<generator class="native" />
		</id>
		<property name="ObjCode" ></property><!-- 项目，可能是单位，警员，日期等-->
		<property name="fileCount" ></property><!-- 文件数量 -->
		<property name="vedioCount" ></property><!-- 视频数量 -->
		<property name="vedioQualify" ></property><!-- 视频质量 -->
		<property name="sumDuration" ></property><!-- 视频时长 -->
		<property name="sumHdDuration" ></property><!-- 高清时长 -->
		<property name="alarmRate" ></property><!-- 警情关联率-->
		<property name="caseRate" ></property><!-- 案件关联率-->
	</class>
	
	<!-- 警情视频关联率 -->
	<class name="com.jsdz.digitalevidence.analysis.bean.AlarmDocRelationRateBean" >
		<id name="id" type="long">
			<generator class="native" />
		</id>
		<property name="orgCode" ></property><!-- 单位编码-->
		<property name="orgName" ></property><!-- 单位名称 -->
		<property name="timeRange" ></property><!-- 时间范围 -->
		<property name="fileCount" ></property><!-- 文件数量(排除日志) -->
		<property name="alarmCount" ></property><!-- 警情数量 -->
		<property name="alarmRelateCount" ></property><!-- 警情关联数 -->
		<property name="alarmRelateRate" ></property><!-- 警情关联率-->
		<property name="docRelationCount" ></property><!-- 文档关联数-->
		<property name="docRelationRate" ></property><!-- 文档关联率-->
	</class>	
	<!-- 统计分析结果集 -->
	<class name="com.jsdz.digitalevidence.analysis.bean.AnalysisDocumentBean" >
		<id name="id" type="long">
			<generator class="native" />
		</id>
		<property name="dateFrom" ></property><!-- 时间段从-->
		<property name="dateTo" ></property><!-- 时间到 -->
		<property name="code" ></property><!-- 对象代码 -->
		<property name="name" ></property><!-- 对象名称 -->
		<property name="orgName" ></property><!-- 单位名称 -->
		<property name="totalDuration" ></property><!-- 总时长 -->
		<property name="vedioDuration" ></property><!-- 视频时长-->
		<property name="audioDuration" ></property><!-- 音频时长-->
		<property name="totalFileSize" ></property><!-- 文件总长度-->
		<property name="totalCount" ></property><!-- 文件总数量-->
		<property name="vedioCount" ></property><!-- 视频数量-->
		<property name="audioCount" ></property><!-- 音频数量-->
		<property name="picCount" ></property><!-- 图片数量-->
		<property name="txtCount" ></property><!-- 日志数量-->
		<property name="importanceCount" ></property><!-- 重要文件 数量-->
		<property name="generalCount" ></property><!-- 一般文件 数量-->
		<property name="signCount" ></property><!-- 标注文件 数量-->		
		<property name="relateCount" ></property><!-- 关联文件 数量-->
		<property name="accessmentCount" ></property><!-- 考核文件 数量-->
	</class>
</hibernate-mapping>