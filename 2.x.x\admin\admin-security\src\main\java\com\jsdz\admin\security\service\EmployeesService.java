package com.jsdz.admin.security.service;

/**
 * 
 * @类名: EmployeesService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-13 13:48:18
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface EmployeesService {

	//新增
	public AjaxResult addEmployees(Employees employees);

	//修改
	public AjaxResult updateEmployees(Employees employees);

	//删除
	public AjaxResult deleteEmployees(Employees employees);

	//按id查询
	public Employees locateEmployeesById(Long id);
	
	//按警号查询
	public Employees locateEmployeesByWorkNumber(String workNumber);

	//单个查询
	public Employees findEmployeesByParam(String queryStr,String[] paramNames,Object[] values);
	
	// 查找警员，带部门，区域，组织
	public Employees findEmployeesBean(Long empId); 

	//查询全部
	public List<Employees> findAllEmployeess();
	public List<Employees> findAllEmployeess(long orgId);

	//列表查询
	public List<Employees> findEmployeessByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Employees> findEmployeessOnPage(Page<Employees> page, String queryStr,String[] paramNames,Object[] values);

	/**
	 * 改变警员单位，登录后切换单位
	 * @param employees
	 */
	public void changeEmployeesOrganization(Employees employees);

	public Employees  locateEmployeesByIdNum(String idNum);
}

