package com.jsdz.digitalevidence.activemq.service;

import javax.jms.ConnectionFactory;
import javax.jms.MessageListener;

/**
 * 
 * @类名: ConsumerService
 * @说明: 消息订阅接口
 *
 * @author: kenny
 * @Date	2017年10月27日上午11:34:07
 * 修改记录：
 *
 * @see
 */
public interface ConsumerService {
	//public void consumerMesaage(ConnectionFactory connectionFactory ,Class<MessageListener> clazz,String topicName);
	public void consumerMesaage(ConnectionFactory connectionFactory,MessageListener listener,String topicName);
}
