package com.jsdz.digitalevidence.archives.service.impl;

import com.jsdz.digitalevidence.archives.model.ArchivesDoc;
import com.jsdz.digitalevidence.archives.mapper.ArchivesDocMapper;
import com.jsdz.digitalevidence.archives.service.IArchivesDocService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * VIEW 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-11
 */
@Service
public class ArchivesDocServiceImpl  implements IArchivesDocService {

    @Autowired
    private ArchivesDocMapper archivesDocMapper;
    @Override
    public List<ArchivesDoc> findDocByArchivesId(Long archivesId) {
        return archivesDocMapper.findDocByArchivesId(archivesId);
    }
}
