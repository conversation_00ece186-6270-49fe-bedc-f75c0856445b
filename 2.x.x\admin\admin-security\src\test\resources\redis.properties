#redis for spring session,spring cache
#redis model:0=none redis
#			 1=single server model
#            2=master-slave model (sentinal)
#            3=cluster model (cluster)
redis.model=0
#redis.host=127.0.0.1
redis.host=127.0.0.1
redis.port=6379
redis.pwd=
redis.maxIdle=8
redis.maxActive=300

redis.maxWait=1000
redis.timeout=100000
redis.maxTotal=1000
redis.minIdle=0
redis.testOnBorrow=true


#spring session
redis.session.timeout=6000

#cluster model
spring.redis.cluster.nodes=*************:7001,*************:7002,*************:7003
spring.redis.cluster.max-redirects=3

#sentinel model
spring.redis.redis.sentinel.masterName=jsmaster
spring.redis.redis.sentinel.password=
#the first string is master node
spring.redis.sentinel.nodes=*************:7100,*************:7101
spring.redis.sentinel.node1=***********:26379
spring.redis.sentinel.node2=***********:26379
spring.redis.sentinel.node3=***********:26379