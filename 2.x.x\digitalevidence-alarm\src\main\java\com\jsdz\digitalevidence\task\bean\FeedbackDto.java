package com.jsdz.digitalevidence.task.bean;

import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.jsdz.digitalevidence.task.constant.BusinessCate;

public class FeedbackDto {

    /**
     * 业务类型
     */
    private BusinessCate cate;

    /**
     * 设备号
     */
    private String code;

    /**
     * 任务id
     */
    private Long id;

    /**
     * 文档路径
     */
    private String docUri;

    /**
     * 执法仪id
     */
    private Long recorderId;

    /**
     * 存储id
     */
    private Long storageId;

    /**
     * 警员id
     */
    private Long employeesId;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 抓拍时间
     */
    private String captureTime;

    /**
     * 其他参数
     */
    private String param;

    public BusinessCate getCate() {
        return cate;
    }

    public void setCate(BusinessCate cate) {
        this.cate = cate;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getDocUri() {
        return docUri;
    }

    public void setDocUri(String docUri) {
        this.docUri = docUri;
    }

    public Long getRecorderId() {
        return recorderId;
    }

    public void setRecorderId(Long recorderId) {
        this.recorderId = recorderId;
    }

    public Long getStorageId() {
        return storageId;
    }

    public void setStorageId(Long storageId) {
        this.storageId = storageId;
    }

    public Long getEmployeesId() {
        return employeesId;
    }

    public void setEmployeesId(Long employeesId) {
        this.employeesId = employeesId;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getCaptureTime() {
        return captureTime;
    }

    public void setCaptureTime(String captureTime) {
        this.captureTime = captureTime;
    }

    public void check() {
        Assert.notNull(id, "任务id不能为空");
        Assert.notEmpty(code, "设备号不能为空");
        Assert.notEmpty(longitude, "经度不能为空");
        Assert.notEmpty(latitude, "纬度不能为空");
        Assert.notEmpty(captureTime, "抓拍时间不能为空");
    }

}
