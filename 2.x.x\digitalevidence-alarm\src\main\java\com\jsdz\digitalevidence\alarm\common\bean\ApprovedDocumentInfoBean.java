package com.jsdz.digitalevidence.alarm.common.bean;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.digitalevidence.document.model.DocumentCate;

/**
 * 
 * @类名: ApprovedDocumentInfoBean
 * @说明: 视频审核DocumentInfo封装类
 *
 * @author: kenny
 * @Date	2018年1月5日上午10:19:41
 * 修改记录：
 *
 * @see
 */
public class ApprovedDocumentInfoBean implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 6218124395662215266L;
	private Long id;
	private Long docId;//对Document id
	private String docName;
	private Long docSize;
	private DocumentCate cate;
	private Long duration;
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date captureTime;//报摄时间，
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date uploadTime;//上传时间
	private String policeCode;//
	private String policeName;//
	private String orgCode;//
	private String orgName;//
	private String fileType; //文件分类
	
	private Integer typical;//典型视频 1 典型 0非典型
	private String typicalContext; //典型分类
	private String note;      //描述
	private String address;   //地点说明
	private String caseType;  //事件类型
	private String caseLevel; //事件等级	
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date expiryTime;  //保存截止时间
	private Integer approved; //是否已审核(1已审核，0未审核)
	private Integer approvedResult; //审核结果(1通过，0不通过)
	private String approveCause; //不通过原因	

	private String alarmCode;    //警情号
    private Date alarmTime;      //报警时间
    private String alarmName;    //报警人姓名
    private String alarmTel;     //报警电话
    private String alarmContext; //报警内容
    
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getDocId() {
		return docId;
	}
	public void setDocId(Long docId) {
		this.docId = docId;
	}
	public String getDocName() {
		return docName;
	}
	public void setDocName(String docName) {
		this.docName = docName;
	}
	public Long getDocSize() {
		return docSize;
	}
	public void setDocSize(Long docSize) {
		this.docSize = docSize;
	}
	public DocumentCate getCate() {
		return cate;
	}
	public void setCate(DocumentCate cate) {
		this.cate = cate;
	}
	public Long getDuration() {
		return duration;
	}
	public void setDuration(Long duration) {
		this.duration = duration;
	}
	public Date getCaptureTime() {
		return captureTime;
	}
	public void setCaptureTime(Date captureTime) {
		this.captureTime = captureTime;
	}
	public Date getUploadTime() {
		return uploadTime;
	}
	public void setUploadTime(Date uploadTime) {
		this.uploadTime = uploadTime;
	}
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getFileType() {
		return fileType;
	}
	public void setFileType(String fileType) {
		this.fileType = fileType;
	}
	public Integer getTypical() {
		return typical;
	}
	public void setTypical(Integer typical) {
		this.typical = typical;
	}
	public String getTypicalContext() {
		return typicalContext;
	}
	public void setTypicalContext(String typicalContext) {
		this.typicalContext = typicalContext;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getCaseType() {
		return caseType;
	}
	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}
	public String getCaseLevel() {
		return caseLevel;
	}
	public void setCaseLevel(String caseLevel) {
		this.caseLevel = caseLevel;
	}
	public Date getExpiryTime() {
		return expiryTime;
	}
	public void setExpiryTime(Date expiryTime) {
		this.expiryTime = expiryTime;
	}
	public Integer getApproved() {
		return approved;
	}
	public void setApproved(Integer approved) {
		this.approved = approved;
	}
	public Integer getApprovedResult() {
		return approvedResult;
	}
	public void setApprovedResult(Integer approvedResult) {
		this.approvedResult = approvedResult;
	}
	public String getApproveCause() {
		return approveCause;
	}
	public void setApproveCause(String approveCause) {
		this.approveCause = approveCause;
	}
	public String getAlarmCode() {
		return alarmCode;
	}
	public void setAlarmCode(String alarmCode) {
		this.alarmCode = alarmCode;
	}
	public Date getAlarmTime() {
		return alarmTime;
	}
	public void setAlarmTime(Date alarmTime) {
		this.alarmTime = alarmTime;
	}
	public String getAlarmName() {
		return alarmName;
	}
	public void setAlarmName(String alarmName) {
		this.alarmName = alarmName;
	}
	public String getAlarmTel() {
		return alarmTel;
	}
	public void setAlarmTel(String alarmTel) {
		this.alarmTel = alarmTel;
	}
	public String getAlarmContext() {
		return alarmContext;
	}
	public void setAlarmContext(String alarmContext) {
		this.alarmContext = alarmContext;
	}
    
    
	
}
