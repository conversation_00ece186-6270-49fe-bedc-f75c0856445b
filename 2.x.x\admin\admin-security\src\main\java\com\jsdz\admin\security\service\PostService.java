package com.jsdz.admin.security.service;

/**
 * 
 * @类名: PostService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-12-30 11:53:22
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import com.jsdz.admin.security.bean.PostBean;
import com.jsdz.admin.security.model.Post;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface PostService {

	//新增
	public AjaxResult addPost(Post post);

	//修改
	public AjaxResult updatePost(Post post);

	//删除
	public AjaxResult deletePost(Post post);

	//按id查询,结果是游离状态的数据
	public Post findPostById(Long id);

	//按id查询
	public Post locatePostById(Long id);

	//单个查询
	public Post findPostByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<Post> findAllPosts();

	//列表查询
	public List<Post> findPostsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Post> findPostsOnPage(Page<Post> page, String queryStr,String[] paramNames,Object[] values);

}

