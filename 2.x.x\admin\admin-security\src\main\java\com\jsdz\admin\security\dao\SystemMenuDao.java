package com.jsdz.admin.security.dao;

/**
 *
 * @类名: SystemMenuDao
 * @说明:
 * @author: kenny
 * @Date 2017-09-08 11:59:54
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import com.jsdz.admin.security.bean.SystemMenuBean;
import com.jsdz.admin.security.model.SystemMenu;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface SystemMenuDao extends GenericORMEntityDAO<SystemMenu,Long> {

	//新增
	public void addSystemMenu(SystemMenu systemMenu);

	//修改
	public void updateSystemMenu(SystemMenu systemMenu);

	//删除
	public void deleteSystemMenu(SystemMenu systemMenu);

	//按id查询,结果是游离状态的数据
	public SystemMenu findSystemMenuById(Long id);

	//按id查询
	public SystemMenu locateSystemMenuById(Long id);

	//单个查询
	public SystemMenu findSystemMenuByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<SystemMenu> findAllSystemMenus();

	//列表查询
	public List<SystemMenu> findSystemMenusByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<SystemMenu> findSystemMenusOnPage(Page<SystemMenu> page,String queryStr,String[] paramNames,Object[] values);
	
	public List<SystemMenuBean> QueryNamedAllDataSQL(final String sqlStr, final String[] paramNames, final Object[] values);

	public List<Long> findAllParentId();
}

