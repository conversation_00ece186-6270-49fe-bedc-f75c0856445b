package com.jsdz.digitalevidence.alarm.alarm110.model;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.model.Operator;
/**
 * @类名: AlarmInfo
 * @说明: 警情
 *
 * <AUTHOR>
 * @Date	 2017年8月29日 上午19:11:05
 * 修改记录：
 *
 * @see 	 
 */
public class AlarmInfo implements Serializable , Cloneable{

	private static final long serialVersionUID = -1250138956283498505L;
	
	private Long id;    //ID
    private String alarmCode;    //警情号
    private String alarmName;    //报警人姓名
    private String title;    //标题
    private String alarmTel;    //报警电话
    private String alarmContext;    //报警内容
/*
    private Organization reveiceOrg;    //处警单位
*/
    private Integer isRelation=0; //是否已关联(0未关联，1已关联)
    //private Operator police;    //处理人
    private Date createTime; //创建时间
    private String alarmResult;//处理过程
    private String caseId; // 案件编号
    /*1{color:#FF0033;}红2{color:#00CC00;}绿3{color:#999900;}黄4{color:#00CC00;}绿5{color:#999900;}黄*/
    private String alarmSuperviseType;//警情监督情况类型
    private String alarmAddress;//警情发生地址
    
    private String alarmProccessCode;//处警单编号
    private String alarmReceiveCode;//接警单编号
    private String alarmFeebackCode;//反馈单编号
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date alarmTime;    //报警时间
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date processTime;    //处警时间
    private String archiveCode;//案件编号
    private String alarmPolices;//处警人，多个人以,号隔开
    private String orgPaths;//处警单位，多个人以;号隔开
	private Date receiveTime;//接警时间
	private Date endTime;//结束接警时间
	private Integer status;//警情状态 -1、未发送 0、未出警 1、已出警 2、结束出警
//	private Long orgId;//单位ID

	private Organization org;

	/*public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}*/

	public Date getEndTime() {
		return endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Date getReceiveTime() {
		return receiveTime;
	}

	public void setReceiveTime(Date receiveTime) {
		this.receiveTime = receiveTime;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getAlarmCode() {
		return alarmCode;
	}
	public void setAlarmCode(String alarmCode) {
		this.alarmCode = alarmCode;
	}
	public Date getAlarmTime() {
		return alarmTime;
	}
	public void setAlarmTime(Date alarmTime) {
		this.alarmTime = alarmTime;
	}
	public String getAlarmName() {
		return alarmName;
	}
	public void setAlarmName(String alarmName) {
		this.alarmName = alarmName;
	}
	public String getAlarmTel() {
		return alarmTel;
	}
	public void setAlarmTel(String alarmTel) {
		this.alarmTel = alarmTel;
	}
	public String getAlarmContext() {
		return alarmContext;
	}
	public void setAlarmContext(String alarmContext) {
		this.alarmContext = alarmContext;
	}
	public Integer getIsRelation() {
		return isRelation;
	}
	public void setIsRelation(Integer isRelation) {
		this.isRelation = isRelation;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getAlarmResult() {
		return alarmResult;
	}
	public void setAlarmResult(String alarmResult) {
		this.alarmResult = alarmResult;
	}
	public String getAlarmAddress() {
		return alarmAddress;
	}
	public void setAlarmAddress(String alarmAddress) {
		this.alarmAddress = alarmAddress;
	}
	public String getCaseId() {
		return caseId;
	}
	public void setCaseId(String caseId) {
		this.caseId = caseId;
	}
	public String getAlarmSuperviseType() {
		return alarmSuperviseType;
	}
	public void setAlarmSuperviseType(String alarmSuperviseType) {
		this.alarmSuperviseType = alarmSuperviseType;
	}
	public String getAlarmProccessCode() {
		return alarmProccessCode;
	}
	public void setAlarmProccessCode(String alarmProccessCode) {
		this.alarmProccessCode = alarmProccessCode;
	}
	public String getAlarmReceiveCode() {
		return alarmReceiveCode;
	}
	public void setAlarmReceiveCode(String alarmReceiveCode) {
		this.alarmReceiveCode = alarmReceiveCode;
	}
	public String getArchiveCode() {
		return archiveCode;
	}
	public void setArchiveCode(String archiveCode) {
		this.archiveCode = archiveCode;
	}
	public String getAlarmFeebackCode() {
		return alarmFeebackCode;
	}
	public void setAlarmFeebackCode(String alarmFeebackCode) {
		this.alarmFeebackCode = alarmFeebackCode;
	}
	public Date getProcessTime() {
		return processTime;
	}
	public void setProcessTime(Date processTime) {
		this.processTime = processTime;
	}
	public String getAlarmPolices() {
		return alarmPolices;
	}
	public void setAlarmPolices(String alarmPolices) {
		this.alarmPolices = alarmPolices;
	}
	public String getOrgPaths() {
		return orgPaths;
	}
	public void setOrgPaths(String orgPaths) {
		this.orgPaths = orgPaths;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Organization getOrg() {
		return org;
	}

	public void setOrg(Organization org) {
		this.org = org;
	}
}
