/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.service;

import java.util.Date;

import com.jsdz.admin.security.model.Operator;
import com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReportBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentBean;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.cycle.CycleSelector;
import com.jsdz.digitalevidence.assessment.model.ReportType;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessment;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.ShootingSummary;

/**
 * @类名: AssessmentService
 * @说明: 拍摄考核应用服务
 *        
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午6:04:13
 * 修改记录：
 *
 * @see 	 
 */
public interface AssessmentService {
	
	/**
	 * @说明：打开视频考核
	 *
	 * <AUTHOR>
	 * @param docId
	 * @return
	 * 
	 */
	public ShootingAssessmentBean openAssessment(Long docId, Operator operator, String ip);
	
	/**
	 * @说明：提交拍考核
	 *
	 * <AUTHOR>
	 * @param docId 考评文档
	 * @param assessment 考评
	 * @param cate 考评分类， 日常；随机
	 */
	public void submitAssessment(ShootingAssessment assessment);
	
	/**
	 * @说明：生成警员考核报告
	 *
	 * <AUTHOR>
	 * @param policeId
	 * @param startDate 报告起始日期
	 * @param endDate   报告结束日期
	 * @param type
	 * @return
	 * 
	 */
	public ShootingAssessmentReport generateAccessmentReport(Long policeId, Date startDate, Date endDate, ReportType type);
	public ShootingAssessmentReport generateAndSaveAccessmentReport(Long policeId, Date startDate, Date endDate, ReportType type);
	
	/**
	 * @说明： 冻结考核报告
	 *        考核结束日，系统自动冻结考核报告
	 *        
	 *
	 * <AUTHOR>
	 * @param reportId
	 * @return
	 * 
	 */
	public ShootingAssessmentReport frozenAccessmentReport(Long reportId);
	

	
	/**
	 * @说明：获取考核报告
	 *
	 * <AUTHOR>
	 * @param reportId
	 * @return
	 * 
	 */
	public ShootingAssessmentReport getShootingAccessmentReport(Long reportId);
	public CompleteAssessmentReportBean getShootingAccessmentReportBean(Long reportId);
	
	/**
	 * @说明：评语
	 *
	 * <AUTHOR>
	 * @param reportId
	 * @param comments
	 * @return
	 * 
	 */
	public void commentShootingAccessmentReport(Long reportId, String comment);
	
	/**
	 * @说明：统计拍摄
	 *
	 * <AUTHOR>
	 * @param policeId
	 * @param startDate
	 * @param endDate
	 * @return
	 * 
	 */
	public ShootingSummary getShootingSummary(Long policeId, Date startDate, Date endDate);
	
	/**
	 * @说明：获取警员拍摄考核统计
	 *
	 * <AUTHOR>
	 * @param policeId
	 * @param startDate
	 * @param endDate
	 * @return
	 * 
	 */
	public ShootingAssessmentSummary getShootingAccessmentSummary(Long policeId, Date startDate, Date endDate);
	
	/**
	 * @说明：获取当前周期
	 *
	 * <AUTHOR>
	 * @return
	 * 
	 */
	public Cycle getCurCycle();
	
	/**
	 * @说明：获取上一周期，下一周期
	 *
	 * <AUTHOR>
	 * @param cycle
	 * @param selector
	 * @return
	 * 
	 */
	public Cycle getCycle(Date cycleDate, CycleSelector selector);

}
