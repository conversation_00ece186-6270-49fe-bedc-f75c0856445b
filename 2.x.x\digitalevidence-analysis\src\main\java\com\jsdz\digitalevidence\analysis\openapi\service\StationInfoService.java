package com.jsdz.digitalevidence.analysis.openapi.service;

/**
 * 
 * @类名: StationInfoService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-06-09 16:03:51
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.analysis.openapi.model.StationInfo;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface StationInfoService {

	//新增
	public AjaxResult addStationInfo(StationInfo stationInfo);

	//修改
	public AjaxResult updateStationInfo(StationInfo stationInfo);

	//删除
	public AjaxResult deleteStationInfo(StationInfo stationInfo);

	//按id查询,结果是游离状态的数据
	public StationInfo findStationInfoById(Long id);

	//按id查询
	public StationInfo locateStationInfoById(Long id);

	//单个查询
	public StationInfo findStationInfoByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<StationInfo> findAllStationInfos();

	//列表查询
	public List<StationInfo> findStationInfosByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<StationInfo> findStationInfosOnPage(Page<StationInfo> page, String queryStr,String[] paramNames,Object[] values);

}

