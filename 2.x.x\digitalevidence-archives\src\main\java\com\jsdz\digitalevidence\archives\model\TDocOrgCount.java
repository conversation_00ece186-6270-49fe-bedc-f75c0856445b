package com.jsdz.digitalevidence.archives.model;

import java.math.BigDecimal;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class TDocOrgCount implements Serializable {

    private static final long serialVersionUID = 1L;

    private String orgname;

    private String orgcode;

    private String path;

    private Long sumdoc;

    private BigDecimal sumsize;

    private BigDecimal sumduration;

    private BigDecimal summp4;

    private BigDecimal sumpicture;

    private BigDecimal sumaudio;


}
