package com.jsdz.digitalevidence.assessment.bean.plan;
import java.text.ParseException;
import java.text.SimpleDateFormat;
/**
 * @类名: AssessmentPlanBean
 * @说明: 
 *
 * <AUTHOR>
 * @Date	 2019年6月21日上午10:12:52
 * 修改记录：
 *
 * @see 	 
 */
import java.util.Date;

import org.codehaus.plexus.util.StringUtils;

import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlan;

public class AssessmentPlanBean {


	private Long id;
	private String description;
	private String beginDate;
	private String endDate;
	private Integer status;
	private String createTime;
	
	public AssessmentPlan assignTo() throws ParseException{
		AssessmentPlan sur = new AssessmentPlan();
		assignTo(sur);
		return sur;
		
	}
	public void assign(AssessmentPlan sur ){
		this.setId(sur.getId());
		this.setDescription(sur.getDescription());
		this.setStatus(sur.getStatus());
		if (sur.getBeginDate()!=null)
			this.setBeginDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(sur.getBeginDate()));
		if (sur.getEndDate()!=null)
			this.setEndDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(sur.getEndDate()));
		if (sur.getCreateTime()!=null)
			this.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(sur.getCreateTime()));
		
	}
	
	public void assignTo(AssessmentPlan dest) throws ParseException{
		dest.setId(this.getId());
		dest.setDescription(this.getDescription());
		dest.setStatus(this.getStatus());
		if (!StringUtils.isEmpty(this.getBeginDate()))
			dest.setBeginDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").parse(this.getBeginDate()));
		if (!StringUtils.isEmpty(this.getEndDate()))
			dest.setEndDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").parse(this.getEndDate()));
		//this.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:dd").format(sur.getCreateTime()));

	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getBeginDate() {
		return beginDate;
	}
	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}
	public String getEndDate() {
		return endDate;
	}
	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getCreateTime() {
		return createTime;
	}
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}

}
