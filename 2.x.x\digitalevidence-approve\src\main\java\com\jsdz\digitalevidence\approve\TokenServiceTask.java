package com.jsdz.digitalevidence.approve;

import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jsdz.digitalevidence.approve.service.BusinessTokenService;

/**
 * @类名: TokenServiceTask
 * @说明: 令牌任务，
 *        接入流程引擎，生成令牌
 *
 * <AUTHOR>
 * @Date	 2017年4月18日 下午6:34:29
 * 修改记录：
 *
 * @see 	
 * 
 */
@Service
public class TokenServiceTask implements JavaDelegate {  
  
	@Autowired
	private BusinessTokenService tokenService;
	
    @Override  
    public void execute(DelegateExecution arg0) throws Exception {  
    	/*// 审批结果
    	Object approvedV = arg0.getVariable(ApproveConsts.Proced_Param_Approved);
    	if(approvedV==null)
    		throw new Exception("流程变量缺失, [approved]");
    	Boolean approved = Boolean.valueOf((String)approvedV);
    	// 
    	String userId = (String)arg0.getVariable(ApproveConsts.Proced_Param_UserId);
    	Long pmId = (Long)arg0.getVariable(ApproveConsts.Proced_Param_ProcedMethodId);
    	//
    	ApproveToken token = tokenDao.getTokneOfPorcMethod(pmId, userId);
    	// 审批评语，可无
    	Object commentsV = arg0.getVariable(ApproveConsts.Proced_Param_Approved_Comments);
    	if(commentsV!=null) {
    		String comments = (String)commentsV;
    		token.setComments(comments);
    	}
    	// 生成令牌
    	token.setStatus(ApproveStatus.completed);
    	token.setApproved(approved);
    	tokenDao.saveOrUpdate(token);*/
       
    }

}  
