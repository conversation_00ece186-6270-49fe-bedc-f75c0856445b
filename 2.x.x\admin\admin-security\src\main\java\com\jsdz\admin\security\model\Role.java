package com.jsdz.admin.security.model;
/**
 * 
 * @类名: Role
 * @说明: 角色类
 *
 * @author: kenny
 * @Date	2017年4月26日上午10:30:26
 * 修改记录：
 *
 * @see
 */

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jsdz.admin.org.model.Organization;

public class Role implements Serializable{
	private static final long serialVersionUID = 3609836724731667198L;
	
	//SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	
	private Long id;
	private String roleName;//角色名称
	private String description;//角色描述（说明）
	@JsonIgnore
	private Operator createBy;//创建人
	private String actionStr;//权限
	private String actionNames;//权限名称
	private String orgNames;//单位名称
	
	private Integer roleLevel = 1;//1 普通警员（只能查看自己的文档）2 资料管理员(能查看本单位的文档)  3 高级资料员（能查看下级单位的文档）

	private Integer orgLevel;//用于角色查看等级
	
	public String getActionNames() {
		return actionNames;
	}
	public void setActionNames(String actionNames) {
		this.actionNames = actionNames;
	}
	private Date createTime;//创建时间
	
	public Role(){
		super();
	}
	public Role(String roleName,String description){
		this.roleName=roleName;
		this.description=description;
		this.createTime = new Date();
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getRoleName() {
		return roleName;
	}
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public Operator getCreateBy() {
		return createBy;
	}
	public void setCreateBy(Operator createBy) {
		this.createBy = createBy;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public String getActionStr() {
		return actionStr;
	}
	public void setActionStr(String actionStr) {
		this.actionStr = actionStr;
	}
	public Integer getRoleLevel() {
		return roleLevel;
	}
	public void setRoleLevel(Integer roleLevel) {
		this.roleLevel = roleLevel;
	}
	public String getOrgNames() {
		return orgNames;
	}
	public void setOrgNames(String orgNames) {
		this.orgNames = orgNames;
	}

	public Integer getOrgLevel() {
		return orgLevel;
	}

	public void setOrgLevel(Integer orgLevel) {
		this.orgLevel = orgLevel;
	}
}
