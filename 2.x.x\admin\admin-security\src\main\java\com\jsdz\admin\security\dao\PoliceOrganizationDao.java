package com.jsdz.admin.security.dao;

/**
 *
 * @类名: PoliceOrganizationDao
 * @说明:
 * @author: kenny
 * @Date 2019-12-19 15:29:11
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.PoliceOrganization;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface PoliceOrganizationDao extends GenericORMEntityDAO<PoliceOrganization,Long> {

	/** 
 	 * 新增
	 */ 
 	public void addPoliceOrganization(PoliceOrganization policeOrganization);

	/** 
 	 * 修改
	 */ 
 	public void updatePoliceOrganization(PoliceOrganization policeOrganization);

	/** 
 	 * 删除
	 */ 
 	public void deletePoliceOrganization(PoliceOrganization policeOrganization);

	/** 
 	 * 按id查询,结果是游离状态的数据
	 */ 
 	public PoliceOrganization findPoliceOrganizationById(Long id);

	/** 
 	 * 按id查询
	 */ 
 	public PoliceOrganization locatePoliceOrganizationById(Long id);

	/* 
 	 * 单个查询
	 */ 
 	public PoliceOrganization findPoliceOrganizationByCondition(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 查询全部
	 */ 
 	public List<PoliceOrganization> findAllPoliceOrganizations();

	/** 
 	 * 列表查询
	 */ 
 	public List<PoliceOrganization> findPoliceOrganizationsByCondition(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 分页查询
	 */ 
 	public Page<PoliceOrganization> findPoliceOrganizationsOnPage(Page<PoliceOrganization> page,String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 执行指定的HQL文件
	 */ 
 	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);

}

