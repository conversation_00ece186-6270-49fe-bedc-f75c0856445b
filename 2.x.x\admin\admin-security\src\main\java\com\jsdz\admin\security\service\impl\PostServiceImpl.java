package com.jsdz.admin.security.service.impl;

import java.util.Date;

/**
 * 
 * @类名: PostServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-12-30 11:53:22
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
//import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.bean.OrganizationBean;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.bean.PostBean;
import com.jsdz.admin.security.dao.EmployeesDao;
import com.jsdz.admin.security.dao.PostDao;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.model.Post;
import com.jsdz.admin.security.service.PostService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.reportquery.ReportQueryDao;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("PostServiceImpl")
public class PostServiceImpl implements PostService {

	@Autowired
	private PostDao postDao;
	@Autowired
	private EmployeesDao employeesDao;
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<PostBean> reportQueryDao;

	//新增
	public AjaxResult addPost(Post post) {
		AjaxResult result = new AjaxResult(100,false,"",null);
		if (post.getPost() == null){
			result.setMsg("岗位必须录入。");
			return result;
		}
		
		if (null != postDao.findPostByCondition("from Post p where p.post=:post",
				  new String[]{"post"},new Object[]{post.getPost()})){
			result.setSuccess(false);
			result.setMsg("此岗位已存在");
			return result;
		}
		
		if (!checkEndlessLoop(post.getParentPost(),post)){
			result.setSuccess(false);
			result.setMsg("上级岗位有冲突");
			return result;
		}
		
		post.setCreateTime(new Date());
		postDao.addPost(post);
		
		if (post.getId() != null){
			String path =  String.valueOf(post.getId()) + "/";
			if (post.getParentPost() != null){
				Post p1 = locatePostById(post.getParentPost().getId());
				path = p1.getPath() + path;
			}
			post.setPath(path);
			postDao.updatePost(post);
		}
		result.setCode(200);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	@Transactional
	public AjaxResult updatePost(Post post) {
		AjaxResult result = new AjaxResult(100,false,"",null);
		if (post.getPost() == null){
			result.setMsg("岗位必须录入。");
			return result;
		}
		
		if (null != postDao.findPostByCondition("from Post p where p.post=:post and p.id <> :id",
				  new String[]{"post","id"},new Object[]{post.getPost(),post.getId()})){
			result.setSuccess(false);
			result.setMsg("岗位不能重复");
			return result;
		}
		
		if (!checkEndlessLoop(post.getParentPost(),post)){
			result.setSuccess(false);
			result.setMsg("上级岗位有冲突");
			return result;
		}
		
		Post srcPost = postDao.findPostById(post.getId());
		if (srcPost == null){
			result.setMsg("不存在的岗位资料");
			return result ;
		}

		//path
		String oldPath = srcPost.getPath();
		String newPath = String.valueOf(post.getId()) + "/";
		if (post.getParentPost() != null){
			Post parPost = postDao.findPostById(post.getParentPost().getId());
			if (parPost != null){
				newPath = parPost.getPath() + newPath;
			}
		}
		srcPost.setPost(post.getPost());
		srcPost.setParentPost(post.getParentPost());
		srcPost.setPath(newPath);
		postDao.updatePost(srcPost);
		//是否修改了上级岗位，如果修改了同时更新所有下级的path
		if (oldPath != newPath){
			postDao.updatePostPath(oldPath,newPath);
		}

		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deletePost(Post post) {
		AjaxResult result = new AjaxResult(100,false,"",null);
		if (null == post){
			result.setMsg("没有数据可删除");
			return result;
		}
		
		if (null == postDao.findPostById(post.getId())){
			result.setMsg("不存在的岗位资料");
			return result ;
		}

		if (null != postDao.findPostByCondition(
				"from Post p where p.parentPost.id=:id",
			  new String[]{"id"},new Object[]{post.getId()})){
			result.setMsg("岗位有下级，不能删除");
			return result;
		}
		
		List<Employees> emps = employeesDao.findEmployeessByPostId(post.getId());
		if (emps != null && emps.size() > 0){
			result.setMsg("此岗位已有警员在用，不能删除");
			return result;
		}
		
		postDao.deletePost(post);
		result.setCode(200);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public Post findPostById(Long id){

		return postDao.findPostById(id);
	}

	//按 id 查询
	public Post locatePostById(Long id) {
		return postDao.locatePostById(id);
	}

	//单个查询
	public Post findPostByParam(String queryStr, String[] paramNames, Object[] values) {
		return postDao.findPostByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<Post> findAllPosts() {
		return postDao.findAllPosts();
	}

	//列表查询
	public List<Post> findPostsByParam(String queryStr, String[] paramNames, Object[] values) {
		return postDao.findPostsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<Post> findPostsOnPage(Page<Post> page, String queryStr, String[] paramNames, Object[] values) {
		Page<Post> pos = postDao.findPostsOnPage(page, queryStr, paramNames, values);
		return pos;
	}


	//检查下级关系冲突,返回true为正确,没有冲突
	public boolean checkEndlessLoop(Post parentPost, Post post ){
		boolean result = true;
		if (parentPost == null){
			return result;
		}
		
		if (parentPost.getId() == post.getId()){
			return false;
		}
		
		//如果没下级了不再继续检查
		List<Post> posts = postDao.findPostsByCondition(
				"from Post p where p.parentPost.id = :id",
				new String[]{"id"},new Object[]{post.getId()});
		if (posts == null || posts.size() == 0){
			return result;
		}
		
		
		for (int i=0;i<posts.size();i++){
			if (posts.get(i).getId() == parentPost.getId()){
				result = false;
				break;
			}
			
			//递归查找
			result = checkEndlessLoop(parentPost,posts.get(i));
			if (result == false){
				return result;
			}
		}
		return result;
	}
}
