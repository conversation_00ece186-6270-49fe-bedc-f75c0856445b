package com.jsdz.digitalevidence.assessment.service;

import java.util.Date;

/**
 * 
 * @类名: AssessmentPlanDocmentService
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-06-19 16:05:25
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanDocment;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface AssessmentPlanDocmentService {

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment);

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updateAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment);

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deleteAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment);

	/** 
 	 * 按id查询,结果是游离状态的数据
	 */ 
 	public AssessmentPlanDocment findAssessmentPlanDocmentById(Long id);

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlanDocment locateAssessmentPlanDocmentById(Long id);

	/** 
 	 * 单个查询
	 */ 
 	public AssessmentPlanDocment findAssessmentPlanDocmentByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanDocment> findAllAssessmentPlanDocments();

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanDocment> findAssessmentPlanDocmentsByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanDocment> findAssessmentPlanDocmentsOnPage(Page<AssessmentPlanDocment> page, String queryStr,String[] paramNames,Object[] values);

 	public List<Object> getRadomDoc(Date time1,Date time2,Integer num);
}

