package com.jsdz.digitalevidence.test.proced;

import java.util.Date;
import java.util.List;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.service.OrganizationService;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.digitalevidence.approve.dao.ApplyProcessDao;
import com.jsdz.digitalevidence.approve.dao.ApproveBusinessDao;
import com.jsdz.digitalevidence.approve.dao.BusinessObjectTypeDao;
import com.jsdz.digitalevidence.approve.dao.BusinessTokenDao;
import com.jsdz.digitalevidence.approve.model.ApplyProcess;
import com.jsdz.digitalevidence.approve.model.ApproveBusiness;
import com.jsdz.digitalevidence.approve.model.ApproveStatus;
import com.jsdz.digitalevidence.approve.model.BusinessObject;
import com.jsdz.digitalevidence.approve.model.BusinessObjectType;
import com.jsdz.digitalevidence.approve.model.BusinessToken;
import com.jsdz.utils.BeanUtils;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: ApproveDaoTest
 * @说明: 业务审批Dao单元测试
 *
 * <AUTHOR>
 * @Date	 2017年4月24日 下午2:00:03
 * 修改记录：
 *
 * @see 	 
 */
@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations={
		"/testApplicationContext-common.xml",
		"/testApplicationContext-activiti.xml",
		"/testApplicationContext-dynsql.xml"
		})
public class ApproveDaoTest {

	@Autowired
	private BusinessObjectTypeDao boTypeDao;
	@Autowired
	private ApproveBusinessDao businessDao;	
	@Autowired
	private BusinessTokenDao tokenDao;	
	@Autowired
	private ApplyProcessDao appProcDao;	
	
	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@Before
	public void setUp() throws Exception {
	}

	/**
	 * @说明：
	 * 
	 * <AUTHOR>
	 * @throws java.lang.Exception
	 * 
	 * @异常：
	 */
	@After
	public void tearDown() throws Exception {
		
	}
	
	// 新建业务对象类型
	@Test
	@Transactional
	public void testAddBOType() {
		BusinessObjectType type = new BusinessObjectType();
		type.setClazz(this.getClass());
		type.setUrl("document/browseDocument");
		this.boTypeDao.saveOrUpdate(type);
	}
	
	// 新建审批业务
	@Test
	@Transactional
	public void testAddApproveBusiness() {
		ApproveBusiness business = new ApproveBusiness();
		business.setCode("10002");
		business.setUri("document/getPlayPathsM");
		business.setComment("文档多幅播放");
		this.businessDao.saveOrUpdate(business);
	}

	// 删除审批业务
	@Test
	@Transactional
	public void testDelApproveBusiness() {
		ApproveBusiness business = businessDao.get(1L);
		this.businessDao.delete(business);
	}
	
	// 获取所有审批业务
	@Test
	@Transactional
	public void testGetAllApproveBusiness() {
		List<ApproveBusiness> businesses = businessDao.getAllApproveBusiness();
		BeanUtils.printBean(businesses);
	}
	
	@Autowired
	private EmployeesService empService;
	
	// 业务令牌
	@Test
	@Transactional
	public void testAddBusinessToken() {
		Employees emp = empService.locateEmployeesById(4L);
		ApproveBusiness business = businessDao.get(1L);
		BusinessToken token = new BusinessToken();
		token.setBusiness(business);
		token.setCreateTime(new Date());
		token.setStartDate(new Date());
		token.setEndDate(DateTimeUtils.getPreDayN(new Date(), 10));
		token.setOwner(emp);
		token.setProcInstId("1000001");
		token.setTimes(1);
		tokenDao.saveOrUpdate(token);
	}

	// 业务令牌增加业务对象
	@Test
	@Transactional
	public void testAddBusinessObject() {
		BusinessToken token = tokenDao.get(1L);
		//
		BusinessObjectType type = boTypeDao.get(1L);
		//
		BusinessObject bo1 = new BusinessObject();
		bo1.setObjId(1L);
		bo1.setObjType(type);
		//
		BusinessObject bo2 = new BusinessObject();
		bo2.setObjId(2L);
		bo2.setObjType(type);
		//
		token.getBos().add(bo1);
		token.getBos().add(bo2);
		//
		tokenDao.saveOrUpdate(token);
	}
	
	@Autowired
	private OrganizationService orgService;
	// 新建适用流程
	@Test
	@Transactional
	public void testAddApplyProcess() {
		Organization org = orgService.locateOrganizationById(2L);
		ApproveBusiness business = businessDao.get(1L);
		//
		ApplyProcess p = new ApplyProcess();
		//
		p.setProcDefId("1000001");
		p.setStatus(ApproveStatus.open);
		p.setOrg(org);
		p.setBusiness(business);
		//
		appProcDao.saveOrUpdate(p);
	}
	
	// 删除适用流程
	@Test
	@Transactional
	public void testDelApplyProcess() {
		ApplyProcess p = appProcDao.get(1L);
		//
		appProcDao.delete(p);
	}
}
