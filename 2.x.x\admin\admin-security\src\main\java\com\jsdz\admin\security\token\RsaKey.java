package com.jsdz.admin.security.token;
/**
 * 
 * @类名: RsaKey.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年6月2日上午10:08:13
 * 修改记录：
 *
 * @see
 */

import java.io.DataInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Date;
public class RsaKey {
	/*
	//生成公钥和私钥
	public static void GenPublicKey() throws NoSuchAlgorithmException, IOException{
		//加密算法: "RSA/ECB/PKCS1Padding", 公钥进行加密; 私钥进行解密.
	    KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");   
	    SecureRandom secureRandom = new SecureRandom(new Date().toString().getBytes());  
	    keyPairGenerator.initialize(1024, secureRandom);  
	    KeyPair keyPair = keyPairGenerator.genKeyPair();  
	    //公钥
	    String publicKeyFilename = "D:/publicKeyFile";  
	    byte[] publicKeyBytes = keyPair.getPublic().getEncoded();  
	    FileOutputStream fos = new FileOutputStream(publicKeyFilename);   
	    fos.write(publicKeyBytes);   
	    fos.close();
	    
	    //私钥
	    String privateKeyFilename = "D:/privateKeyFile";   
	    byte[] privateKeyBytes = keyPair.getPrivate().getEncoded();  
	    fos = new FileOutputStream(privateKeyFilename);   
	    fos.write(privateKeyBytes);   
	    fos.close(); 		
	}
 
	//读取公钥方法
    public static PublicKey getPublicKey(String filename) throws Exception {  
        File f = new File(filename);  
        FileInputStream fis = new FileInputStream(f);   
        DataInputStream dis = new DataInputStream(fis);  
        byte[] keyBytes = new byte[(int)f.length()];   
        dis.readFully(keyBytes);   
        dis.close();  
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);  
        KeyFactory kf = KeyFactory.getInstance("RSA");   
        return kf.generatePublic(spec);  
    } 
    */
    
/*    //读取私钥方法 
    @SuppressWarnings("restriction")
	public static PrivateKey getPrivateKey(String filename)throws Exception {  
        File f = new File(filename);  
        FileInputStream fis = new FileInputStream(f);  
        DataInputStream dis = new DataInputStream(fis);  
        byte[] keyBytes = new byte[(int)f.length()];  
        dis.readFully(keyBytes);  
        dis.close();  
        BASE64Encoder encoder = new BASE64Encoder();
        //Base64Encoder encoder = new Base64Encoder();
        String outstr = encoder.encode(keyBytes);
        System.out.println(outstr);
        
        PKCS8EncodedKeySpec spec =new PKCS8EncodedKeySpec(keyBytes);  
        KeyFactory kf = KeyFactory.getInstance("RSA");  
        return kf.generatePrivate(spec);  
      }  
    

  */
      
    //public static void main(String[] args) throws Exception, InvalidKeySpecException, IOException {  
    //	getPrivateKey("d:/privateKeyFile");  
    //}	

}
