package com.jsdz.digitalevidence.analysis.openapi;
/**
 * 
 * @类名: CxdsjLogResultBean
 * @说明: 表5　执法记录仪日志信息查询结果参数表
 *
 * @author: kenny
 * @Date	2017年6月7日下午9:33:47
 * 修改记录：
 *
 * @see
 */
import java.util.Date;
import com.jsdz.digitalevidence.analysis.openapi.model.CxDsjLog;


public class CxdsjLogResultBean {

	private Long id;
	private String cpxh;//产品序号
	private String rzlx;//日志类型
	private String wjmc;//文件名称
	private Date rzrq;//日志时间(yy-MM-dd HH:mm:ss)
	
	public void assign(CxDsjLog src){
		this.setCpxh(src.getGzz_xh());
		this.setRzlx(src.getRzlx());
		this.setWjmc(src.getWjmc());
		this.setRzrq(src.getRzrq());
	}
	
	public String getCpxh() {
		return cpxh;
	}
	public void setCpxh(String cpxh) {
		this.cpxh = cpxh;
	}
	public String getRzlx() {
		return rzlx;
	}
	public void setRzlx(String rzlx) {
		this.rzlx = rzlx;
	}
	public String getWjmc() {
		return wjmc;
	}
	public void setWjmc(String wjmc) {
		this.wjmc = wjmc;
	}
	public Date getRzrq() {
		return rzrq;
	}
	public void setRzrq(Date rzrq) {
		this.rzrq = rzrq;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	

}
