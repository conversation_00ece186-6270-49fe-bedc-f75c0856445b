/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.dao.impl;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.dao.ShootingAssessmentDao;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessment;
import com.jsdz.digitalevidence.document.model.Document;

/**
 * @类名: ShootingAssessmentDaoImpl
 * @说明: 拍摄考核dao实现
 *
 * <AUTHOR>
 * @Date	 2017年8月14日 下午4:07:05
 * 修改记录：
 *
 * @see 	 
 */
@Repository
public class ShootingAssessmentDaoImpl extends GenericEntityDaoHibernateImpl<ShootingAssessment,Long> 
					implements ShootingAssessmentDao {

}
