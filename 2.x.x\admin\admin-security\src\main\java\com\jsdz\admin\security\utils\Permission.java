package com.jsdz.admin.security.utils;
/**
 * 
 * @类名: Permission
 * @说明: annotation类, 用于标注到需要权限验证的地方
 * 用法：
 * 	   在方法前输入如下注解
 *   如：@Permission(name="下载文件",tag="downlaod")
 *   name=权限名称，tag=权限标识
 *   
 *
 * @author: kenny
 * @Date	2017年4月27日下午8:12:51
 * 修改记录：
 *
 * @see
 */

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import java.lang.annotation.RetentionPolicy;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Permission {
	String name=""; //权限名称
	String tag = ""; //权限标识
	String name() default "";//权限名称
	String tag() default "";//权限名称
	ResultType resultType() default ResultType.page; //当权限验证失败时，返回的结果类型是page或json;
}

