package com.jsdz.digitalevidence.activemq.service.impl;
/**
 * 
 * @类名: ProducerServiceImpl
 * @说明: ProducerService实现类
 *
 * @author: kenny
 * @Date	2017年10月25日上午10:17:33
 * 修改记录：
 *
 * @see
 */
import java.util.Map;
import javax.jms.Connection;
import javax.jms.ConnectionFactory;
import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.MessageProducer;
import javax.jms.Session;
import javax.jms.TextMessage;
import org.apache.activemq.command.ActiveMQObjectMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jsdz.digitalevidence.activemq.model.SysLog;
import com.jsdz.digitalevidence.activemq.service.ProducerService;
import com.jsdz.digitalevidence.activemq.service.SysLogService;
import com.jsdz.digitalevidence.activemq.utils.JsonParse;
import com.jsdz.digitalevidence.activemq.utils.LogType;
import com.jsdz.digitalevidence.activemq.utils.MqConnectionPool;
import com.jsdz.digitalevidence.activemq.utils.MqMode;
import com.jsdz.digitalevidence.activemq.utils.MqTopic;

@Service("ProducerServiceImpl")
public class ProducerServiceImpl implements ProducerService{
	
	 private Logger log = LoggerFactory.getLogger(this.getClass());
	 
	 @Autowired
	 private SysLogService sysLogService;

	 //Connection connection = null;
	 Session session;
	 Destination destination;// 消息的目的地  
	 MessageProducer messageProducer;// 消息生产者
	 
	 private MqMode mode = MqMode.QUEUE;
	 SysLog sysLog = new SysLog(this.getClass(),"");
	 String logTopic=null;
	 String keyWord=null;
	 
	//发送字符串消息
	@Override
	public boolean sendMessage(String topic,final String message){
		boolean result = false;
		try {
			//连接ActiveMq
			if (createTopicConnection(topic)==false){
				return result;
			}
			//写入系统 日志
			if (topic.equals(MqTopic.DOCUMENT.getName())){
				logTopic = "文档数据传递至MQ";
				keyWord=(String)JsonParse.getValue(message,"","data.docName");
			}else if (topic.equals(MqTopic.SITE.getName())){
				logTopic = "站点数据传递至MQ";
				keyWord=(String)JsonParse.getValue(message,"","data.siteNo");
			}else if (topic.equals(MqTopic.RECORDER.getName())){
				logTopic = "执法仪数据传递至MQ";
				keyWord=(String)JsonParse.getValue(message,"","data.code");
			}else if (topic.equals(MqTopic.RECODERREVIEW.getName())){
				logTopic = "视频监督回放传递至MQ";
				keyWord=(String)JsonParse.getValue(message,"","data.docName");
			}
			sysLog.setTopic(logTopic);
			try {
                TextMessage textmessage = session.createTextMessage(message); 
                keyWord="MQ消息";
                sysLogService.setLog(sysLog, LogType.INFO, keyWord, "传递内容："+message);
                messageProducer.send(textmessage); 
                result=true;
            } catch (JMSException e) { 
            	sysLogService.setLog(sysLog, LogType.ERROR, keyWord, "Send时出错"+e.toString());
            }  
            // 如果使用事务，这里需要使用提交才能将数据发送出去  
/*            session.commit();  
			} catch (JMSException e) {
			sysLog.setTopic("JMS错误");
			sysLogService.setLog(sysLog, LogType.ERROR, "JMS", e.toString());
*/		}finally{ 
			try {
/*				if (connection != null)
					connection.close();
*/				if (session != null)
					session.close();
			}catch (Exception e){
				sysLog.setTopic("ActiveMq错误");
          	  	sysLogService.setLog(sysLog, LogType.ERROR, keyWord, e.getMessage());
			}
 /*          if (connection != null) {  
               try {  
                  connection.close();  
              } catch (JMSException e) {
            	  sysLog.setTopic("ActiveMq错误");
            	  sysLogService.setLog(sysLog, LogType.ERROR, keyWord, e.toString());
              }  
           }
*/
		}
		return result;
	}


	//建立连接
	private boolean createTopicConnection(String topic) {//throws JMSException{
		boolean result=false;

/*		@SuppressWarnings("unchecked")
		Map<String,Object> mqMap = (Map<String,Object>)SysSource.getInstance().getAttribute("ACTIVE_MQ");
		if (mqMap == null){
			return result;
		}
		ConnectionFactory connectionFactory = (ConnectionFactory)mqMap.get("connectionFactory");
		if (connectionFactory == null){
			return result;
		}
*/		
		try{
/*			// 通过连接工厂获取连接  
			Connection connection = connectionFactory.createConnection();
			// 启动连接  
		    connection.start();
*/		    
		    //通过连接池取得连接
		    @SuppressWarnings("static-access")
			Connection connection = MqConnectionPool.getInstance().getConn();
	        //true 为支持事务，Session.AUTO_ACKNOWLEDGE 自动确认
	        //Session.CLIENT_ACKNOWLEDGE 用户确认
	        // 创建Session 
	        session=connection.createSession(Boolean.FALSE, Session.AUTO_ACKNOWLEDGE); 
	        //session=connection.createSession(Boolean.FALSE, Session.CLIENT_ACKNOWLEDGE); 
		    if (mode==MqMode.QUEUE)
		    	destination = session.createQueue(topic);// 创建消息队列  
		    else if (mode==MqMode.TOPIC)
		    	destination = session.createTopic(topic);// 创建主题消息 
		    messageProducer = session.createProducer(destination);// 创建消息生产者  			
		}catch (Exception e){
			sysLog.setTopic("ActiveMq连接错误");
			sysLogService.setLog(sysLog, LogType.ERROR, "ActiveMq", e.toString());
			return result;
		}
	    result = true;
	    return result;
	}
}
