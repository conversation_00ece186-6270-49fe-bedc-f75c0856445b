package com.jsdz.digitalevidence.alarm.alarm110.bean;
/**
 * 
 * @类名: AlarmRelationBean
 * @说明: 警那个关联封装类
 *
 * @author: kenny
 * @Date	2017年8月30日下午2:49:28
 * 修改记录：
 *
 * @see
 */
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.utils.EnumUtils;

public class AlarmRelationBean {

	private Long id;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date relationTime; //关联时间
    //警情
	private Long alarmId;    //ID
    private String alarmCode;    //警情号
    private Date alarmTime;    //报警时间
    private String alarmContext;    //报警内容

    //接警单位
    private Long orgId;    
    private String orgCode;
    private String orgName;
    //关联人 
    private Long operatorId;    
    private String loginName;
    private Long employeesId;
    private String policeName; //姓名
    private String workNumber; //警号
    //视频
    private Long docId;
    private String docName;
    private String documentName;

	private DocumentCate cate;

	private String type;

    public void assign(AlarmRelation sur){
    	this.setId(sur.getId());
    	this.setRelationTime(sur.getRelationTime());
    	if (sur.getOperator()!=null){
    		this.setOperatorId(sur.getOperator().getId());
    		this.setLoginName(sur.getOperator().getLoginName());
    	}
    	if (sur.getMediaInfo() != null){
    		this.setDocId(sur.getMediaInfo().getId());
    		this.setDocName(sur.getMediaInfo().getName());
    		this.setDocumentName(sur.getMediaInfo().getCate().getName());
			this.setCate(sur.getMediaInfo().getCate());
			this.setType(sur.getMediaInfo().getContentType());
    		if (sur.getMediaInfo().getPolice() != null){
    			this.setWorkNumber(sur.getMediaInfo().getPolice().getWorkNumber());
    			this.setPoliceName(sur.getMediaInfo().getPolice().getName());
    			if (sur.getMediaInfo().getOrganization() != null){
    				this.setOrgCode(sur.getMediaInfo().getOrganization().getOrgCode());
    				this.setOrgName(sur.getMediaInfo().getOrganization().getOrgName());
    			}
    		}
     	}
        if (sur.getAlarmInfo() != null){
        	this.setAlarmCode(sur.getAlarmInfo().getAlarmCode());;
        }
    	

    }
    
    public void assignTo(AlarmRelation dest){
    	dest.setId(id);
    	dest.setRelationTime(relationTime);
    	if (alarmId != null){
    		AlarmInfo a = new AlarmInfo();
    		a.setId(alarmId);
    		dest.setAlarmInfo(a);
    	}
    	
    	if (docId != null){
    		Document doc = new Document();
    		doc.setId(docId);
    		dest.setMediaInfo(doc);
    	}
    	
    	if (operatorId != null){
    		Operator o = new Operator();
    		o.setId(operatorId);
    		dest.setOperator(o);
    	}
    }
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Date getRelationTime() {
		return relationTime;
	}
	public void setRelationTime(Date relationTime) {
		this.relationTime = relationTime;
	}
	public Long getAlarmId() {
		return alarmId;
	}
	public void setAlarmId(Long alarmId) {
		this.alarmId = alarmId;
	}
	public String getAlarmCode() {
		return alarmCode;
	}
	public void setAlarmCode(String alarmCode) {
		this.alarmCode = alarmCode;
	}
	public Date getAlarmTime() {
		return alarmTime;
	}
	public void setAlarmTime(Date alarmTime) {
		this.alarmTime = alarmTime;
	}
	public String getAlarmContext() {
		return alarmContext;
	}
	public void setAlarmContext(String alarmContext) {
		this.alarmContext = alarmContext;
	}

	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Long getOperatorId() {
		return operatorId;
	}
	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public Long getEmployeesId() {
		return employeesId;
	}
	public void setEmployeesId(Long employeesId) {
		this.employeesId = employeesId;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public String getWorkNumber() {
		return workNumber;
	}
	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}
	public Long getDocId() {
		return docId;
	}
	public void setDocId(Long docId) {
		this.docId = docId;
	}
	public String getDocName() {
		return docName;
	}
	public void setDocName(String docName) {
		this.docName = docName;
	}

	public String getDocumentName() {
		return documentName;
	}

	public void setDocumentName(String documentName) {
		this.documentName = documentName;
	}

	public DocumentCate getCate() {
		return cate;
	}

	public void setCate(DocumentCate cate) {
		this.cate = cate;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
}
