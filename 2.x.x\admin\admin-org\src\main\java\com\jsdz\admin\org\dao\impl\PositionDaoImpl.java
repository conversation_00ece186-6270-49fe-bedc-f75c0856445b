package com.jsdz.admin.org.dao.impl;

/**
 *
 * @类名: PositionDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-09 21:50:03
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.org.dao.PositionDao;
import com.jsdz.admin.org.model.Position;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class PositionDaoImpl extends GenericEntityDaoHibernateImpl<Position,Long> implements PositionDao{

	//新增
	public void addPosition(Position position) {
		this.saveOrUpdate(position);
	}

	//删除
	public void deletePosition(Position position) {
		this.delete(position);
	}

	//修改
	public void updatePosition(Position position) {
		this.saveOrUpdate(position);
	}

	//按id查询
	public Position locatePositionById(Long id){

		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery("from Position o where o.id=:id");
		query.setParameter("id", id);
		List<Position> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//单个查询
	public Position findPositionByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		};
		List<Position> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//查询全部
	public List<Position> findAllPositions(){
		return  this.find("from Position position ");
	}

	//列表查询
	public List<Position> findPositionsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<Position> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<Position> findPositionsOnPage(Page<Position> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<Position>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
