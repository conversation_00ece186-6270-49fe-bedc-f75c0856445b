package com.jsdz.digitalevidence.task.model;

public class TaskRecorderBean {

//    r.id, r.CODE, r.EMPLOYEES_ID policeId
    private Long id;

    private String code;

    private Long policeId;

    private Integer hasTask;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getPoliceId() {
        return policeId;
    }

    public void setPoliceId(Long policeId) {
        this.policeId = policeId;
    }

    public Integer getHasTask() {
        return hasTask;
    }

    public void setHasTask(Integer hasTask) {
        this.hasTask = hasTask;
    }
}
