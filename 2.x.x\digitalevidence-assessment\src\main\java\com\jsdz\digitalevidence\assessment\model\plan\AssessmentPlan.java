package com.jsdz.digitalevidence.assessment.model.plan;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
/**
 * @类名: AssessmentPlan
 * @说明: 考核方案
 *
 * <AUTHOR>
 * @Date	 2019年6月19日下午2:57:12
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentPlan {
	private Long id;
	private String description;
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date beginDate;
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date endDate;
	private Integer status;//状态0 初始 1启动 2结束
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTime;
	
	private List<AssessmentPlanPolice> polices = new ArrayList<AssessmentPlanPolice>();
	
	private List<AssessmentPlanDocment> docs = new ArrayList<AssessmentPlanDocment>();

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Date getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(Date beginDate) {
		this.beginDate = beginDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public List<AssessmentPlanPolice> getPolices() {
		return polices;
	}

	public void setPolices(List<AssessmentPlanPolice> polices) {
		this.polices = polices;
	}

	public List<AssessmentPlanDocment> getDocs() {
		return docs;
	}

	public void setDocs(List<AssessmentPlanDocment> docs) {
		this.docs = docs;
	}
	
	
	
	
}
