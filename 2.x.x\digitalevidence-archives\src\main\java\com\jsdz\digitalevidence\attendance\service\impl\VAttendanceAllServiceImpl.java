package com.jsdz.digitalevidence.attendance.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.archives.bean.param.DocEnforParam;
import com.jsdz.digitalevidence.archives.model.DocEnfor;
import com.jsdz.digitalevidence.attendance.bean.param.AttendanceAllParam;
import com.jsdz.digitalevidence.attendance.mapper.VAttendanceAllMapper;
import com.jsdz.digitalevidence.attendance.model.AttendanceMonthModel;
import com.jsdz.digitalevidence.attendance.model.VAttendanceAllModel;
import com.jsdz.digitalevidence.attendance.service.IVAttendanceAllService;
import com.jsdz.digitalevidence.cache.utils.ResultUtil;
import com.jsdz.digitalevidence.utils.DateUtil;
import com.jsdz.digitalevidence.utils.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * VIEW 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Service
public class VAttendanceAllServiceImpl extends ServiceImpl<VAttendanceAllMapper, VAttendanceAllModel> implements IVAttendanceAllService {

    @Resource
    private VAttendanceAllMapper attendanceAllMapper;


    @Override
    public Page<VAttendanceAllModel> getAttendanceDatas(AttendanceAllParam param, PerLvlBean permLvl) {
        Page<VAttendanceAllModel> page = param.getPage();
        try {

//            permLvl.getOrgPath();
//            QueryWrapper<VAttendanceAllModel> wrapper = new QueryWrapper<>();
//            String includeSub = param.getIncludeSub();
//            String attendancename = param.getAttendancename();
//            String username = param.getUsername();
//            String devicecode = param.getDevicecode();
//            if(StringUtils.isNoEmpty(orgPathu)) wrapper.likeLeft("PATH",orgPathu);
//            if(StringUtils.isNoEmpty(orgPath) && "1".equals(includeSub)) wrapper.likeLeft("PATH",orgPathu);
//            if(StringUtils.isNoEmpty(orgPath) && "0".equals(includeSub)) wrapper.eq("PATH",orgPathu);
//            if(StringUtils.isNoEmpty(attendancename)) wrapper.like("attendancename",attendancename);
//            if(StringUtils.isNoEmpty(username)) wrapper.like("username",username);
//            if(StringUtils.isNoEmpty(devicecode)) wrapper.like("devicecode",devicecode);
//            com.baomidou.mybatisplus.extension.plugins.pagination.Page<VAttendanceAllModel> modelPage = attendanceAllMapper.selectPage(new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(page.getPage(), page.getPageSize()), wrapper);
//            List<VAttendanceAllModel> vAttendanceAllModels = attendanceAllMapper.selectList(wrapper);
//            if(StringUtils.isNoEmpty(modelPage.getTotal())&&modelPage.getTotal()>0){
//                page.setTotal((int) modelPage.getTotal());
//            }else {
//                Integer total = Math.toIntExact(attendanceAllMapper.selectCount(wrapper));
//                page.setTotal(total);
//            }
//            Integer total = Math.toIntExact(attendanceAllMapper.selectCount(wrapper));
//            page.setTotal(total);
//            page.setRows(modelPage.getRecords());


            String orgPath = param.getOrgPath();
            if(StringUtils.isEmpty(orgPath)) {
                param.setOrgPath(permLvl.getOrgPath());
                Integer permissionLevel = permLvl.getPermissionLevel();
                param.setIncludeSub((permissionLevel<3?0:1)+"");
            }
            List<VAttendanceAllModel> vAttendanceAllModels =attendanceAllMapper.selectModels(param);
            for (VAttendanceAllModel attendanceAllModel : vAttendanceAllModels) {
                attendanceAllModel.setTattendancetime(DateUtil.convertMinuteOfStringToTime(Integer.parseInt(attendanceAllModel.getTattendancetime())));
            }
            Integer total =attendanceAllMapper.selectModelCount(param);
            page.setTotal(total);
            page.setRows(vAttendanceAllModels);
            return page;
        }catch (Exception e){
            System.out.println(e.getMessage());
            return page;
        }
    }

    @Override
    public Page<AttendanceMonthModel> getAttendanceCountsOfMonth(AttendanceAllParam param, PerLvlBean permLvl) {
        Page<AttendanceMonthModel> page = new Page<>();
        BeanUtils.copyProperties(param.getPage(), page);
        try {
            String orgPath = param.getOrgPath();
            if(StringUtils.isEmpty(orgPath)) {
                param.setOrgPath(permLvl.getOrgPath());
                Integer permissionLevel = permLvl.getPermissionLevel();
                param.setIncludeSub((permissionLevel<3?0:1)+"");
            }
            if(param.getMonth() != null) {
//                param.setMonth(new Date());
                param.setCreateTimeStart(DateUtil.getFirstDayOfMonth(param.getMonth()));
                param.setCreateTimeEnd(DateUtil.getLastDayOfMonth(param.getMonth()));
            }
            List<AttendanceMonthModel> vAttendanceAllModels =attendanceAllMapper.selectAttendanceCountsOfMonth(param);
            Integer total =attendanceAllMapper.selectAttendanceCountsOfMonthCount(param);
            page.setTotal(total);
            page.setRows(vAttendanceAllModels);
            return page;
        }catch (Exception e){
            System.out.println(e.getMessage());
            return page;
        }
    }

    @Override
    public Page<DocEnfor> getDocEnfor(DocEnforParam param, PerLvlBean permLvl) {
        Page<DocEnfor> page = param.getPage();
        try {
            String orgPath = param.getOrgPath();
            if(StringUtils.isEmpty(orgPath)) {
                param.setOrgPath(permLvl.getOrgPath());
                Integer permissionLevel = permLvl.getPermissionLevel();
                param.setIncludeSub((permissionLevel<3?0:1)+"");
            }
            List<DocEnfor> vAttendanceAllModels =attendanceAllMapper.selectEnfor(param);
            Integer total =attendanceAllMapper.selectEnforCount(param);
            page.setTotal(total);
            page.setRows(vAttendanceAllModels);
            return page;
        }catch (Exception e){
            System.out.println(e.getMessage());
            return page;
        }
    }

    @Override
    public Map<String, Object> deviceInfoById(Long deviceId) {
        HashMap<String, Object> map = new HashMap<>();
        Date day0 = DateUtil.getDate000();
        Date month1 = DateUtil.getDate1000();
        Integer dayNum = attendanceAllMapper.getDeviceCount(day0,deviceId);
        Integer monthNum = attendanceAllMapper.getDeviceCount(month1,deviceId);
        map.put("dayNum", dayNum);
        map.put("monthNum", monthNum);
        return map;
    }

    @Override
    public Map<String, Object> taskInfoById(Long taskId) {
        HashMap<String, Object> map = new HashMap<>();
        Date day0 = DateUtil.getDate000();
        Date month1 = DateUtil.getDate1000();
        Integer dayNum = attendanceAllMapper.getTaskCount(day0,taskId);
        Integer monthNum = attendanceAllMapper.getTaskCount(month1,taskId);
        map.put("dayNum", dayNum);
        map.put("monthNum", monthNum);
        return map;
    }

    @Override
    public List<Map> getOperatorByEid(String idnum) {
        return attendanceAllMapper.getOperatorByEid(idnum);
    }

    @Override
    public void saveAlarmOrg(Long alarmId,Long orgId) {
        attendanceAllMapper.saveAlarmOrg(alarmId,orgId);
    }

    @Override
    public AjaxResult updateCase(long id, Integer isRelation) {
        try {
            attendanceAllMapper.updateCase( id, isRelation);
            return ResultUtil.ok();
        }catch (Exception e){
            e.printStackTrace();
            return ResultUtil.error();
        }

    }

}
