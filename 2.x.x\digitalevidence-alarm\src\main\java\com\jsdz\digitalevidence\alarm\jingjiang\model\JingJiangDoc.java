package com.jsdz.digitalevidence.alarm.jingjiang.model;

import java.util.Date;

/**
 * 
 * @类名: <PERSON><PERSON><PERSON><PERSON>D<PERSON>
 * @说明: 靖江交警数据交换
 *
 * @author: kenny
 * @Date	2017年10月25日下午3:54:14
 * 修改记录：
 *
 * @see
 */
public class JingJiangDoc {
	private Long id;
	private String alarmCode;//	警情编号
	private String policeCode;//警号
	private String PoliceName;// 警员姓名
	private Date  captureTime;//采集时间
	private Date uploadTime;//上传时间
	private String orgCode;//部门代码;
	private String mediaUrl;//文件地址
	private Long fileSize;//文件大小
	private Long docId;//数据id
	private Date createTime;
	private Integer readFlag;
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getAlarmCode() {
		return alarmCode;
	}
	public void setAlarmCode(String alarmCode) {
		this.alarmCode = alarmCode;
	}
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return PoliceName;
	}
	public void setPoliceName(String policeName) {
		PoliceName = policeName;
	}
	public Date getCaptureTime() {
		return captureTime;
	}
	public void setCaptureTime(Date captureTime) {
		this.captureTime = captureTime;
	}
	public Date getUploadTime() {
		return uploadTime;
	}
	public void setUploadTime(Date uploadTime) {
		this.uploadTime = uploadTime;
	}
	public String getMediaUrl() {
		return mediaUrl;
	}
	public void setMediaUrl(String mediaUrl) {
		this.mediaUrl = mediaUrl;
	}
	public Long getDocId() {
		return docId;
	}
	public void setDocId(Long docId) {
		this.docId = docId;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Integer getReadFlag() {
		return readFlag;
	}
	public void setReadFlag(Integer readFlag) {
		this.readFlag = readFlag;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public Long getFileSize() {
		return fileSize;
	}
	public void setFileSize(Long fileSize) {
		this.fileSize = fileSize;
	}
}
