package com.jsdz.admin.security.utils;
/**
 * 
 * @类名: HqlGenerator.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月2日下午3:06:21
 * 修改记录：
 *
 * @see
 */

import java.lang.reflect.Field;

public class HqlGenerator {
	
		/**
		 * @说明：根据传入的对象自动产生hql查询语句
		 * 
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年5月3日 下午2:09:54
		 */
	public static HqlUtil generateParams(Object o){
		String ClassName = o.getClass().getName();
		HqlUtil hqlUntil = new HqlUtil();
		Excule(hqlUntil,ClassName,o);
		String shortName = GetShortClassName(ClassName);
		
		StringBuilder sb = new StringBuilder();
		sb.append("from " + shortName + " " + shortName);
		//hqlUntil.setHqlString("from " + ClassName);
		if (hqlUntil.getArrayValueList().size() > 0){
			sb.append(" where ");
			for (int i = 0;i<hqlUntil.getArrayParamList().size();i++){
				if (i>0){
					sb.append(" and ");
				}
				sb.append(shortName);
				sb.append(".");
				sb.append(hqlUntil.getArrayParamList().get(i));
				sb.append(" = :");
				sb.append(hqlUntil.getArrayParamList().get(i));
				sb.append(" ");
			}
		}
		hqlUntil.setHqlString(sb.toString());
		System.out.println(sb.toString());
		return hqlUntil;

	}
	
	private static void Excule(HqlUtil hu,String ClassName,Object o){
		try {
			   Class clazz = Class.forName(ClassName);//根据类名获得其对应的Class对象 写上你想要的类名就是了 注意是全名 如果有包的话要加上 比如java.Lang.String
			   Field[] fields = clazz.getDeclaredFields();//根据Class对象获得属性 私有的也可以获得
			   for(Field f : fields) {
				   f.setAccessible(true); //设置些属性是可以访问的
				   Object v = f.get(o);
				   
				   
				   if (v != null){
					   if ("java.lang.String".equals(f.getType().getName()) &&"".equals(v)){continue;}
					   if ("serialVersionUID".equals(f.getName())){continue;}
					   System.out.println("name = " + f.getName() + "【"+v+"】");
					   hu.getArrayParamList().add(f.getName());
					   hu.getArrayValueList().add(v);
				   }
			   }
			   
			   if (!"java.lang.Object".equals(clazz.getSuperclass().getName())){
				   Excule(hu,clazz.getSuperclass().getName(),o);  
			   }
			   
			} catch(Exception e) {
			   e.printStackTrace();
			}		
	}
	
	private static String GetShortClassName(String className){
		String[] sc = className.split("\\.");
		return sc[sc.length-1];
	}
	

}
