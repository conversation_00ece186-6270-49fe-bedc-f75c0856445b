package com.jsdz.digitalevidence.attendance.bean.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceCheckBean;
import com.jsdz.utils.DateTimeUtils;

import java.util.Date;

public class AttendanceCheckParam {

    Page<AttendanceCheckBean> page;

    private String orgPath;

    private String sortName;

    private String sortOrder;
    //是否包含下级 （0不，1包含）
    private String includeSub;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTimeStart;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTimeEnd;


    public AttendanceCheckParam(){
        page = new Page<AttendanceCheckBean>();
        page.setOffset(0);
        page.setPageSize(20);
    }

    public Date getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(Date createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public Date getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(Date createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public Page<AttendanceCheckBean> getPage() {
        return page;
    }

    public void setPage(Page<AttendanceCheckBean> page) {
        this.page = page;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public String getSortName() {
        return sortName;
    }

    public void setSortName(String sortName) {
        this.sortName = sortName;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(String includeSub) {
        this.includeSub = includeSub;
    }
}
