package com.jsdz.admin.org.service.impl;

import java.util.Date;

/**
 * 
 * @类名: PositionServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-09 21:50:03
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.admin.org.dao.PositionDao;
import com.jsdz.admin.org.model.Position;
import com.jsdz.admin.org.service.PositionService;
import com.jsdz.admin.org.utils.MD5;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

@Service("PositionServiceImpl")
public class PositionServiceImpl implements PositionService {

	@Autowired
	private PositionDao positionDao;

	//新增
	public AjaxResult addPosition(Position position) {
		AjaxResult result = new AjaxResult();
		if (null == position){
			result.setSuccess(false);
			result.setMsg("没有数据可保存");
			return result;
		}
		
		if (null == position.getPosition() || "".equals(position.getPosition())){
			result.setMsg("职位名称必须输入");
			result.setSuccess(false);
			return result ;
		}
		
		if (null != positionDao.findPositionByCondition("from Position p where p.position=:position",
				  new String[]{"position"},new Object[]{position.getPosition()})){
			result.setSuccess(false);
			result.setMsg("此职位名称已存在");
			return result;
		}
		
		position.setCreateTime(new Date());

		positionDao.addPosition(position);
		result.setSuccess(true);
		result.setData(position);
		result.setMsg("新增保存成功。");
		return result;  
	}

	//修改
	public AjaxResult updatePosition(Position position) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == position){
			result.setMsg("没有数据可修改");
			return result;
		}
		Position position1 = positionDao.locatePositionById(position.getId());
		if (null==position1){
			result.setMsg("不存在的职位资料");
			return result;
		}
		
		if (null == position.getPosition() || "".equals(position.getPosition())){
			result.setMsg("职位名称必须输入");
			return result ;
		}
		
		
		
		if (null != positionDao.findPositionByCondition(
					"from Position p where p.position=:position and p.id != :id",
				  new String[]{"position","id"},new Object[]{position.getPosition(),position.getId()})){
			result.setMsg("职位名称重复");
			return result;
		}

		position1.setPosition(position.getPosition());
		position1.setDescription(position.getDescription());
		positionDao.updatePosition(position1);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 	}

	//删除
	public AjaxResult deletePosition(Position position) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == position){
			result.setMsg("没有数据可删除");
			return result;
		}
		
		if (null==positionDao.locatePositionById(position.getId())){
			result.setMsg("不存在的职位资料");
			return result;
		}
		
		
		positionDao.deletePosition(position);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 	
	}

	//按 id 查询
	public Position locatePositionById(Long id) {
		return positionDao.locatePositionById(id);
	}

	//单个查询
	public Position findPositionByParam(String queryStr, String[] paramNames, Object[] values) {
		return positionDao.findPositionByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<Position> findAllPositions() {
		return positionDao.findAllPositions();
	}

	//列表查询
	public List<Position> findPositionsByParam(String queryStr, String[] paramNames, Object[] values) {
		return positionDao.findPositionsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<Position> findPositionsOnPage(Page<Position> page, String queryStr, String[] paramNames, Object[] values) {
		Page<Position> pos = positionDao.findPositionsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
