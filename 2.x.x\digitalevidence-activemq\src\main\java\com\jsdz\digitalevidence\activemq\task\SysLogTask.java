package com.jsdz.digitalevidence.activemq.task;

import java.util.concurrent.Future;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;
import com.jsdz.digitalevidence.activemq.service.SysLogService;
import com.jsdz.digitalevidence.activemq.utils.LogType;
import com.jsdz.digitalevidence.cache.task.Task;
import com.jsdz.digitalevidence.activemq.model.SysLog;
/**
 * 说明：给海康 KafKa服务器 GPS 信息
 * @类名: KafkaTask
 *
 * <AUTHOR>
 * @Date	 2021年1月21日下午7:28:43
 */
@Component("sysLogTask")
public class SysLogTask implements Task<SysLog>{
	private final Logger logger = Logger.getLogger(this.getClass());
	
	@Autowired
	private SysLogService sysLogService;

	@Override
	@Async("taskExecutor")
	public Future<String> run(SysLog t) throws Exception {
		try{
			if (LogType.ERROR.getName().equals(t.getLogType())){
				sysLogService.setLog(t, LogType.ERROR,t.getContext());
			}else if (LogType.INFO.getName().equals(t.getLogType())){
				sysLogService.setLog(t, LogType.INFO,t.getContext());
			}
		}catch(Exception e){
			logger.error(">>>>> {SysLogTask error}: "+e.toString());
			return new AsyncResult<String>("任务失败");
		}
		return new AsyncResult<String>("完成任务");
	}
}
