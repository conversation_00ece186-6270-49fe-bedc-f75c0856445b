package tools;
/**
 * 
 * @类名: GenerateCode
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年7月1日下午5:25:00
 * 修改记录：
 *
 * @see
 */

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.io.Serializable;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Date;

public class MybatisGenerateCode {
	
	private static SimpleDateFormat sdf =   new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	
	public static int SERVICE=1;
	public static int SERVICE_IMPL=2;
	public static int DAO=3;
	public static int DAO_XML=4;
	public static int MODEL=0;
	public static int ALL=9;
	
	static String importStr = "";
	static String path = System.getProperty("user.dir") + "\\src\\main\\java";
	
	public static String TABLE_SCHEMA = "jsdz_4gdb";
	

	public static void main(String[] args) throws Exception {
		String pagekage = "com.jsdz.digitalevidence.site";
		String tableName = "t_recorder_notice";
		String javaClass = "RecorderNotice";
		int outType = 4;
		boolean toFile = true;
		
		output(outType,pagekage,tableName,javaClass,toFile);
	}
	
	public static void output(int outType,String pagekage, String tableName,String javaClass, boolean toFile) throws Exception{
		if (outType == ALL){
			createModel(pagekage, tableName,javaClass,toFile);
			createService(pagekage, tableName, javaClass,true,toFile);
			createServiceImpl(pagekage, tableName, javaClass,true,toFile);
			createDao(pagekage, tableName, javaClass,true,toFile);
			createXml(pagekage, tableName, javaClass,true,toFile);
		}
		else if (outType == MODEL) 
			createModel(pagekage, tableName,javaClass,toFile);
		else if (outType == SERVICE) 
			createService(pagekage, tableName, javaClass,true,toFile);
		else if (outType == SERVICE_IMPL) 
			createServiceImpl(pagekage, tableName, javaClass,true,toFile);
		else if (outType == DAO) 
			createDao(pagekage, tableName, javaClass,true,toFile);
		else if (outType == DAO_XML)
			createXml(pagekage, tableName, javaClass,true,toFile);
	}
	
	public static Connection getConnection() {
		Connection conn = null;
		try {
			Class.forName("com.mysql.cj.jdbc.Driver");
			String url = "***************************/"+TABLE_SCHEMA+"?useOldAliasMetadataBehavior=true&useUnicode=true&zeroDateTimeBehavior=CONVERT_TO_NULL&characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true";
			String user = "root";
			String pass = "jsdz1234";
			conn = DriverManager.getConnection(url, user, pass);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return conn;
	}

	public static ResultSet selectInformation(String tableName)
			throws SQLException {
		Connection conn = getConnection();
		String sql = "SELECT column_name as columnName, COLUMN_KEY as columnKey ,column_comment as columnComment "
				+ " FROM Information_schema.COLUMNS WHERE table_Name = '"
				+ tableName + "' and TABLE_SCHEMA='" + TABLE_SCHEMA + "'";
		Statement stmt = conn.createStatement();
		ResultSet rs = stmt.executeQuery(sql);
		return rs;
	}

	/**
	 * 创建文件 王梦圆 2015年6月23日
	 */
	public static void createFile(String pagekagePathStr, String tableName,	String javaClass,String suffix, 
			String importStr, boolean flag, boolean toFile) throws Exception {
		String pagekageStr = pagekagePathStr.replace("/", ".");// 首行引入包名
		
		Connection conn = getConnection();
		String sql = "select * from " + tableName;
		PreparedStatement stmt;
		stmt = conn.prepareStatement(sql);
		ResultSet rs = stmt.executeQuery(sql);
		ResultSetMetaData data = rs.getMetaData();
		String javaName = getFileName(javaClass);// 表名
		pagekagePathStr = pagekagePathStr.replace(".", "/");
		
		if (!suffix.equals("service.impl")) {
			pagekagePathStr = pagekagePathStr + "/" + suffix;// 创建接口的路径
		}
		String name = "";
		if (suffix.equals("service.impl")) {
			String suffixStr = suffix.replace(".", "");
			String before = suffixStr.substring(0, 7);
			String end = suffixStr.substring(7);
			pagekagePathStr = pagekagePathStr + "/" + before + "/" + end;// 创建接口的路径
			end = end.replaceFirst(end.substring(0, 1), end.substring(0, 1).toUpperCase());
			name = before + end;
			name = name.replaceFirst(name.substring(0, 1), name.substring(0, 1).toUpperCase());
			name = javaName + name;
		} else {
			String suff = suffix.replaceFirst(suffix.substring(0, 1), suffix.substring(0, 1).toUpperCase());// service或dao首字母大写，拼类名
			name = javaName + suff;// 类名
		}
		String packageStr = "";
		if (!suffix.equals("mapper")) {
			packageStr = "package " + pagekageStr + "." + suffix + ";\r\n\n";// 包名
		}
		
		
		
		File file = null;
		if (toFile == true){
			File fileDir = new File(path + "/" + pagekagePathStr);// 创建文件夹
			if (!fileDir.exists()) {
				fileDir.mkdirs();
			}
			
			
			file = new File("");
			if (suffix.equals("mapper")) {
				file = new File(path + "/" + pagekagePathStr + "/" + javaName + "Dao.xml");
			} else {
				file = new File(path + "/" + pagekagePathStr + "/" + name + ".java");// 创建文件
			}
			if (!file.exists()) {
				file.createNewFile();
			}
			
		}
		
		
		String fileContent = "";
		if (suffix.equals("service")) {
			fileContent = createSerivceContent(javaName);
		} else if (suffix.equals("dao")) {
			fileContent = createDaoContent(javaName);
		} else if (suffix.equals("mapper")) {
			fileContent = createXmlContent(pagekageStr, data, tableName,javaName,flag);
		} else if (suffix.equals("service.impl")) {
			fileContent = createImplContent(javaName);
		}
		
		StringBuilder filePath = new StringBuilder();
		filePath.append(packageStr);
		if  (!suffix.equals("mapper"))
			filePath.append(generateComments(name));
		filePath.append(importStr);
		filePath.append(fileContent);
		System.out.println(filePath);

		if (toFile == true){
			try {
				FileOutputStream fos = new FileOutputStream(file);
				//fos.write(bytes, 0, b);
				fos.write(filePath.toString().getBytes());
				fos.close();
			} catch (Exception e) {
				e.printStackTrace();
			}			
		}
	}


	public static void createServiceImpl(String pagekagePathStr,
			String tableName, String javaClass,boolean flag, boolean toFile) throws Exception {
		String javaName = getFileName(javaClass);
		String pagekageStr = pagekagePathStr.replace("/", ".");// 首行引入包名
		importStr = "";
		importStr += "import java.util.List;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\n"
				+ "import org.springframework.stereotype.Service;\r\n"
				+ "import "
				+ pagekageStr
				+ ".dao."
				+ javaName
				+ "Dao;\r\nimport "
				+ pagekageStr
				+ ".service."
				+ javaName
				+ "Service;\r\n"
				+ "import "
				+ pagekageStr
				+ ".model."
				+ javaName + ";\r\nimport "+pagekageStr+".utils.PageUtil;\r\n\n";
		createFile(pagekagePathStr, tableName, javaClass,"service.impl", importStr, flag,toFile);
	}


	public static String createImplContent(String javaName) {
		StringBuffer str = new StringBuffer("");
		String javaNameStr = javaName.replaceFirst(javaName.substring(0, 1),
				javaName.substring(0, 1).toLowerCase());
		str.append("@Service\r\n");
		str.append("public class " + javaName + "ServiceImpl implements "
				+ javaName + "Service {\r\n\n" + "     @Autowired\r\n"
				+ "     private " + javaName + "Dao " + javaNameStr
				+ "Dao;\r\n\n" + "     @Override\r\n" + "     public List<"
				+ javaName + "> findByConditions(" + javaName + " "
				+ javaNameStr + "," + "PageUtil page) {\r\n" + "        List<"
				+ javaName + "> lists=" + javaNameStr + "Dao.findByConditions("
				+ javaNameStr + ",page);\r\n" + "        return lists;\r\n"
				+ "     }\r\n\n" + "     @Override\r\n"
				+ "     public long findByConditionsCount(" + javaName + " "
				+ javaNameStr + ") {\r\n" + "        long count=" + javaNameStr
				+ "Dao.findByConditionsCount(" + javaNameStr + ");\r\n"
				+ "        return count;\r\n" + "     }\r\n\n"
				+ "     @Override\r\n" + "     public void save(" + javaName
				+ " " + javaNameStr + ") {\r\n" + "        " + javaNameStr
				+ "Dao.save(" + javaNameStr + ");\r\n" + "     }\r\n\n"
				+ "     @Override\r\n" + "     public void update(" + javaName
				+ " " + javaNameStr + ") {\r\n" + "        " + javaNameStr
				+ "Dao.update(" + javaNameStr + ");\r\n" + "     }\r\n\n"
				+ "     @Override\r\n" + "     public void delete(" + javaName
				+ " " + javaNameStr + ") {\r\n" + "        " + javaNameStr
				+ "Dao.delete(" + javaNameStr + ");\r\n" + "     }\r\n"
				+ "}\r\n\n");
		return str.toString();
	}


	public static void createXml(String pagekagePathStr, String tableName,String javaClass,
			boolean flag, boolean toFile) throws Exception {
		createFile(pagekagePathStr, tableName,javaClass, "mapper", "", flag,toFile);
	}

	
	public static String createXmlContent(String pagekagePathStr,
			ResultSetMetaData data, String tableName, String javaClass,boolean flag)
			throws SQLException, IOException {

		String idname = null;
		
		ResultSet rs = selectInformation(tableName);
		while (rs.next()) {
			String coulmnName = rs.getString("columnName");
			String columnKey = rs.getString("columnKey");
			if (columnKey.equals("PRI")) {
				idname = coulmnName;
			}
		}

		String javaName = getFileName(javaClass);
		String javaNameStr = javaName.replaceFirst(javaName.substring(0, 1),
				javaName.substring(0, 1).toLowerCase());
		StringBuffer str = new StringBuffer("");
		String time = "";
		String id = "";
		String idStr = "";
		str.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n");
		str.append("<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\" >\r\n");
		str.append("<mapper namespace=\"" + pagekagePathStr + ".dao."
				+ javaName + "Dao\">\r\n");
		str.append("   <resultMap id=\"BaseResultMap\" type=\""
				+ pagekagePathStr + ".model." + javaName + "\">\r\n");
		for (int i = 1; i <= data.getColumnCount(); i++) {
			// 获得指定列的列名
			String columnName = data.getColumnName(i);
			String columnNameStr = replaceUnderlineAndfirstToUpper(columnName,"_", "");
			if (columnNameStr.contains("CreateTime")) {
				time = columnName;
			}
			// 对应数据类型的类
			String columnClassName = getColumnClassName(data.getColumnClassName(i));
			columnClassName = columnClassName.toUpperCase();
			
			if (columnClassName.equalsIgnoreCase("String")) {
				columnClassName = "VARCHAR";
			} else if (columnClassName.equalsIgnoreCase("Integer")) {
				columnClassName = "INTEGER";
			} else if (columnClassName.equalsIgnoreCase("Date")) {
				columnClassName = "TIMESTAMP";
			} else if (columnClassName.equalsIgnoreCase("Long")) {
				columnClassName = "BIGINT";
			}
			//
			//if (columnName.toLowerCase().contains("id")) {
			if (columnName.equals(idname)){
				id = columnName;
				idStr = columnNameStr;
				str.append("         <id column=\"" + columnName
						+ "\" property=\"" + columnNameStr + "\" jdbcType=\""
						+ columnClassName + "\" />\r\n");
			} else {
				str.append("         <result column=\"" + columnName
						+ "\" property=\"" + columnNameStr + "\" jdbcType=\""
						+ columnClassName + "\" />\r\n");
			}
		}
		str.append("   </resultMap>\r\n");
		str.append("   <sql id=\"findWhere\">\r\n");
		//str.append("   <where>\r\n");
		str.append("   <trim prefix=\"WHERE\" prefixOverrides=\"AND |OR\">\r\n");
		for (int i = 1; i <= data.getColumnCount(); i++) {
			// 获得指定列的列名
			String columnName = data.getColumnName(i);
			// System.out.println(columnName);
			String columnNameStr = replaceUnderlineAndfirstToUpper(columnName,"_", "");
			str.append("      <if test=\"" + javaNameStr + "." + columnNameStr
					+ "!=null \">\r\n");//and " + javaNameStr + "." + columnNameStr	+ "!='' \">\r\n");
			str.append("        and " + columnName + "=#{" + javaNameStr + "."
					+ columnNameStr + "}\r\n" + "      </if>\r\n");
		}
		//str.append("   </where>\r\n");
		str.append("   </trim>\r\n");
		str.append("   </sql>\r\n");
		
		str.append("   <select id=\"findByConditions\" resultMap=\"BaseResultMap\">\r\n");
		str.append("       select * from " + tableName + "\r\n");
		str.append("       <include refid=\"findWhere\"></include>\r\n");
		str.append("       order by \r\n");
		str.append("       <choose>\r\n");
		str.append("       <when test=\"page.sort !=null and page.sort!=\'\' \">\r\n");
		str.append("          ${page.sort}  ${page.order}\r\n");
		str.append("       </when >\r\n");
		if (flag && !time.equals("")) {
			str.append("       <otherwise>\r\n");
			str.append("	    " + time + " DESC\r\n");
			str.append("	   </otherwise>\r\n");
		}
		str.append("       </choose >\r\n");
		str.append("       limit #{page.offset},#{page.rows}\r\n");
		str.append("   </select>\r\n\n");
		str.append("   <select id=\"findByConditionsCount\" resultType=\"java.lang.Long\">\r\n");
		str.append("      select count(1) from " + tableName + "\r\n");
		str.append("      <include refid=\"findWhere\"></include>\r\n");
		str.append("   </select>\r\n\n");
		str.append("   <insert id=\"save\">\r\n");
		
		//insert
		str.append("      insert into " + tableName + "(\n"); 
		for (int i = 1; i <= data.getColumnCount(); i++) {
			String columnName = data.getColumnName(i);
			if (!columnName.equals(idname)){
				if (i<data.getColumnCount())
					str.append("         "+columnName + ",");
				else
					str.append("         "+columnName);
				
				if (i==data.getColumnCount())
					str.append(")\n");
				else
				    str.append("\n");				
			}
		}
		str.append("      values(\r\n");
		for (int i = 1; i <= data.getColumnCount(); i++) {
			// 获得指定列的列名
			String columnName = data.getColumnName(i);
			String columnNameStr = replaceUnderlineAndfirstToUpper(columnName,"_", "");
			if (!columnName.equals(idname)){
				if (i == data.getColumnCount()) {
					str.append("         #{" + javaNameStr + "." + columnNameStr
							+ "}\r\n");
				} else {
					str.append("         #{" + javaNameStr + "." + columnNameStr
							+ "},\r\n");
				}
				
			}
		}
		str.append("      )\r\n");
		str.append("   </insert>\r\n\n");
		
		
		str.append("   <update id=\"update\">\r\n");
		str.append("      update " + tableName + " \r\n");
		
		String trimStr = null;
/*		String idname = "";
		ResultSet rs = selectInformation(tableName);
		while (rs.next()) {
			String coulmnName = rs.getString("columnName");
			String columnKey = rs.getString("columnKey");
			if (columnKey.equals("PRI")) {
				idname = coulmnName;
			}
		}
*/		
		StringBuffer subStr = new StringBuffer("");
		for (int i = 1; i <= data.getColumnCount(); i++) {
			// 获得指定列的列名
			String columnName = data.getColumnName(i);
			// System.out.println(columnName);
			String columnNameStr = replaceUnderlineAndfirstToUpper(columnName,"_", "");
			
/*			if (columnName.equals(idname)) {
				idsql = "         <if test=\"" + javaNameStr + "." + columnNameStr
						+ "!=null and " + javaNameStr + "." + columnNameStr
						+ "!='' \">\r\n" + "           " + columnName + "=#{"
						+ javaNameStr + "." + columnNameStr + "}\r\n"
						+ "         </if>\r\n";
				
			} else {
				str.append("      <if test=\"" + javaNameStr + "."
						+ columnNameStr + "!=null and " + javaNameStr + "."
						+ columnNameStr + "!='' \">\r\n");

				str.append("      <if test=\"" + javaNameStr + "."
						+ columnNameStr + "!=old" + javaNameStr + "." + columnNameStr + "\">\r\n");
				str.append("        " + columnName + "=#{" + javaNameStr + "."
						+ columnNameStr + "},\r\n" + "      </if>\r\n");
			}
*/
			if (!columnName.equals(idname)){
				subStr.append("         <if test=\"" + javaNameStr + "." + columnNameStr +  "!=null\">\r\n");
				subStr.append("            "+columnName + "=#{"+ javaNameStr + "." + columnNameStr + "},\r\n");
				subStr.append("         </if>\r\n");
			}else{
				trimStr ="      <trim prefix=\"set\" suffixOverrides=\",\" suffix=\" where ";
				trimStr += idname + "=#{" + javaNameStr + "."	+ columnNameStr + "}\">\r\n";
			}
		}
		
		str.append(trimStr);
		str.append(subStr.toString());
		
		str.append("      </trim>\r\n");
		str.append("   </update>\r\n\n");
		str.append("\r\n");
		
		str.append("   <delete id=\"delete\">\r\n");
		String idcolumnNameStr = replaceUnderlineAndfirstToUpper(idname, "_",
				"");
		str.append("      DELETE FROM " + tableName + " WHERE " + idname
				+ "=#{" + javaNameStr + "." + idcolumnNameStr + "}\r\n");
		str.append("   </delete>\r\n\n");
		str.append("</mapper>");
		return str.toString();
	}


	public static void createService(String pagekagePathStr, String tableName,String javaClass,boolean flag, boolean toFile) throws Exception {
		String javaName = getFileName(javaClass);// 表名
		String pagekageStr = pagekagePathStr.replace("/", ".");
		importStr = "";
		importStr += "import "
				+ pagekageStr
				+ ".model."
				+ javaName
				+ ";\r\nimport java.util.List;\r\nimport "+pagekageStr+".utils.PageUtil;\r\n\n";
		createFile(pagekagePathStr, tableName, javaClass,"service", importStr, flag, toFile);
	}


	public static String createSerivceContent(String javaName) {
		StringBuffer str = new StringBuffer("");
		String javaNameStr = javaName.replaceFirst(javaName.substring(0, 1),
				javaName.substring(0, 1).toLowerCase());
		str.append("public interface " + javaName + "Service {\r\n\n");
		str.append("     List<" + javaName + "> findByConditions(" + javaName
				+ " " + javaNameStr + ",PageUtil page);\r\n\n");
		str.append("     long findByConditionsCount" + "(" + javaName + " "
				+ javaNameStr + ");\r\n\n");
		str.append("     void save(" + javaName + " " + javaNameStr
				+ ");\r\n\n");
		str.append("     void update(" + javaName + " " + javaNameStr
				+ ");\r\n\n");
		str.append("     void delete(" + javaName + " " + javaNameStr
				+ ");\r\n\n");
		str.append("}");
		return str.toString();
	}


	public static void createDao(String pagekagePathStr, String tableName,String javaClass,
			boolean flag,boolean toFile) throws Exception {
		String javaName = getFileName(javaClass);
		String pagekageStr = pagekagePathStr.replace("/", ".");
		importStr = "";
		importStr += "import "
				+ pagekageStr
				+ ".model."
				+ javaName
				+ ";\r\nimport java.util.List;\r\nimport org.apache.ibatis.annotations.Param;\r\nimport "+pagekageStr+".utils.PageUtil;\n\n";
		createFile(pagekagePathStr, tableName,javaClass, "dao", importStr, flag,toFile);
	}


	public static String createDaoContent(String javaName) {
		StringBuffer str = new StringBuffer("");
		String javaNameStr = javaName.replaceFirst(javaName.substring(0, 1),
				javaName.substring(0, 1).toLowerCase());
		str.append("public interface " + javaName + "Dao {\r\n\n");
		str.append("     List<" + javaName + "> findByConditions"
				+ "(@Param(\"" + javaNameStr + "\")" + javaName + " "
				+ javaNameStr + ",@Param(\"page\")PageUtil page);\r\n\n");
		str.append("     long findByConditionsCount" + "(@Param(\""
				+ javaNameStr + "\")" + javaName + " " + javaNameStr
				+ ");\r\n\n");
		str.append("     void save(@Param(\"" + javaNameStr + "\")" + javaName
				+ " " + javaNameStr + ");\r\n\n");
		str.append("     void update(@Param(\"" + javaNameStr + "\")"
				+ javaName + " " + javaNameStr + ");\r\n\n");
		str.append("     void delete(@Param(\"" + javaNameStr + "\")"
				+ javaName + " " + javaNameStr + ");\r\n\n");
		str.append("}");
		return str.toString();
	}


	public static void createModel(String pagekagePathStr, String tableName, String javaClass,boolean toFile)
			throws SQLException, IOException {
		String pagekageStr = pagekagePathStr.replaceAll("/", ".");
		Connection conn = getConnection();
		String sql = "select * from " + tableName;
		PreparedStatement stmt;
		stmt = conn.prepareStatement(sql);
		ResultSet rs = stmt.executeQuery(sql);
		ResultSetMetaData data = rs.getMetaData();
		StringBuffer str = new StringBuffer("");
		String pagekegeStr = "package " + pagekageStr + ".model;\r\n\n";
		//String javaName = getFileName(tableName);
		String javaName = getFileName(javaClass);
		//Model文件内容
		String fileContent = GenerateModel(str, data, tableName,javaName);
		pagekagePathStr = pagekagePathStr.replace(".", "/");
		pagekagePathStr = pagekagePathStr + "/model";
		System.out.println(pagekegeStr + generateComments(javaName) + importStr + fileContent);

		if (toFile == true){
			File fileDir = new File(path + "/" + pagekagePathStr);
			if (!fileDir.exists()) {
				fileDir.mkdirs();
			}
			File file = new File(path + "/" + pagekagePathStr + "/" + javaName
					+ ".java");
			if (!file.exists()) {
				file.createNewFile();
			}
			byte bytes[] = new byte[2000];
			bytes = (pagekegeStr + importStr + fileContent).toString().getBytes(
					"utf-8");
			try {
				RandomAccessFile fos = new RandomAccessFile(file, "rw");
				fos.write(bytes);
				fos.close();
			} catch (Exception e) {
				e.printStackTrace();
			}			
		}

		
	}


	public static String getFileName(String tableName) {
		String javaName = tableName.replace("myw_", "");
		javaName = javaName.replace("zyzj_", "");
		javaName = javaName.replaceFirst(javaName.substring(0, 1), javaName
				.substring(0, 1).toUpperCase());
		javaName = replaceUnderlineAndfirstToUpper(javaName, "_", "");
		return javaName;
	}


	public static String GenerateModel(StringBuffer str, ResultSetMetaData data,
			String tableName,String javaClass) throws SQLException {
		String javaName = getFileName(javaClass);
		str.append("import java.io.Serializable;\r\n");
		str.append("public class " + javaName
				+ " implements Serializable {\n\n");
		str.append("    private static final long serialVersionUID = 1L;\r\n");
		for (int i = 1; i <= data.getColumnCount(); i++) {
			// 获得指定列的列名
			String columnName = data.getColumnName(i);
			columnName = replaceUnderlineAndfirstToUpper(columnName, "_", "");
			// 对应数据类型的类
			String columnClassName = getColumnClassName(data.getColumnClassName(i));
			ResultSet rs = selectInformation(tableName);
//			while (rs.next()) {
//				String coulmnContent = rs.getString("columnComment");
//				String columName = rs.getString("columnName");
//				if (columName.equals(data.getColumnName(i))) {
//					str.append("    /**\r\n" + "     * " + coulmnContent
//							+ "\r\n" + "     */\r\n");
//					break;
//				}
//			}
//			str.append("    private " + columnClassName + " " + columnName + ";\n");
			str.append("    private " + columnClassName + " " + columnName	+ ";");
			while (rs.next()) {
				String coulmnContent = rs.getString("columnComment");
				String columName = rs.getString("columnName");
				if (columName.equals(data.getColumnName(i))) {
					str.append("    //" +  coulmnContent );
					break;
				}
			}
			str.append( "\r\n");
		}
		for (int i = 1; i <= data.getColumnCount(); i++) {
			// 获得指定列的列名
			String columnName = data.getColumnName(i);
			columnName = replaceUnderlineAndfirstToUpper(columnName, "_", "");
			// 对应数据类型的类
			String columnClassName = getColumnClassName(data
					.getColumnClassName(i));
			// 获取某列对应的表名
			str.append("    public "
					+ columnClassName
					+ " "
					+ "get"
					+ columnName.replaceFirst(columnName.substring(0, 1),
							columnName.substring(0, 1).toUpperCase())
					+ "(){\n"
					+ "        return "
					+ columnName
					+ ";\n    }\n"
					+ "    public void set"
					+ columnName.replaceFirst(columnName.substring(0, 1),
							columnName.substring(0, 1).toUpperCase()) + "("
					+ columnClassName + " " + columnName + "){\n"
					+ "        this." + columnName + "=" + columnName
					+ ";\n    }\n");
		}
		str.append("}");
		//System.out.println(str);
		return str.toString();
	}


	public static String getColumnClassName(String columnClassName) {
		int begin = columnClassName.lastIndexOf(".") + 1;
		int end = columnClassName.length();
		columnClassName = columnClassName.substring(begin, end);
		if (columnClassName.equals("Timestamp")) {
			columnClassName = "Date";
			importStr = "import java.util.Date;\r\n";
		}
		
		//return columnClassName.toUpperCase();
		return columnClassName;
	}


	public static String replaceUnderlineAndfirstToUpper(String srcStr,
			String org, String ob) {
		String newString = "";
		int first = 0;
		while (srcStr.indexOf(org) != -1) {
			first = srcStr.indexOf(org);
			if (first != srcStr.length()) {
				newString = newString + srcStr.substring(0, first) + ob;
				srcStr = srcStr
						.substring(first + org.length(), srcStr.length());
				srcStr = srcStr.replaceFirst(srcStr.substring(0, 1), srcStr
						.substring(0, 1).toUpperCase());
			}
		}
		newString = newString + srcStr;
		return newString;
	}
	
	//注释部分
	public static String generateComments(String className){
		StringBuffer str = new StringBuffer("");
		str.append("/**\n");
		str.append(" *\n");
		str.append(" * @类名: " + className + "\n");
		str.append(" * @说明:\n");
		str.append(" *\n");
		str.append(" * @author: kenny\n");
		str.append(" * @Date	"+sdf.format(new Date())+"\n");
		str.append(" * 修改记录：\n");
		str.append(" *\n");
		str.append(" * @see\n");
		str.append(" */\n");
		str.append("\r\n");
		return str.toString();
	}
}
