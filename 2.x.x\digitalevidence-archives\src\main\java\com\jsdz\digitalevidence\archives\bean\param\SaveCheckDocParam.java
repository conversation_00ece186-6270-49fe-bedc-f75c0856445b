package com.jsdz.digitalevidence.archives.bean.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;

import java.io.Serializable;
import java.util.Date;


public class SaveCheckDocParam implements Serializable {


    private Long docId;

    private String checkdescribe;

    private Double checkscore;

    private String checkCode;

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public String getCheckdescribe() {
        return checkdescribe;
    }

    public void setCheckdescribe(String checkdescribe) {
        this.checkdescribe = checkdescribe;
    }

    public Double getCheckscore() {
        return checkscore;
    }

    public void setCheckscore(Double checkscore) {
        this.checkscore = checkscore;
    }

    public String getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(String checkCode) {
        this.checkCode = checkCode;
    }
}
