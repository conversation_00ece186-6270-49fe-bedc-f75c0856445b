package com.jsdz.digitalevidence.alarm.alarm110.service;

/**
 * 
 * @类名: AlarmPoliceService
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-03-25 13:43:48
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmPolice;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface AlarmPoliceService {

	//新增
	public AjaxResult addAlarmPolice(AlarmPolice alarmPolice);

	//修改
	public AjaxResult updateAlarmPolice(AlarmPolice alarmPolice);

	//删除
	public AjaxResult deleteAlarmPolice(AlarmPolice alarmPolice);

	//按id查询,结果是游离状态的数据
	public AlarmPolice findAlarmPoliceById(Long id);

	//按id查询
	public AlarmPolice locateAlarmPoliceById(Long id);

	//单个查询
	public AlarmPolice findAlarmPoliceByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmPolice> findAllAlarmPolices();

	//列表查询
	public List<AlarmPolice> findAlarmPolicesByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmPolice> findAlarmPolicesOnPage(Page<AlarmPolice> page, String queryStr,String[] paramNames,Object[] values);

}

