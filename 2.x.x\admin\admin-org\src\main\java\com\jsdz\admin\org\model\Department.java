package com.jsdz.admin.org.model;
/**
 * 
 * @类名: Department
 * @说明: 部门
 *
 * @author: kenny
 * @Date	2017年4月26日上午11:00:37
 * 修改记录：
 *
 * @see
 */
import java.io.Serializable;
import java.util.Date;


public class Department implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1241942981347138626L;
	
	private Long id;
	private String name; //部门
	private String description; //描述
	private Date createTime;

	public Department(){
		super();
	}
	public Department(String name){
		this.name=name;
		this.createTime = new Date();
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	

	

	
	
}
