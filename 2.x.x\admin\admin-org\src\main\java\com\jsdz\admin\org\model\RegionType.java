package com.jsdz.admin.org.model;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @类名: RegionType
 * @说明: 地区类型(省区、市、县、乡)
 *
 * @author: kenny
 * @Date	2017年4月25日下午4:54:44
 * 修改记录：
 *
 * @see
 */
public class RegionType implements Serializable{
	private Long id;
	private Integer level;//级别（从小到大，比喻省是1级，市是2级......  方便以后分等级可能有分等级的需求）
	private String  name;//类型(省(直辖市)/市/县(区)/镇(乡))
	private Integer description;//说明
	
	public RegionType(){
		super();
	}
	public RegionType(String name,Integer level){
		this.name=name;
		this.level=level;
	}

	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	public Integer getLevel() {
		return level;
	}
	public void setLevel(Integer level) {
		this.level = level;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Integer getDescription() {
		return description;
	}
	public void setDescription(Integer description) {
		this.description = description;
	}


	

}
