package com.jsdz.digitalevidence.activemq.utils;
/**
 * 
 * @类名: MqTopic
 * @说明: ActveMq主题
 *
 * @author: kenny
 * @Date	2018年2月1日下午10:19:41
 * 修改记录：
 *
 * @see
 */
public enum MqTopic {
	/*1视频文档，2采集站 3执法仪 4监督回放*/
	DOCUMENT(1, "DocumentTopic"), SITE(2, "SiteTopic"),RECORDER(3,"RecorderTopic"),
	RECODERREVIEW(4,"RecoderReviewTopic");
	
	public int index;
	public String name;
	private MqTopic(int index, String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
