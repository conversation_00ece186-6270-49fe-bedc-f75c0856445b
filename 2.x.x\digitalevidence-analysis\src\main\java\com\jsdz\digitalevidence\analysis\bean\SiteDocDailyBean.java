package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: SiteDocDailyBean
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年6月1日下午4:10:49
 * 修改记录：
 *
 * @see
 */
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.jsdz.core.AbstractDTO;
import com.jsdz.utils.DateTimeUtils;

public class SiteDocDailyBean extends AbstractDTO{

	/**
	 * 
	 */
	private static final long serialVersionUID = -5960826579063214995L;

	@JsonIgnore
	private Long id;
	@JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
	private Date dateFrom; //时间段从
	@JsonFormat(pattern=DateTimeUtils.defaultPatten,timezone="GMT+8")
	private Date dateTo;//时间到
	private Long siteId;//站点ID
	private String code;//站点编号
	private String name;//站点名称
	private String orgName;//单位名称
	//@JsonIgnore
	private Long fileSize;//文件长度
	private String fileSizeStr;//文件长度
	//@JsonIgnore
	private Long totalDuration;//视频时长
	private String totalDurationStr;//视频时长
	
	private Long fileCount;//文件数量
	
	private Long vedeoDuration;//视频时长
	private String vedeoDurationStr;//视频时长
	
	private Long audioDuration;//音频时长
	private String audioDurationStr;//视频时长
	
	private Long picCount;//照片数量
	private Long txtCount;//日志数量
	private Long vedioCount;//视频数量
	private Long audioCount;//日志数量
	
	private Long importanceCount;//重要文件 数量
	private Long generalCount;//一般文件 数量

	
	/**
	 * 从SiteDocDailyDto复制
	 * @param src
	 */
	public void assign(SiteDocDailyDto src) {
		try {
			this.setId(src.getId());
			this.setDateFrom(src.getDateFrom());
			this.setDateTo(src.getDateTo());
			this.setFileCount(src.getFileCount());
			this.setFileSize(src.getFileSize());
			this.setSiteId(src.getId());
			this.setName(src.getName());
			this.setCode(src.getCode());
			this.setTotalDuration(src.getVedioLen());
			this.setVedeoDuration(src.getVedeoSize());
			this.setAudioDuration(src.getAudioSize());
			this.setPicCount(src.getPicCount());
			this.setTxtCount(src.getTxtCount());
			this.setImportanceCount(src.getImportanceCount());
			this.setGeneralCount(src.getGeneralCount());
		} catch (Exception e) {
			throw new RuntimeException("Assign Error.", e);
		}
	}
	
	
	/**
	 * 从AnalysisDocumentBean 复制
	 * @param sec
	 */
	public void assgionFromAnalysisDoc(AnalysisDocumentBean src){
		try {
			this.setId(src.getId());
			this.setDateFrom(src.getDateFrom());
			this.setDateTo(src.getDateTo());
			this.setFileSize(src.getTotalFileSize());
			this.setSiteId(src.getId());
			this.setCode(src.getCode());
			this.setName(src.getName());
			this.setOrgName(src.getOrgName());
			this.setTotalDuration(src.getTotalDuration());
			this.setVedeoDuration(src.getVedioDuration());
			this.setAudioDuration(src.getAudioDuration());
			
			this.setFileCount(src.getTotalCount());
			this.setVedioCount(src.getVedioCount());
			this.setAudioCount(src.getAudioCount());
			this.setPicCount(src.getPicCount());
			this.setTxtCount(src.getTxtCount());
			this.setImportanceCount(src.getImportanceCount());
			this.setGeneralCount(src.getGeneralCount());
		} catch (Exception e) {
			throw new RuntimeException("Assign Error.", e);
		}		
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Date getDateFrom() {
		return dateFrom;
	}
	public void setDateFrom(Date dateFrom) {
		this.dateFrom = dateFrom;
	}
	public Date getDateTo() {
		return dateTo;
	}
	public void setDateTo(Date dateTo) {
		this.dateTo = dateTo;
	}
	public Long getFileSize() {
		return fileSize;
	}
	public void setFileSize(Long fileSize) {
		this.fileSize = fileSize;
	}
	public String getFileSizeStr() {
		return(genFileSizeStr(this.fileSize));
	}
	public void setFileSizeStr(String fileSizeStr) {
		this.fileSizeStr = fileSizeStr;
	}
	public Long getFileCount() {
		return fileCount;
	}
	public void setFileCount(Long fileCount) {
		this.fileCount = fileCount;
	}

	public Long getSiteId() {
		return siteId;
	}

	public void setSiteId(Long siteId) {
		this.siteId = siteId;
	}
	
	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String toString(){
		
		return 	"siteNo:" + this.getCode() + "  siteName:" + this.getName() +
				"  fileZise:" + this.getFileSizeStr() +
				"  vedioLen:" + this.getTotalDuration();
	}

	public Long getPicCount() {
		return picCount;
	}

	public void setPicCount(Long picCount) {
		this.picCount = picCount;
	}

	public Long getTxtCount() {
		return txtCount;
	}

	public void setTxtCount(Long txtCount) {
		this.txtCount = txtCount;
	}

	public Long getImportanceCount() {
		return importanceCount;
	}

	public void setImportanceCount(Long importanceCount) {
		this.importanceCount = importanceCount;
	}

	public Long getGeneralCount() {
		return generalCount;
	}

	public void setGeneralCount(Long generalCount) {
		this.generalCount = generalCount;
	}
	
	public String getOrgName() {
		return orgName;
	}

	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Long getVedioCount() {
		return vedioCount;
	}

	public void setVedioCount(Long vedioCount) {
		this.vedioCount = vedioCount;
	}

	public Long getAudioCount() {
		return audioCount;
	}

	public void setAudioCount(Long audioCount) {
		this.audioCount = audioCount;
	}
	
	public Long getTotalDuration() {
		return totalDuration;
	}


	public void setTotalDuration(Long totalDuration) {
		this.totalDuration = totalDuration;
	}


	public String getTotalDurationStr() {
		return this.genDurationStr(this.totalDuration);
	}


	public void setTotalDurationStr(String totalDurationStr) {
		this.totalDurationStr = totalDurationStr;
	}


	public Long getVedeoDuration() {
		return vedeoDuration;
	}


	public void setVedeoDuration(Long vedeoDuration) {
		this.vedeoDuration = vedeoDuration;
	}


	public String getVedeoDurationStr() {
		return this.genDurationStr(this.vedeoDuration);
	}


	public void setVedeoDurationStr(String vedeoDurationStr) {
		this.vedeoDurationStr = vedeoDurationStr;
	}


	public Long getAudioDuration() {
		return audioDuration;
	}


	public void setAudioDuration(Long audioDuration) {
		this.audioDuration = audioDuration;
	}


	public String getAudioDurationStr() {
		return this.genDurationStr(this.audioDuration);
	}


	public void setAudioDurationStr(String audioDurationStr) {
		this.audioDurationStr = audioDurationStr;
	}

	//视频时长的显示格式
	private String genDurationStr(Long val){
		//视频时长
		String vedionLens = null;
		if (val != null && val != 0){
			vedionLens = "";
			Integer hour=null;
			Integer minite=null;
			long sec = val / 1000;
			
			hour = (int)sec / (60*60);
			sec = sec %  (60*60);
			minite = (int)sec / 60;
			sec = sec %  60;
			
			if (hour != null && hour !=0 ){
				vedionLens = String.valueOf(hour) + "小时";
			}
			if (minite != null && minite !=0){
				vedionLens += String.valueOf(minite) + "分";
			}
			vedionLens += String.valueOf(sec) + "秒";
		}
		return vedionLens;		
	}
	//文件长度的显示格式
	private String genFileSizeStr(Long val){
		String fss = null;
		if (val != null){
			if (val > 1000*1000*1000){
				double fSize = val/(1000*1000*1000.00);
				fss = String.valueOf(Math.round(fSize*100.0)/100.0) + "G";
			}else if ( (val > 1000*1000)){
				double fSize = val/(1000*1000.00);
				fss = String.valueOf(Math.round(fSize*100.0)/100.0) + "M";
				
			}else if ( (val > 1000)){
				double fSize = val/(1000.00);
				fss = String.valueOf(Math.round(fSize*100.00)/100.00) + "K";
				
			}else{
				double fSize = val;
				fss = String.valueOf(fSize) + "B";
			}			
		}

		return fss;
	}
	
	
}
