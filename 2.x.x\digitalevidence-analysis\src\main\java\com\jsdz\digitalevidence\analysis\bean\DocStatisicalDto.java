package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: DocStatisicalDto
 * @说明: 文档查询统计分析SQL查询结果类
 *
 * @author: kenny
 * @Date	2017年6月13日下午1:59:27
 * 修改记录：
 *
 * @see
 */
public class DocStatisicalDto {
	private Long id;
	private String code;
	private String name;
	private Double sumValue;
	
	
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Double getSumValue() {
		return sumValue;
	}
	public void setSumValue(Double sumValue) {
		this.sumValue = sumValue;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
}
