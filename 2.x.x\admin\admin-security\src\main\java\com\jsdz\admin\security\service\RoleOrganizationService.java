package com.jsdz.admin.security.service;

/**
 * 
 * @类名: RoleOrganizationService
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-05-15 16:42:50
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.bean.RoleMenuBean;
import com.jsdz.admin.security.model.RoleOrganization;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface RoleOrganizationService {

	//新增
	public AjaxResult addRoleOrganization(RoleOrganization roleOrganization);

	//修改
	public AjaxResult updateRoleOrganization(RoleOrganization roleOrganization);

	//删除
	public AjaxResult deleteRoleOrganization(RoleOrganization roleOrganization);

	//按id查询,结果是游离状态的数据
	public RoleOrganization findRoleOrganizationById(Long id);

	//按id查询
	public RoleOrganization locateRoleOrganizationById(Long id);

	//单个查询
	public RoleOrganization findRoleOrganizationByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<RoleOrganization> findAllRoleOrganizations();

	//列表查询
	public List<RoleOrganization> findRoleOrganizationsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RoleOrganization> findRoleOrganizationsOnPage(Page<RoleOrganization> page, String queryStr,String[] paramNames,Object[] values);

	//设置角色单位权限
    public AjaxResult setRoleOrganization(String orgIds,Long roleId);
    
    //查询所有的单位给角色设置
    public List<RoleMenuBean> findAllOrganitionForRoleSet(Long roleId) throws Exception;
    
    //取权限单位
    public String getRoleOrgPath(Long userId);
}

