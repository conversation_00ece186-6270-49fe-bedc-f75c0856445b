package com.jsdz.digitalevidence.analysis.openapi.dao.impl;

/**
 *
 * @类名: CxDsjLogDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-06-09 16:03:20
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.analysis.openapi.dao.CxDsjLogDao;
import com.jsdz.digitalevidence.analysis.openapi.model.CxDsjLog;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class CxDsjLogDaoImpl extends GenericEntityDaoHibernateImpl<CxDsjLog,Long> implements CxDsjLogDao{

	//新增
	public void addCxDsjLog(CxDsjLog cxDsjLog) {
		this.saveOrUpdate(cxDsjLog);
	}

	//删除
	public void deleteCxDsjLog(CxDsjLog cxDsjLog) {
		this.delete(cxDsjLog);
	}

	//修改
	public void updateCxDsjLog(CxDsjLog cxDsjLog) {
		this.merge(cxDsjLog);
	}

	//按id查询(游离状态)
	public CxDsjLog findCxDsjLogById(Long id){

		final String  hql = "from CxDsjLog d where d.id = :id";
		final Long oid = id;
		CxDsjLog data = getHibernateTemplate().execute(new HibernateCallback<CxDsjLog>() {
			public CxDsjLog doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<CxDsjLog> list = query.list();
				CxDsjLog rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public CxDsjLog locateCxDsjLogById(Long id){

		final String  hql = "from CxDsjLog d where d.id = :id";
		final Long oid = id;
		CxDsjLog data = getHibernateTemplate().execute(new HibernateCallback<CxDsjLog>() {
			public CxDsjLog doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<CxDsjLog> list = query.list();
				CxDsjLog rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public CxDsjLog findCxDsjLogByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		CxDsjLog data = getHibernateTemplate().execute(new HibernateCallback<CxDsjLog>() {
		public CxDsjLog doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<CxDsjLog> list = query.list();
			CxDsjLog rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<CxDsjLog> findAllCxDsjLogs(){
		return this.find("from CxDsjLog cxDsjLog ");
	}

	//列表查询
	public List<CxDsjLog> findCxDsjLogsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<CxDsjLog> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<CxDsjLog> findCxDsjLogsOnPage(Page<CxDsjLog> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<CxDsjLog>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
