/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.dao.impl;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;

import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.assessment.dao.AssessmentPushDocDao;
import com.jsdz.digitalevidence.assessment.model.push.AssessmentPushDoc;

/**
 * @类名: ShootingAssessmentDaoImpl
 * @说明: 拍摄考核dao实现
 *
 * <AUTHOR>
 * @Date	 2017年8月14日 下午4:07:05
 * 修改记录：
 *
 * @see 	 
 */
@Repository
public class AssessmentPushDocDaoImpl extends GenericEntityDaoHibernateImpl<AssessmentPushDoc, Long> 
					implements AssessmentPushDocDao {

	@Override
	public int updatePushDocStatus(Long pushingDocId, boolean isAssessed) {
		String hql = "update AssessmentPushDoc p "
				+ "set p.hasAssessed = :isAssessed "
				+ "where 1=1 and p.pushDocId = :pushingDocId ";
		int c = this.getHibernateTemplate().execute(new HibernateCallback<Integer>() {
			@Override
			public Integer doInHibernate(Session session) throws HibernateException {
				// 获取查询对象
				Query queryObject = session.createQuery(hql);
				queryObject.setParameter("pushingDocId", pushingDocId);
				queryObject.setParameter("isAssessed", isAssessed);
				return queryObject.executeUpdate();
				
		}});
		return c;
	}
	
}
