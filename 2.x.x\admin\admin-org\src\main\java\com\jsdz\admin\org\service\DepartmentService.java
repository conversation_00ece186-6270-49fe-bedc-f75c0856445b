package com.jsdz.admin.org.service;

/**
 * 
 * @类名: DepartmentService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-10 08:34:34
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.org.model.Department;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface DepartmentService {

	//新增
	public AjaxResult addDepartment(Department department);

	//修改
	public AjaxResult updateDepartment(Department department);

	//删除
	public AjaxResult deleteDepartment(Department department);

	//按id查询
	public Department locateDepartmentById(Long id);

	//单个查询
	public Department findDepartmentByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<Department> findAllDepartments();

	//列表查询
	public List<Department> findDepartmentsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Department> findDepartmentsOnPage(Page<Department> page, String queryStr,String[] paramNames,Object[] values);

}

