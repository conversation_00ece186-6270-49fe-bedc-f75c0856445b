package com.jsdz.digitalevidence.activemq.utils;
//日志类型
public enum LogType {
	/**/
	INFO(1, "提示"), ERROR(2, "错误");
	
	public int index;
	public String name;
	
	private LogType(int index, String name) {
		this.name = name;
	}

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	
}
