package com.jsdz.digitalevidence.alarm.alarm110.dao;

/**
 *
 * @类名: AlarmRelationDao
 * @说明:
 * @author: kenny
 * @Date 2017-08-29 19:53:45
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;

public interface AlarmRelationDao extends GenericORMEntityDAO<AlarmRelation,Long> {

	//新增
	public void addAlarmRelation(AlarmRelation alarmRelation);

	//修改
	public void updateAlarmRelation(AlarmRelation alarmRelation);

	//删除
	public void deleteAlarmRelation(AlarmRelation alarmRelation);

	//按id查询,结果是游离状态的数据
	public AlarmRelation findAlarmRelationById(Long id);

	//按id查询
	public AlarmRelation locateAlarmRelationById(Long id);

	//单个查询
	public AlarmRelation findAlarmRelationByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<AlarmRelation> findAllAlarmRelations();

	//列表查询
	public List<AlarmRelation> findAlarmRelationsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<AlarmRelation> findAlarmRelationsOnPage(Page<AlarmRelation> page,String queryStr,String[] paramNames,Object[] values);

	//查询是否重复
	public AlarmRelation findMutipleAlarmRelation(AlarmRelation alarmRelation);
	
	//执行指定的HQL文件
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);
}

