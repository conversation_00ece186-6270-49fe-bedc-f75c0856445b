package com.jsdz.digitalevidence.assessment.model.alert.rule;

import java.util.ArrayList;
import java.util.List;

import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.ruleengine.RuleState;
import com.jsdz.ruleengine.annotation.Given;
import com.jsdz.ruleengine.annotation.Param;
import com.jsdz.ruleengine.annotation.Result;
import com.jsdz.ruleengine.annotation.Rule;
import com.jsdz.ruleengine.annotation.Then;
import com.jsdz.ruleengine.annotation.When;

/**
 * @类名: ShootingAssessmentNoWFWJRule
 * @说明: 拍摄考核文明规范百分比规则
                   不存在违法违纪数百分比
 *        
 *        
 *
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 
 * 修改记录：
 *
 * @see
 */
@Rule(name="noWfwjPercent", description="没有违法违纪百分比规则")
public class ShootingAssessmentNoWFWJRule extends ShootingAssessmentRuleBase {
	
	public static final String key = "没有违法违纪百分比";

	@Param(name="noWfwjPercent", description="没有违法违纪百分比")
	private float noWFWJPercent;
	
	/** */
	@Result
	private List<AssessmentAlertItem<?>> _result;
	
	@Given
	private ShootingAssessmentSummary sum;
	
	@When
	public boolean when() {
	    if((float)sum.getCountOfNoWFWJ()/sum.getTotalAssessingDoc()<noWFWJPercent)
	    	return true;
		return false;
	}

	@Then
	public RuleState then() {
		AssessmentAlertItem<Float> item = new AssessmentAlertItem<Float>();
		item.setItem(key);
		item.setV((float)sum.getCountOfNoWFWJ()/sum.getTotalAssessingDoc());
		item.setRef(noWFWJPercent);
		if(_result==null)
			_result = new ArrayList<AssessmentAlertItem<?>>();
		_result.add(item);
		return RuleState.NEXT;
	}
	
}
