package com.jsdz.admin.security.service;

/**
 * 
 * @类名: SysConfigService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-16 20:48:56
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.SysConfig;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface SysConfigService {

	//新增
	public AjaxResult addSysConfig(SysConfig sysConfig);

	//修改
	public AjaxResult updateSysConfig(SysConfig sysConfig);

	//删除
	public AjaxResult deleteSysConfig(SysConfig sysConfig);

	//按id查询,结果是游离状态的数据
	public SysConfig findSysConfigById(Long id);

	//按id查询
	public SysConfig locateSysConfigById(Long id);

	//单个查询
	public SysConfig findSysConfigByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<SysConfig> findAllSysConfigs();

	//列表查询
	public List<SysConfig> findSysConfigsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<SysConfig> findSysConfigsOnPage(Page<SysConfig> page, String queryStr,String[] paramNames,Object[] values);


	//按key查询
	public SysConfig findSysConfigByKey(String key);

}

