/**
 * 
 */
package com.jsdz.digitalevidence.assessment.model;

import java.util.Date;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.document.model.Document;

/**
 * @类名: ShootingAssessment
 * @说明: 拍摄考核
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午5:46:40
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingAssessment extends AbstractDTO {

	/** */
	private Long id;
	/** 评核类型，日常/随机 */
	private AssessmentCate cate;
	/** 是否文明规范*/
	private boolean isWMGF;
	/** 不存在违法违纪*/
	private boolean isNoWFWJ;
	/** 按要求拍摄*/
	private boolean isAYQPS;
	/** 按要求运用语言*/
	private boolean isYYYY;
	/** 拍摄角度*/
	private ShootingAngleLevel shootingAngle;
	/** 拍摄内容要素*/
	private ShootingContentLevel shootingContent;
	/** 拍摄分辨率*/
	private ShootingResolutionLevel shootingResolution;
	/** 考核时间*/
	private Date assessDate;
	/** 考核文档日期*/
	private Date docDate;
	/** 考核目标资料*/
	private Document document;
	/** 考核员*/
	private Employees inspector;
	
	public Document getDocument() {
		return document;
	}
	public void setDocument(Document document) {
		this.document = document;
	}
	public Employees getInspector() {
		return inspector;
	}
	public void setInspector(Employees inspector) {
		this.inspector = inspector;
	}
	public AssessmentCate getCate() {
		return cate;
	}
	public void setCate(AssessmentCate cate) {
		this.cate = cate;
	}
	public boolean isWMGF() {
		return isWMGF;
	}
	public boolean getIsWMGF() {
		return isWMGF;
	}
	public void setWMGF(boolean isWMGF) {
		this.isWMGF = isWMGF;
	}
	public void setIsWMGF(boolean isWMGF) {
		this.isWMGF = isWMGF;
	}
	public boolean isNoWFWJ() {
		return isNoWFWJ;
	}
	public void setNoWFWJ(boolean noWFWJ) {
		this.isNoWFWJ = noWFWJ;
	}
	public boolean getIsNoWFWJ() {
		return isNoWFWJ;
	}
	public void setIsNoWFWJ(boolean noWFWJ) {
		this.isNoWFWJ = noWFWJ;
	}
	public boolean isAYQPS() {
		return isAYQPS;
	}
	public void setAYQPS(boolean isAYQPS) {
		this.isAYQPS = isAYQPS;
	}
	public boolean getIsAYQPS() {
		return isAYQPS;
	}
	public void setIsAYQPS(boolean isAYQPS) {
		this.isAYQPS = isAYQPS;
	}
	public boolean isYYYY() {
		return isYYYY;
	}
	public void setYYYY(boolean isYYYY) {
		this.isYYYY = isYYYY;
	}
	public boolean getIsYYYY() {
		return isYYYY;
	}
	public void setIsYYYY(boolean isYYYY) {
		this.isYYYY = isYYYY;
	}
	public ShootingAngleLevel getShootingAngle() {
		return shootingAngle;
	}
	public void setShootingAngle(ShootingAngleLevel shootingAngle) {
		this.shootingAngle = shootingAngle;
	}
	public ShootingResolutionLevel getShootingResolution() {
		return shootingResolution;
	}
	public void setShootingResolution(ShootingResolutionLevel shootingResolution) {
		this.shootingResolution = shootingResolution;
	}
	public Date getAssessDate() {
		return assessDate;
	}
	public void setAssessDate(Date assessDate) {
		this.assessDate = assessDate;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public ShootingContentLevel getShootingContent() {
		return shootingContent;
	}
	public void setShootingContent(ShootingContentLevel shootingContent) {
		this.shootingContent = shootingContent;
	}
	
}
