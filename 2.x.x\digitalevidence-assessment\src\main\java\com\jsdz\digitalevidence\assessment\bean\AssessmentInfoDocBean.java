/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.model.ImportantLevel;

import java.util.Date;
import java.util.List;

/**
 * @类名: AssessmentDocBean
 * @说明: 考核文档Bean
 * <AUTHOR>
 */
public class AssessmentInfoDocBean extends AbstractDTO {

    // 考核员信息
    /**考核员警号*/
    private String assessmentPoliceId;
	/**考核员姓名*/
    private String assessmentName;
    /**考核员警号*/
    private String assessmentPoliceCode;
    /**考核员单位*/
    private String assessmentOrg;
	/**考核员单位名称*/
	private String assessmentOrgName;
	/**考核ID*/
	private String assessmentId;


    //考核信息
	/** 考核备注*/
	private String assessmentRamks;
	/** 考核考核评分*/
	private Integer assessmentScore;
	/** 考核时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Date assessmentCreateTime;
	List<AssessmentDocBean> assessmentDocBeans;


	/** 页数, 以0开始 */
	private Integer page;
	/** 页大小/窗口大小 */
	private Integer pageSize;
	/** 偏移量 */
	private Integer offset;
	/** 总页数 */
	private Integer total;

	public Integer getPage() {
		return page;
	}

	public void setPage(Integer page) {
		this.page = page;
	}

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public Integer getOffset() {
		return offset;
	}

	public void setOffset(Integer offset) {
		this.offset = offset;
	}

	public Integer getTotal() {
		return total;
	}

	public void setTotal(Integer total) {
		this.total = total;
	}

	/** 排序Bean */



	public String getAssessmentPoliceId() {
		return assessmentPoliceId;
	}

	public void setAssessmentPoliceId(String assessmentPoliceId) {
		this.assessmentPoliceId= assessmentPoliceId;
	}

	public String getAssessmentName() {
		return assessmentName;
	}

	public void setAssessmentName(String assessmentName) {
		this.assessmentName = assessmentName;
	}

	public String getAssessmentOrg() {
		return assessmentOrg;
	}

	public void setAssessmentOrg(String assessmentOrg) {
		this.assessmentOrg = assessmentOrg;
	}

	public String getAssessmentOrgName() {
		return assessmentOrgName;
	}

	public void setAssessmentOrgName(String assessmentOrgName) {
		this.assessmentOrgName = assessmentOrgName;
	}

	public String getAssessmentId() {
		return assessmentId;
	}

	public void setAssessmentId(String assessmentId) {
		this.assessmentId = assessmentId;
	}

	public String getAssessmentRamks() {
		return assessmentRamks;
	}

	public void setAssessmentRamks(String assessmentRamks) {
		this.assessmentRamks = assessmentRamks;
	}

	public Integer getAssessmentScore() {
		return assessmentScore;
	}

	public void setAssessmentScore(Integer assessmentScore) {
		this.assessmentScore = assessmentScore;
	}

	public Date getAssessmentCreateTime() {
		return assessmentCreateTime;
	}

	public void setAssessmentCreateTime(Date assessmentCreateTime) {
		this.assessmentCreateTime = assessmentCreateTime;
	}

	public List<AssessmentDocBean> getAssessmentDocBeans() {
		return assessmentDocBeans;
	}

	public void setAssessmentDocBeans(List<AssessmentDocBean> assessmentDocBeans) {
		this.assessmentDocBeans = assessmentDocBeans;
	}

	public String getAssessmentPoliceCode() {
		return assessmentPoliceCode;
	}

	public void setAssessmentPoliceCode(String assessmentPoliceCode) {
		this.assessmentPoliceCode = assessmentPoliceCode;
	}
	
}
