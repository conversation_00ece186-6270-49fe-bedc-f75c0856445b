package com.jsdz.admin.security.dao;

/**
 *
 * @类名: DataDictionaryDao
 * @说明:
 * @author: kenny
 * @Date 2017-10-31 11:43:33
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.security.model.DataDictionary;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface DataDictionaryDao extends GenericORMEntityDAO<DataDictionary,Long> {

	//新增
	public void addDataDictionary(DataDictionary dataDictionary);

	//修改
	public void updateDataDictionary(DataDictionary dataDictionary);

	//删除
	public void deleteDataDictionary(DataDictionary dataDictionary);

	//按id查询,结果是游离状态的数据
	public DataDictionary findDataDictionaryById(Long id);

	//按id查询
	public DataDictionary locateDataDictionaryById(Long id);

	//单个查询
	public DataDictionary findDataDictionaryByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<DataDictionary> findAllDataDictionarys();

	//列表查询
	public List<DataDictionary> findDataDictionarysByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<DataDictionary> findDataDictionarysOnPage(Page<DataDictionary> page,String queryStr,String[] paramNames,Object[] values);

}

