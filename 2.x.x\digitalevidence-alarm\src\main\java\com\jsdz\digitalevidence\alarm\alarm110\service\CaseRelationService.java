package com.jsdz.digitalevidence.alarm.alarm110.service;

import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;
import com.jsdz.digitalevidence.alarm.alarm110.model.CaseRelation;

public interface CaseRelationService {
	
	// 分页查询
	Page<CaseRelation> findCaseRelationOnPage(Page<CaseRelation> page, String queryStr, String[] paramNames,
			Object[] values);
	
	// 增加关联
	AjaxResult addCaseRelation(CaseRelation caseRelation);
	
	// 根据id查询(非游离)
	CaseRelation findCaseRelation(Long id);
	
	//删除关联
	AjaxResult deleteCaseRelation(CaseRelation caseRelation);

	

}
