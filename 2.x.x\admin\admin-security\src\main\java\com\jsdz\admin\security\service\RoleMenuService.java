package com.jsdz.admin.security.service;

/**
 * 
 * @类名: RoleMenuService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-09-08 12:03:22
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.admin.security.model.RoleMenu;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface RoleMenuService {

	//新增
	public AjaxResult addRoleMenu(RoleMenu roleMenu);

	//修改
	public AjaxResult updateRoleMenu(RoleMenu roleMenu);

	//删除
	public AjaxResult deleteRoleMenu(RoleMenu roleMenu);

	//按id查询,结果是游离状态的数据
	public RoleMenu findRoleMenuById(Long id);

	//按id查询
	public RoleMenu locateRoleMenuById(Long id);

	//单个查询
	public RoleMenu findRoleMenuByParam(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<RoleMenu> findAllRoleMenus();

	//列表查询
	public List<RoleMenu> findRoleMenusByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RoleMenu> findRoleMenusOnPage(Page<RoleMenu> page, String queryStr,String[] paramNames,Object[] values);

	//设置权限
    public AjaxResult setSysMenuRole(String menuIds,Long roleId);
}

