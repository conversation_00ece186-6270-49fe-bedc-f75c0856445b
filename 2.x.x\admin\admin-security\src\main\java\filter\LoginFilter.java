package filter;


import java.awt.event.ActionEvent;
import java.io.IOException;
import java.io.PrintWriter;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.jsdz.admin.security.model.Operator;
/**
 * 
 * @类名: LoginFilter
 * @说明: 用于检查用户是否登录了系统的过滤器
 *
 * @author: kenny
 * @Date	2017年4月28日上午10:01:57
 * 修改记录：
 *
 * @see
 */ 
public class LoginFilter implements Filter {
 
    //@Override
    public void init(FilterConfig filterConfig) throws ServletException {
    	;
    }

    //@Override
    public void doFilter(ServletRequest req, ServletResponse res, Filter<PERSON>hain chain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) res;
        String servletPath = request.getRequestURI();
        String contextPath =request.getContextPath();
        Operator user=(Operator) request.getSession().getAttribute("operator");
        // 如果Session为空，则跳转到指定页面
        if (user == null) {
        	 if(examineUrl(servletPath)){
             	 chain.doFilter(req, res);
             	 return;
             }
        	 if(request.getHeader("x-requested-with")!=null && request.getHeader("x-requested-with").equalsIgnoreCase("XMLHttpRequest")){ 
						PrintWriter printWriter = response.getWriter(); 
						printWriter.print("{sessionState:0,url:"+request.getContextPath()+"}"); 
						printWriter.flush(); 
						printWriter.close(); 
						return;
					}else{
						response.sendRedirect(contextPath +"/xxxxxx/index.html");
						return;
					}
           
        } else {
            chain.doFilter(req, res);
        }
    }
    /**
     *  校验是否放行
     *  黄扬仲
     *  2015年5月25日
     */
	 public boolean examineUrl(String servletPath){
		 boolean pass=false;
		 for(String url:PropertieUtil.unCheckUrlList){
			 if(servletPath.indexOf(url)!=-1){
				 pass=true;
				 break;
			 }
		 }
		 return pass;
	 }
	 
	//@Override
	public void destroy() {
		
	}
}
