package com.jsdz.digitalevidence.alarm.alarm110.bean;
/**
 * 
 * @类名: AlarmInfoBean
 * @说明: AlarmInfo封装类
 *
 * @author: kenny
 * @Date	2017年8月30日上午9:25:29
 * 修改记录：
 *
 * @see
 */

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmOrganization;

public class AlarmInfoBean {

	private Long id;    //ID
    private String alarmCode;    //警情号
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date alarmTime;    //报警时间
    private String alarmName;    //报警人姓名
    private String alarmTel;    //报警电话
    private String alarmContext;    //报警内容
    private String alarmResult; //处理结果
    //private Organization reveiceOrg;    //接警单位
    private Integer isRelation; //是否已关联(0未关联，1已关联)	
    //private Operator police;    //处警人
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime; //创建时间
    private String alarmSuperviseType;//警情监督情况类型
    //接警单位
    private Long orgId;    
    private String orgCode;
    private String orgName;
    //处警人 
    private Long operatorId;    
    private String loginName;
    private Long employeesId;
    private String policeName; //姓名
    private String workNumber; //警号
    
    //查询时间
    private String createTimeBegin;
    private String createTimeEnd;
    private String alarmTimeBegin;
    private String alarmTimeEnd;
    private String processTimeBegin;
    private String processTimeEnd;

	//是否可操作(关联，取消关联)
    private Integer canRelation;
    
    private String alarmProccessCode;//处警单编号
    private String alarmReceiveCode;//接警单编号
    
    private String archiveCode;//案件编号
    
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date processTime;    //处警时间
    private String alarmFeebackCode;//反馈单编号
    private String alarmAddress;//事发地址
    
    private String alarmPolices;//处警人，多个人以,号隔开

	private String cljg;

	public String getCljg() {
		return cljg;
	}

	public void setCljg(String cljg) {
		this.cljg = cljg;
	}

	public void assign(AlarmInfo sur){
    	id = sur.getId();
    	alarmCode = sur.getAlarmCode();
    	alarmTime = sur.getAlarmTime();
    	alarmName = sur.getAlarmName();
    	alarmTel = sur.getAlarmTel();
    	alarmContext = sur.getAlarmContext();
    	alarmResult = sur.getAlarmResult();
    	isRelation = sur.getIsRelation();
    	createTime = sur.getCreateTime();
    	alarmSuperviseType = sur.getAlarmSuperviseType();
    	alarmProccessCode = sur.getAlarmProccessCode();
    	alarmReceiveCode = sur.getAlarmReceiveCode();
    	archiveCode = sur.getArchiveCode();
		this.cljg = sur.getTitle(); //长沙，处理结果
    	this.processTime = sur.getProcessTime();
    	this.alarmFeebackCode = sur.getAlarmFeebackCode();
    	this.alarmAddress = sur.getAlarmAddress();
    	this.setAlarmPolices(sur.getAlarmPolices());
    	
    	//接警单位
/*    	orgId = sur.getReveiceOrg() == null?null:sur.getReveiceOrg().getId();
    	orgCode = sur.getReveiceOrg() == null?null:sur.getReveiceOrg().getOrgCode();
    	orgName = sur.getReveiceOrg() == null?null:sur.getReveiceOrg().getOrgName();
*/        //处警人
/*    	operatorId = sur.getPolice() == null?null:sur.getPolice().getId();
    	loginName = sur.getPolice() == null?null:sur.getPolice().getLoginName();
    	employeesId = sur.getPolice() != null && sur.getPolice().getEmployees() != null?sur.getPolice().getEmployees().getId():null;
    	policeName = sur.getPolice() != null && sur.getPolice().getEmployees() != null?sur.getPolice().getEmployees().getName():null;
    	workNumber = sur.getPolice() != null && sur.getPolice().getEmployees() != null?sur.getPolice().getEmployees().getWorkNumber():null;
*/    
    	}
	

	
    public void assignTo(AlarmInfo dest){
    	dest.setId(id);
    	dest.setAlarmCode(alarmCode);
    	dest.setAlarmName(alarmName);
    	dest.setAlarmTime(alarmTime);
    	dest.setAlarmName(alarmName);
    	dest.setAlarmTel(alarmTel);
    	dest.setCreateTime(createTime);
    	dest.setAlarmContext(alarmContext);
    	dest.setIsRelation(isRelation==null?0:isRelation);
    	dest.setAlarmSuperviseType(alarmSuperviseType);
    	dest.setAlarmProccessCode(this.alarmProccessCode);
    	dest.setAlarmReceiveCode(this.alarmReceiveCode);
    	dest.setArchiveCode(this.archiveCode);
    	dest.setAlarmFeebackCode(this.getAlarmFeebackCode());
    	dest.setProcessTime(this.processTime);
    	dest.setAlarmAddress(this.alarmAddress);
    	dest.setAlarmPolices(this.getAlarmPolices());

    	
/*    	if (orgId != null){
        	Organization org = new Organization();
        	org.setId(orgId);
        	dest.setReveiceOrg(org);
    	}
    	
    	if (operatorId != null){
    		Operator opr = new Operator();
    		opr.setId(operatorId);
    		dest.setPolice(opr);
    	}*/
    }
    
	public void assign(AlarmOrganization sur){
		this.setId(sur.getAlarmInfo().getId());
		this.setAlarmCode(sur.getAlarmInfo().getAlarmCode());
		this.setAlarmTime(sur.getAlarmInfo().getAlarmTime());
		this.setAlarmName(sur.getAlarmInfo().getAlarmName());
		this.setAlarmTel(sur.getAlarmInfo().getAlarmTel());
		this.setAlarmContext(sur.getAlarmInfo().getAlarmContext());
		this.setIsRelation(sur.getAlarmInfo().getIsRelation());
		this.setCreateTime(sur.getAlarmInfo().getCreateTime());
    	this.setAlarmPolices(sur.getAlarmInfo().getAlarmPolices());
		this.setCljg(sur.getAlarmInfo().getTitle());
		this.setAlarmAddress(sur.getAlarmInfo().getAlarmAddress());
    	//处警单位
    	this.setOrgId(sur.getOrg().getId());
    	this.setOrgCode(sur.getOrg().getOrgCode());
    	this.setOrgName(sur.getOrg().getOrgName());
    }    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getAlarmCode() {
		return alarmCode;
	}
	public void setAlarmCode(String alarmCode) {
		this.alarmCode = alarmCode;
	}
	public Date getAlarmTime() {
		return alarmTime;
	}
	public void setAlarmTime(Date alarmTime) {
		this.alarmTime = alarmTime;
	}
	public String getAlarmName() {
		return alarmName;
	}
	public void setAlarmName(String alarmName) {
		this.alarmName = alarmName;
	}
	public String getAlarmTel() {
		return alarmTel;
	}
	public void setAlarmTel(String alarmTel) {
		this.alarmTel = alarmTel;
	}
	public String getAlarmContext() {
		return alarmContext;
	}
	public void setAlarmContext(String alarmContext) {
		this.alarmContext = alarmContext;
	}
	public Integer getIsRelation() {
		return isRelation;
	}
	public void setIsRelation(Integer isRelation) {
		this.isRelation = isRelation;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Long getOrgId() {
		return orgId;
	}
	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Long getOperatorId() {
		return operatorId;
	}
	public void setOperatorId(Long operatorId) {
		this.operatorId = operatorId;
	}
	public String getLoginName() {
		return loginName;
	}
	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}
	public Long getEmployeesId() {
		return employeesId;
	}
	public void setEmployeesId(Long employeesId) {
		this.employeesId = employeesId;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public String getWorkNumber() {
		return workNumber;
	}
	public void setWorkNumber(String workNumber) {
		this.workNumber = workNumber;
	}
	public String getCreateTimeBegin() {
		return createTimeBegin;
	}
	public void setCreateTimeBegin(String createTimeBegin) {
		this.createTimeBegin = createTimeBegin;
	}
	public String getCreateTimeEnd() {
		return createTimeEnd;
	}
	public void setCreateTimeEnd(String createTimeEnd) {
		this.createTimeEnd = createTimeEnd;
	}
	public String getAlarmTimeBegin() {
		return alarmTimeBegin;
	}
	public void setAlarmTimeBegin(String alarmTimeBegin) {
		this.alarmTimeBegin = alarmTimeBegin;
	}
	public String getAlarmTimeEnd() {
		return alarmTimeEnd;
	}
	public void setAlarmTimeEnd(String alarmTimeEnd) {
		this.alarmTimeEnd = alarmTimeEnd;
	}

	public Integer getCanRelation() {
		return canRelation;
	}

	public void setCanRelation(Integer canRelation) {
		this.canRelation = canRelation;
	}

	public String getAlarmResult() {
		return alarmResult;
	}

	public void setAlarmResult(String alarmResult) {
		this.alarmResult = alarmResult;
	}

	public String getAlarmSuperviseType() {
		return alarmSuperviseType;
	}

	public void setAlarmSuperviseType(String alarmSuperviseType) {
		this.alarmSuperviseType = alarmSuperviseType;
	}

	public String getAlarmProccessCode() {
		return alarmProccessCode;
	}

	public void setAlarmProccessCode(String alarmProccessCode) {
		this.alarmProccessCode = alarmProccessCode;
	}

	public String getAlarmReceiveCode() {
		return alarmReceiveCode;
	}

	public void setAlarmReceiveCode(String alarmReceiveCode) {
		this.alarmReceiveCode = alarmReceiveCode;
	}

	public String getArchiveCode() {
		return archiveCode;
	}

	public void setArchiveCode(String archiveCode) {
		this.archiveCode = archiveCode;
	}
    
    
    
    public String getProcessTimeBegin() {
		return processTimeBegin;
	}

	public void setProcessTimeBegin(String processTimeBegin) {
		this.processTimeBegin = processTimeBegin;
	}

	public String getProcessTimeEnd() {
		return processTimeEnd;
	}

	public void setProcessTimeEnd(String processTimeEnd) {
		this.processTimeEnd = processTimeEnd;
	}

	public String getAlarmAddress() {
		return alarmAddress;
	}

	public void setAlarmAddress(String alarmAddress) {
		this.alarmAddress = alarmAddress;
	}
	
    public Date getProcessTime() {
		return processTime;
	}

	public void setProcessTime(Date processTime) {
		this.processTime = processTime;
	}

	public String getAlarmFeebackCode() {
		return alarmFeebackCode;
	}

	public void setAlarmFeebackCode(String alarmFeebackCode) {
		this.alarmFeebackCode = alarmFeebackCode;
	}

	public String getAlarmPolices() {
		return alarmPolices;
	}

	public void setAlarmPolices(String alarmPolices) {
		this.alarmPolices = alarmPolices;
	}	
	
	
}
