package TestService;

import java.util.Date;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.service.OperatorService;
import com.jsdz.admin.security.service.RoleService;
import com.jsdz.core.AjaxResult;

/**
 * 
 * @类名: TestRole.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月3日下午1:53:56
 * 修改记录：
 *
 * @see
 */
@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { "classpath:/testApplicationContext-common.xml" })

public class TestRole {
	@Autowired  
    private RoleService roleService;
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}
	
	//@Test
	@Transactional
	public void addRole(){
		Role role = new Role();
		role.setRoleName("总经理助理");
		role.setCreateTime(new Date());
		
		AjaxResult result = roleService.addRole(role);
		System.out.println("【"+result.isSuccess() + ";" + result.getMsg().toString()+"】");
	}
	
	//@Test
	@Transactional
	public void updateRole(){
		
		Role role = new Role();
		role.setRoleName("总经理助理");
		role.setCreateTime(new Date());

		
		AjaxResult result = roleService.addRole(role);
		System.out.println("【"+result.isSuccess() + ";" + result.getMsg().toString()+"】");
	}
	
	@Test
	@Transactional
	public void deleteRole(){
		
		Role role = new Role();
		role.setId((long)3);
		//role = roleService.f .findRole(role);
		
		AjaxResult result = roleService.deleteRole(role);
		System.out.println("【"+result.isSuccess() + ";" + result.getMsg().toString()+"】");
	}	
}
