package com.jsdz.digitalevidence.statistics.service;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.bean.param.AbnormalParam;
import com.jsdz.digitalevidence.statistics.model.SysAbnormalStatisticsModel;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
public interface ISysAbnormalStatisticsService extends IService<SysAbnormalStatisticsModel> {

    Page<SysAbnormalStatisticsModel> getDatas(AbnormalParam param, PerLvlBean permLvl);

    List<SysAbnormalStatisticsModel> UltraShortVideoStatistics();

    List<SysAbnormalStatisticsModel> UltraShortAlarmStatistics();

    List<SysAbnormalStatisticsModel> UltraShortCaseStatistics();

    List<SysAbnormalStatisticsModel> deviceIdleStatistics(int day);

    void updateDoc(Long id);

    void updateAlarm(Long id);

    void updateCase(Long id);

    List<SysAbnormalStatisticsModel> siteOnlineStatistics(float rate);

    List<SysAbnormalStatisticsModel> siteCapacityStatistics(float rate);

    /**
     * 修改采集站在线时长
     * @param siteNo 采集站编号
     * @param totalTime 累计在线时长
     */
    void updateSiteOnlineTime(String siteNo, Long totalTime);

    List<SysAbnormalStatisticsModel> videoQualityStatistics(float v);

    /**
     * 设置视频质量异常
     * @param t
     */
    void setVideoQualityAbnormal(SysAbnormalStatisticsModel t);
}
