package com.js.transfer.utils;

import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.WKTReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.net.*;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.Properties;

import org.springframework.util.StringUtils;


/**
 *
 * <AUTHOR>
 * @create 2024/01/16 23:22:41
 *
 */
public class Minor extends StringUtils {

    public final static long TEN_MINUTE =  60 * 1000 * 10;

    public static boolean isNoEmpty( Object str) {
        return !isEmpty(str);
    }

    public static boolean isBlank( Object str) {
        return !isEmpty(str)?isEmpty(str.toString().trim()):true;
    }

    public static boolean isNoBlank( Object str) {
        return !isBlank(str);
    }

    public static String toString(Object str) {return isNoEmpty(str)?str.toString():"";}

    /**
     * 判断操作系统是否是Windows
     *
     * @return
     */
    public static boolean isWindowsOS() {
        boolean isWindowsOS = false;
        String osName = System.getProperty("os.name");
        if (osName.toLowerCase().indexOf("windows") > -1) {
            isWindowsOS = true;
        }
        return isWindowsOS;
    }

    /**
     * 获取Linux下的IP地址
     *
     * @return IP地址
     * @throws SocketException
     */
    private static String getLinuxLocalIp() throws SocketException {
        String ip = "";
        try {
            for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements();) {
                NetworkInterface intf = en.nextElement();
                String name = intf.getName();
                if (!name.contains("docker") && !name.contains("lo")) {
                    for (Enumeration<InetAddress> enumIpAddr = intf.getInetAddresses(); enumIpAddr.hasMoreElements();) {
                        InetAddress inetAddress = enumIpAddr.nextElement();
                        if (!inetAddress.isLoopbackAddress()) {
                            String ipaddress = inetAddress.getHostAddress().toString();
                            if (!ipaddress.contains("::") && !ipaddress.contains("0:0:")
                                    && !ipaddress.contains("fe80")) {
                                ip = ipaddress;
                                break;
                            }
                        }
                    }
                }
            }
        } catch (SocketException ex) {
            System.out.println("获取ip地址异常");
            ip = "127.0.0.1";
            ex.printStackTrace();
        }
        return ip;
    }

    /**
     * 获取本地IP地址
     *
     * @throws SocketException
     */
    public static String getLocalIP(){
        if (isWindowsOS()) {
            try {
                return InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                e.printStackTrace();
            }
        } else {
            try {
                return getLinuxLocalIp();
            } catch (SocketException e) {
                e.printStackTrace();
            }
        }
        return "127.0.0.1";
    }

    public static String getRequestIp(HttpServletRequest request) throws UnknownHostException {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if (ip.equals("127.0.0.1")) {
                InetAddress inetAddress = InetAddress.getLocalHost();
                ip = inetAddress.getHostAddress();
            }
        }

        if (ip != null && ip.length() > 15 && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(","));
        }

        return "0:0:0:0:0:0:0:1".equals(ip) ? "127.0.0.1" : ip;
    }

    /**
     * 获取无线网IP
     * Created by ycq on 2019/04/19 09:46:30
     * @return
     */
    public static String getWlanIp() {
        String ip = null;
        try {
            Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
            NetworkInterface networkInterface;
            Enumeration<InetAddress> inetAddresses;
            InetAddress inetAddress;
            while (networkInterfaces.hasMoreElements()) {
                networkInterface = networkInterfaces.nextElement();
                String name = networkInterface.getName();
                String osname = "wlan1";
                if(OsisLinux()) osname = "eth0";
                //只获取无线网卡IP
                if(name.toLowerCase().equals(osname)){
                    inetAddresses = networkInterface.getInetAddresses();
                    while (inetAddresses.hasMoreElements()) {
                        inetAddress = inetAddresses.nextElement();
                        if (inetAddress != null && inetAddress instanceof Inet4Address) { // IPV4
                            ip = inetAddress.getHostAddress();
                        }
                    }
                }
            }
        } catch (SocketException e) {
            e.printStackTrace();
        }
        return ip;
    }

    /**
     * 判断操作系统是否是Linux
     * Created by ycq on 2019/04/19 09:46:35
     * @return
     */
    public static Boolean OsisLinux(){
        //判断操作系统
        Properties prop = System.getProperties();
        String property = prop.getProperty("os.name");
        //操作系统为Linux
        if(property!=null && property.toLowerCase().indexOf("linux")>-1){
            return true;
        }else{
            return false;
        }
    }

    public static Logger logger(Class<?> cla){
        return LoggerFactory.getLogger(cla.getClass());
    }

    public static Logger logger(){
        return LoggerFactory.getLogger("");
    }

    /**
     * 将日期对象转换为指定格式的字符串。
     * @param date 日期对象
     * @param pattern 日期格式模式
     * @return 格式化后的日期字符串
     */
    public static String dateToStr(Date date, String pattern) {
        DateFormat df = new SimpleDateFormat(pattern);
        return df.format(date);
    }

    public static String dateToStr(Date date) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.format(date);
    }

    /**
     * 将指定格式的字符串转换为日期对象。
     * @param str 日期字符串
     * @param pattern 日期格式模式
     * @return 转换后的日期对象
     * @throws ParseException 如果字符串无法转换为日期对象
     */
    public static Date strToDate(String str, String pattern) throws ParseException {
        DateFormat df = new SimpleDateFormat(pattern);
        return df.parse(str);
    }

    public static Date strToDate(String str) throws ParseException {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return df.parse(str);
    }
    public static Date getDate0() throws ParseException {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }
    public static String getDateStr0() throws ParseException {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
       return dateToStr(calendar.getTime());
    }

    public static Long onlyNumber(String str) {
        try {
            // 使用正则表达式过滤非数字字符
            String digitsOnly = str.replaceAll("\\D", "");
            return Long.valueOf(digitsOnly);
        }catch (Exception e){
            return null;
        }
    }


    /**
     * 点是否在面内
     * @param dot
     * @param dots
     * @return
     */
    public static boolean geometryContainsPoint(Point dot, Geometry dots){
        return dots.contains(dot);
    }

    public static boolean geometryContainsPoint(double[] dot,double[][] dots){
        return geometryContainsPoint(getPoint(dot),getGeometry(dots));
    }

    public static Point getPoint(double[] dot){
        return new GeometryFactory().createPoint(new Coordinate(dot[0], dot[1]));
    }

    public static  Geometry getGeometry(double[][] dots){
        try {
            StringBuilder dotStr= new StringBuilder();
            for (double[] dot : dots) {
                dotStr.append(dot[0]).append(" ").append(dot[1]).append(",");
            }
            if(dotStr.toString().contains(",")){
                dotStr = new StringBuilder(dotStr.substring(0, dotStr.length() - 1));
            }
            return  new WKTReader().read( "POLYGON((" + dotStr + "))");
        }catch (org.locationtech.jts.io.ParseException e){
            e.printStackTrace();
        }
        return null;
    }


}
