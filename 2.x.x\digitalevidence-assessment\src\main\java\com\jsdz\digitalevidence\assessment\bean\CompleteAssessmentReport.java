/**
 * 
 */
package com.jsdz.digitalevidence.assessment.bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.AbstractDTO;
import com.jsdz.utils.DateTimeUtils;

/**
 * @类名: CompleteAssessmentReport
 * @说明: 完整考核报告
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月25日 上午10:16:11
 * 修改记录：
 *
 * @see 	 
 */
public class CompleteAssessmentReport extends AbstractDTO {
	
	/********警员资料********/
	/** 考核预警Id*/
	private Long reportId;
	/** 警号*/
	private String policeCode;
	/** 姓名*/
	private String policeName;
	/** 性别*/
	private String sex;
	/** 年龄*/
	private Integer age;
	/** 局*/
	private String orgName;
	/** 部门*/
	private String deptName;
	/** 职位*/
	private String position;
    /** 报告生成日期*/
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime; 
    /** 考核周期*/
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date startDate;
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date endDate;
	/** 报告生成日期*/
	@JsonFormat(pattern="yyyy-MM-dd")
	private Date submitDate;
	/** 评语*/
	private String comments;
	
	/** 视频数*/
	private Integer countOfVedio;
	/** 视频时长，单位毫秒*/
	private Long lengthOfVedio;
	public String getLengthOfVedioStr() {
		return DateTimeUtils.durationToStr(lengthOfVedio);
	}
	/** 视频大小, 单位: G*/
	private Long sizeOfVedio;
	/** 音频数*/
	private Integer countOfAudio;
	/** 音频时长*/
	private Long lengthOfAudio;
	public String getLengthOfAudioStr() {
		return DateTimeUtils.durationToStr(lengthOfAudio);
	}
	/** 音频大小, 单位: G*/
	private Long sizeOfAudio;
	/** 图片数*/
	private Integer countOfPic;
	/** 图片大小，单位：byte*/
	private Long sizeOfPic;
	/** 日志数*/
	private Integer countOfLog;
	/** 日志大小*/
	private Long sizeOfLog;
	
	/** 文明规范数*/
	private Integer countOfWMGF;
	/** 不存在违法违纪数*/
	private Integer countOfNoWFWJ;
	/** 按要求拍摄数*/
	private Integer countOfAYQPS;
	/** 按要求运用语言数*/
	private Integer countOfYYYY;	
	
	/** 拍摄角度，准确数*/
	private Integer countOfShootingAngleA;
	/** 拍摄角度，一般数*/
	private Integer countOfShootingAngleG;
	/** 拍摄角度，错误数*/
	private Integer countOfShootingAngleE;
	
	/** 拍摄内容要素， 准确数*/
	private Integer countOfShootingContentA;
	/** 拍摄内容要素， 一般数*/
	private Integer countOfShootingContentG;
	/** 拍摄内容要素， 错误数*/
	private Integer countOfShootingContentE;
	
	/** 拍摄分辨率，高数*/
	private Integer countOfShootingResolutionH;
	/** 拍摄分辨率，一般数*/
	private Integer countOfShootingResolutionG;
	/** 拍摄分辨率，低数*/
	private Integer countOfShootingResolutionL;	
	
	/** 期间，考核资料数*/
	private Integer totalAssessingDoc;
	/** 期间，上传资料总数*/
	private Integer totalDoc;

	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public Integer getAge() {
		return age;
	}
	public void setAge(Integer age) {
		this.age = age;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getDeptName() {
		return deptName;
	}
	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}
	public String getPosition() {
		return position;
	}
	public void setPosition(String position) {
		this.position = position;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Date getSubmitDate() {
		return submitDate;
	}
	public void setSubmitDate(Date submitDate) {
		this.submitDate = submitDate;
	}
	public Long getReportId() {
		return reportId;
	}
	public void setReportId(Long reportId) {
		this.reportId = reportId;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public Integer getCountOfVedio() {
		return countOfVedio;
	}
	public void setCountOfVedio(Integer countOfVedio) {
		this.countOfVedio = countOfVedio;
	}
	public Long getLengthOfVedio() {
		return lengthOfVedio;
	}
	public void setLengthOfVedio(Long lengthOfVedio) {
		this.lengthOfVedio = lengthOfVedio;
	}
	public Long getSizeOfVedio() {
		return sizeOfVedio;
	}
	public void setSizeOfVedio(Long sizeOfVedio) {
		this.sizeOfVedio = sizeOfVedio;
	}
	public Integer getCountOfAudio() {
		return countOfAudio;
	}
	public void setCountOfAudio(Integer countOfAudio) {
		this.countOfAudio = countOfAudio;
	}
	public Long getLengthOfAudio() {
		return lengthOfAudio;
	}
	public void setLengthOfAudio(Long lengthOfAudio) {
		this.lengthOfAudio = lengthOfAudio;
	}
	public Long getSizeOfAudio() {
		return sizeOfAudio;
	}
	public void setSizeOfAudio(Long sizeOfAudio) {
		this.sizeOfAudio = sizeOfAudio;
	}
	public Integer getCountOfPic() {
		return countOfPic;
	}
	public void setCountOfPic(Integer countOfPic) {
		this.countOfPic = countOfPic;
	}
	public Long getSizeOfPic() {
		return sizeOfPic;
	}
	public void setSizeOfPic(Long sizeOfPic) {
		this.sizeOfPic = sizeOfPic;
	}
	public Integer getCountOfLog() {
		return countOfLog;
	}
	public void setCountOfLog(Integer countOfLog) {
		this.countOfLog = countOfLog;
	}
	public Long getSizeOfLog() {
		return sizeOfLog;
	}
	public void setSizeOfLog(Long sizeOfLog) {
		this.sizeOfLog = sizeOfLog;
	}
	public Integer getCountOfWMGF() {
		return countOfWMGF;
	}
	public void setCountOfWMGF(Integer countOfWMGF) {
		this.countOfWMGF = countOfWMGF;
	}
	public Integer getCountOfNoWFWJ() {
		return countOfNoWFWJ;
	}
	public void setCountOfNoWFWJ(Integer countOfNoWFWJ) {
		this.countOfNoWFWJ = countOfNoWFWJ;
	}
	public Integer getCountOfAYQPS() {
		return countOfAYQPS;
	}
	public void setCountOfAYQPS(Integer countOfAYQPS) {
		this.countOfAYQPS = countOfAYQPS;
	}
	public Integer getCountOfYYYY() {
		return countOfYYYY;
	}
	public void setCountOfYYYY(Integer countOfYYYY) {
		this.countOfYYYY = countOfYYYY;
	}
	public Integer getCountOfShootingAngleA() {
		return countOfShootingAngleA;
	}
	public void setCountOfShootingAngleA(Integer countOfShootingAngleA) {
		this.countOfShootingAngleA = countOfShootingAngleA;
	}
	public Integer getCountOfShootingAngleG() {
		return countOfShootingAngleG;
	}
	public void setCountOfShootingAngleG(Integer countOfShootingAngleG) {
		this.countOfShootingAngleG = countOfShootingAngleG;
	}
	public Integer getCountOfShootingAngleE() {
		return countOfShootingAngleE;
	}
	public void setCountOfShootingAngleE(Integer countOfShootingAngleE) {
		this.countOfShootingAngleE = countOfShootingAngleE;
	}
	public Integer getCountOfShootingContentA() {
		return countOfShootingContentA;
	}
	public void setCountOfShootingContentA(Integer countOfShootingContentA) {
		this.countOfShootingContentA = countOfShootingContentA;
	}
	public Integer getCountOfShootingContentG() {
		return countOfShootingContentG;
	}
	public void setCountOfShootingContentG(Integer countOfShootingContentG) {
		this.countOfShootingContentG = countOfShootingContentG;
	}
	public Integer getCountOfShootingContentE() {
		return countOfShootingContentE;
	}
	public void setCountOfShootingContentE(Integer countOfShootingContentE) {
		this.countOfShootingContentE = countOfShootingContentE;
	}
	public Integer getCountOfShootingResolutionH() {
		return countOfShootingResolutionH;
	}
	public void setCountOfShootingResolutionH(Integer countOfShootingResolutionH) {
		this.countOfShootingResolutionH = countOfShootingResolutionH;
	}
	public Integer getCountOfShootingResolutionG() {
		return countOfShootingResolutionG;
	}
	public void setCountOfShootingResolutionG(Integer countOfShootingResolutionG) {
		this.countOfShootingResolutionG = countOfShootingResolutionG;
	}
	public Integer getCountOfShootingResolutionL() {
		return countOfShootingResolutionL;
	}
	public void setCountOfShootingResolutionL(Integer countOfShootingResolutionL) {
		this.countOfShootingResolutionL = countOfShootingResolutionL;
	}
	public Integer getTotalAssessingDoc() {
		return totalAssessingDoc;
	}
	public void setTotalAssessingDoc(Integer totalAssessingDoc) {
		this.totalAssessingDoc = totalAssessingDoc;
	}
	public Integer getTotalDoc() {
		return totalDoc;
	}
	public void setTotalDoc(Integer totalDoc) {
		this.totalDoc = totalDoc;
	}
	
}
