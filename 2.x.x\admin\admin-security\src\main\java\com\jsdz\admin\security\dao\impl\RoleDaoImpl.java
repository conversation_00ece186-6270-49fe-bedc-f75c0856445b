package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: RoleDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-05 17:43:37
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;

import com.jsdz.admin.org.model.Department;
import com.jsdz.admin.security.dao.RoleDao;
import com.jsdz.admin.security.model.Role;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class RoleDaoImpl extends GenericEntityDaoHibernateImpl<Role,Long> implements RoleDao{

	//新增
	public void addRole(Role role) {
		this.saveOrUpdate(role);
	}

	//删除
	/* (non-Javadoc)
	 * @see com.jsdz.admin.security.dao.RoleDao#deleteRole(com.jsdz.admin.security.model.Role)
	 */
	public void deleteRole(Role role) {

		//删除角色权限表中相关记录
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();

/*		String deleteStr1 = "delete RoleAction ra where ra.role.id = :id";
		int count1 = session.createQuery(deleteStr1)
				.setString( "id", role.getId().toString())
				.executeUpdate();
*/	
		//删除角色权限表中相关记录
		String deleteStr2 = "delete RoleMenu m where m.role.id = :id";
		int count2 = session.createQuery(deleteStr2)
				.setString( "id", role.getId().toString())
				.executeUpdate();
		
		String deleteStr3 = "delete Role r where r.id = :id";
		int count3 = session.createQuery(deleteStr3)
				.setString( "id", role.getId().toString())
				.executeUpdate();
		session.close();
	}

	//修改
	public void updateRole(Role role) {
		this.saveOrUpdate(role);
	}
	
	//查询所有
	public List<Role> findAll(){
		return this.find("from Role r order by id");
	}
	//按id查询
	public Role locateRoleById(Long id){

		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery("from Role r where r.id=:id");
		query.setParameter("id", id);
		List<Role> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}
	
	//单个查询
	public Role findRoleByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		};
		List<Role> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//列表查询
	public List<Role> findRolesByCondition(String queryStr,String[] paramNames,Object[] values){
		List<Role> ops = this.findByNamedParam(queryStr,paramNames,values);
		return ops;
	}

	//分页查询
	public Page<Role> findRolesOnPage(Page<Role> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<Role>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
