package com.jsdz.digitalevidence.statistics.model;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_delete_approval")
public class SysDeleteApprovalModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @TableField("DOCID")
    private long docid;

    @TableField("REQUESTUSERDESCRIBE")
    private String requestuserdescribe;

    @TableField("REQUESTUSERID")
    private long requestuserid;

    @TableField("REQUESTUSERNAME")
    private String requestusername;

    @TableField("REQUESTORGID")
    private long requestorgid;

    @TableField("REQUESTORGNAME")
    private String requestorgname;

    @TableField("APPROVEDUSEID")
    private long approveduseid;

    @TableField("APPROVEDUSENAME")
    private String approvedusename;

    @TableField("APPROVEDORGID")
    private long approvedorgid;

    @TableField("APPROVEDORGNAME")
    private String approvedorgname;

    @TableField("REVIEWCOMMENTS")
    private String reviewcomments;

    @TableField("ISAGREE")
    private Integer isagree;

    @TableField("APPROVEDTIME")
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date approvedtime;

    @TableField("EXPIRETIME")
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date expiretime;

    @TableField("TASKSTATUS")
    private Integer taskstatus;

    @TableField("CREATETIME")
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createtime;

    @TableField("UPDATETIME")
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date updatetime;


}
