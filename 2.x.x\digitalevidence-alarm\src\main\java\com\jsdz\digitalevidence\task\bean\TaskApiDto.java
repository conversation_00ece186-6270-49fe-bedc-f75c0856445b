package com.jsdz.digitalevidence.task.bean;

import com.jsdz.core.Page;

public class TaskApiDto {

    private Long taskId;

    private String code;

    private String startTime;

    private String endTime;

    /**
     * 任务类别
     */
    private Integer cate;

    private Page page;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getCate() {
        return cate;
    }

    public void setCate(Integer cate) {
        this.cate = cate;
    }

    public Page getPage() {
        return page;
    }

    public void setPage(Page page) {
        this.page = page;
    }

    public TaskPageBean toPageBean() {
        TaskPageBean taskPageBean = new TaskPageBean();
        taskPageBean.setPage(this.getPage());
        return taskPageBean;
    }
}
