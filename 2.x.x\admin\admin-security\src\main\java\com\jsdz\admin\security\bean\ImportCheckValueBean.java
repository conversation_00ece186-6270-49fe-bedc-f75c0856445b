package com.jsdz.admin.security.bean;
/**
 * 
 * @类名: ImportCheckValueBean
 * @说明: 在导入的数据中，数据有效性检查
 * 1、某个字段数据是否重复导入
 * 2、某个字段数据在原数据中是否已存在，不能重复导入
 * 3、某个字段数据是无效数据(如外键)
 * 
 * 每次通过SQL查询一个字段
 *
 * @author: kenny
 * @Date	2017年11月6日上午9:53:46
 * 修改记录：
 *
 * @see
 */
public class ImportCheckValueBean {
	private Long id;
	private String importValue;  //导入的值
	private String originalValue;//已存在的值
	private Integer repeatCount; //重复次数
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getImportValue() {
		return importValue;
	}
	public void setImportValue(String importValue) {
		this.importValue = importValue;
	}
	public String getOriginalValue() {
		return originalValue;
	}
	public void setOriginalValue(String originalValue) {
		this.originalValue = originalValue;
	}
	public Integer getRepeatCount() {
		return repeatCount;
	}
	public void setRepeatCount(Integer repeatCount) {
		this.repeatCount = repeatCount;
	}
	
	
}
