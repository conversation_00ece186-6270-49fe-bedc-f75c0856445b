package com.jsdz.digitalevidence.attendance.bean.vomodel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AttendanceCheckBean implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 维度
     */
    private String longitude;

    /**
     * 经度
     */
    private String latitude;

    /**
     * 人员设备考勤配置表
     */
    private Integer attendanceInfoId;

    /**
     * 考勤时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date attendanceTime;

    /**
     * 打卡描述
     */
    private String attendanceDescribe;

    /**
     * 是否自动打卡（0自动，1手动）
     */
    private Integer isAutomatic;

    /**
     * 创建时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date updateTime;


    /**
     * 单位相关
     */
    private String orgName;
    private String orgPath;
    private String orgCode;
    private String orgId;


}
