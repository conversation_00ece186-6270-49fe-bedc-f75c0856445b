package com.jsdz.digitalevidence.alarm.common.dao.impl;

/**
 *
 * @类名: DocumentInfoDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-11-06 16:23:56
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.alarm.common.dao.DocumentInfoDao;
import com.jsdz.digitalevidence.document.model.DocumentInfo;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class DocumentInfoDaoImpl extends GenericEntityDaoHibernateImpl<DocumentInfo,Long> implements DocumentInfoDao{

	//新增
	public void addDocumentInfo(DocumentInfo documentInfo) {
		this.saveOrUpdate(documentInfo);
	}

	//删除
	public void deleteDocumentInfo(DocumentInfo documentInfo) {
		this.delete(documentInfo);
	}

	//修改
	public void updateDocumentInfo(DocumentInfo documentInfo) {
		this.merge(documentInfo);
	}

	//按id查询(游离状态)
	public DocumentInfo findDocumentInfoById(Long id){

		final String  hql = "from DocumentInfo d where d.id = :id";
		final Long oid = id;
		DocumentInfo data = getHibernateTemplate().execute(new HibernateCallback<DocumentInfo>() {
			public DocumentInfo doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DocumentInfo> list = query.list();
				DocumentInfo rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public DocumentInfo locateDocumentInfoById(Long id){

		final String  hql = "from DocumentInfo d where d.id = :id";
		final Long oid = id;
		DocumentInfo data = getHibernateTemplate().execute(new HibernateCallback<DocumentInfo>() {
			public DocumentInfo doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<DocumentInfo> list = query.list();
				DocumentInfo rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public DocumentInfo findDocumentInfoByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		DocumentInfo data = getHibernateTemplate().execute(new HibernateCallback<DocumentInfo>() {
		public DocumentInfo doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<DocumentInfo> list = query.list();
			DocumentInfo rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<DocumentInfo> findAllDocumentInfos(){
		return this.find("from DocumentInfo documentInfo ");
	}

	//列表查询
	public List<DocumentInfo> findDocumentInfosByCondition(String queryStr,String[] paramNames,Object[] values){
		List<DocumentInfo> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<DocumentInfo> findDocumentInfosOnPage(Page<DocumentInfo> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<DocumentInfo>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
