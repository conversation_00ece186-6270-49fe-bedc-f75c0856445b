package com.jsdz.digitalevidence.assessment.service;

/**
 * 
 * @类名: AssessmentPlanPoliceService
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-06-21 16:28:56
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanPolice;

public interface AssessmentPlanPoliceService {

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addAssessmentPlanPolice(AssessmentPlanPolice assessmentPlanPolice);

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updateAssessmentPlanPolice(AssessmentPlanPolice assessmentPlanPolice);

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deleteAssessmentPlanPolice(AssessmentPlanPolice assessmentPlanPolice);

	/** 
 	 * 按id查询,结果是游离状态的数据
	 */ 
 	public AssessmentPlanPolice findAssessmentPlanPoliceById(Long id);

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlanPolice locateAssessmentPlanPoliceById(Long id);

	/** 
 	 * 单个查询
	 */ 
 	public AssessmentPlanPolice findAssessmentPlanPoliceByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanPolice> findAllAssessmentPlanPolices();

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanPolice> findAssessmentPlanPolicesByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanPolice> findAssessmentPlanPolicesOnPage(Page<AssessmentPlanPolice> page, String queryStr,String[] paramNames,Object[] values);

}

