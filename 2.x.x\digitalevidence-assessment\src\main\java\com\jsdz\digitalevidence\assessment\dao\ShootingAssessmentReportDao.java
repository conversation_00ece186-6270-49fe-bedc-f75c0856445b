/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.dao;

import java.util.Date;

import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;
import com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReportBean;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;

/**
 * @类名: ShootingAssessmentReportDao
 * @说明: 拍摄考核报告Dao
 *
 * <AUTHOR>
 * @Date	 2017年8月7日 下午5:51:34
 * 修改记录：
 *
 * @see 	 
 */
public interface ShootingAssessmentReportDao extends GenericORMEntityDAO<ShootingAssessmentReport, Long> {
	
	/**
	 * @说明：报告是否已冻结
	 *
	 * <AUTHOR>
	 * @param reportId
	 * @return
	 * 
	 */
	public boolean isReportFrozen(Long reportId);
	
	/**
	 * @说明：查找警员拍摄考核报告
	 *
	 * <AUTHOR>
	 * @param page
	 * @param policeId
	 * @param startDate
	 * @param endDate
	 * @return
	 * 
	 */
	public CompleteAssessmentReportBean getShootingAssessmentReport(
			Long reportId);
	
	public ShootingAssessmentReport getShootingAssessmentReport(
			Long policeId,
			Date cycleDate);
	
	public Page<ShootingAssessmentReport> queryShootingAssessmentReport(
			Page<ShootingAssessmentReport> page,
			Long policeId,
			Date startDate, Date endDate);
	
}
