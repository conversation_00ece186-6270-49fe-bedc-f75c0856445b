package com.jsdz.admin.security.service;

/**
 * 
 * @类名: RoleActionService
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-03 17:59:01
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleAction;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface RoleActionService {

	//新增
	public AjaxResult addRoleAction(RoleAction roleAction);

	//修改
	public AjaxResult updateRoleAction(RoleAction roleAction);

	//删除
	public AjaxResult deleteRoleAction(RoleAction roleAction);

	//批量设置角色权限
	public AjaxResult updateBatchRoleAction(Long rid,Long[] aids,Operator createBy);
	
	//单个查询
	public RoleAction findRoleActionByParam(String queryStr,String[] paramNames,Object[] values);

	//列表查询
	public List<RoleAction> findRoleActionsByParam(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<RoleAction> findRoleActionsOnPage(Page<RoleAction> page, String queryStr,String[] paramNames,Object[] values);

	//按角色查所有动作
	public List<RoleAction> findRoleActionsByRole(Role role);
	


	
}

