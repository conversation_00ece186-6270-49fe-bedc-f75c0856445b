/**
 * 
 */
package com.jsdz.digitalevidence.assessment.model;

import com.jsdz.core.AbstractDTO;

/**
 * @类名: ShootingAssesssmentSummary
 * @说明: 拍摄考核统计
 *
 * <AUTHOR>
 * @Date	 2017年8月3日 下午5:46:40
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingAssessmentSummary extends AbstractDTO {

	/** 所属报告Id*/
	private Long id;
	/** 文明规范数*/
	private Integer countOfWMGF;
	/** 不存在违法违纪数*/
	private Integer countOfNoWFWJ;
	/** 按要求拍摄数*/
	private Integer countOfAYQPS;
	/** 按要求运用语言数*/
	private Integer countOfYYYY;	
	
	/** 拍摄角度，准确数*/
	private Integer countOfShootingAngleA;
	/** 拍摄角度，一般数*/
	private Integer countOfShootingAngleG;
	/** 拍摄角度，错误数*/
	private Integer countOfShootingAngleE;
	
	/** 拍摄内容要素， 准确数*/
	private Integer countOfShootingContentA;
	/** 拍摄内容要素， 一般数*/
	private Integer countOfShootingContentG;
	/** 拍摄内容要素， 错误数*/
	private Integer countOfShootingContentE;
	
	/** 拍摄分辨率，高数*/
	private Integer countOfShootingResolutionH;
	/** 拍摄分辨率，一般数*/
	private Integer countOfShootingResolutionG;
	/** 拍摄分辨率，低数*/
	private Integer countOfShootingResolutionL;	
	
	/** 期间，考核资料数*/
	private Integer totalAssessingDoc;
	/** 期间，上传资料总数*/
	private Integer totalDoc;
	
	/**
	 * @说明：考核率
	 *
	 * <AUTHOR>
	 * @return
	 * 
	 */
	public float getAssessmentRate() {
		return (totalDoc==null||totalDoc==0) ? 0 : (float)totalAssessingDoc/(float)totalDoc;
	}

	public Integer getCountOfAYQPS() {
		return countOfAYQPS;
	}
	public void setCountOfAYQPS(Integer countOfAYQPS) {
		this.countOfAYQPS = countOfAYQPS;
	}
	public Integer getCountOfYYYY() {
		return countOfYYYY;
	}
	public void setCountOfYYYY(Integer countOfYYYY) {
		this.countOfYYYY = countOfYYYY;
	}
	public Integer getCountOfShootingAngleA() {
		return countOfShootingAngleA;
	}
	public void setCountOfShootingAngleA(Integer countOfShootingAngleA) {
		this.countOfShootingAngleA = countOfShootingAngleA;
	}
	public Integer getCountOfShootingAngleG() {
		return countOfShootingAngleG;
	}
	public void setCountOfShootingAngleG(Integer countOfShootingAngleG) {
		this.countOfShootingAngleG = countOfShootingAngleG;
	}
	public Integer getCountOfShootingAngleE() {
		return countOfShootingAngleE;
	}
	public void setCountOfShootingAngleE(Integer countOfShootingAngleE) {
		this.countOfShootingAngleE = countOfShootingAngleE;
	}
	public Integer getCountOfShootingResolutionH() {
		return countOfShootingResolutionH;
	}
	public void setCountOfShootingResolutionH(Integer countOfShootingResolutionH) {
		this.countOfShootingResolutionH = countOfShootingResolutionH;
	}
	public Integer getCountOfShootingResolutionG() {
		return countOfShootingResolutionG;
	}
	public void setCountOfShootingResolutionG(Integer countOfShootingResolutionG) {
		this.countOfShootingResolutionG = countOfShootingResolutionG;
	}
	public Integer getCountOfShootingResolutionL() {
		return countOfShootingResolutionL;
	}
	public void setCountOfShootingResolutionL(Integer countOfShootingResolutionL) {
		this.countOfShootingResolutionL = countOfShootingResolutionL;
	}
	public Integer getTotalAssessingDoc() {
		return totalAssessingDoc;
	}
	public void setTotalAssessingDoc(Integer totalAssessingDoc) {
		this.totalAssessingDoc = totalAssessingDoc;
	}
	public Integer getTotalDoc() {
		return totalDoc;
	}
	public void setTotalDoc(Integer totalDoc) {
		this.totalDoc = totalDoc;
	}

	public Integer getCountOfNoWFWJ() {
		return countOfNoWFWJ;
	}

	public void setCountOfNoWFWJ(Integer countOfNoWFWJ) {
		this.countOfNoWFWJ = countOfNoWFWJ;
	}

	public Integer getCountOfShootingContentA() {
		return countOfShootingContentA;
	}

	public void setCountOfShootingContentA(Integer countOfShootingContentA) {
		this.countOfShootingContentA = countOfShootingContentA;
	}

	public Integer getCountOfShootingContentG() {
		return countOfShootingContentG;
	}

	public void setCountOfShootingContentG(Integer countOfShootingContentG) {
		this.countOfShootingContentG = countOfShootingContentG;
	}

	public Integer getCountOfShootingContentE() {
		return countOfShootingContentE;
	}

	public void setCountOfShootingContentE(Integer countOfShootingContentE) {
		this.countOfShootingContentE = countOfShootingContentE;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getCountOfWMGF() {
		return countOfWMGF;
	}

	public void setCountOfWMGF(Integer countOfWMGF) {
		this.countOfWMGF = countOfWMGF;
	}

}
