package com.jsdz.digitalevidence.alarm.alarm110.service.impl;

/**
 * 
 * @类名: AlarmFeedbackServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2018-08-28 11:11:14
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.context.transaction.TransactionConfiguration;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmFeedbackDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmFeedback;
import com.jsdz.digitalevidence.alarm.alarm110.service.AlarmFeedbackService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("AlarmFeedbackServiceImpl")
public class AlarmFeedbackServiceImpl implements AlarmFeedbackService {

	@Autowired
	private AlarmFeedbackDao alarmFeedbackDao;

	//新增
	public AjaxResult addAlarmFeedback(AlarmFeedback alarmFeedback) {
		AjaxResult result = new AjaxResult();
		alarmFeedbackDao.addAlarmFeedback(alarmFeedback);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateAlarmFeedback(AlarmFeedback alarmFeedback) {
		AjaxResult result = new AjaxResult();
		alarmFeedbackDao.updateAlarmFeedback(alarmFeedback);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteAlarmFeedback(AlarmFeedback alarmFeedback) {
		AjaxResult result = new AjaxResult();
		alarmFeedbackDao.deleteAlarmFeedback(alarmFeedback);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public AlarmFeedback findAlarmFeedbackById(Long id){

		return alarmFeedbackDao.findAlarmFeedbackById(id);
	}

	//按 id 查询
	public AlarmFeedback locateAlarmFeedbackById(Long id) {
		return alarmFeedbackDao.locateAlarmFeedbackById(id);
	}

	//单个查询
	public AlarmFeedback findAlarmFeedbackByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmFeedbackDao.findAlarmFeedbackByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<AlarmFeedback> findAllAlarmFeedbacks() {
		return alarmFeedbackDao.findAllAlarmFeedbacks();
	}

	//列表查询
	public List<AlarmFeedback> findAlarmFeedbacksByParam(String queryStr, String[] paramNames, Object[] values) {
		return alarmFeedbackDao.findAlarmFeedbacksByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<AlarmFeedback> findAlarmFeedbacksOnPage(Page<AlarmFeedback> page, String queryStr, String[] paramNames, Object[] values) {
		Page<AlarmFeedback> pos = alarmFeedbackDao.findAlarmFeedbacksOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
