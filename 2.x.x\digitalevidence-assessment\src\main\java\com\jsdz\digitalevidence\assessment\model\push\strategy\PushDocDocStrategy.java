package com.jsdz.digitalevidence.assessment.model.push.strategy;

import java.util.Date;
import java.util.List;

import com.jsdz.digitalevidence.document.model.Document;

/**
 * @类名: PushDocDocStrategy
 * @说明: 拍摄考核视频推送文档策略
 *        推送什么视频
 *
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 修改记录：
 *
 * @see
 */
public interface PushDocDocStrategy {
	
	public List<Document> findPushDoc(Long policeId, Date startDate, Date endDate, int topN);

}
