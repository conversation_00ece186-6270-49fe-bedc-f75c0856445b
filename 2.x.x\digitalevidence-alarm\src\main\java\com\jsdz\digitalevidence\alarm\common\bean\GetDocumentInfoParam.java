package com.jsdz.digitalevidence.alarm.common.bean;

/**
 * 
 * @类名: GetDocumentInfoParam
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年11月7日上午11:28:11
 * 修改记录：
 *
 * @see
 */
import java.util.HashMap;
import java.util.Map;
import com.jsdz.admin.security.bean.ParamGenerateHql;
import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.document.bean.DocumentInfoBean;

public class GetDocumentInfoParam extends ParamGenerateHql{
	private Page<DocumentInfoBean> page;
	private Map<String, Object> kvs = new HashMap<String, Object>();
	private Long docId;
	private String captureTimeBegin;
	private String captureTimeEnd;
	private String uploadTimeBegin;
	private String uploadTimeEnd;
	private String docName;
	private Long orgId;//
	private String path;//路径
	private String includeSub;//是否包括下级
	private String policeCode;//
	private String fileType; //文件分类
	private String alarmCode; //警情号
	private Integer typical;//典型视频 1 典型 0非典型
	private String typicalContext; //典型分类
	private String note;//描述
	private String address;//地点说明
	private String caseType;//事件类型
	private String caseLevel;//事件等级	
	private Integer approved; //是否已审核(1已审核，0未审核)
	private Integer approvedResult; //审核结果(1通过，0不通过)
	private String approveCause; //不通过原因	
	private String expiryTimeBegin;//保存期止
	private String expiryTimeEnd;//保存期止

	
	
	public GetDocumentInfoParam(){
		page = new Page<DocumentInfoBean>();
		page.setOffset(0);
		page.setPageSize(10);
	}
	
	public void generateParam(PerLvlBean p){
		//{22}
		//按id查询
		if (this.getDocId()!= null ){
			kvs.put("docId", this.getDocId());
			//id查询则所有条件忽略
			return;
		}

		
		if (p.getPermissionLevel() <= 1){ //只能查自己的数据
			//{4}
			kvs.put("policeId", p.getEmpId());
		}else if (p.getPermissionLevel() == 2){ //只能查本单位的数据
			//{5}
			kvs.put("orgId", p.getOrgId());
		}else if (p.getPermissionLevel() == 3){ //只能查本单位及下级单位的数据
			//{6}
			if (this.getOrgId() != null && !"".equals(this.getOrgId())){
				kvs.put("orgId", this.getOrgId());
				if (this.getPath()!= null && !"".equals(this.getPath())){
					kvs.put("orgPath", p.getOrgPath() + "%");
				}
			}
			else 
				kvs.put("orgPath", p.getOrgPath() + "%");
		};
		
		//{0}
		if (this.getCaptureTimeBegin()!= null && !"".equals(this.getCaptureTimeBegin())){
			kvs.put("captureTimeBegin", this.getCaptureTimeBegin());
		}
		//{1} 
		if (this.getCaptureTimeEnd()!= null && !"".equals(this.getCaptureTimeEnd())){
			kvs.put("captureTimeEnd", this.getCaptureTimeEnd());
		}
		//{2}
		if (this.getUploadTimeBegin()!= null && !"".equals(this.getUploadTimeBegin())){
			kvs.put("uploadTimeBegin", this.getUploadTimeBegin());
		}
		//{3}
		if (this.getUploadTimeEnd()!= null && !"".equals(this.getUploadTimeEnd())){
			kvs.put("uploadTimeEnd", this.getUploadTimeEnd());
		}
		//{7}
		if (this.getDocName()!= null && !"".equals(this.getDocName())){
			kvs.put("docName", "%" + this.getDocName() + "%");
		}
/*		//单位{8}
		if (this.getOrgId()!= null ){
			kvs.put("orgId1", this.getOrgId());
		}
		//单位{9}
		if (this.getPath()!= null && this.getOrgId()!= null){
			kvs.put("orgPath1", this.getPath() + "%");
		}
*/		
		//警员编号名称{8}
		if (this.getPoliceCode()!= null && !"".equals(this.getPoliceCode())){
			kvs.put("policeCode", "%" + this.getPoliceCode() + "%");
		}
		//执法类型{9}
		if (this.getFileType()!= null && !"".equals(this.getFileType())){
			kvs.put("fileType", "%" + this.getFileType() + "%");
		}
		//警情号{10}
		if (this.getAlarmCode()!= null && !"".equals(this.getAlarmCode())){
			kvs.put("alarmCode", "%" + this.getAlarmCode() + "%");
		}
		//典型视频 1 典型 0非典型{11}
		if (this.getTypical()!= null ){
			kvs.put("typical", this.getTypical());
		}
		//典型分类{12}
		if (this.getTypicalContext()!= null && !"".equals(this.getTypicalContext())){
			kvs.put("typicalContext", this.getTypicalContext());
		}
		//描述{13}
		if (this.getNote()!= null && !"".equals(this.getNote())){
			kvs.put("note", "%" + this.getNote() + "%");
		}
		//地点说明{14}
		if (this.getAddress()!= null && !"".equals(this.getAddress())){
			kvs.put("address", "%" + this.getAddress() + "%");
		}
		//事件类型{15}
		if (this.getCaseType()!= null && !"".equals(this.getCaseType())){
			kvs.put("caseType", "%" + this.getCaseType() + "%");
		}
		//事件等级{16}
		if (this.getCaseLevel()!= null && !"".equals(this.getCaseLevel())){
			kvs.put("caseLevel", this.getCaseLevel());
		}
		//保存截止时间{17}
		if (this.getExpiryTimeBegin()!= null && !"".equals(this.getExpiryTimeBegin())){
			kvs.put("expiryTimeBegin", this.getExpiryTimeBegin());
		}
		//{18}
		if (this.getExpiryTimeEnd()!= null && !"".equals(this.getExpiryTimeEnd())){
			kvs.put("expiryTimeEnd", this.getExpiryTimeEnd());
		}
		//{19}
		//是否已审核(1已审核，0未审核)
		if (this.getApproved()!= null ){
			kvs.put("approved", this.getApproved());
		}
		//{20}
		//审核结果(1通过，0不通过)
		if (this.getApprovedResult()!= null && this.getApproved()!= null ){
			kvs.put("approvedResult", this.getApprovedResult());
		}
		//{21}
		//审核不通过原因
		if (this.getApproveCause()!= null && !"".equals(this.getApproveCause())){
			kvs.put("approveCause", "%" + this.getApproveCause() + "%"); 
		}	
	}

	public Page<DocumentInfoBean> getPage() {
		return page;
	}

	public void setPage(Page<DocumentInfoBean> page) {
		this.page = page;
	}

	public Map<String, Object> getKvs() {
		return kvs;
	}

	public void setKvs(Map<String, Object> kvs) {
		this.kvs = kvs;
	}

	public String getCaptureTimeBegin() {
		return captureTimeBegin;
	}

	public void setCaptureTimeBegin(String captureTimeBegin) {
		this.captureTimeBegin = captureTimeBegin;
	}

	public String getCaptureTimeEnd() {
		return captureTimeEnd;
	}

	public void setCaptureTimeEnd(String captureTimeEnd) {
		this.captureTimeEnd = captureTimeEnd;
	}

	public String getDocName() {
		return docName;
	}

	public void setDocName(String docName) {
		this.docName = docName;
	}

	public String getUploadTimeBegin() {
		return uploadTimeBegin;
	}

	public void setUploadTimeBegin(String uploadTimeBegin) {
		this.uploadTimeBegin = uploadTimeBegin;
	}

	public String getUploadTimeEnd() {
		return uploadTimeEnd;
	}

	public void setUploadTimeEnd(String uploadTimeEnd) {
		this.uploadTimeEnd = uploadTimeEnd;
	}

	public String getFileType() {
		return fileType;
	}

	public void setFileType(String fileType) {
		this.fileType = fileType;
	}

	public String getAlarmCode() {
		return alarmCode;
	}

	public void setAlarmCode(String alarmCode) {
		this.alarmCode = alarmCode;
	}

	public Integer getTypical() {
		return typical;
	}

	public void setTypical(Integer typical) {
		this.typical = typical;
	}

	public String getTypicalContext() {
		return typicalContext;
	}

	public void setTypicalContext(String typicalContext) {
		this.typicalContext = typicalContext;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getCaseType() {
		return caseType;
	}

	public void setCaseType(String caseType) {
		this.caseType = caseType;
	}

	public String getCaseLevel() {
		return caseLevel;
	}

	public void setCaseLevel(String caseLevel) {
		this.caseLevel = caseLevel;
	}
	public Integer getApproved() {
		return approved;
	}

	public void setApproved(Integer approved) {
		this.approved = approved;
	}

	public Integer getApprovedResult() {
		return approvedResult;
	}

	public void setApprovedResult(Integer approvedResult) {
		this.approvedResult = approvedResult;
	}

	public String getApproveCause() {
		return approveCause;
	}

	public void setApproveCause(String approveCause) {
		this.approveCause = approveCause;
	}

	public String getExpiryTimeBegin() {
		return expiryTimeBegin;
	}

	public void setExpiryTimeBegin(String expiryTimeBegin) {
		this.expiryTimeBegin = expiryTimeBegin;
	}

	public String getExpiryTimeEnd() {
		return expiryTimeEnd;
	}

	public void setExpiryTimeEnd(String expiryTimeEnd) {
		this.expiryTimeEnd = expiryTimeEnd;
	}

	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}

	public String getPoliceCode() {
		return policeCode;
	}

	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public String getIncludeSub() {
		return includeSub;
	}

	public void setIncludeSub(String includeSub) {
		this.includeSub = includeSub;
	}

	public Long getDocId() {
		return docId;
	}

	public void setDocId(Long docId) {
		this.docId = docId;
	}


}
