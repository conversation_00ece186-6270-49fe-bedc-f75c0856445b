package com.jsdz.admin.org.dao.impl;
import java.util.List;

import org.springframework.stereotype.Repository;

/**
 * 
 * @类名: RegionTypeDaoImpl
 * @说明: 地区Dao实现类
 *
 * @author: kenny
 * @Date	2017年4月25日下午5:11:30
 * 修改记录：
 *
 * @see
 */
import com.jsdz.admin.org.dao.RegionTypeDao;
import com.jsdz.admin.org.model.RegionType;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
@Repository
public class RegionTypeDaoImpl extends GenericEntityDaoHibernateImpl<RegionType,Long> implements RegionTypeDao{
	
	public List<RegionType> findAll(){
		String hql = " from RegionType";
		return this.find(hql);
	}

	@Override
	public List<RegionType> All() {
		String hql = " from RegionType";
		return this.find(hql);
	}
}
