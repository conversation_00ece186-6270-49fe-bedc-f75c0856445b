package com.jsdz.digitalevidence.attendance.bean.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.attendance.bean.vomodel.AttendanceCheckBean;
import com.jsdz.digitalevidence.attendance.model.VAttendanceAllModel;
import com.jsdz.utils.DateTimeUtils;

import java.util.Date;

public class AttendanceAllParam {

    Page<VAttendanceAllModel> page;

    private String orgPath;
    //是否包含下级 （0不，1包含）
    private String includeSub;
    private String sortName;

    private String sortOrder;

    private String attendancename;
    private String username;
    private String devicecode;

    private String workNumber;


    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTimeStart;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTimeEnd;

    @JsonFormat(pattern= "yyyy-MM",timezone="GMT+8")
    private Date month;

    public Date getCreateTimeStart() {
        return createTimeStart;
    }

    public void setCreateTimeStart(Date createTimeStart) {
        this.createTimeStart = createTimeStart;
    }

    public Date getCreateTimeEnd() {
        return createTimeEnd;
    }

    public void setCreateTimeEnd(Date createTimeEnd) {
        this.createTimeEnd = createTimeEnd;
    }

    public String getAttendancename() {
        return attendancename;
    }

    public void setAttendancename(String attendancename) {
        this.attendancename = attendancename;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getDevicecode() {
        return devicecode;
    }

    public void setDevicecode(String devicecode) {
        this.devicecode = devicecode;
    }

    public AttendanceAllParam(){
        page = new Page<VAttendanceAllModel>();
        page.setOffset(0);
        page.setPageSize(20);
    }

    public Page<VAttendanceAllModel> getPage() {
        return page;
    }

    public void setPage(Page<VAttendanceAllModel> page) {
        this.page = page;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public String getSortName() {
        return sortName;
    }

    public void setSortName(String sortName) {
        this.sortName = sortName;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getIncludeSub() {
        return includeSub;
    }

    public void setIncludeSub(String includeSub) {
        this.includeSub = includeSub;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public Date getMonth() {
        return month;
    }

    public void setMonth(Date month) {
        this.month = month;
    }
}
