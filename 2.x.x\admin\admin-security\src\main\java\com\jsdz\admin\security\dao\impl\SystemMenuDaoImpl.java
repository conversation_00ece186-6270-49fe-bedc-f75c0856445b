package com.jsdz.admin.security.dao.impl;

/**
 *
 * @类名: SystemMenuDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-09-08 11:59:54
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;

import com.jsdz.admin.security.bean.MenuTreeBean;
import com.jsdz.admin.security.bean.SystemMenuBean;
import com.jsdz.admin.security.dao.SystemMenuDao;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.SystemMenu;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;


@Repository
public class SystemMenuDaoImpl extends GenericEntityDaoHibernateImpl<SystemMenu,Long> implements SystemMenuDao{

	//新增
	public void addSystemMenu(SystemMenu systemMenu) {
		this.saveOrUpdate(systemMenu);
	}

	//删除
	public void deleteSystemMenu(SystemMenu systemMenu) {
		this.delete(systemMenu);
	}

	//修改
	public void updateSystemMenu(SystemMenu systemMenu) {
		this.merge(systemMenu);
	}

	//按id查询(游离状态)
	public SystemMenu findSystemMenuById(Long id){

		final String  hql = "from SystemMenu s left join fetch s.parentMenu p where s.id = :id";
		final Long oid = id;
		SystemMenu data = getHibernateTemplate().execute(new HibernateCallback<SystemMenu>() {
			public SystemMenu doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<SystemMenu> list = query.list();
				SystemMenu rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public SystemMenu locateSystemMenuById(Long id){

		final String  hql = "from SystemMenu s where s.id = :id";
		final Long oid = id;
		SystemMenu data = getHibernateTemplate().execute(new HibernateCallback<SystemMenu>() {
			public SystemMenu doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<SystemMenu> list = query.list();
				SystemMenu rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public SystemMenu findSystemMenuByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		SystemMenu data = getHibernateTemplate().execute(new HibernateCallback<SystemMenu>() {
		public SystemMenu doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<SystemMenu> list = query.list();
			SystemMenu rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<SystemMenu> findAllSystemMenus(){
		return this.find("from SystemMenu systemMenu ");
	}

	//列表查询
	public List<SystemMenu> findSystemMenusByCondition(String queryStr,String[] paramNames,Object[] values){
		List<SystemMenu> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<SystemMenu> findSystemMenusOnPage(Page<SystemMenu> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<SystemMenu>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	public List<SystemMenuBean> QueryNamedAllDataSQL(final String sqlStr, final String[] paramNames, final Object[] values) {

		List<SystemMenuBean> data = getHibernateTemplate().execute(new HibernateCallback<List<SystemMenuBean>>() {
			@SuppressWarnings("unchecked")
			public List<SystemMenuBean> doInHibernate(Session session) throws HibernateException {
				Query queryObject = session.createSQLQuery(sqlStr);
				// 设置实体转换
				SQLQuery sqlQuery = (SQLQuery) queryObject;
				//sqlQuery.addEntity(SystemMenuBean.getItemClass());
				sqlQuery.addEntity(SystemMenuBean.class);
				if (values != null) {
					for (int i = 0; i < values.length; i++) {
						applyNamedParameterToQuery(queryObject, paramNames[i], values[i]);
					}
				}
				return queryObject.list();
			}
		});
		return data;
	}
	
	public List<Long> findAllParentId() {
		//所有父菜单的ID 
		final String  hqlStr = "select s.parentMenu.id from SystemMenu s group by s.parentMenu.id";
		List<Long> data = getHibernateTemplate().execute(new HibernateCallback<List<Long>>() {      	 
			//@Override  
    	    public List<Long> doInHibernate(Session session)throws HibernateException, SQLException {  
    	    	// 获取查询对象
				Query queryObject = session.createQuery(hqlStr);
				//queryObject.executeUpdate();
				return queryObject.list();
    	    }  
    	});
		return data;
	}
}
