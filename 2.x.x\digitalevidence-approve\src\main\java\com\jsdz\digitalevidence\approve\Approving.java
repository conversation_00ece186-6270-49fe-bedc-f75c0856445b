/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @类名: Approving
 * @说明: 审批业务标注
 *
 * <AUTHOR>
 * @Date	 2017年4月17日 上午10:35:18
 * 修改记录：
 *
 * @see 	 
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Approving {

	// 业务编码
	public String code();
	// 业务描述
	public String desc();
	
}
