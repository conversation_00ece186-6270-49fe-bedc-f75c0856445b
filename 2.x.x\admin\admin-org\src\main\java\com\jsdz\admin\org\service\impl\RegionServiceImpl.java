package com.jsdz.admin.org.service.impl;

import java.util.ArrayList;
import java.util.Date;

/**
 * 
 * @类名: RegionServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-08 17:08:54
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jsdz.admin.org.bean.OrganizationBean;
import com.jsdz.admin.org.bean.RegionBean;
import com.jsdz.admin.org.dao.OrganizationDao;
import com.jsdz.admin.org.dao.RegionDao;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.model.Region;
import com.jsdz.admin.org.service.RegionService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.core.TreeBean;
import com.jsdz.reportquery.ReportQueryDao;

@Service("RegionServiceImpl")
public class RegionServiceImpl implements RegionService {

	@Autowired
	private RegionDao regionDao;
	@Autowired
	private OrganizationDao organizationDao;
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<RegionBean> reportQueryDao;	

	//新增
	public AjaxResult addRegion(Region region) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (region == null){
			result.setMsg("没有数据可保存");
			return result;
		}
		
		if (region.getRegionName() == null || "".equals(region.getRegionName())){
			result.setMsg("区域名称必须输入");
			return result;
		}
		
		if (null != regionDao.findRegionByCondition("from Region r where r.regionName=:regionName",
				  new String[]{"regionName"},new Object[]{region.getRegionName()})){
			result.setSuccess(false);
			result.setMsg("此区域名称已存在");
			return result;
		}
		
		if (!checkEndlessLoop(region.getParentRegion(),region)){
			result.setSuccess(false);
			result.setMsg("上级区域有冲突");
			return result;
		}
		
		region.setCreateTime(new Date());
		regionDao.addRegion(region);
		result.setSuccess(true);
		result.setData(region);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateRegion(Region region) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == region){
			result.setMsg("没有数据可修改");
			return result;
		}
		
		Region region1 = regionDao.locateRegionById(region.getId());
		if (null == region1){
			result.setMsg("不存在的区域资料");
			return result;
		}
		
		if (null == region.getRegionName() || "".equals(region.getRegionName())){
			result.setMsg("区域名称必须输入");
			result.setSuccess(false);
			return result ;
		}
		
		if (null != regionDao.findRegionByCondition(
					"from Region r where r.regionName=:regionName and r.id != :id",
				  new String[]{"regionName","id"},new Object[]{region.getRegionName(),region.getId()})){
			result.setSuccess(false);
			result.setMsg("区域名称重复");
			return result;
		}
		
		if (!checkEndlessLoop(region.getParentRegion(),region)){
			result.setSuccess(false);
			result.setMsg("上级区域有冲突");
			return result;
		}		
		
		region1.setRegionName(region.getRegionName());
		region1.setParentRegion(region.getParentRegion());
		regionDao.updateRegion(region1);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 	
	}

	//删除
	public AjaxResult deleteRegion(Region region) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (null == region){
			result.setMsg("没有数据可删除");
			return result;
		}
		
		if (null == regionDao.locateRegionById(region.getId())){
			result.setMsg("不存在的区域资料");
			return result;
		}

		if (null != regionDao.findRegionByCondition( 
				"from Region r where r.parentRegion.id=:id",
			  new String[]{"id"},new Object[]{region.getId()})){
			result.setMsg("区域有下级，不能删除");
			return result;
		}
		
		if (null != organizationDao.findOrganizationByCondition( 
				"from Organization o where o.region.id=:id",
			  new String[]{"id"},new Object[]{region.getId()})){
			result.setMsg("区域已在单位中使用，不能删除");
			return result;
		}
		
		
		regionDao.deleteRegion(region);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 	
	}

	//按 id 查询
	public Region locateRegionById(Long id) {
		return regionDao.locateRegionById(id);
	}

	//单个查询
	public Region findRegionByParam(String queryStr, String[] paramNames, Object[] values) {
		return regionDao.findRegionByCondition(queryStr,paramNames,values);
	}

	//列表查询
	public List<Region> findRegionsByParam(String queryStr, String[] paramNames, Object[] values) {
		return regionDao.findRegionsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<Region> findRegionsOnPage(Page<Region> page, String queryStr, String[] paramNames, Object[] values) {
		Page<Region> pos = regionDao.findRegionsOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
	//查询所有的区域并生成tree
	public List<TreeBean> findRegionOnTree(){
		List<Region> list =  regionDao.findRegionsByCondition(
				"from Region r where r.parentRegion is null ",null,null);

		List<TreeBean> resultList = null ;
		if (list != null){
			resultList = new ArrayList<TreeBean>();
			for (Region r : list){
				TreeBean tb = new TreeBean();
				tb.setId(r.getId());
				tb.setText(r.getRegionName());
				resultList.add(tb);
				findSubRegion(r,tb);
			}
		}
		return resultList;
	}
	
	//递归查询子单位
	private void findSubRegion(Region r,TreeBean parentTb){
		List<Region> list =  regionDao.findRegionsByCondition(
				"from Region r where r.parentRegion.id = :id ",
				new String[]{"id"},
				new Object[]{r.getId()});
		
		if (list != null && list.size() > 0){
			List<TreeBean> nodes = new ArrayList<TreeBean>();
			parentTb.setNodes(nodes);
			for (Region  region: list){
				TreeBean tb = new TreeBean();
				tb.setText(region.getRegionName());
				tb.setId(region.getId());
				nodes.add(tb);
				
				//递归查询下级
				findSubRegion(region,tb);
			}
		}
	}	
	
	//检查下级关系冲突,返回true为正确,没有冲突
	public boolean checkEndlessLoop(Region parentRegion, Region curRegion ){
		boolean result = true;
		if (parentRegion == null){
			return result;
		}
		
		if (parentRegion.getId() == curRegion.getId()){
			return false;
		}
		
		List<Region> list = regionDao.findRegionsByCondition(
				"from Region r where r.parentRegion.id = :id",
				new String[]{"id"},new Object[]{curRegion.getId()});
		if (list == null){
			return result;
		}
		
		for (int i=0;i<list.size();i++){
			if (list.get(i).getId() == parentRegion.getId()){
				result = false;
				break;
			}
			
			//递归查找
			result = checkEndlessLoop(parentRegion,list.get(i));
			if (result == false){
				return result;
			}
		}
		return result;
	}	

	@Override
	//分页查询数据给前端easyUi datagrid使用
	public Page<RegionBean> findReginOnPageForSy(Page<RegionBean> page, String[] paramNames, Object[] values){	
		page = reportQueryDao.pageQueryNamedSQL(page, "findReginOnPageForSy", paramNames, values);

		return page;
	}
}
