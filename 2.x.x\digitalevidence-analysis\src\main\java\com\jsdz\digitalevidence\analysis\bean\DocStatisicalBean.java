package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: DocStatisicalBean
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年6月13日下午6:55:44
 * 修改记录：
 *
 * @see
 */
public class DocStatisicalBean {

	private String code;//编号
    private String name;//名称
    private double vedioCount;//视频
    private double audioCount;//音频
    private double picCount;//照片
    private double logCount;//日志
    private double importanceCount;//重要
    private double generalCount;//一般
    

    
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public double getVedioCount() {
		return vedioCount;
	}
	public void setVedioCount(double vedioCount) {
		this.vedioCount = vedioCount;
	}
	public double getAudioCount() {
		return audioCount;
	}
	public void setAudioCount(double audioCount) {
		this.audioCount = audioCount;
	}
	public double getPicCount() {
		return picCount;
	}
	public void setPicCount(double picCount) {
		this.picCount = picCount;
	}
	public double getLogCount() {
		return logCount;
	}
	public void setLogCount(double logCount) {
		this.logCount = logCount;
	}
	public double getImportanceCount() {
		return importanceCount;
	}
	public void setImportanceCount(double importanceCount) {
		this.importanceCount = importanceCount;
	}
	public double getGeneralCount() {
		return generalCount;
	}
	public void setGeneralCount(double generalCount) {
		this.generalCount = generalCount;
	}


	
    
}
