package com.jsdz.admin.security.service.impl;

import java.util.Date;

/**
 * 
 * @类名: RoleServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-05-05 17:43:37
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jsdz.admin.security.dao.OperatorRoleDao;
import com.jsdz.admin.security.dao.RoleDao;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.service.RoleService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.cache.encrypt.MD5;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("RoleServiceImpl")
public class RoleServiceImpl implements RoleService {

	@Autowired
	private RoleDao roleDao;
	@Autowired
	private OperatorRoleDao operatorRoleDao;
	
	

	//新增
	public AjaxResult addRole(Role role) {
		AjaxResult result = new AjaxResult();
		if (role==null){
			result.setSuccess(false);
			result.setMsg("没有数据可保存。");
			return result;
		}
		
		if (null==role.getRoleName() || "".equals(role.getRoleName())){
			result.setSuccess(false);
			result.setMsg("角色名称必须提供。");
			return result;
		}
		
		List<Role> ros = roleDao.findRolesByCondition("from Role r where r.roleName=:roleName",
							new String[]{"roleName"},new Object[]{role.getRoleName()});
		if (null != ros && ros.size() > 0)
		{
			result.setSuccess(false);
			result.setMsg("角色已存在，新增失败。");
			return result;
		}
		
		if (null==role.getRoleLevel()){
			result.setMsg("角色级别必须提供。");
			return result;
		}

		role.setCreateTime(new Date());
		roleDao.addRole(role);
		
		result.setSuccess(true);
		result.setMsg("新增成功");
		return result;
	}

	//修改
	public AjaxResult updateRole(Role role) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		
		if (role==null){
			result.setMsg("没有数据可保存。");
			return result;
		}

		Role role1 = roleDao.locateRoleById(role.getId());
		if (role1 ==null){
			result.setMsg("不存在的角色资料。");
			return result;
		}
		
		if (null==role.getRoleName() || "".equals(role.getRoleName())){
			result.setMsg("角色名称必须提供。");
			return result;
		}
		
		List<Role> ros = roleDao.findRolesByCondition("from Role r where r.roleName=:roleName and r.id != :id",
							new String[]{"roleName","id"},new Object[]{role.getRoleName(),role.getId()});
		if (null != ros && ros.size() > 0)
		{
			result.setMsg("角色名称重复，修改失败。");
			return result;
		}
		
		if (null==role.getRoleLevel()){
			result.setMsg("角色级别必须提供。");
			return result;
		}
		
		role1.setRoleName(role.getRoleName());
		role1.setDescription(role.getDescription());
		role1.setRoleLevel(role.getRoleLevel());
		if(role.getOrgLevel() != null) {
			role1.setOrgLevel(role.getOrgLevel());
		}
		roleDao.updateRole(role1);
		result.setSuccess(true);
		result.setMsg("修改成功");
		return result;	
	}

	//删除
	@Transactional
	public AjaxResult deleteRole(Role role) {
		AjaxResult result = new AjaxResult();
		result.setSuccess(false);
		if (role==null){
			result.setMsg("没有数据可删除。");
			return result;
		}
		
		if (roleDao.locateRoleById(role.getId())==null){
			result.setMsg("不存在的角色资料。");
			return result;
		}
		
		List<OperatorRole> ors = operatorRoleDao.findOperatorRolesByCondition(
				"from OperatorRole operatorRole "
				+ " where operatorRole.role.id=:id", 
				new String[]{"id"},new Object[]{role.getId()});
		if (null != ors && ors.size() > 0){
			result.setMsg("此角色已有用户分配，不能删除。");
			return result;
		}
		
		
		//先删除相对应的角色权限表
		roleDao.deleteRole(role);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result;
	}
	
	//查所有
	public List<Role> findAll(){
		return roleDao.findAll();
	}
	
	
	//按id查询
	public Role locateRoleById(Long id){
		return roleDao.locateRoleById(id);
	}
	

	//单个查询
	public Role findRoleByParam(String queryStr, String[] paramNames, Object[] values) {
		return roleDao.findRoleByCondition(queryStr,paramNames,values);
	}

	//列表查询
	public List<Role> findRolesByParam(String queryStr, String[] paramNames, Object[] values) {
		return roleDao.findRolesByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<Role> findRolesOnPage(Page<Role> page, String queryStr, String[] paramNames, Object[] values) {
		Page<Role> pos = roleDao.findRolesOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
