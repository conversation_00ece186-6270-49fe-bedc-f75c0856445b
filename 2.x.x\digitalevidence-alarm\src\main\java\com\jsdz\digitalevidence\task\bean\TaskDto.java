package com.jsdz.digitalevidence.task.bean;

import com.jsdz.digitalevidence.task.model.TTask;
import org.springframework.beans.BeanUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;

public class TaskDto {

    private String title;
    //类别
    private Integer cate;
    //内容
    private String content;

    //等级 是否布控按钮
    private int level ;

    //地点
    private String address;

    //任务开始时间
    private String startTime;

    //任务结束时间
    private String endTime;

    //任务延期时间
    private String delayTime;

    //经度
    private String longitude;

    //纬度
    private String latitude;

    private String libIdNum;

    private String libPersonName;

    private String libLabel;

    private String libPersonSex;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getCate() {
        return cate;
    }

    public void setCate(Integer cate) {
        this.cate = cate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(String delayTime) {
        this.delayTime = delayTime;
    }

    public TTask toTask() {
        TTask task = new TTask();
        BeanUtils.copyProperties(this, task);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if(this.getStartTime() != null) {
            try {
                task.setStartTime(dateFormat.parse(this.getStartTime()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        if(this.getEndTime() != null) {
            try {
                task.setEndTime(dateFormat.parse(this.getEndTime()));
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return task;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLibIdNum() {
        return libIdNum;
    }

    public void setLibIdNum(String libIdNum) {
        this.libIdNum = libIdNum;
    }

    public String getLibPersonName() {
        return libPersonName;
    }

    public void setLibPersonName(String libPersonName) {
        this.libPersonName = libPersonName;
    }

    public String getLibLabel() {
        return libLabel;
    }

    public void setLibLabel(String libLabel) {
        this.libLabel = libLabel;
    }

    public String getLibPersonSex() {
        return libPersonSex;
    }

    public void setLibPersonSex(String libPersonSex) {
        this.libPersonSex = libPersonSex;
    }
}
