package com.jsdz.digitalevidence.activemq.utils;

/**
 * 
 * @类名: MqConnectionPool
 * @说明: ActiveMq连接池
 *
 * @author: kenny
 * @Date	2018年3月3日下午2:46:41
 * 修改记录：
 *
 * @see
 */
import java.util.concurrent.ExecutorService;
import javax.jms.JMSException;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.activemq.pool.PooledConnection;
import org.apache.activemq.pool.PooledConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MqConnectionPool {
	 private static Logger logger = LoggerFactory.getLogger(MqConnectionPool.class);

	 //强制使用同步返回数据的格式  
	 private static boolean useAsyncSendForJMS = true;   
	 //线程池数量  
	 private static int threadPoolSize=50;
	 //连接的最大连接数  
	 private static int maxConnections = 10; 
	 //每个连接中使用的最大活动会话数
	 private static int maximumActiveSessionPerConnection=300;
     //是否持久化消息  
	 private boolean isPersistent=true;
	 
	 private static MqConnectionPool instance;
	 private static PooledConnection conn;  
	 //url
	 private static String brokerurl;
	 private static ExecutorService threadPool;
	 
	 
	private MqConnectionPool ()
	{
		MqConnectionPool.init();
	}

	public static synchronized MqConnectionPool getInstance(){
		if (instance == null) {  
			instance = new MqConnectionPool();
		}  
		return instance;  
	}
		
	public static Boolean init() {
        //设置JAVA线程池  
        //threadPool = Executors.newFixedThreadPool(threadPoolSize); 
        
		String url = "failover:("+brokerurl+")?initialReconnectDelay=1000&timeout=3000&startupMaxReconnectAttempts=2";  
	    ActiveMQConnectionFactory factory = new ActiveMQConnectionFactory(url); 
	    //ActiveMQConnectionFactory actualConnectionFactory = new ActiveMQConnectionFactory(userName, password, brokerUrl)
	    factory.setUseAsyncSend(useAsyncSendForJMS);
	    try {  
	    	PooledConnectionFactory poolFactory = new PooledConnectionFactory(factory);
	    	poolFactory.setCreateConnectionOnStartup(true);
	    	poolFactory.setMaxConnections(maxConnections);
	    	poolFactory.setMaximumActiveSessionPerConnection(maximumActiveSessionPerConnection);
	    	 
	    	conn = (PooledConnection) poolFactory.createConnection();  
	    	conn.start();  
	    } catch (JMSException e) {  
	    	logger.error("*** 错误：" + e.getMessage()); 
	    	return false;
	    }  
	    return true;
	} 
	
    public static void destroy(){  
    	try {  
    		if(conn != null) {  
	           conn.close();  
    		}  
	     } catch (JMSException e) {
	    	 logger.error("*** 错误：" + e.getMessage());  
	            e.printStackTrace();  
	     }  
    }  
	      
    public static PooledConnection getConn() {  
    	return conn;  
    }  
	      
    public static void setConn(PooledConnection poolconn) {  
    	MqConnectionPool.conn = poolconn;  
    } 
    
	public static String getBrokerurl() {
		return brokerurl;
	}
	public static void setBrokerurl(String brokerurl) {
		MqConnectionPool.brokerurl = brokerurl;
	}
}
