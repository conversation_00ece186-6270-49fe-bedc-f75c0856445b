package com.jsdz.admin.security.utils;
/**
 * 
 * @类名: PermissionInterceptor
 * @说明: 拦截方法判断是否有权限控制
 *
 * @author: kenny
 * @Date	2017年4月27日下午8:12:51
 * 修改记录：
 *
 * @see
 */
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.AjaxResult;
import com.jsdz.digitalevidence.cache.utils.Constant;

public class PermissionInterceptor extends HandlerInterceptorAdapter{
	final Logger logger = LoggerFactory.getLogger(getClass());

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
		HandlerMethod handler2=(HandlerMethod) handler;
		Permission permission = handler2.getMethodAnnotation(Permission.class);
	
		if(null == permission){
			//没有声明权限自动放行
			return true;
		}
		
		logger.debug("*** permission", permission.toString());
		
		HttpSession session = request.getSession();
		Operator operator = (Operator)session.getAttribute(Constant.VAR_LOGIN_OPERATOR);//"_loginOperator");
		
		//判断该用户是否有权限
		if (PermissionHelper.checkPermission(Permission.tag, operator.getRightContent())){
			return true;
		}

		//没有条件的跳到没有权限提示页面
		if (permission.resultType() == ResultType.page){
			//response.sendRedirect("permission.jsp");
	        request.getRequestDispatcher("/permission.jsp").forward(request, response);  
		}
		else if (permission.resultType() == ResultType.json){
			AjaxResult result = new AjaxResult();
			result.setSuccess(false);
			result.setMsg("没有权限");
	       	response.setCharacterEncoding("utf-8");
        	response.setHeader("Content-type", "text/html;charset=UTF-8"); 
        	StringBuilder sb = new StringBuilder();
        	sb.append("{");
        	sb.append("\"code\":200,");
        	sb.append("isSuccess\":false,");
        	sb.append("\"msg\":\"没有权限\",");
        	sb.append("\"data\":null");
        	sb.append("}");
        	response.getWriter().write(sb.toString());
		}
		return false;
	}
}
