package com.jsdz.admin.security.utils;
/**
 * 
 * @类名: CustomJsonConfig
 * @说明: 自定认JsonConfig
 *
 * @author: kenny
 * @Date	2018年2月2日下午10:24:34
 * 修改记录：
 *
 * @see
 */

import java.util.Date;

import net.sf.json.JsonConfig;
import net.sf.json.util.CycleDetectionStrategy;
import net.sf.json.util.PropertyFilter;

public class CustomJsonConfig {
	
	private static CustomJsonConfig instance;
	private JsonConfig jsonConfig = null;
	String[] excludes = null;
	
	private CustomJsonConfig (){
		jsonConfig = new JsonConfig();
		//防止自包含
		jsonConfig.setCycleDetectionStrategy(CycleDetectionStrategy.LENIENT);
		//Date类型转换
		jsonConfig.registerJsonValueProcessor(Date.class, new JsonDateValueProcessor());
		//过滤字段
		//jsonConfig.setExcludes(EXCLUDES);  
		
		//空值不转换
		jsonConfig.setJsonPropertyFilter(new PropertyFilter() {
			public boolean apply(Object source, String name, Object value) {
			return value == null;//value为null时返回true，返回true的就是需要过滤调的
			}
		});

	}
	
	public static synchronized CustomJsonConfig getInstance() {  
		if (instance == null) {  
			instance = new CustomJsonConfig();  
		}  
		return instance;  
	}


	public void setExcludes(String[] excludes) {
		this.excludes = excludes;
		if (excludes != null || excludes.length > 0)
			jsonConfig.setExcludes(this.excludes);
	}

	public JsonConfig getJsonConfig() {
		return jsonConfig;
	}
}
