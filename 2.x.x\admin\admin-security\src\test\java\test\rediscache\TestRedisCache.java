package test.rediscache;
/**
 * 
 * @类名: TestRedisCache
 * @说明: 测试TestRedisCache
 *
 * @author: kenny
 * @Date	2018年3月29日下午6:32:25
 * 修改记录：
 *
 * @see
 */

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;

import com.jsdz.digitalevidence.cache.redis.RedisCache;

@RunWith(SpringJUnit4ClassRunner.class)
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = true)
@ContextConfiguration(
		locations={
				"/test_ApplicationContext-common.xml",
				"/test_ApplicationContext-dynsql.xml",
				})
public class TestRedisCache {
	@Test
	public void testPut(){
		ApplicationContext context= new ClassPathXmlApplicationContext("/test_ApplicationContext-cache.xml" );
		RedisCache redisCache = (RedisCache)context.getBean("redisCache");
	}

}
