package com.jsdz.digitalevidence.assessment.bean.plan;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
/**
 * @类名: ShootingAssessmentBean
 * @说明: 拍摄考核
 *
 * <AUTHOR>
 * @Date	 2019年6月26日下午8:39:32
 * 修改记录：
 *
 * @see 	 
 */
public class ShootingAssessmentBean {


	/** */
	private Long id;
	/** 是否文明规范*/
	private boolean isWMGF;
	/** 不存在违法违纪*/
	private boolean isNoWFWJ;
	/** 按要求拍摄*/
	private boolean isAYQPS;
	/** 按要求运用语言*/
	private boolean isYYYY;
	/** 拍摄角度*/
	private Integer shootingAngle;
	/** 拍摄内容要素*/
	private Integer shootingContent;
	/** 拍摄分辨率*/
	private Integer shootingResolution;
	/** 考核时间*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
	private Date assessDate;
	/** 考核文档日期*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm",timezone="GMT+8")
	private Date uploadTime;
	
	private Long docId;
	
	private String docName;
	/** 被考核人*/
	private Long policeId;
	private String policeCode;
	private String policeName;
    /** 考核员*/
	private Long inspectorId;
	private String inspectorCode;
	private String inspectorName;
	
	public boolean getIsWMGF(){
		return isWMGF;
	}
	public boolean getIsNoWFWJ(){
		return isNoWFWJ;
	}
	public boolean getIsAYQPS(){
		return isAYQPS;
	}	
	public boolean getIsYYYY(){
		return isYYYY;
	}	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public boolean isWMGF() {
		return isWMGF;
	}

	public void setIsWMGF(boolean isWMGF) {
		this.isWMGF = isWMGF;
	}
	public boolean isNoWFWJ() {
		return isNoWFWJ;
	}
	public void setIsNoWFWJ(boolean isNoWFWJ) {
		this.isNoWFWJ = isNoWFWJ;
	}
	public boolean isAYQPS() {
		return isAYQPS;
	}
	public void setIsAYQPS(boolean isAYQPS) {
		this.isAYQPS = isAYQPS;
	}
	public boolean isYYYY() {
		return isYYYY;
	}
	public void setIsYYYY(boolean isYYYY) {
		this.isYYYY = isYYYY;
	}
	public Integer getShootingAngle() {
		return shootingAngle;
	}
	public void setShootingAngle(Integer shootingAngle) {
		this.shootingAngle = shootingAngle;
	}
	public Integer getShootingContent() {
		return shootingContent;
	}
	public void setShootingContent(Integer shootingContent) {
		this.shootingContent = shootingContent;
	}
	public Integer getShootingResolution() {
		return shootingResolution;
	}
	public void setShootingResolution(Integer shootingResolution) {
		this.shootingResolution = shootingResolution;
	}
	public Date getAssessDate() {
		return assessDate;
	}
	public void setAssessDate(Date assessDate) {
		this.assessDate = assessDate;
	}
	public Date getUploadTime() {
		return uploadTime;
	}
	public void setUploadTime(Date uploadTime) {
		this.uploadTime = uploadTime;
	}
	public Long getDocId() {
		return docId;
	}
	public void setDocId(Long docId) {
		this.docId = docId;
	}
	public String getDocName() {
		return docName;
	}
	public void setDocName(String docName) {
		this.docName = docName;
	}
	public Long getPoliceId() {
		return policeId;
	}
	public void setPoliceId(Long policeId) {
		this.policeId = policeId;
	}
	public String getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(String policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	public Long getInspectorId() {
		return inspectorId;
	}
	public void setInspectorId(Long inspectorId) {
		this.inspectorId = inspectorId;
	}
	public String getInspectorCode() {
		return inspectorCode;
	}
	public void setInspectorCode(String inspectorCode) {
		this.inspectorCode = inspectorCode;
	}
	public String getInspectorName() {
		return inspectorName;
	}
	public void setInspectorName(String inspectorName) {
		this.inspectorName = inspectorName;
	}
	

	
}
