<?xml version="1.0" encoding="utf-8"?>
<!-- 警员动态sql -->
<sqlTemplates>
  <entry>
  	<!-- 查询警员, 包括警员所在部门，局，区域 -->
    <string>findEmployeesBean</string>
    <sqlTemplate>
      <!-- hql -->
      <sqlPattern>
	      from Employees emp 
	        left join fetch emp.department d
	        left join fetch emp.organization o
	        left join fetch o.region r
	      where e.isDeleted = false  {0} 
      </sqlPattern>
      <!-- 返回Bean -->
      <itemClass>com.jsdz.admin.security.model.Employees</itemClass>
      <!-- 可变块 -->
      <pieces>
      	 <sqlPiece>
          <fork>
            <paths>
              <path>
                <paramName>empId</paramName>
                <t>and emp.id = :empId</t>
                <a class="IsNotNull"/>
              </path>
            </paths>
          </fork>
        </sqlPiece>
      </pieces>
    </sqlTemplate>
  </entry>
  
</sqlTemplates>