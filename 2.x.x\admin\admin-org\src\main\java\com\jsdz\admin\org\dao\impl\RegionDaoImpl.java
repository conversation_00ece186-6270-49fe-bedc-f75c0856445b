package com.jsdz.admin.org.dao.impl;

/**
 *
 * @类名: RegionDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-08 17:08:54
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.org.dao.RegionDao;
import com.jsdz.admin.org.model.Region;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class RegionDaoImpl extends GenericEntityDaoHibernateImpl<Region,Long> implements RegionDao{

	//新增
	public void addRegion(Region region) {
		this.saveOrUpdate(region);
	}

	//删除
	public void deleteRegion(Region region) {
		this.delete(region);
	}

	//修改
	public void updateRegion(Region region) {
		this.saveOrUpdate(region);
	}

	//按id查询
	public Region locateRegionById(Long id){

		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(
				"from Region o left join fetch o.parentRegion left join fetch o.regionType"
				+ "  where o.id=:id");
		query.setParameter("id", id);
		List<Region> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//单个查询
	public Region findRegionByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		};
		List<Region> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//列表查询
	public List<Region> findRegionsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<Region> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<Region> findRegionsOnPage(Page<Region> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<Region>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
