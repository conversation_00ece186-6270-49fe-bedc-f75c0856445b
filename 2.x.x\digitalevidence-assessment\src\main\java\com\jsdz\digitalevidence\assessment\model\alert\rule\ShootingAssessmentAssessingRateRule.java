package com.jsdz.digitalevidence.assessment.model.alert.rule;

import java.util.ArrayList;
import java.util.List;

import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.alert.AssessmentAlertItem;
import com.jsdz.ruleengine.RuleState;
import com.jsdz.ruleengine.annotation.Given;
import com.jsdz.ruleengine.annotation.Param;
import com.jsdz.ruleengine.annotation.Result;
import com.jsdz.ruleengine.annotation.Rule;
import com.jsdz.ruleengine.annotation.Then;
import com.jsdz.ruleengine.annotation.When;

/**
 * @类名: ShootingAssessmentAssessingRateRule
 * @说明: 拍摄考核上传文档考核率规则
 *
 * <AUTHOR>
 * @Date 2017年9月4日 上午11:29:26 修改记录：
 *
 * @see
 */
@Rule(name="assessingRateRule", description="考核率百分比规则")
public class ShootingAssessmentAssessingRateRule {

	/** 上传文档考核率阀值*/
	@Param
	private float threshold;

	@Given
	private ShootingAssessmentSummary sum;
	
	/** */
	@Result
	private List<AssessmentAlertItem<?>> _result;

	@When
	public boolean when() {
		float r = sum.getAssessmentRate();
		if(r<threshold)
			return true;
		return false;
	}

	@Then
	public RuleState then() {
		AssessmentAlertItem<Float> item = new AssessmentAlertItem<Float>();
		item.setItem("考核率");
		item.setV(sum.getAssessmentRate());
		item.setRef(threshold);
		if(_result==null)
			_result = new ArrayList<AssessmentAlertItem<?>>();
		_result.add(item);
		// 考核率不足
		return RuleState.BREAK;
	}

}
