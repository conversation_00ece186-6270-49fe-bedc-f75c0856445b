package com.jsdz.digitalevidence.analysis.service;
/**
 * 
 * @接口名: AnalysisService
 * @说明: 查询分析：系统简报，趋势研判，趋势对比
 *
 * @author: kenny
 * @Date	2018年3月28日上午11:42:25
 * 修改记录：
 *
 * @see
 */
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.analysis.bean.AlarmDocRelationRateBean;
import com.jsdz.digitalevidence.analysis.bean.AnalysisDocumentBean;
import com.jsdz.digitalevidence.analysis.bean.BriefReport;
import com.jsdz.digitalevidence.analysis.bean.TrendReport;

public interface AnalysisService {

	//系统简报
	Page<BriefReport> findBriefReport(Page<BriefReport> page,String[] paramNames,Object[] values);
	
	//趋势对比
	Page<TrendReport> findTrendCompareReport(Page<TrendReport> page,String[] paramNames,Object[] values);

	//趋势对比
	Page<TrendReport> findTrendForecastReport(Page<TrendReport> page,String[] paramNames,Object[] values);
	//警情视频关联率查询
	Page<AlarmDocRelationRateBean> findAlarmDocRelationFromSql(Page<AlarmDocRelationRateBean> page,String[] paramNames, Object[] values);

/*	//按警员统计上传文件
	Page<AnalysisDocumentBean> findAnalysisDocByPoliceFromSql(Page<AnalysisDocumentBean> page,String[] paramNames, Object[] values);

	//按站点统计上传文件
	Page<AnalysisDocumentBean> findAnalysisDocBySiteFromSql(Page<AnalysisDocumentBean> page,String[] paramNames, Object[] values);
*/
	//按警员,站点，设备，单位统计上传文件
	 Page<AnalysisDocumentBean> findAnalysisDocumentFromSql(Page<AnalysisDocumentBean> page,String sqlName,String[] paramNames, Object[] values);

}
