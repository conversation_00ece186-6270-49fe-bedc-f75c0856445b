package com.jsdz.admin.org.dao;

/**
 *
 * @类名: PositionDao
 * @说明:
 * @author: kenny
 * @Date 2017-05-09 21:50:03
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.admin.org.model.Position;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface PositionDao extends GenericORMEntityDAO<Position,Long> {

	//新增
	public void addPosition(Position position);

	//修改
	public void updatePosition(Position position);

	//删除
	public void deletePosition(Position position);

	//按id查询
	public Position locatePositionById(Long id);

	//单个查询
	public Position findPositionByCondition(String queryStr,String[] paramNames,Object[] values);

	//查询全部
	public List<Position> findAllPositions();

	//列表查询
	public List<Position> findPositionsByCondition(String queryStr,String[] paramNames,Object[] values);

	//分页查询
	public Page<Position> findPositionsOnPage(Page<Position> page,String queryStr,String[] paramNames,Object[] values);

}

