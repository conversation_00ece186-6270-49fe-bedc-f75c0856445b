package com.jsdz.digitalevidence.approve.model;

/**
 * @类名: ApproveStatus
 * @说明: 审批状态
 *
 * <AUTHOR>
 * @Date	 2017年4月20日 上午8:49:08
 * 修改记录：
 *
 * @see 	 
 */
public enum ApproveStatus {
	
    open(1, "开启"), close(2, "关闭"), disabled(3, "失效");
 
	private int index;
    private String value;
 
    private ApproveStatus(int index, String value) {
    	this.index = index;
        this.value = value;
    }

	public int getIndex() {
		return index;
	}

	public void setIndex(int index) {
		this.index = index;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
    
 
 
}