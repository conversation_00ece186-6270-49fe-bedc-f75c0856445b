package com.jsdz.admin.security.dao.impl;

import java.sql.SQLException;

/**
 *
 * @类名: OperateLogDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-11 08:56:21
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.OperateLogDao;
import com.jsdz.admin.security.model.OperateLog;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class OperateLogDaoImpl extends GenericEntityDaoHibernateImpl<OperateLog,Long> implements OperateLogDao{

	//新增
	public void addOperateLog(OperateLog operateLog) {
		this.saveOrUpdate(operateLog);
	}

	//删除
	public void deleteOperateLog(OperateLog operateLog) {
		this.delete(operateLog);
	}

	//修改
	public void updateOperateLog(OperateLog operateLog) {
		this.saveOrUpdate(operateLog);
	}

	//按id查询
	public OperateLog locateOperateLogById(Long id){

		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery("from OperateLog o where o.id=:id "); 
		query.setParameter("id", id);
		List<OperateLog> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//单个查询
	public OperateLog findOperateLogByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		};
		List<OperateLog> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}

	//查询全部
	public List<OperateLog> findAllOperateLogs(){
		return this.find("from OperateLog operateLog ");
	}

	//列表查询
	public List<OperateLog> findOperateLogsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<OperateLog> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<OperateLog> findOperateLogsOnPage(Page<OperateLog> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<OperateLog>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}
	
	//按条件执行HQL（非查询）
	public Integer execHqlByCondition(final String hql,final String[] params,final Object[] values ){
		int data = getHibernateTemplate().execute(new HibernateCallback<Integer>() {      	 
		    public Integer doInHibernate(Session session)throws HibernateException, SQLException {
				Query query = session.createQuery(hql);
				for (int i =0;i<params.length;i++){
					applyNamedParameterToQuery(query, params[i], values[i]);
				}
				return query.executeUpdate();
		    }  
		});
		return data;
	}
	
}
