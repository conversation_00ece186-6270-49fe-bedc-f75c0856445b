package com.jsdz.digitalevidence.archives.bean;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.utils.DateTimeUtils;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * VIEW
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-22
 */
public class DocCheckInit implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long docId;

    private String type;

    private String contentType;

    private String docName;

    private Long docSize;

    private String docSizeStr;

    private Long duration;

    private String durationStr;
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date createTime;
    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date uploadTime;

    private Long siteId;

    private Long storageId;

    private Long oid;

    private String orgName;

    private String orgCode;

    private String path;

    private Long eid;

    private String userName;

    private Object document;

    public Object getDocument() {
        return document;
    }

    public void setDocument(Object document) {
        this.document = document;
    }

    private String workNumber;

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date relationTime;

    public String getDocSizeStr() {
        return docSizeStr;
    }

    public void setDocSizeStr(String docSizeStr) {
        this.docSizeStr = docSizeStr;
    }

    public String getDurationStr() {
        return durationStr;
    }

    public void setDurationStr(String durationStr) {
        this.durationStr = durationStr;
    }

    public String getArchivesStr() {
        return archivesStr;
    }

    public void setArchivesStr(String archivesStr) {
        this.archivesStr = archivesStr;
    }

    public String getRelationStr() {
        return relationStr;
    }

    public void setRelationStr(String relationStr) {
        this.relationStr = relationStr;
    }

    @JsonFormat(pattern= DateTimeUtils.defaultPatten,timezone="GMT+8")
    private Date archivesTime;

    private String archivesStr;

    private String relationStr;

    public Date getArchivesTime() {
        return archivesTime;
    }

    public void setArchivesTime(Date archivesTime) {
        this.archivesTime = archivesTime;
    }

    private String cheakCode;

    public String getCheakCode() {
        return cheakCode;
    }

    public void setCheakCode(String cheakCode) {
        this.cheakCode = cheakCode;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getDocId() {
        return docId;
    }

    public void setDocId(Long docId) {
        this.docId = docId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getDocName() {
        return docName;
    }

    public void setDocName(String docName) {
        this.docName = docName;
    }

    public Long getDocSize() {
        return docSize;
    }

    public void setDocSize(Long docSize) {
        this.docSize = docSize;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(Date uploadTime) {
        this.uploadTime = uploadTime;
    }

    public Long getSiteId() {
        return siteId;
    }

    public void setSiteId(Long siteId) {
        this.siteId = siteId;
    }

    public Long getStorageId() {
        return storageId;
    }

    public void setStorageId(Long storageId) {
        this.storageId = storageId;
    }

    public Long getOid() {
        return oid;
    }

    public void setOid(Long oid) {
        this.oid = oid;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Long getEid() {
        return eid;
    }

    public void setEid(Long eid) {
        this.eid = eid;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getWorkNumber() {
        return workNumber;
    }

    public void setWorkNumber(String workNumber) {
        this.workNumber = workNumber;
    }

    public Date getRelationTime() {
        return relationTime;
    }

    public void setRelationTime(Date relationTime) {
        this.relationTime = relationTime;
    }
}
