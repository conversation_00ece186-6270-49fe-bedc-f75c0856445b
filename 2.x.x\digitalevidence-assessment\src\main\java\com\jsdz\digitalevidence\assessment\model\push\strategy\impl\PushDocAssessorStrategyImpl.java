/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.model.push.strategy.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;

import org.hibernate.transform.ResultTransformer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.jsdz.digitalevidence.assessment.AssessmentUtils;
import com.jsdz.digitalevidence.assessment.bean.AssessmentAssessorSumBean;
import com.jsdz.digitalevidence.assessment.model.push.strategy.PushDocAssessorStrategy;
import com.jsdz.reportquery.ReportQueryDao;

/**
 * @类名: PushDocAssessorStrategyImpl
 * @说明: 视频推送查找考核员策略
 *
 * <AUTHOR>
 * @Date	 2017年10月17日 下午6:59:43
 * 修改记录：
 *
 * @see 	 
 */
@Component
public class PushDocAssessorStrategyImpl implements PushDocAssessorStrategy {

	@Autowired
	private ReportQueryDao rqDao;
	private List<AssessmentAssessorSumBean> assessors;
	
	public List<AssessmentAssessorSumBean> getAssessors() {
		return assessors;
	}

	// 考核视频量，包括已推送但未考核
	@SuppressWarnings("unchecked")
	@Override
	public List<AssessmentAssessorSumBean> findAssessor(Date startDate, Date endDate, int topN) {
		assessors = (List<AssessmentAssessorSumBean>)rqDao.queryNamedSQL(
				"findPushingAssessorTopN", 
				new String[]{"startDate","endDate", "topN"}, 
				new Object[]{startDate, endDate,  topN},
				new ResultTransformer() {
					@Override
					public Object transformTuple(Object[] tuples, String[] aliases) {
						Long policeId = ((BigInteger) tuples[0]).longValue();
						Integer assessDocs = AssessmentUtils.nullOrInteger((BigDecimal) tuples[1]);
						Integer totalPushDocs = AssessmentUtils.nullOrInteger((BigDecimal) tuples[2]);
						Integer notAssessPushDocs = AssessmentUtils.nullOrInteger((BigDecimal) tuples[3]);
						AssessmentAssessorSumBean bean = new AssessmentAssessorSumBean();
						bean.setPoliceId(policeId);
						bean.setAssessDocs(assessDocs);
						bean.setTotalPushDocs(totalPushDocs);
						bean.setNotAssessPushDocs(notAssessPushDocs);
						return bean;
					}

					@Override
					public List transformList(List collection) {
						return collection;
					}
				});
		return assessors;
	}

}
