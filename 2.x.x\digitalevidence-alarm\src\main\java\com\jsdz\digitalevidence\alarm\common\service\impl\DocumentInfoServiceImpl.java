package com.jsdz.digitalevidence.alarm.common.service.impl;
/**
 * 
 * @类名: DocumentInfoServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-11-06 16:23:56
 * 修改记录：
 *
 * @see
*/

import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.utils.JSDateFormatUtils;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.activemq.model.DataExchange;
import com.jsdz.digitalevidence.activemq.model.UserApiMessage;
import com.jsdz.digitalevidence.activemq.service.DataExchangeService;
import com.jsdz.digitalevidence.activemq.service.ProducerService;
import com.jsdz.digitalevidence.activemq.utils.CustomJsonConfig;
import com.jsdz.digitalevidence.activemq.utils.FunctionUtils;
import com.jsdz.digitalevidence.activemq.utils.MqTopic;
import com.jsdz.digitalevidence.activemq.utils.OptionType;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmInfoDao;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmRelationDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmInfo;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmRelation;
import com.jsdz.digitalevidence.alarm.common.bean.DocumentInfoParam;
import com.jsdz.digitalevidence.alarm.common.bean.RecoderReviewBean;
import com.jsdz.digitalevidence.alarm.common.dao.DocumentInfoDao;
import com.jsdz.digitalevidence.alarm.common.dao.RecoderViewDao;
import com.jsdz.digitalevidence.alarm.common.model.RecoderView;
import com.jsdz.digitalevidence.alarm.common.service.DocumentInfoService;
import com.jsdz.digitalevidence.document.bean.DocumentInfoBean;
import com.jsdz.digitalevidence.document.dao.DocumentDao;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.digitalevidence.document.model.DocumentInfo;
import com.jsdz.reportquery.ReportQueryDao;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("DocumentInfoServiceImpl")
public class DocumentInfoServiceImpl implements DocumentInfoService {

	@Autowired
	private DocumentInfoDao documentInfoDao;
	@Autowired
	private AlarmRelationDao alarmRelationDao;
	@Autowired
	private AlarmInfoDao alarmInfoDao;
	@Autowired
	private DocumentDao documentDao;
	@Autowired
	private RecoderViewDao  recoderViewDao;
	@Autowired
	private DataExchangeService  dataExchangeService;
	
	
	
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<DocumentInfoBean> reportQueryDao;

	//消息服务
	@Autowired  
	private ProducerService producerService;  
	
	//新增
	public AjaxResult addDocumentInfo(DocumentInfo documentInfo) {
		AjaxResult result = new AjaxResult();
		documentInfo.setId(null);
//		documentInfo.setApproved(0);
//		documentInfo.setApprovedResult(0);
//		documentInfo.setTypical(0);
		documentInfo.setCreateTime(new Date());
		documentInfoDao.addDocumentInfo(documentInfo);
		DocumentInfoBean bean = null;

		if (FunctionUtils.activemqActive()==true){
			bean = getDocumentInfoByDocIdFromSql(documentInfo.getDocId());
			sendDocInfoMessage(bean,OptionType.UPDATE.getName(),"视频编辑/审核");
		}
		if (FunctionUtils.exchangeActive() ==true){
			bean = getDocumentInfoByDocIdFromSql(documentInfo.getDocId());
			saveToExchange(bean,OptionType.UPDATE.getName(),"视频编辑/审核");
		}
		
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateDocumentInfo(DocumentInfo documentInfo) {
		AjaxResult result = new AjaxResult();
		documentInfoDao.updateDocumentInfo(documentInfo);
		DocumentInfoBean bean = null;
		if (FunctionUtils.activemqActive()==true){
			bean = getDocumentInfoByDocIdFromSql(documentInfo.getDocId());
			sendDocInfoMessage(bean,OptionType.UPDATE.getName(),"视频编辑/审核");
		}
		if (FunctionUtils.exchangeActive()==true){
			bean = getDocumentInfoByDocIdFromSql(documentInfo.getDocId());
			saveToExchange(bean,OptionType.UPDATE.getName(),"视频编辑/审核");
		}
		
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteDocumentInfo(DocumentInfo documentInfo) {
		AjaxResult result = new AjaxResult();
		documentInfoDao.deleteDocumentInfo(documentInfo);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public DocumentInfo findDocumentInfoById(Long id){
		return documentInfoDao.findDocumentInfoById(id);
	}

	//按 id 查询
	public DocumentInfo locateDocumentInfoById(Long id) {
		return documentInfoDao.locateDocumentInfoById(id);
	}

	//单个查询
	public DocumentInfo findDocumentInfoByParam(String queryStr, String[] paramNames, Object[] values) {
		return documentInfoDao.findDocumentInfoByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<DocumentInfo> findAllDocumentInfos() {
		return documentInfoDao.findAllDocumentInfos();
	}

	//列表查询
	public List<DocumentInfo> findDocumentInfosByParam(String queryStr, String[] paramNames, Object[] values) {
		return documentInfoDao.findDocumentInfosByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<DocumentInfo> findDocumentInfosOnPage(Page<DocumentInfo> page, String queryStr, String[] paramNames, Object[] values) {
		Page<DocumentInfo> pos = documentInfoDao.findDocumentInfosOnPage(page, queryStr, paramNames, values);
		return pos;
	}
	
	//按条件查询所有的文档
	public Page<DocumentInfoBean> findDocumentInfosBeanFromSql(Page<DocumentInfoBean> page,String[] paramNames, Object[] values){
		Page<DocumentInfoBean> p1 = reportQueryDao.pageQueryNamedSQL(page,"findDocmentInfo",paramNames,values);		
		return p1;
	}
	
	//按查询视频文件不分页
	public List<Object> findDocumentInfosBeanFromSql(String[] paramNames, Object[] values){
		List<Object> list =  reportQueryDao.QueryNamedAllDataSQL("findDocmentInfoByDocId", paramNames, values);
		return list;
	}

	//编辑
	@Override
	@Transactional
	public AjaxResult editDocumentInfo(DocumentInfoBean docinfo,Operator operator) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		//如何是典型视频，则必须选择典型分类
		if (docinfo.getTypical() == null){
			docinfo.setTypical(0);
			docinfo.setTypicalContext(null);
		}
		if (docinfo.getTypical()==1 && docinfo.getTypicalContext()==null){
			result.setMsg("典型分类必须录入");
			return result;
		}
		try{
			updateDocumentInfo(docinfo);
			//设置警情关联
			setAlarmRelation(docinfo,operator);		
		}catch(Exception e){
			result.setMsg("错误:" + e.toString());
			return result;
		}
		
		result.setCode(200);
		result.setSuccess(true);
		result.setMsg("编辑成功");
		return result;

	}
	private void updateDocumentInfo(DocumentInfoBean docinfo){
		
			String queryStr = "from DocumentInfo d where docId = :docId";
			DocumentInfo updateDocinfo = documentInfoDao.findDocumentInfoByCondition(queryStr, new String[]{"docId"}, new Object[]{docinfo.getDocId()});
			if (updateDocinfo != null){
				updateDocinfo.setAlarmCode(docinfo.getAlarmCode());
				updateDocinfo.setFileType(docinfo.getFileType());
				updateDocinfo.setTypical(docinfo.getTypical());
				updateDocinfo.setTypicalContext(docinfo.getTypicalContext());
				//updateDocinfo.setCreateTime(new Date());
				updateDocinfo.setNote(docinfo.getNote());
				updateDocinfo.setAddress(docinfo.getAddress());
				updateDocinfo.setCaseLevel(docinfo.getCaseLevel());
				updateDocinfo.setCaseType(docinfo.getCaseType());
				updateDocinfo.setExpiryTime(strToDate(docinfo.getExpiryTimeStr()));
				updateDocinfo.setApproved(docinfo.getApproved());//未审核
				updateDocinfo.setApprovedResult(docinfo.getApprovedResult());
				updateDocinfo.setApproveCause(docinfo.getApproveCause());
				this.updateDocumentInfo(updateDocinfo);
			}else{
				updateDocinfo = new DocumentInfo();
				docinfo.assignTo(updateDocinfo);
				//updateDocinfo.setApproved(0);//未审核
				//updateDocinfo.setApprovedResult(null);
				//updateDocinfo.setApproveCause(null);
				this.addDocumentInfo(updateDocinfo);
			}
	}

	//审核
	@Override
	@Transactional
	public AjaxResult approveDocumentInfo(DocumentInfoParam docInfoParam,Operator operator) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		
		DocumentInfoBean docinfo = docInfoParam.getDocinfo();
		if (docinfo.getApprovedResult() == null ){
			result.setMsg("审批意见必须录入");
			return result;
		}
		if (docinfo.getApproveCause()==null || docinfo.getApproveCause().equals("")){
			result.setMsg("审批问题必须录入");
			return result;
		}
		
		//如何是典型视频，则必须选择典型分类
		if (docinfo.getTypical() == null){
			docinfo.setTypical(0);
			docinfo.setTypicalContext(null);
		}
		if (docinfo.getTypical()==1 && docinfo.getTypicalContext()==null){
			result.setMsg("典型分类必须录入");
			return result;
		}
		
		if (operator.getEmployees() == null){
			result.setMsg("操作员="+operator.getLoginName()+" 没有绑定警员, 不能审批");
			return result;
		}
		
		if (operator.getEmployees().getOrganization()==null){
			result.setMsg("操作员="+operator.getLoginName()+" 没有所属单位部门, 不能审批");
			return result;
		}
		
		try{
			updateDocumentInfo(docinfo);

			//设置警情关联
			setAlarmRelation(docinfo,operator);
			
			//审核维护视频信息
			RecoderView rv = new RecoderView();
			rv.setDoId(docinfo.getDocId());//视频的id，docid;
			if (operator.getEmployees() != null && operator.getEmployees().getOrganization() != null){
				rv.setGlbm(operator.getEmployees().getOrganization().getOrgCode());//管理部门编码，观看者管理部门编码
			}
			
			if (operator.getEmployees() != null){
				rv.setGjbh(operator.getEmployees().getWorkNumber());//干警编号，观看警员编号 
				rv.setXm(operator.getEmployees().getName()); //干警姓名，观看警员姓名
			}
			rv.setGxsj(new Date());//更新时间，监督回放时间
			rv.setStart(docInfoParam.getStartTime());//开始时长，视频开始时长，单位秒
			rv.setEnd(docInfoParam.getEndTime());//结束时长，视频结束时长，单位秒
			rv.setSpyj(docInfoParam.getApproveStr());//审批意见 合格/不合格
			rv.setSpwt(docinfo.getApproveCause());//审批问题；多选用（;）分割
			rv.setDocName(docinfo.getDocName());
			recoderViewDao.addRecoderView(rv);
			if (FunctionUtils.activemqActive()==true){
				sendRecorderViewMessage(rv,OptionType.ADD.getName(),"视频监督回放");
			}

		}catch(Exception e){
			result.setMsg("错误:" + e.toString());
			return result;
		}
		
		result.setCode(200);
		result.setSuccess(true);
		result.setMsg("审批成功");
		return result;		
	}

	//行政复议
	@Override
	@Transactional
	public AjaxResult reeditDocumentInfo(DocumentInfoBean docinfo,Operator operator) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		//如何是典型视频，则必须选择典型分类
		if (docinfo.getTypical() == null){
			docinfo.setTypical(0);
			docinfo.setTypicalContext(null);
		}
		if (docinfo.getTypical()==1 && docinfo.getTypicalContext()==null){
			result.setMsg("典型分类必须录入");
			return result;
		}
		
		try{
			String queryStr = "from DocumentInfo d where docId = :docId";
			DocumentInfo updateDocinfo = documentInfoDao.findDocumentInfoByCondition(queryStr, new String[]{"docId"}, new Object[]{docinfo.getDocId()});
			if (updateDocinfo != null){
				updateDocinfo.setAlarmCode(docinfo.getAlarmCode());
				updateDocinfo.setFileType(docinfo.getFileType());
				updateDocinfo.setTypical(docinfo.getTypical());
				updateDocinfo.setTypicalContext(docinfo.getTypicalContext());
				updateDocinfo.setCreateTime(new Date());
				updateDocinfo.setNote(docinfo.getNote());
				updateDocinfo.setAddress(docinfo.getAddress());
				updateDocinfo.setCaseLevel(docinfo.getCaseLevel());
				updateDocinfo.setCaseType(docinfo.getCaseType());
				updateDocinfo.setExpiryTime(strToDate(docinfo.getExpiryTimeStr()));
				updateDocinfo.setApproved(docinfo.getApproved());//未审核
				updateDocinfo.setApprovedResult(docinfo.getApprovedResult());
				updateDocinfo.setApproveCause(docinfo.getApproveCause());
				this.updateDocumentInfo(updateDocinfo);
			}else{
				updateDocinfo = new DocumentInfo();
				docinfo.assignTo(updateDocinfo);
/*				updateDocinfo.setApproved(0);//未审核
				updateDocinfo.setApprovedResult(null);
				updateDocinfo.setApproveCause(null);
*/				this.addDocumentInfo(updateDocinfo);
			}
			//设置警情关联
			setAlarmRelation(docinfo,operator);
		}catch(Exception e){
			result.setMsg("错误:" + e.toString());
			return result;
		}
		
		result.setCode(200);
		result.setSuccess(true);
		result.setMsg("复议成功");
		return result;	
	}
	
	private Date strToDate(String str){
		Date d = null;
		try{
			d = JSDateFormatUtils.parseDate(str);
		}catch(Exception e){
			return null;
		}
		return d;
	}
	
	//设置警情关联
	private void setAlarmRelation(DocumentInfoBean docinfo,Operator operator){
		//警情关联
		//1、警情号为空则删除此视频的所有警情关联
		if (docinfo.getAlarmCode()==null || "".equals(docinfo.getAlarmCode())){
			String queryStr=" delete from AlarmRelation a where a.mediaInfo.id=:docId";
			alarmRelationDao.exeCommHql(queryStr, 
					new String[]{"docId"}, new Object[]{docinfo.getDocId()});
			return;
		}
		//2、判断警情表是否有存在此警情
		
		String queryStr = "from AlarmRelation r "
				+ " left join fetch r.mediaInfo m "
				+ " left join fetch r.alarmInfo a "
				+ " where m.id=:docId ";//and a.alarmCode = :alarmCode ";
		List<AlarmRelation> arList = alarmRelationDao.findAlarmRelationsByCondition(queryStr,
				new String[]{"docId"}, new Object[]{docinfo.getDocId()});
		boolean found = false;
		for (AlarmRelation ar : arList){
			if (ar.getAlarmInfo() != null && ar.getAlarmInfo().getAlarmCode() == docinfo.getAlarmCode()){
				found = true;
			}else
			    alarmRelationDao.delete(ar);
		}
		if (found == true)
			return;
		
		queryStr = "from AlarmInfo a where a.alarmCode = :alarmCode ";
		AlarmInfo ai = alarmInfoDao.findAlarmInfoByCondition(
				queryStr,new String[]{"alarmCode"}, new Object[]{docinfo.getAlarmCode()});
		
		queryStr = "from Document d where d.id = :docId ";
		Document doc = documentDao.findDocumentByCondition(
					queryStr,new String[]{"docId"}, new Object[]{docinfo.getDocId()});
		
		if (ai!=null && doc!=null){
				AlarmRelation ar1 = new AlarmRelation();
				ar1.setAlarmInfo(ai);
				ar1.setMediaInfo(doc);
				ar1.setOperator(operator);
				ar1.setRelationTime(new Date());
				alarmRelationDao.addAlarmRelation(ar1);
		}
	}
	
	//按docId取得Document
	public DocumentInfoBean getDocumentInfoByDocIdFromSql(Long docId){
		String[] paramNames = new String[]{"docId"};
		Object[] values = new Object[]{docId};
		List<Object> list =  reportQueryDao.QueryNamedAllDataSQL("findDocmentInfoByDocId", paramNames, values);
		if (list != null && list.size() > 0)
			return (DocumentInfoBean)list.get(0);
		else
			return null;
	}
	
	//消息发送
	public void sendDocInfoMessage(DocumentInfoBean doc,String option,String msg){
		UserApiMessage message = new UserApiMessage();
		message.setOption(option);
		message.setMsg(msg);
		message.setData(doc);
		JSONObject json=JSONObject.fromObject(message,CustomJsonConfig.getInstance().getJsonConfig());
		producerService.sendMessage(MqTopic.DOCUMENT.getName(),json.toString());
	}
	
	//发送监督回放消息
	public void sendRecorderViewMessage(RecoderView rv,String option,String msg){
		RecoderReviewBean rvb = new RecoderReviewBean();
		rvb.assign(rv);
		UserApiMessage message = new UserApiMessage();
		message.setOption(option);
		message.setMsg(msg);
		message.setData(rvb);
		JSONObject json=JSONObject.fromObject(message,CustomJsonConfig.getInstance().getJsonConfig());
		System.out.println(json);
		producerService.sendMessage(MqTopic.RECODERREVIEW.getName(),json.toString());
	}
	
	//保存到转换表
	public void saveToExchange(DocumentInfoBean doc,String option,String msg){
		UserApiMessage message = new UserApiMessage();
		message.setOption(option);
		message.setMsg(msg);
		message.setData(doc);
		JSONObject context=JSONObject.fromObject(message,CustomJsonConfig.getInstance().getJsonConfig());
		
		DataExchange de = new DataExchange();
		de.setOption(option);
		de.setTopic(MqTopic.DOCUMENT.getName());
		de.setContext(context.toString());
		de.setNote(msg);
		de.setProccessFlag(0);
		de.setProccessCount(0);
		de.setCreateTime(new Date());
		dataExchangeService.addDataExchange(de);
	}
	
	@Override
	public boolean existsRecoderView(RecoderView rv){
		String queryStr = "from RecoderView r where r.doId = :docId and r.start=:starttime and r.end=:endtime";
		String[] paramNames = new String[]{"docId","starttime","endtime"};
		Object[] values = new Object[]{rv.getDoId(),rv.getStart(),rv.getEnd()};
		RecoderView rv1 = recoderViewDao.findRecoderViewByCondition(queryStr,paramNames,values);
		return rv1 != null;
	}
	
	
}
