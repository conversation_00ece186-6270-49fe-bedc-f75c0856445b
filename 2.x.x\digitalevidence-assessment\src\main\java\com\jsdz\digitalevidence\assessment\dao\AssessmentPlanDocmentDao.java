package com.jsdz.digitalevidence.assessment.dao;

import java.util.Date;

/**
 *
 * @类名: AssessmentPlanDocmentDao
 * @说明:
 * @author: kenny
 * @Date 2019-06-19 16:05:25
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlanDocment;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface AssessmentPlanDocmentDao extends GenericORMEntityDAO<AssessmentPlanDocment,Long> {

	/** 
 	 * 新增
	 */ 
 	public void addAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment);

	/** 
 	 * 修改
	 */ 
 	public void updateAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment);

	/** 
 	 * 删除
	 */ 
 	public void deleteAssessmentPlanDocment(AssessmentPlanDocment assessmentPlanDocment);

	/** 
 	 * 按id查询,结果是游离状态的数据
	 */ 
 	public AssessmentPlanDocment findAssessmentPlanDocmentById(Long id);

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlanDocment locateAssessmentPlanDocmentById(Long id);

	/* 
 	 * 单个查询
	 */ 
 	public AssessmentPlanDocment findAssessmentPlanDocmentByCondition(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlanDocment> findAllAssessmentPlanDocments();

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlanDocment> findAssessmentPlanDocmentsByCondition(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlanDocment> findAssessmentPlanDocmentsOnPage(Page<AssessmentPlanDocment> page,String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 执行指定的HQL文件
	 */ 
 	public void exeCommHql(String queryStr, String[] paramNames, Object[] values);
 	
 	public List<Object> getRadomDoc(Date time1,Date time2,Integer num);

}

