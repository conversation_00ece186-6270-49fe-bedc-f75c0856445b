package com.jsdz.digitalevidence.task.constant;

public enum BusinessCate {

    // 人脸抓拍
    PHOTO_SHOT(1);

    private int value;

    BusinessCate(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public boolean check(int value) {
        for (BusinessCate BusinessCate : values()) {
            if(BusinessCate.getValue() == value) {
                return true;
            }
        }
        return false;
    }

}
