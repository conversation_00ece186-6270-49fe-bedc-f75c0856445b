package com.jsdz.digitalevidence.analysis.bean;
/**
 * 
 * @类名: TrendReport
 * @说明: 趋势报表：包括趋势研判，趋势对比
 *
 * @author: kenny
 * @Date	2018年3月28日上午11:49:56
 * 修改记录：
 *
 * @see
 */
import java.io.Serializable;

public class TrendReport implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4190025651055266203L;
	private Long id;
	private String objCode;//项目，可能是单位，警员，日期等
	private Long fileCount;//文件数量
	private Long vedioCount;//视频数量
	private Long vedioQualify;//视频质量
	private Double sumDuration;//视频时长
	private Double sumHdDuration;//高清时长
	private Double alarmRate;//警情关联率
	private Double caseRate;//案件关联率
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getObjCode() {
		return objCode;
	}
	public void setObjCode(String objCode) {
		this.objCode = objCode;
	}
	public Long getFileCount() {
		return fileCount;
	}
	public void setFileCount(Long fileCount) {
		this.fileCount = fileCount;
	}
	public Long getVedioCount() {
		return vedioCount;
	}
	public void setVedioCount(Long vedioCount) {
		this.vedioCount = vedioCount;
	}
	public Long getVedioQualify() {
		return vedioQualify;
	}
	public void setVedioQualify(Long vedioQualify) {
		this.vedioQualify = vedioQualify;
	}
	public Double getSumDuration() {
		return sumDuration;
	}
	public void setSumDuration(Double sumDuration) {
		this.sumDuration = sumDuration;
	}
	public Double getSumHdDuration() {
		return sumHdDuration;
	}
	public void setSumHdDuration(Double sumHdDuration) {
		this.sumHdDuration = sumHdDuration;
	}
	public Double getAlarmRate() {
		return alarmRate;
	}
	public void setAlarmRate(Double alarmRate) {
		this.alarmRate = alarmRate;
	}
	public Double getCaseRate() {
		return caseRate;
	}
	public void setCaseRate(Double caseRate) {
		this.caseRate = caseRate;
	}
	
	

}
