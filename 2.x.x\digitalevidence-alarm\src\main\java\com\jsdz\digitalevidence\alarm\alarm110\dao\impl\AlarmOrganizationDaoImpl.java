package com.jsdz.digitalevidence.alarm.alarm110.dao.impl;

/**
 *
 * @类名: AlarmOrganizationDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2018-08-29 21:08:19
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.stereotype.Repository;
import com.jsdz.digitalevidence.alarm.alarm110.dao.AlarmOrganizationDao;
import com.jsdz.digitalevidence.alarm.alarm110.model.AlarmOrganization;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;

@Repository
public class AlarmOrganizationDaoImpl extends GenericEntityDaoHibernateImpl<AlarmOrganization,Long> implements AlarmOrganizationDao{

	//新增
	public void addAlarmOrganization(AlarmOrganization alarmOrganization) {
		this.saveOrUpdate(alarmOrganization);
	}

	//删除
	public void deleteAlarmOrganization(AlarmOrganization alarmOrganization) {
		this.delete(alarmOrganization);
	}

	//修改
	public void updateAlarmOrganization(AlarmOrganization alarmOrganization) {
		this.merge(alarmOrganization);
	}

	//按id查询(游离状态)
	public AlarmOrganization findAlarmOrganizationById(Long id){

		final String  hql = "from AlarmOrganization o where o.id = :id";
		final Long oid = id;
		AlarmOrganization data = getHibernateTemplate().execute(new HibernateCallback<AlarmOrganization>() {
			public AlarmOrganization doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmOrganization> list = query.list();
				AlarmOrganization rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public AlarmOrganization locateAlarmOrganizationById(Long id){

		final String  hql = "from AlarmOrganization o where o.id = :id";
		final Long oid = id;
		AlarmOrganization data = getHibernateTemplate().execute(new HibernateCallback<AlarmOrganization>() {
			public AlarmOrganization doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<AlarmOrganization> list = query.list();
				AlarmOrganization rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public AlarmOrganization findAlarmOrganizationByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		AlarmOrganization data = getHibernateTemplate().execute(new HibernateCallback<AlarmOrganization>() {
		public AlarmOrganization doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<AlarmOrganization> list = query.list();
			AlarmOrganization rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<AlarmOrganization> findAllAlarmOrganizations(){
		return this.find("from AlarmOrganization alarmOrganization ");
	}

	//列表查询
	public List<AlarmOrganization> findAlarmOrganizationsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<AlarmOrganization> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<AlarmOrganization> findAlarmOrganizationsOnPage(Page<AlarmOrganization> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<AlarmOrganization>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//执行自定义的hql
	public void exeCommHql(String queryStr, String[] paramNames, Object[] values) {
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlStr = queryStr;
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		query.executeUpdate();
		session.close();
	}
}
