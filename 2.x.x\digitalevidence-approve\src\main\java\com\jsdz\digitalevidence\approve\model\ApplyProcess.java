/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.approve.model;

import com.jsdz.admin.org.model.Organization;
import com.jsdz.core.AbstractDTO;

/**
 * @类名: ApplyProcess
 * @说明: 适用流程
 *        
 *
 * <AUTHOR>
 * @Date	 2017年4月17日 上午10:59:23
 * 修改记录：
 *
 * @see 	 
 */
public class ApplyProcess extends AbstractDTO {
	
	/** ID*/
	private Long id;
	/** 流程定义(Id)*/
	private String procDefId;
	/** 状态*/
	private ApproveStatus status;
	/** 适用业务*/
	private ApproveBusiness business;
	/** 适用组织*/
	private Organization org;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getProcDefId() {
		return procDefId;
	}
	public void setProcDefId(String procDefId) {
		this.procDefId = procDefId;
	}
	public ApproveBusiness getBusiness() {
		return business;
	}
	public void setBusiness(ApproveBusiness business) {
		this.business = business;
	}
	public Organization getOrg() {
		return org;
	}
	public void setOrg(Organization org) {
		this.org = org;
	}
	public ApproveStatus getStatus() {
		return status;
	}
	public void setStatus(ApproveStatus status) {
		this.status = status;
	}
	
}
