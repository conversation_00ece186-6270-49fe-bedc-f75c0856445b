import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TestDate {
	public static Date date = null;

	public static DateFormat dateFormat = null;

	public static Calendar calendar = null;
	
	public static void main(String[] args) throws Exception {
		Date toDate = new Date();
		Date fromDate = null;
		calendar = Calendar.getInstance();
		calendar.setTime(new Date());
		calendar.add(Calendar.MONTH, -2);
		fromDate = calendar.getTime();
		while (fromDate.compareTo(toDate) < 0){
			
			System.out.println(formatDate(fromDate));
			calendar.add(Calendar.DAY_OF_MONTH,1);
			fromDate = calendar.getTime();
		}
		

	}
	
	
	/**
	 * 功能描述：常用的格式化日期
	 * 
	 * @param date
	 *            Date 日期
	 * @return String 日期字符串 yyyy-MM-dd格式
	 */
	public static String formatDate(Date date) {
		return formatDateByFormat(date, "yyyy-MM-dd");
	}

	/**
	 * 功能描述：以指定的格式来格式化日期
	 * 
	 * @param date
	 *            Date 日期
	 * @param format
	 *            String 格式
	 * @return String 日期字符串
	 */
	public static String formatDateByFormat(Date date, String format) {
		String result = "";
		if (date != null) {
			try {
				SimpleDateFormat sdf = new SimpleDateFormat(format);
				result = sdf.format(date);
			} catch (Exception ex) {
				ex.printStackTrace();
			}
		}
		return result;
	}
}
