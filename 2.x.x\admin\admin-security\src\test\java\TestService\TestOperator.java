package TestService;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.org.service.OrganizationService;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.service.OperatorService;
import com.jsdz.admin.security.utils.HqlGenerator;
import com.jsdz.admin.security.utils.HqlUtil;
import com.jsdz.core.AjaxResult;

import net.sf.json.JSONArray;

@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { "classpath:/testApplicationContext-common.xml" })

public class TestOperator {
	SimpleDateFormat sdf =   new SimpleDateFormat( "yyyy-MM-dd" );
	
	@Autowired
	private OperatorService operatorService;
	@Autowired
	private  OrganizationService organizationService;
	
	@Test
	public void testLocateOperatorById(){
		Operator o = null;
		try{
			 o = operatorService.locateOperatorById(1L);
		}catch(Exception e){
			System.out.println("【错误】" + e);
		}
		
		
		if (o.getEmployees() != null){
			System.out.println("【values】" + o.getEmployees().getOrganization() == null?null:o.getEmployees().getOrganization().getOrgName());
/*			this.setEmployeesId(src.getEmployees().getId());
			this.setName(src.getEmployees().getIdNum());
			this.setWorkNumber(src.getEmployees().getWorkNumber());
			this.setOrganizationId(src.getEmployees().getOrganization() == null?null:src.getEmployees().getOrganization().getId());
			this.setOrganization(src.getEmployees().getOrganization() == null?null:src.getEmployees().getOrganization().getOrgName());
			this.setDepartmentId(src.getEmployees().getDepartment() == null?null:src.getEmployees().getDepartment().getId());
			this.setDepartment(src.getEmployees().getDepartment() == null?null:src.getEmployees().getDepartment().getName());
			this.setPositionId(src.getEmployees().getPosition() == null?null:src.getEmployees().getPosition().getId());
			this.setPosition(src.getEmployees().getPosition() == null?null:src.getEmployees().getPosition().getPosition());
*/		}

//		OperatorBean ob = new OperatorBean();
		
		
		//JSONArray pJson = JSONArray.fromObject(o);//将java对象转换为json对象
		//System.out.println("【Operator】" +  pJson.toString());//将json对象转换为字符串
		//JSONArray vJson = JSONArray.fromObject(values);//将java对象转换为json对象
		//System.out.println("【values】" +  vJson.toString() );//将json对象转换为字符串
		
	}
	
	
/*	
	public void aaa(){
		ArrayList<String> sm = new ArrayList();
		//ArrayList al=new ArrayList();  
		sm.add("a");
		String[] sa = (String[]) sm.toArray(new String[0]);
		setfild(sa);
	}
	
	public void setfild(String[] s){
		return;
	}
	@Test
	public void getField(){
		   Operator o = new Operator();
		   //o.setName("王洪军");
		   o.setLoginName("zh");
		   //o.setMale("男");
		   
		   //System.out.println(o.getClass().getName());
		   HqlUtil hu = HqlGenerator.generateParams(o);
		   List<Operator> os = operatorService.findOperatorsByParam(
				   hu.getHqlString(), 
				   hu.paramNames(), 
				   hu.values());
		   
		   if (os != null && os.size() > 0)
			   System.out.println(os.get(0).toString());
		   else
			   System.out.println("【Null】");
		   System.out.println(hu.toString());
		   //System.out.println(hu.getHqlString());
		   ;
	}
*/
		   
/*		   
		   Class clazz = Class.forName("com.jsdz.admin.security.model.Employees");//根据类名获得其对应的Class对象 写上你想要的类名就是了 注意是全名 如果有包的话要加上 比如java.Lang.String
		   Field[] fields = clazz.getDeclaredFields();//根据Class对象获得属性 私有的也可以获得
		   for(Field f : fields) {
			   f.setAccessible(true); //设置些属性是可以访问的
			   System.out.println(f.getType().getName());//打印每个属性的类型名字
			   System.out.println("name = " + f.getName());
			   System.out.println("decl class = " + f.getDeclaringClass());
			   System.out.println("type = " + f.getType());
			   System.out.println(f.getName() + " = " + f.get(o));
			   System.out.println("-----");  
			   
		   }
		   //if ("java.lang.Class".equals(clazz.getClass().getName())){
			//   return ;
		   //}
		   System.out.println("*****************ParentClass:" + clazz.getSuperclass().getName() + "*****************");
		   Class parentClass = Class.forName(clazz.getSuperclass().getName());
		   Field[] fs = parentClass.getDeclaredFields();//根据Class对象获得属性 私有的也可以获得
		   for(Field f1 : fs) {
			   f1.setAccessible(true); //设置些属性是可以访问的
			   System.out.println(f1.getType().getName());//打印每个属性的类型名字
			   System.out.println("name = " + f1.getName());
			   System.out.println("decl class = " + f1.getDeclaringClass());
			   System.out.println("type = " + f1.getType());
			   System.out.println(f1.getName() + " = " + f1.get(o));
			   System.out.println("-----");  
		   }
		   
		} catch(Exception e) {
		   e.printStackTrace();
		}
*/		

		   
	//@Test
	@Transactional
	public void add() throws ParseException
	{
		/*
		try{
			
			Organization o1 = organizationService.findOrganizationByParam("from Organization O where id=:id",
					     new String[]{"id"}, new Object[]{(long)1});
			
			Operator operator = new Operator();
			operator.setName("黄仲扬");
			operator.setBirthDate(sdf.parse("1986-01-10"));
			operator.setLoginName("hzy");
			operator.setPassword("888888");
			operator.setOrganization(o1);
			
			AjaxResult result = operatorService.addOperator(operator);
			if (null!=result){
				System.out.println("【"+result.getMsg()+"】");
			}else{
				System.out.println("【resut==null】");
			}			
		}catch (ParseException e) {
			e.printStackTrace();
		}
		*/
	}

/*	@Test
	@Transactional
	public void uodate() throws ParseException
	{
			
			List<Operator> os = operatorService.findOperatorsByParam(
					"from Operator o where o.id=:id",
					new String[]{"id"},
					new Object[]{(long)3}
					);
			Operator operator = null;
			if (null != os && os.size() >0 ){
				operator = os.get(0);
			}
			operator.setMale("男");
			AjaxResult result = operatorService.updateOperator(operator);
			if (null!=result){
				System.out.println("【"+result.getMsg()+"】");
			}else{
				System.out.println("【resut==null】");
			}			
	}*/	
	
/*	@Test
	@Transactional
	public void login()
	{
		Operator operator = new Operator();
		operator.setId((long)1);
		operator.setLoginName("hhj");
		operator.setPassword("123456");
		AjaxResult result = operatorService.login(operator);
		if (null!=result){
			System.out.println("【"+result.getMsg()+"】");
		}else{
			System.out.println("【resut==null】");
		}
			
	}	*/
	
}
