package com.jsdz.admin.security.bean;

import java.io.Serializable;

import com.jsdz.admin.security.model.Post;

/**
 * 
 * @类名: PostBean
 * @说明: Post封装类
 *
 * @author: kenny
 * @Date	2017年12月30日下午4:21:41
 * 修改记录：
 *
 * @see
 */
public class PostBean implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -3363376973915635236L;
	
	private Long id;
	private String post;//岗位
	private String description;//说明
	private Long parId; //上级岗位
	private String parPost; //上级岗位
	private String path;//岗位
	private Integer index; //显示顺序
	public void assign(Post src){
		this.setId(src.getId());
		this.setPost(src.getPost());
		this.setDescription(src.getDescription());
		this.setPath(src.getPath());
		this.setIndex(src.getIndex());
		if (src.getParentPost() != null){
			this.setParId(src.getParentPost().getId());
			this.setParPost(src.getParentPost().getPost());
		}
	}
	
	public void assignTo(Post dest){
		dest.setId(this.getId());
		dest.setPost(this.getPost());
		dest.setDescription(this.getDescription());
		dest.setPath(this.getPath());
		dest.setIndex(this.getIndex());
		if (this.getParId() != null){
			Post p = new Post();
			dest.setParentPost(p);
		}
	}
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getPost() {
		return post;
	}
	public void setPost(String post) {
		this.post = post;
	}
	public Long getParId() {
		return parId;
	}
	public void setParId(Long parId) {
		this.parId = parId;
	}
	public String getParPost() {
		return parPost;
	}
	public void setParPost(String parPost) {
		this.parPost = parPost;
	}
	public String getPath() {
		return path;
	}
	public void setPath(String path) {
		this.path = path;
	}
	public Integer getIndex() {
		return index;
	}
	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	
}
