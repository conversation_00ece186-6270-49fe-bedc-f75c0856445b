package com.jsdz.digitalevidence.alarm.alarm110.model;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.org.model.Organization;

/**
 * @类名: AlarmInfo
 * @说明: 警情反馈单
 *
 * <AUTHOR>
 * @Date	 2018年8月28日 上午09:11:05
 * 修改记录：
 *
 * @see 	 
 */
public class AlarmFeedback implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 4198594943037621958L;
	
	private Long id;
	private AlarmInfo alarmInfo; //警情(接警单)
	private String feedbackCode;//反馈单号
	private Organization feedbackOrg;//反馈单位
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date feedbackTime;//反馈时间
	private String alarmPolices;//处警人多个以,号隔开
	private String alarmResult;//处理结果
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createTime;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public AlarmInfo getAlarmInfo() {
		return alarmInfo;
	}
	public void setAlarmInfo(AlarmInfo alarmInfo) {
		this.alarmInfo = alarmInfo;
	}
	public String getFeedbackCode() {
		return feedbackCode;
	}
	public void setFeedbackCode(String feedbackCode) {
		this.feedbackCode = feedbackCode;
	}
	public Organization getFeedbackOrg() {
		return feedbackOrg;
	}
	public void setFeedbackOrg(Organization feedbackOrg) {
		this.feedbackOrg = feedbackOrg;
	}
	public Date getFeedbackTime() {
		return feedbackTime;
	}
	public void setFeedbackTime(Date feedbackTime) {
		this.feedbackTime = feedbackTime;
	}
	public String getAlarmPolices() {
		return alarmPolices;
	}
	public void setAlarmPolices(String alarmPolices) {
		this.alarmPolices = alarmPolices;
	}
	public String getAlarmResult() {
		return alarmResult;
	}
	public void setAlarmResult(String alarmResult) {
		this.alarmResult = alarmResult;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	
}
