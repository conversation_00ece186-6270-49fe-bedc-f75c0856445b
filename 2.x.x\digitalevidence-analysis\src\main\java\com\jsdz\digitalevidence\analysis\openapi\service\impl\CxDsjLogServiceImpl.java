package com.jsdz.digitalevidence.analysis.openapi.service.impl;

/**
 * 
 * @类名: CxDsjLogServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2017-06-09 16:03:20
 * 修改记录：
 *
 * @see
*/

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.jsdz.digitalevidence.analysis.openapi.dao.CxDsjLogDao;
import com.jsdz.digitalevidence.analysis.openapi.model.CxDsjLog;
import com.jsdz.digitalevidence.analysis.openapi.service.CxDsjLogService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

//@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@Service("CxDsjLogServiceImpl")
public class CxDsjLogServiceImpl implements CxDsjLogService {

	@Autowired
	private CxDsjLogDao cxDsjLogDao;

	//新增
	public AjaxResult addCxDsjLog(CxDsjLog cxDsjLog) {
		AjaxResult result = new AjaxResult();
		cxDsjLogDao.addCxDsjLog(cxDsjLog);
		result.setSuccess(true);
		result.setMsg("新增保存成功。");
		return result; 
	}

	//修改
	public AjaxResult updateCxDsjLog(CxDsjLog cxDsjLog) {
		AjaxResult result = new AjaxResult();
		cxDsjLogDao.updateCxDsjLog(cxDsjLog);
		result.setSuccess(true);
		result.setMsg("修改成功。");
		return result; 
	}

	//删除
	public AjaxResult deleteCxDsjLog(CxDsjLog cxDsjLog) {
		AjaxResult result = new AjaxResult();
		cxDsjLogDao.deleteCxDsjLog(cxDsjLog);
		result.setSuccess(true);
		result.setMsg("删除成功。");
		return result; 
	}

	//按id查询(游离数据)
	public CxDsjLog findCxDsjLogById(Long id){

		return cxDsjLogDao.findCxDsjLogById(id);
	}

	//按 id 查询
	public CxDsjLog locateCxDsjLogById(Long id) {
		return cxDsjLogDao.locateCxDsjLogById(id);
	}

	//单个查询
	public CxDsjLog findCxDsjLogByParam(String queryStr, String[] paramNames, Object[] values) {
		return cxDsjLogDao.findCxDsjLogByCondition(queryStr,paramNames,values);
	}

	//查询全部
	public List<CxDsjLog> findAllCxDsjLogs() {
		return cxDsjLogDao.findAllCxDsjLogs();
	}

	//列表查询
	public List<CxDsjLog> findCxDsjLogsByParam(String queryStr, String[] paramNames, Object[] values) {
		return cxDsjLogDao.findCxDsjLogsByCondition(queryStr,paramNames,values);
	}

	//分页查询
	public Page<CxDsjLog> findCxDsjLogsOnPage(Page<CxDsjLog> page, String queryStr, String[] paramNames, Object[] values) {
		Page<CxDsjLog> pos = cxDsjLogDao.findCxDsjLogsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

}
