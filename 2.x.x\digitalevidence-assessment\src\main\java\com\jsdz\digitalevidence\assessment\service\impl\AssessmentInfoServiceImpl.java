/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.service.impl;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Env;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentInfoDocBean;
import com.jsdz.digitalevidence.assessment.bean.DocBean;
import com.jsdz.digitalevidence.assessment.dao.mapper.AssessmentInfoMapper;
import com.jsdz.digitalevidence.assessment.model.AssessmentDocInfo;
import com.jsdz.digitalevidence.assessment.model.AssessmentInfo;
import com.jsdz.digitalevidence.assessment.service.AssessmentInfoService;
import com.jsdz.digitalevidence.document.utils.EnumUtils;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @类名: AssessmentInfoServiceImpl
 * @说明: 考核应用服务实现 查询 考核 报告
 */
@Service("com.jsdz.digitalevidence.assessment.service.impl.AssessmentInfoServiceImpl")
public class AssessmentInfoServiceImpl implements AssessmentInfoService {
	SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	SimpleDateFormat sdf1 = new SimpleDateFormat("yyyyMMddHHmmss");

	private final Logger logger = Logger.getLogger(this.getClass());

	@Autowired
	private AssessmentInfoMapper assessmentInfoMapper;

	@Override
	public List<DocBean> getAssessmentInfo(AssessmentInfo param, PerLvlBean perLvlBean) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("empId", perLvlBean.getEmpId());
		paramMap.put("orgPath", perLvlBean.getOrgPath());
		paramMap.put("permissionLevel", perLvlBean.getPermissionLevel());
		paramMap.put("policeCode", perLvlBean.getLoginName());

		// Map<Object, Object> assessmentInfoDocBean = new HashMap<>();
		List<DocBean> assessmentInfoDocList = new ArrayList<>();
		/*
		 * String assessmentId; // 获取人员信息 if (paramMap.get("empId") != null &&
		 * paramMap.get("empId") != "") {
		 *
		 * assessmentId = paramMap.get("empId") + sdf1.format(new Date()); }
		 * else { assessmentId = "000000" + sdf1.format(new Date()); }
		 * assessmentInfoDocBean.put("assessmentId", assessmentId);
		 */
		try {
			if (Env.getProperty("Document.include.logfile").equals("0")) {
				paramMap.put("excludeCate", 3);
			}
			if (param.getOrgPath() != null && param.getOrgPath().toString() != "") {
				paramMap.put("orgPath", param.getOrgPath());
			}
			if (param.getAeeseeImpLevel() != null && param.getAeeseeImpLevel().toString() != "") {
				paramMap.put("aeeseeImpLevel", param.getAeeseeImpLevel());
			}
			if (perLvlBean.getOrgPath() != null && perLvlBean.getOrgPath().toString() != "") {
				paramMap.put("assessmentOrgPath", perLvlBean.getOrgPath());
			}
			if (param.getPoliceCode() != null && param.getPoliceCode().toString() != "") {
				paramMap.put("policeCode", param.getPoliceCode());
			}

			if (param.getUploadEndTime() != null) {
				paramMap.put("uploadEnd", param.getUploadEndTime());
			}

			if (param.getUploadStrTime() != null) {
				paramMap.put("uploadSta", param.getUploadStrTime());
			}

			if (param.getCreateEndTime() != null) {
				paramMap.put("createEnd", param.getCreateEndTime());
			}

			if (param.getCreateStrTime() != null) {
				paramMap.put("createSta", param.getCreateStrTime());
			}

			if (param.getAeeseeImpLevel() != null) {
				paramMap.put("aeeseeImpLevel", param.getAeeseeImpLevel());
			} else {
				paramMap.put("aeeseeImpLevel", 4);
			}

			/*
			 * if (param.getEndTime() != null && param.getEndTime().toString()
			 * != "") { paramMap.put("endUT",
			 * sdf.format(param.getUploadEndTime())); paramMap.put("startUT",
			 * sdf.format(param.getUploadStrTime())); } if (param.getEndTime()
			 * != null && param.getEndTime().toString() != "") {
			 * paramMap.put("startCT", sdf.format(param.getCreateStrTime()));
			 * paramMap.put("endCT", sdf.format(param.getCreateEndTime())); }
			 */

			// 处理枚举类型
			Object obj = param.getImpLevel();
			if (obj != null) {
				paramMap.put("impLevel", EnumUtils.getImplvlOrd(obj.toString()));
			}
			// 文档类型
			obj = param.getCate();
			if (obj != null) {
				paramMap.put("cate", EnumUtils.getDocumentCate(obj.toString()));
			}

			if (param.getPageSize() != null && param.getOffset() != null) {
				paramMap.put("offset", param.getOffset());
				paramMap.put("pageSize", param.getPageSize());
			} else {
				paramMap.put("offset", 0);
				paramMap.put("pageSize", 10);
			}

			assessmentInfoDocList = assessmentInfoMapper.findAssessmetDocuments(paramMap);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return assessmentInfoDocList;
	}

	@Override
	public int getAssessmentInfoCount(AssessmentInfo param, PerLvlBean perLvlBean) {
		Map<String, Object> paramMap = new HashMap<String, Object>();
		paramMap.put("empId", perLvlBean.getEmpId());
		paramMap.put("path", perLvlBean.getOrgPath());
		paramMap.put("permissionLevel", perLvlBean.getPermissionLevel());

		if (Env.getProperty("Document.include.logfile").equals("0")) {
			paramMap.put("excludeCate", 3);
		}
		if (param.getOrgPath() != null && param.getOrgPath().toString() != "") {
			paramMap.put("orgPath", param.getOrgPath());
		}
		if (param.getAeeseeImpLevel() != null && param.getAeeseeImpLevel().toString() != "") {
			paramMap.put("aeeseeImpLevel", param.getAeeseeImpLevel());
		}
		if (perLvlBean.getOrgPath() != null && perLvlBean.getOrgPath().toString() != "") {
			paramMap.put("assessmentOrgPath", perLvlBean.getOrgPath());
		}
		if (param.getPoliceCode() != null && param.getPoliceCode().toString() != "") {
			paramMap.put("policeCode", param.getPoliceCode());
		}

		if (param.getUploadEndTime() != null) {
			paramMap.put("uploadEnd", param.getUploadEndTime());
		}

		if (param.getUploadStrTime() != null) {
			paramMap.put("uploadSta", param.getUploadStrTime());
		}

		if (param.getCreateEndTime() != null) {
			paramMap.put("createEnd", param.getCreateEndTime());
		}

		if (param.getCreateStrTime() != null) {
			paramMap.put("createSta", param.getCreateStrTime());
		}

		/*
		 * if(param.getPageSize() != null && param.getOffset() != null){
		 * paramMap.put("offset", param.getOffset()); paramMap.put("pageSize",
		 * param.getPageSize()); }else { paramMap.put("offset", 0);
		 * paramMap.put("pageSize", 10); }
		 */

		// 处理枚举类型
		Object obj = param.getImpLevel();
		if (obj != null) {
			paramMap.put("impLevel", EnumUtils.getImplvlOrd(obj.toString()));
		}
		// 文档类型
		obj = param.getCate();
		if (obj != null) {
			paramMap.put("cate", EnumUtils.getDocumentCate(obj.toString()));
		}
		int count = assessmentInfoMapper.getAssessmentInfoCount(paramMap);
		return count;

	}

	/**
	 * 保存抽查视频信息
	 */
	@Override
	public AjaxResult saveAssessmentInfo(List<AssessmentDocInfo> assessmentDocBeanList, Map<String, Object> paramMap) {
		AjaxResult result = new AjaxResult(300, false, "", null);
		List<AssessmentDocInfo> assessmentInfoList = new ArrayList<AssessmentDocInfo>();
		try {
			// 保存抽查视频信息
			// 保存抽查信息关联编号
			Integer empId = Integer.valueOf(paramMap.get("empId") + "");
			// 获取人员信息
			Map<String, Object> empInfo = assessmentInfoMapper.getEmpId(empId);

			PerLvlBean perLvlBean = new PerLvlBean();
			perLvlBean.setLoginName((String) empInfo.get("workNumber"));

			// 封装抽查信息关联表信息
			AssessmentDocInfo assessmentDocInfo = new AssessmentDocInfo();
			/*
			 * //生成抽查ID ：抽查民警警号+当前时间戳组成 String assessmentId; if
			 * (paramMap.get("empId") != null && paramMap.get("empId") != "") {
			 *
			 * assessmentId = paramMap.get("empId") + sdf1.format(new Date()); }
			 * else { assessmentId = "000000" + sdf1.format(new Date()); }
			 * assessmentDocInfo.put("assessmentId", assessmentId);
			 */
			// 统计本组抽得分
			AtomicReference<Double> assessmentScore = new AtomicReference<>(0.0);
			assessmentDocBeanList.forEach(assessmentDocBean -> {

				if (assessmentDocBean.getAssessmentDocId() != null) {
					assessmentScore.updateAndGet(v -> v + assessmentDocBean.getAssessmentScore());
					assessmentDocInfo.setAssessmentDocId(assessmentDocBean.getAssessmentDocId());
				}
				if (assessmentDocBean.getAssessmentDocId() != null) {
					assessmentDocInfo.setAssessmentDocId(assessmentDocBean.getAssessmentDocId());
				}
				if (assessmentDocBean.getAssessmentPoliceId() != null) {
					assessmentDocInfo.setAssessmentPoliceId(assessmentDocBean.getAssessmentPoliceId());
				}
				if (assessmentDocBean.getAssessmentRemarks() != null) {
					assessmentDocInfo.setAssessmentRemarks(assessmentDocBean.getAssessmentRemarks());
				}
				if (assessmentDocBean.getAssessmentScore() != null) {
					assessmentDocInfo.setAssessmentScore(assessmentDocBean.getAssessmentScore());
				}
				if (assessmentDocBean.getAssessmentId() != null) {
					assessmentDocInfo.setAssessmentId(assessmentDocBean.getAssessmentId());
				}
				if (assessmentDocBean.getAssessmentCreateTime() == null) {
					assessmentDocInfo.setAssessmentCreateTime(new Date());
					Integer saveAssessmentDocInfos = assessmentInfoMapper.saveAssessmentDocInfo(assessmentDocInfo);
				} else {
					assessmentDocInfo.setAssessmentUpdateTime(new Date());
					Integer saveAssessmentDocInfos = assessmentInfoMapper.updateAssessmentDocInfo(assessmentDocInfo);
				}
				assessmentInfoList.add(assessmentDocInfo);
			});

			int i = 0;
			DecimalFormat df = new DecimalFormat("#.00");

			double b = 2.0;
			if (assessmentInfoList.size() > 0) {
				Double aDouble = assessmentScore.get();
				b = (aDouble / assessmentInfoList.size());
			}
			// 封装抽查表信息
			AssessmentInfo assessmentInfo = new AssessmentInfo();
			// Integer saveAssessmentDocInfos =
			// assessmentInfoMapper.saveAssessmentDocInfos1(assessmentInfoList);
			// 保存抽查评分信息
			if (empInfo.get("orgId") != null && empInfo.get("orgId") != "") {
				assessmentInfo.setAssessmentOrg(Integer.parseInt(empInfo.get("orgId") + ""));
			}
			if (empInfo.get("empId") != null && empInfo.get("empId") != "") {
				assessmentInfo.setAssessmentPoliceId(Integer.valueOf(empInfo.get("empId") + ""));
			}
			assessmentInfo.setAssessmentId(assessmentDocBeanList.get(0).getAssessmentId());

			assessmentInfo.setAssessmentScore(b);
			// 通过单条得分累积获取本次评分总分并获取平均分
			if (assessmentDocBeanList.get(0).getAssessmentCreateTime() == null) {
				assessmentInfo.setAssessmentTime(new Date());
				Integer assessmentInfos = assessmentInfoMapper.saveAssessmentInfo(assessmentInfo);
			} else {
				assessmentInfo.setAssessmentUpdateTime(new Date());
				Integer assessmentInfos = assessmentInfoMapper.updateAssessmentInfo(assessmentInfo);
			}
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("新增保存成功" + assessmentInfoList.size());
		} catch (Exception e) {
			result.setMsg(e.toString());
		}
		return result;
	}

	/**
	 * 通过条件获取获取考核信息
	 *
	 * @param param
	 * @throws Exception
	 * <AUTHOR>
	 */
	@Override
	public List<AssessmentInfoDocBean> getAssessmentInfosByAssess(AssessmentInfo param, Map<String, Object> paramMap) {

		List<AssessmentInfoDocBean> assessmentInfoDocBeanList = new ArrayList<>();
		if (param.getAssessmentPoliceId() != null && param.getAssessmentPoliceId().toString() != "") {
			paramMap.put("empId", param.getAssessmentPoliceId());
		}
		if (param.getOrgPath() != null && param.getOrgPath().toString() != "") {
			paramMap.put("path", param.getOrgPath());
		}
		if (param.getCreateStrTime() != null && param.getEndTime().toString() != "") {
			paramMap.put("createEndTime", sdf.format(param.getCreateEndTime()));
			paramMap.put("createStrTime", sdf.format(param.getCreateStrTime()));
		}
		if (param.getUploadEndTime() != null && param.getEndTime().toString() != "") {
			paramMap.put("uploadEndTime", sdf.format(param.getUploadEndTime()));
			paramMap.put("uploadStrTime", sdf.format(param.getUploadStrTime()));
		}
		if (param.getPage() != null && param.getPage() >= 0 && param.getPageSize() > 0) {
			paramMap.put("offset", param.getPage());
			paramMap.put("pageSize", param.getPageSize());
		} else {
			paramMap.put("offset", 0);
			paramMap.put("pageSize", 10);
		}
		assessmentInfoDocBeanList = assessmentInfoMapper.getAssessmentInfosByAssess(paramMap);
		return assessmentInfoDocBeanList;
	}

	@Override
	public Integer getAssessmentInfosCounts(AssessmentInfo param, Map<String, Object> paramMap) {

		if (param.getAssessmentPoliceId() != null && param.getAssessmentPoliceId().toString() != "") {
			paramMap.put("empId", param.getAssessmentPoliceId());
		}
		if (param.getOrgPath() != null && param.getOrgPath().toString() != "") {
			paramMap.put("path", param.getOrgPath());
		}
		if (param.getCreateStrTime() != null && param.getEndTime().toString() != "") {
			paramMap.put("createEndTime", sdf.format(param.getCreateEndTime()));
			paramMap.put("createStrTime", sdf.format(param.getCreateStrTime()));
		}
		if (param.getUploadEndTime() != null && param.getEndTime().toString() != "") {
			paramMap.put("uploadEndTime", sdf.format(param.getUploadEndTime()));
			paramMap.put("uploadStrTime", sdf.format(param.getUploadStrTime()));
		}
		if (param.getPage() != null && param.getPage() >= 0 && param.getPageSize() > 0) {
			paramMap.put("offset", param.getPage());
			paramMap.put("pageSize", param.getPageSize());
		} else {
			paramMap.put("offset", 0);
			paramMap.put("pageSize", 10);
		}

		Integer assessmentInfoDocBeanList = assessmentInfoMapper.getAssessmentInfosCounts(paramMap);
		return assessmentInfoDocBeanList;
	}

	/**
	 * 通过考核编号获取获取考核信息
	 *
	 * @param param
	 * @throws Exception
	 * <AUTHOR>
	 */
	@Override
	public AssessmentInfoDocBean getAssessmentInfoByAssessId(AssessmentInfo param, Map<String, Object> paramMap) {
		AssessmentInfoDocBean assessmentInfoDocBean = new AssessmentInfoDocBean();
		if (param.getAssessmentPoliceId() != null && param.getAssessmentPoliceId().toString() != "") {
			paramMap.put("empId", param.getAssessmentPoliceId());
		}
		if (param.getOrgPath() != null && param.getOrgPath().toString() != "") {
			paramMap.put("path", param.getOrgPath());
		}
		if (param.getAssessmentId() != null && param.getAssessmentId().toString() != "") {
			paramMap.put("assessmentId", param.getAssessmentId());
		}
		if (param.getAssessmentPoliceId() != null && param.getAssessmentPoliceId().toString() != "") {
			paramMap.put("assessmentPoliceId", param.getAssessmentPoliceId());
		}
		/*
		 * if(param.getCreateStrTime()!=null &&
		 * param.getEndTime().toString()!=""){
		 * paramMap.put("createEndTime",sdf.format(param.getCreateEndTime()));
		 * paramMap.put("createStrTime", sdf.format(param.getCreateStrTime()));
		 * } if(param.getUploadEndTime()!=null &&
		 * param.getEndTime().toString()!=""){
		 * paramMap.put("uploadEndTime",sdf.format(param.getUploadEndTime()));
		 * paramMap.put("uploadStrTime", sdf.format(param.getUploadStrTime()));
		 * }
		 */
		if (param.getPage() != null && param.getPage() >= 0 && param.getPageSize() > 0) {
			paramMap.put("offset", param.getPage());
			paramMap.put("pageSize", param.getPageSize());
		} else {
			paramMap.put("offset", 0);
			paramMap.put("pageSize", 10);
		}
		List<AssessmentDocBean> assessmentDocBeanList = assessmentInfoMapper.getAssessmentInfoByAssessId(paramMap);
		assessmentInfoDocBean.setAssessmentDocBeans(assessmentDocBeanList);
		if(assessmentDocBeanList.size() > 0){
			AssessmentDocBean assessmentDocBean = assessmentDocBeanList.get(0);
			assessmentInfoDocBean.setAssessmentId(assessmentDocBean.getAssessmentId());
			assessmentInfoDocBean.setAssessmentScore(assessmentDocBean.getAssessmentScore());
			assessmentInfoDocBean.setAssessmentName(assessmentDocBean.getAssessmentName());
			assessmentInfoDocBean.setAssessmentPoliceCode(assessmentDocBean.getAssessmentPoliceCode());
			assessmentInfoDocBean.setAssessmentOrg(assessmentDocBean.getAssessmentOrgCode());
			assessmentInfoDocBean.setAssessmentOrgName(assessmentDocBean.getAssessmentOrgName());
			assessmentInfoDocBean.setAssessmentCreateTime(assessmentDocBean.getAssessmentTime());
		}
		return assessmentInfoDocBean;
	}

}
