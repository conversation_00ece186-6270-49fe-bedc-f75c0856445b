package com.jsdz.digitalevidence.statistics.service.impl;

import com.jsdz.admin.security.bean.PerLvlBean;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.statistics.bean.param.AbnormalParam;
import com.jsdz.digitalevidence.statistics.bean.param.SysDailyDeviceParam;
import com.jsdz.digitalevidence.statistics.mapper.SysAbnormalStatisticsMapper;
import com.jsdz.digitalevidence.statistics.model.SysAbnormalStatisticsModel;
import com.jsdz.digitalevidence.statistics.model.SysDailyDeviceModel;
import com.jsdz.digitalevidence.statistics.mapper.SysDailyDeviceMapper;
import com.jsdz.digitalevidence.statistics.service.ISysDailyDeviceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jsdz.digitalevidence.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-20
 */
@Service
public class SysDailyDeviceServiceImpl extends ServiceImpl<SysDailyDeviceMapper, SysDailyDeviceModel> implements ISysDailyDeviceService {

    @Resource
    private SysDailyDeviceMapper sysDailyDeviceMapper;

    @Override
    public Page<SysDailyDeviceModel> getDatas(SysDailyDeviceParam param, PerLvlBean permLvl) {
        Page<SysDailyDeviceModel> page = param.getPage();

        String orgPath = param.getOrgPath();
        if(StringUtils.isEmpty(orgPath)) {
            param.setOrgPath(permLvl.getOrgPath());
            Integer permissionLevel = permLvl.getPermissionLevel();
            param.setIncludeSub((permissionLevel<3?0:1)+"");
        }
        List<SysDailyDeviceModel> vAttendanceAllModels =sysDailyDeviceMapper.selectModels(param);
        Integer total =sysDailyDeviceMapper.selectModelCount(param);
        page.setTotal(total);
        page.setRows(vAttendanceAllModels);
        return page;
    }

    @Override
    public void startDailyDevice() {
        sysDailyDeviceMapper.startDailyDevice();
    }

    @Override
    public void addCaseFlow(Long id, String caseFlow) {
        sysDailyDeviceMapper.updateCaseFlow(id,caseFlow);
    }

}
