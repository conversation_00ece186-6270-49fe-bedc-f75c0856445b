package com.jsdz.admin.security.token;

import com.jsdz.admin.security.utils.JSDateFormatUtils;
import com.jsdz.digitalevidence.cache.encrypt.RSAHelper;
import com.jsdz.digitalevidence.cache.utils.Constant;
import com.jsdz.digitalevidence.cache.utils.SysSource;
import org.apache.log4j.Logger;

import java.util.Calendar;
import java.util.Date;

/**
 * @类名: Token.java
 * @说明:
 * @author: kenny
 * @Date 2017年6月2日下午3:35:37 修改记录：
 * @see
 */
public class Token {

	public static final Logger logger = Logger.getLogger(Token.class);
	
	@SuppressWarnings("unchecked")
	public static String genToken(){
		//取token有效时间
		int effective_second  =300;

		Object obj;
		if ((obj= SysSource.getInstance().hget(Constant.VAR_SYSCONFIG, "TOKEN_EFFECTIVE_SECOND"))!=null){
			effective_second = Integer.valueOf(obj.toString());
		}
		
		Calendar calendar = Calendar.getInstance ();
		calendar.setTime(new Date());
		calendar.add (Calendar.SECOND, effective_second);
        String inputStr = JSDateFormatUtils.formatDateTime(calendar.getTime());
		
        //加密
        try{
        	return RSAHelper.RsaEncrypt(inputStr);
        }catch(Exception e){
            logger.error("*** 加密错误" + e);
           return null;
        }
	}
	
	public static boolean checkToken(String token){
		try{
			
			String dateStr = RSAHelper.RsaDecrypt(token);
			//RSAHelper.testEnDe();
			Date tokenDate = JSDateFormatUtils.parseDateTime(dateStr);
			Date nowDate = new Date();
			if (tokenDate.getTime() <= nowDate.getTime()){
				logger.error("*** Token已过期 " + dateStr);
				return false;
			}
		}catch(Exception e){
			logger.error("*** 解密错误" + e);
			return false;
		}
		return true;
	}	
	
}
