package com.jsdz.admin.security.dao.impl;

import java.sql.SQLException;

/**
 *
 * @类名: OperatorRoleDaoImpl
 * @说明:
 * @author: kenny
 * @Date 2017-05-03 14:48:29
 * 修改记录：
 * @see
 * @see
 */

import java.util.List;
import java.util.Map;

import org.hibernate.HibernateException;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;
import com.jsdz.admin.security.dao.OperatorRoleDao;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.admin.security.model.OperatorRole;
import com.jsdz.core.Page;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import org.springframework.orm.hibernate3.HibernateCallback;

@Repository
public class OperatorRoleDaoImpl extends GenericEntityDaoHibernateImpl<OperatorRole,Long> implements OperatorRoleDao{
//public class OperatorRoleDaoImpl extends CommDaoImpl implements OperatorRoleDao{

	//新增
	public void addOperatorRole(OperatorRole operatorRole) {
		this.saveOrUpdate(operatorRole);
	}

	//删除
	public void deleteOperatorRole(OperatorRole operatorRole) {
		this.delete(operatorRole);
	}

	//修改
	public void updateOperatorRole(OperatorRole operatorRole) {
		this.saveOrUpdate(operatorRole);
	}
	
	//查询单个
	@SuppressWarnings("deprecation")
	public OperatorRole findOperatorRoleByCondition(String queryStr,String[] paramNames,Object[] values){
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		Query query = session.createQuery(queryStr);
		for (int i=0;i<values.length;i++){
			query.setParameter(paramNames[i], values[i]);
		}
		List<OperatorRole> ors= query.list();
		session.evict(ors);
		session.close();
		return null==ors || ors.size() <= 0?null:ors.get(0);
	}	
	/*	
		query.setString(name, val)
		int deleteds = session.createQuery(queryStr)
				.setString( "id", operator.getId().toString())
				.executeUpdate();
		session.close();

		
		List<OperatorRole> ops = this.findByNamedParam(queryStr,paramNames,values);
		//getHibernateTemplate().getSessionFactory();
		this.getSession().evict(ops);
		return null==ops || ops.size() <= 0? null:ops.get(0);
		
		//删除角色权限表中相关记录
		//SessionFactory sessionFactory = this.getSessionFactory();
		Session session = this.getSession();//sessionFactory.openSession();
		String hqlDelete = "delete OperatorRole o where o.operator.id = :id";
		int deleteds = session.createQuery(hqlDelete)
				.setString( "id", operator.getId().toString())
				.executeUpdate();
		session.close();
	*/	
		//
		/*
		Long data = getHibernateTemplate().execute(new HibernateCallback<Long>() {      	 
			@Override  
    	    public Long doInHibernate(Session session)throws HibernateException, SQLException {  
    	    	// 获取查询对象
				Query queryObject = session.createQuery(hqlTotal);
				// 置入参数
				if (values != null) {
					for (int i = 0; i < values.size(); i++) {
						applyNamedParameterToQuery(queryObject, params.get(i), values.get(i));
					}
				}
				return (Long)queryObject.uniqueResult();
    	    }  
    	});
    	*/		
	//}

	//查询
	public List<OperatorRole> findOperatorRolesByCondition(String queryStr,String[] paramNames,Object[] values){
		List<OperatorRole> ops = this.findByNamedParam(queryStr,paramNames,values);
		return ops;
	}

	//分页查询
	public Page<OperatorRole> findOperatorRolesOnPage(Page<OperatorRole> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<OperatorRole>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

	//删除该用户关联的所有角色
	public void deleteAllRoleByOperator(Operator operator) {
		//按用户删除角色关联
		SessionFactory sessionFactory = this.getSessionFactory();
		Session session = sessionFactory.openSession();
		String hqlDelete = "delete OperatorRole o where o.operator.id = :id";
		int deleteds = session.createQuery(hqlDelete)
				.setString( "id", operator.getId().toString())
				.executeUpdate();
		session.close();
	}
	
	//查询与该操作员关联的角色
	public List<OperatorRole> findRoleByOperator(Operator operator){
		return this.findByNamedParam(
					"from OperatorRole orl left join fetch orl.operator o"
					+ " left join fetch orl.role r where o.isDeleted = false and o.id = :id",
					new String[]{"id"},new Object[]{operator.getId()});
	}

	//取最大权限级别
	@Override
	public List<Map<String,Object>> findMaxPermission(Long userId){
		final String hql = 
				" select max(t1.roleLevel) roleLevel, max(t1.orgLevel) orgLevel from admin_operatorroles t "
				+ " left join admin_roles t1 on t.roleId = t1.id "
				+ " where t.operatorId = :id "
				+ " group by t.operatorId ";

		
		List<Map<String,Object>> data = getHibernateTemplate().execute(new HibernateCallback<List<Map<String,Object>>>() {
			@SuppressWarnings("unchecked")
			@Override
			public List<Map<String,Object>> doInHibernate(Session session) throws HibernateException, SQLException {
				// 获取查询对象
				Query queryObject = session.createSQLQuery(hql);
				// 置入参数
				applyNamedParameterToQuery(queryObject, "id", userId);
				queryObject.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
				return queryObject.list();
			}
		});
		
		return data;
	}
}
