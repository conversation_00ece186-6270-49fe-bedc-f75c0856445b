package com.jsdz.admin.security.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.jsdz.admin.org.dao.OrganizationDao;
import com.jsdz.admin.org.model.Organization;
import com.jsdz.admin.security.bean.RoleMenuBean;
import com.jsdz.admin.security.dao.EmployeesDao;
import com.jsdz.admin.security.dao.PoliceOrganizationDao;
import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.model.PoliceOrganization;
import com.jsdz.admin.security.model.Role;
import com.jsdz.admin.security.model.RoleOrganization;
import com.jsdz.admin.security.model.SystemMenu;
import com.jsdz.admin.security.service.PoliceOrganizationService;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;
import com.jsdz.reportquery.ReportQueryDao;
/**
 * 
 * @类名: PoliceOrganizationServiceImpl
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-12-19 15:29:11
 * 修改记录：
 *
 * @see
*/
@Service("PoliceOrganizationServiceImpl")
public class PoliceOrganizationServiceImpl implements PoliceOrganizationService {
	private final Logger logger = Logger.getLogger(this.getClass());

	@Autowired
	private PoliceOrganizationDao policeOrganizationDao;
	
	/** 动态查询Dao*/
	@Resource(name="rqDao")
	private ReportQueryDao<SystemMenu> reportQueryDao;	
	
	@Autowired
	private EmployeesDao employeesDao;
	
	@Autowired
	private OrganizationDao organizationDao;

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addPoliceOrganization(PoliceOrganization policeOrganization) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			//不存在则的增加
			if (this.findByOrgPolice(policeOrganization) != null)
				policeOrganizationDao.addPoliceOrganization(policeOrganization);
			
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("新增保存成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updatePoliceOrganization(PoliceOrganization policeOrganization) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			policeOrganizationDao.updatePoliceOrganization(policeOrganization);
			result.setCode(200);
			result.setSuccess(true);
			result.setMsg("修改成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deletePoliceOrganization(PoliceOrganization policeOrganization) {
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			policeOrganizationDao.deletePoliceOrganization(policeOrganization);
			result.setSuccess(true);
			result.setCode(200);
			result.setMsg("删除成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
	}

	/** 
 	 * 按id查询(游离数据)
	 */ 
 	public PoliceOrganization findPoliceOrganizationById(Long id){

		return policeOrganizationDao.findPoliceOrganizationById(id);
	}

	/** 
 	 * 按 id 查询
	 */ 
 	public PoliceOrganization locatePoliceOrganizationById(Long id) {
		return policeOrganizationDao.locatePoliceOrganizationById(id);
	}

	/** 
 	 * 单个查询
	 */ 
 	public PoliceOrganization findPoliceOrganizationByParam(String queryStr, String[] paramNames, Object[] values) {
		return policeOrganizationDao.findPoliceOrganizationByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 查询全部
	 */ 
 	public List<PoliceOrganization> findAllPoliceOrganizations() {
		return policeOrganizationDao.findAllPoliceOrganizations();
	}

	/** 
 	 * 列表查询
	 */ 
 	public List<PoliceOrganization> findPoliceOrganizationsByParam(String queryStr, String[] paramNames, Object[] values) {
		return policeOrganizationDao.findPoliceOrganizationsByCondition(queryStr,paramNames,values);
	}

	/** 
 	 * 分页查询
	 */ 
 	public Page<PoliceOrganization> findPoliceOrganizationsOnPage(Page<PoliceOrganization> page, String queryStr, String[] paramNames, Object[] values) {
		Page<PoliceOrganization> pos = policeOrganizationDao.findPoliceOrganizationsOnPage(page, queryStr, paramNames, values);
		return pos;
	}

    /**
     * 查询所有的单位给角色设置
     * @param EmployeesId
     * @return
     * @throws Exception
     */
	@Override
	public List<RoleMenuBean> findAllOrganitionForEmployessSet(Long employeesId) throws Exception {
		Map<String,Object> map = new HashMap<String,Object>();
    	if (employeesId != null)
    		map.put("employeesId", employeesId);
    	
    	String[] paramNames = map.keySet().toArray(new String[0]);
		Object[] values = map.values().toArray(new Object[0]);
		List<RoleMenuBean> result = new ArrayList<RoleMenuBean>(); 
		List<Object> list= reportQueryDao.QueryNamedAllDataSQL("findAllOrganitionForEmployessSet",paramNames,values);
		for(Object o : list ){
			result.add((RoleMenuBean)o);
		}
		return result;
	}
	
    /**
     * 设置警员单位权限
     * @param orgIds
     * @param empId
     * @return
     */
    @Transactional
    public AjaxResult setPoliceOrganization(String orgIds,Long empId){
    	AjaxResult result = new AjaxResult(0,false,"",null);
   	 try{
   		//删除原来数据
   		String queryStr = "from PoliceOrganization r where r.police.id = :id";
   		List<PoliceOrganization> list = 
   				policeOrganizationDao.findPoliceOrganizationsByCondition(queryStr, new String[]{"id"},new Object[]{empId});
   		
   		if (list != null && list.size() > 0){
   			policeOrganizationDao.batchDelete(list);
   		}
   		
   		if (orgIds == null || orgIds.equals("")){
   			result.setSuccess(true);
   			result.setMsg("操作成功");
   			return result;
   		}
   		
   		//写入
   		Employees police = employeesDao.get(empId);
   		String orgNames = "";
  	   	String[] orgArray = orgIds.split(",");
  	   	List<PoliceOrganization> insertList = new ArrayList<PoliceOrganization>();
  	   	for (String sorgId:orgArray){
  	   		Long orgId = Long.valueOf(sorgId);
  	   		Organization org = organizationDao.get(orgId);
  	   		
  	   		PoliceOrganization pg = new PoliceOrganization();
  	   		pg.setPolice(police);
  	   	    pg.setOrganization(org);
  	   	    insertList.add(pg);
  	   	    
  	   	    orgNames = orgNames + ";" + org.getOrgName();
  	   	}
  	   	policeOrganizationDao.batchInsert(insertList);
  	   	//role.setOrgNames(orgNames);
  	   	//roleDao.updateRole(role);
   	 }catch(Exception e){
   		logger.error("内部错误：" + e);
   		 result.setMsg("内部错误：" + e);
   		 result.setSuccess(false);
   	 }
   	 result.setSuccess(true);
   	 return result;
    }
    /**
     * 设置警员单位
     * @param oldObj
     * @param newObj
     * @return
     */
	public AjaxResult changeOrgPolice(
			PoliceOrganization oldObj,PoliceOrganization newObj){
		AjaxResult result = new AjaxResult(300,false,"",null);
		try{
			if (oldObj != null && oldObj.equals(newObj))
				return result;
			
			if (oldObj !=null && oldObj.getOrganization() != null && oldObj.getPolice() != null){
				PoliceOrganization pp = this.findByOrgPolice(oldObj);
				if (pp != null)
					this.deletePoliceOrganization(pp);				
			}

			if (newObj !=null && newObj.getOrganization() != null && newObj.getPolice() != null){
				policeOrganizationDao.addPoliceOrganization(newObj);
			}
			
			
			result.setSuccess(true);
			result.setCode(200);
			result.setMsg("修改成功。");
		}catch(Exception e){
			result.setMsg(e.toString());
		}
		return result; 
		
	}
	
	/**
	 * 按单位和警员查询
	 * @param policeOrganization
	 * @return
	 */
	private PoliceOrganization findByOrgPolice(PoliceOrganization policeOrganization){
		return policeOrganizationDao.findPoliceOrganizationByCondition(
				"from PoliceOrganization p where p.police=:police and p.organization=:organization", 
				new String[]{"police","organization"}, 
				new Object[]{policeOrganization.getPolice(),policeOrganization.getOrganization()});

	}

}
