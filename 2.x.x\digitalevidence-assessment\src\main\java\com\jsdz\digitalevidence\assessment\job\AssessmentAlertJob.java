/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.job;

import java.util.List;
import java.util.Map;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.cycle.CycleConfiguration;
import com.jsdz.digitalevidence.assessment.service.AssessmentAlertService;
//import com.jsdz.scheduler.job.AbstractJob;
import com.sun.xml.bind.v2.TODO;

/**
 * @类名: AssessmentAlertJob
 * @说明: 考核预警作业，
 *        生成考核预警，考核报告冻结后
 *
 * <AUTHOR>
 * @Date	 2017年6月5日 下午4:10:36
 * 修改记录：
 *
 * @see 	 
 * 
 * TODO
 *  *. 分块
 */
//public class AssessmentAlertJob extends AbstractJob {
public class AssessmentAlertJob {
	
	public static final String KEY_PARAM_CYCLE = "cycle";
	public static final String KEY_PARAM_POLICE = "police";
	
	@Autowired
	private CycleConfiguration cc;
	@Autowired
	private EmployeesService empService;
	@Autowired
	private AssessmentAlertService alertService;
	
	/* (non-Javadoc)
	 * @see com.jsdz.scheduler.job.AbstractJob#execute(org.quartz.JobExecutionContext)
	 */
/*	@SuppressWarnings("unchecked")
	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
		// 传入参数
		Map<String, Object> params = this.getParams();
		// 周期
		Cycle c = (Cycle)params.get(AssessmentAlertJob.KEY_PARAM_CYCLE);
		//
		if(c==null) {
			c = cc.getCurrentCycle();
		    c= cc.prevCycle(c);
		}
		// 警员
		List<Employees> emps = (List<Employees>)params.get(AssessmentAlertJob.KEY_PARAM_POLICE);
		//
		if(emps==null)
			emps = empService.findAllEmployeess();
		//
		try {
			for(Employees emp : emps) {
				alertService.genAssessmentAlertOfPoliceIfNotExist(emp.getId(), c.getStart(), c.getEnd());
			}
		} catch (Exception e) {
			throw new JobExecutionException(e.getMessage());
		}
	}*/
	
	

}
