<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
        http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">
	
	<!-- 报表引擎库 -->
	<bean id="reRepo"
		class="com.jsdz.reportengine.RERopository">
		<property name="res">
			<map>
			    <entry key="xlsx">
			     	<ref bean="xlsxReportEngine" />
			    </entry>
			    <entry key="xls">
			    	<ref bean="xlsReportEngine" />
			    </entry>
			    <entry key="docx">
			    	<ref bean="docxReportEngine" />
			    </entry>
			    <entry key="pdf">
			    	<ref bean="pdfReportEngine" />
			    </entry>
			</map>
		</property>
	</bean>	
	
	<!-- 文档类型映射 -->
	<bean id="contentTypeMapper" 
			class="com.jsdz.reportengine.ContentTypeMapper">  
		<!-- 映射别名配置 -->
		<property name="mapper">
        	<map>  
            	<entry key ="docx">
                	<value>application/vnd.openxmlformats-officedocument.wordprocessingml.document</value>
                </entry>     
                <entry key ="xlsx">
                	<value>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</value>
                </entry>    
                <entry key ="pdf">
                	<value>com.jsdz.digitalevidence.document.model.Audio</value>
                </entry>     
             
            </map>            
   		</property>
   		
	</bean>	
</beans>
