package com.jsdz.admin.security.dao;
/**
 * 
 * @类名: OperatorDao
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年4月26日下午9:07:38
 * 修改记录：
 *
 * @see
 */

import java.util.List;

import com.jsdz.admin.org.model.Region;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.core.Page;
import com.jsdz.core.dao.GenericORMEntityDAO;

public interface OperatorDao extends GenericORMEntityDAO<Operator,Long> {
	
	//public List<Operator> findAll();
	
	public Operator findOperatorById(Long id);
	
	public List<Operator> findByCondition(String queryStr,String[] paramNames,Object[] values);
	
	public void updateOperator(Operator operator);
	
	public void deleteOperator(Operator operator);
	
	public void addOperator(Operator operator);
	
	//按id查询
	public Operator locateOperatorById(Long id);
	
		/**
		 * @说明：分页查询操作员
		 *
		 * <AUTHOR>
		 * @param 
		 * @return
		 * 2017年4月29日 下午6:05:24
		 */
	public Page<Operator> findOperatorOnPage(
			Page<Operator> page,
			String queryStr,
			String[] paramNames,
			Object[] values);
	
	//按警员警号找操作员
	public Operator findOperatorByWorkNum(String workNum);
	
	/**
	 * 删除操作员数据，设置删除标志
	 * @param operator
	 * @return
	 */
	public Operator setDeleteFlag(Operator operator);
}
