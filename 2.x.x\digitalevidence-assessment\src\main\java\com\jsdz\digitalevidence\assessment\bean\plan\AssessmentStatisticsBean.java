package com.jsdz.digitalevidence.assessment.bean.plan;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @类名: AssessmentStatisticsBean
 * @说明: 按警员查询 的考核统计报表
 *
 * <AUTHOR>
 * @Date	 2019年6月26日下午3:05:49
 * 修改记录：
 *
 * @see 	 
 */
public class AssessmentStatisticsBean {
	/**
	 * 警员
	 */
	private Long id;
	private Long policeCode;
	private String policeName;
	/** 考核时段，开始、结束*/
	@JsonFormat(pattern="yyyy-MM-dd", timezone="GMT+8")
	private Date startDate;
	@JsonFormat(pattern="yyyy-MM-dd", timezone="GMT+8")
	private Date endDate;
	/** 视频考核视频数*/
	private Integer assessDocs;
	/** 考核期间视频上传数*/
	private Integer uploadDocs;
	/** 抽查数*/
	private Integer samplingDocs;	
	/** 抽查率 */
	private Double samplingRate;
	/** 考核率 */
	private Double accessRate;
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}

	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	public Integer getAssessDocs() {
		return assessDocs;
	}
	public void setAssessDocs(Integer assessDocs) {
		this.assessDocs = assessDocs;
	}
	public Integer getUploadDocs() {
		return uploadDocs;
	}
	public void setUploadDocs(Integer uploadDocs) {
		this.uploadDocs = uploadDocs;
	}
	public Double getSamplingRate() {
		return samplingRate;
	}
	public void setSamplingRate(Double samplingRate) {
		this.samplingRate = samplingRate;
	}
	public Double getAccessRate() {
		return accessRate;
	}
	public void setAccessRate(Double accessRate) {
		this.accessRate = accessRate;
	}
	public Integer getSamplingDocs() {
		return samplingDocs;
	}
	public void setSamplingDocs(Integer samplingDocs) {
		this.samplingDocs = samplingDocs;
	}
	public Long getPoliceCode() {
		return policeCode;
	}
	public void setPoliceCode(Long policeCode) {
		this.policeCode = policeCode;
	}
	public String getPoliceName() {
		return policeName;
	}
	public void setPoliceName(String policeName) {
		this.policeName = policeName;
	}
	
	
	
}
