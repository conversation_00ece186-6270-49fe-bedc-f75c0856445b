package com.jsdz.digitalevidence.alarm.common.dao.impl;
/**
 * 
 * @类名: RecoderViewDaoImpl
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年10月26日上午10:46:45
 * 修改记录：
 *
 * @see
 */
import java.util.List;
import org.hibernate.Query;
import org.hibernate.Session;
import org.springframework.stereotype.Repository;
import com.jsdz.core.Page;
import org.springframework.orm.hibernate3.HibernateCallback;
import org.hibernate.HibernateException;
import java.sql.SQLException;
import com.jsdz.core.dao.impl.hibernate.GenericEntityDaoHibernateImpl;
import com.jsdz.digitalevidence.alarm.common.dao.RecoderViewDao;
import com.jsdz.digitalevidence.alarm.common.model.RecoderView;

@Repository
public class RecoderViewDaoImpl extends GenericEntityDaoHibernateImpl<RecoderView,Long> implements RecoderViewDao{

	//新增
	public void addRecoderView(RecoderView recoderView) {
		this.saveOrUpdate(recoderView);
	}

	//删除
	public void deleteRecoderView(RecoderView recoderView) {
		this.delete(recoderView);
	}

	//修改
	public void updateRecoderView(RecoderView recoderView) {
		this.merge(recoderView);
	}

	//按id查询(游离状态)
	public RecoderView findRecoderViewById(Long id){

		final String  hql = "from RecoderView r where r.id = :id";
		final Long oid = id;
		RecoderView data = getHibernateTemplate().execute(new HibernateCallback<RecoderView>() {
			public RecoderView doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<RecoderView> list = query.list();
				RecoderView rsult = list == null || list.size() <= 0?null:list.get(0);
				if (rsult != null){
					session.evict(rsult);
				}
				return rsult;
			}
		});
		return data;
	}

	//按id查询
	public RecoderView locateRecoderViewById(Long id){

		final String  hql = "from RecoderView r where r.id = :id";
		final Long oid = id;
		RecoderView data = getHibernateTemplate().execute(new HibernateCallback<RecoderView>() {
			public RecoderView doInHibernate(Session session)throws HibernateException, SQLException { 
				Query query = session.createQuery(hql);
				query.setParameter("id", oid);
				List<RecoderView> list = query.list();
				RecoderView rsult = list == null || list.size() <= 0?null:list.get(0);
				return rsult;
			}
		});
		return data;
	}

	//单个查询(根据其它字段查询)
	public RecoderView findRecoderViewByCondition(final String queryStr,final String[] paramNames,final Object[] values){
		RecoderView data = getHibernateTemplate().execute(new HibernateCallback<RecoderView>() {
		public RecoderView doInHibernate(Session session)throws HibernateException, SQLException { 
			Query query = session.createQuery(queryStr);
			for(int i=0;i<values.length;i++){
				query.setParameter(paramNames[i], values[i]);
			}
			List<RecoderView> list = query.list();
			RecoderView rsult = list == null || list.size() <= 0?null:list.get(0);
			if (rsult != null){
				session.evict(rsult);
			}
			return rsult;
			}
		});
		return data;
	}

	//查询全部
	public List<RecoderView> findAllRecoderViews(){
		return this.find("from RecoderView recoderView ");
	}

	//列表查询
	public List<RecoderView> findRecoderViewsByCondition(String queryStr,String[] paramNames,Object[] values){
		List<RecoderView> lists = null;
		if (values == null || values.length <= 0){
			lists = this.find(queryStr);
		}else{
			lists = this.findByNamedParam(queryStr,paramNames,values);
		}
		return lists;
	}

	//分页查询
	public Page<RecoderView> findRecoderViewsOnPage(Page<RecoderView> page, String queryStr, String[] paramNames, Object[] values) {
		return (Page<RecoderView>) this.pageQueryHQL(page, queryStr, paramNames,values);
	}

}
