package com.jsdz.digitalevidence.analysis.model;
/**
 * 
 * @类名: DailyLog
 * @说明: 日结日志
 *
 * @author: kenny
 * @Date	2017年5月31日下午3:05:32
 * 修改记录：
 *
 * @see
 */
import java.util.Date;

public class DailyLog {

	private Long id;
	private String objectDesc; //项目描述
	private String dailyType;//日结月结年结
	private Date dateFrom; //日结期间从
	private Date dateTo; //日结期间到
	private Date createTime; //日期时间
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getObjectDesc() {
		return objectDesc;
	}
	public void setObjectDesc(String objectDesc) {
		this.objectDesc = objectDesc;
	}
	public String getDailyType() {
		return dailyType;
	}
	public void setDailyType(String dailyType) {
		this.dailyType = dailyType;
	}
	public Date getDateFrom() {
		return dateFrom;
	}
	public void setDateFrom(Date dateFrom) {
		this.dateFrom = dateFrom;
	}
	public Date getDateTo() {
		return dateTo;
	}
	public void setDateTo(Date dateTo) {
		this.dateTo = dateTo;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	
	
}
