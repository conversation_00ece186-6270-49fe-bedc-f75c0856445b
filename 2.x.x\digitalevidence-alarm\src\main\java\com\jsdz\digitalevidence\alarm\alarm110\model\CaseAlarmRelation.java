package com.jsdz.digitalevidence.alarm.alarm110.model;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jsdz.admin.security.model.Operator;
import com.jsdz.digitalevidence.document.model.Case;

/**
 *  用来保存警情信息中的id和案件信息中的id
 * <AUTHOR>
 *
 */
public class CaseAlarmRelation {
	
	private Long id; 
	private Case cas ;
	private AlarmInfo alas ;
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date relationTime; //关联时间
	private Operator operator;    //操作员
	
	public CaseAlarmRelation() {
		super();
	}
	
	public CaseAlarmRelation(Long id, Case cas, AlarmInfo alas, Date relationTime, Operator operator) {
		super();
		this.id = id;
		this.cas = cas;
		this.alas = alas;
		this.relationTime = relationTime;
		this.operator = operator;
	}



	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Case getCas() {
		return cas;
	}

	public void setCas(Case cas) {
		this.cas = cas;
	}

	public AlarmInfo getAlas() {
		return alas;
	}

	public void setAlas(AlarmInfo alas) {
		this.alas = alas;
	}

	public Date getRelationTime() {
		return relationTime;
	}

	public void setRelationTime(Date relationTime) {
		this.relationTime = relationTime;
	}

	public Operator getOperator() {
		return operator;
	}

	public void setOperator(Operator operator) {
		this.operator = operator;
	}
	
}
