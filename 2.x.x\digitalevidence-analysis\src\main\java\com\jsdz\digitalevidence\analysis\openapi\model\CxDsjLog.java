package com.jsdz.digitalevidence.analysis.openapi.model;
/**
 * 
 * @类名: CxDsjLog
 * @说明: 执法记录仪日志信息上传实体类
 *
 * @author: kenny
 * @Date	2017年6月9日下午3:17:03
 */
import java.util.Date;
public class CxDsjLog {


	private Long id;
	private String gzz_xh;//执法数据采集设备产品编码
	private String rzlx;//日志类型
	private String wjmc;//文件名称
	private Date rzrq;//日志时间
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getGzz_xh() {
		return gzz_xh;
	}
	public void setGzz_xh(String gzz_xh) {
		this.gzz_xh = gzz_xh;
	}
	public String getRzlx() {
		return rzlx;
	}
	public void setRzlx(String rzlx) {
		this.rzlx = rzlx;
	}
	public String getWjmc() {
		return wjmc;
	}
	public void setWjmc(String wjmc) {
		this.wjmc = wjmc;
	}
	public Date getRzrq() {
		return rzrq;
	}
	public void setRzrq(Date rzrq) {
		this.rzrq = rzrq;
	}
	
	


}
