package com.jsdz.digitalevidence.assessment.service;

/**
 * 
 * @类名: AssessmentPlanService
 * @说明: 
 *
 * @author: kenny
 * @Date 2019-06-19 15:40:26
 * 修改记录：
 *
 * @see
*/

import java.util.List;

import com.jsdz.digitalevidence.assessment.bean.plan.AssessmentStatisticsBean;
import com.jsdz.digitalevidence.assessment.model.plan.AssessmentPlan;
import com.jsdz.digitalevidence.assessment.bean.plan.ShootingAssessmentBean;
import com.jsdz.core.AjaxResult;
import com.jsdz.core.Page;

public interface AssessmentPlanService {

	/** 
 	 * 新增
	 */ 
 	public AjaxResult addAssessmentPlan(AssessmentPlan assessmentPlan);

	/** 
 	 * 修改
	 */ 
 	public AjaxResult updateAssessmentPlan(AssessmentPlan assessmentPlan);

	/** 
 	 * 删除
	 */ 
 	public AjaxResult deleteAssessmentPlan(AssessmentPlan assessmentPlan);

	/** 
 	 * 按id查询,结果是游离状态的数据
	 */ 
 	public AssessmentPlan findAssessmentPlanById(Long id);

	/** 
 	 * 按id查询
	 */ 
 	public AssessmentPlan locateAssessmentPlanById(Long id);

	/** 
 	 * 单个查询
	 */ 
 	public AssessmentPlan findAssessmentPlanByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 查询全部
	 */ 
 	public List<AssessmentPlan> findAllAssessmentPlans();

	/** 
 	 * 列表查询
	 */ 
 	public List<AssessmentPlan> findAssessmentPlansByParam(String queryStr,String[] paramNames,Object[] values);

	/** 
 	 * 分页查询
	 */ 
 	public Page<AssessmentPlan> findAssessmentPlansOnPage(Page<AssessmentPlan> page, String queryStr,String[] paramNames,Object[] values);

	public Page<AssessmentStatisticsBean> queryAssessmentStatistics(Page<AssessmentStatisticsBean> page, String[] paramNames, Object[] values);

	public Page<ShootingAssessmentBean> queryShootingAssessmentDetail(Page<ShootingAssessmentBean>  page, String[] paramNames, Object[] values); 

	public Page<ShootingAssessmentBean> queryShootingAssessmentDetail1(Page<ShootingAssessmentBean>  page, String[] paramNames, Object[] values); 
	
}

