package com.jsdz.digitalevidence.activemq.model;
/**
 * 
 * @类名: DataExchange
 * @说明: 数据转换表
 *
 * @author: kenny
 * @Date	2018年3月11日下午9:22:59
 * 修改记录：
 *
 * @see
 */

import java.util.Date;

public class DataExchange {
	
	private Long id;
	private String topic;
	private String option;
	private String context;
	private String note;
	private Date createTime;
	private Integer proccessFlag=0;//是否已处理
	private Integer proccessCount=0; //重复处理次数
	private String proccessResult; //处理结果
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getTopic() {
		return topic;
	}
	public void setTopic(String topic) {
		this.topic = topic;
	}
	public String getOption() {
		return option;
	}
	public void setOption(String option) {
		this.option = option;
	}
	public String getContext() {
		return context;
	}
	public void setContext(String context) {
		this.context = context;
	}
	public String getNote() {
		return note;
	}
	public void setNote(String note) {
		this.note = note;
	}
	public Date getCreateTime() {
		return createTime;
	}
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	public Integer getProccessFlag() {
		return proccessFlag;
	}
	public void setProccessFlag(Integer proccessFlag) {
		this.proccessFlag = proccessFlag;
	}
	public Integer getProccessCount() {
		return proccessCount;
	}
	public void setProccessCount(Integer proccessCount) {
		this.proccessCount = proccessCount;
	}
	public String getProccessResult() {
		return proccessResult;
	}
	public void setProccessResult(String proccessResult) {
		this.proccessResult = proccessResult;
	}
	
	
}
