/**
 * %%
 * %%
 */
package com.jsdz.digitalevidence.assessment.service.impl;

import static com.jsdz.digitalevidence.assessment.AssessmentUtils.nullOrInteger;
import static com.jsdz.digitalevidence.assessment.AssessmentUtils.nullOrLong;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.jsdz.admin.security.model.Operator;
import org.hibernate.transform.ResultTransformer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.security.model.Employees;
import com.jsdz.admin.security.service.EmployeesService;
import com.jsdz.admin.security.utils.SessionUtils;
import com.jsdz.core.Page;
import com.jsdz.digitalevidence.assessment.AssessmentConsts;
import com.jsdz.digitalevidence.assessment.AssessmentUtils;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentDocumentQueryBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentReportBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentReportQueryBean;
import com.jsdz.digitalevidence.assessment.bean.AssessmentWorkStatisticsBean;
import com.jsdz.digitalevidence.assessment.bean.CompleteAssessmentReportBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumBean;
import com.jsdz.digitalevidence.assessment.bean.ShootingAssessmentSumQueryBean;
import com.jsdz.digitalevidence.assessment.cycle.Cycle;
import com.jsdz.digitalevidence.assessment.cycle.CycleConfiguration;
import com.jsdz.digitalevidence.assessment.cycle.CycleSelector;
import com.jsdz.digitalevidence.assessment.dao.ShootingAssessmentDao;
import com.jsdz.digitalevidence.assessment.dao.ShootingAssessmentReportDao;
import com.jsdz.digitalevidence.assessment.model.AssessmentCate;
import com.jsdz.digitalevidence.assessment.model.ReportType;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessment;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentReport;
import com.jsdz.digitalevidence.assessment.model.ShootingAssessmentSummary;
import com.jsdz.digitalevidence.assessment.model.ShootingItem;
import com.jsdz.digitalevidence.assessment.model.ShootingSummary;
import com.jsdz.digitalevidence.assessment.service.AssessmentQueryService;
import com.jsdz.digitalevidence.assessment.service.AssessmentService;
import com.jsdz.digitalevidence.document.model.Document;
import com.jsdz.digitalevidence.document.model.DocumentCate;
import com.jsdz.digitalevidence.document.service.DocumentService;
import com.jsdz.reportquery.ChainFilter;
import com.jsdz.reportquery.NullFilter;
import com.jsdz.reportquery.ReportQueryDao;
import com.jsdz.reportquery.ReportQueryUtils;
import com.jsdz.reportquery.dynsql.fork.support.tree.TreeField;
/*import com.jsdz.scheduler.service.JobService;
import com.jsdz.scheduler.service.SchedulerService;*/
import com.jsdz.utils.DTOUtils.Filter;

/**
 * @类名: AssessmentServiceImpl
 * @说明: 考核应用服务实现
 *        查询
 *        考核
 *        报告
 *
 * <AUTHOR>
 * @Date	 2017年8月15日 上午11:26:16
 * 修改记录：
 *
 * @see 	 
 * 
 *   
 */
@Service
public class AssessmentServiceImpl implements AssessmentQueryService, AssessmentService {
	private Logger log = LoggerFactory.getLogger(this.getClass());

	/** 调度作业服务*/
	//@Autowired
	//private JobService jobService;
	/** 调度服务*/
	//@Autowired
	//private SchedulerService schService;

	/** 文档服务*/
	@Autowired
	private DocumentService docService;
	/** 警员服务*/
	@Autowired
	private EmployeesService empService;
	// 考核周期设置
	@Resource(name=AssessmentConsts.Assessment_Cycle_Configuration_Key)
	private CycleConfiguration cc;
	@Autowired
	private ShootingAssessmentReportDao reportDao;
	@SuppressWarnings({ "rawtypes" })
	// 报表查询dao
	@Autowired
	private ReportQueryDao rqDao;
	@Autowired
	private ShootingAssessmentDao saDao;
	/*	*//** 拍摄统计dao*//*
	@Autowired
	private ShootingSummaryDao ssDao;
	 *//** 拍摄考核统计dao*//*
	@Autowired
	private ShootingAssessmentSummaryDao sasDao;*/

	@Override
	public Page<ShootingAssessmentSumBean> queryShootingAssessmentSummary(Page<ShootingAssessmentSumBean> page,
			ShootingAssessmentSumQueryBean qb) throws Exception {
		// 转换参数
		Map<String, Object> kvs = ReportQueryUtils.queryBean3Map(qb, 
				new ChainFilter(
						// 过滤Null参数
						new NullFilter(), 
						// 过滤TreeField参数
						new Filter() {
							public boolean accept(String propName, Object value) {
								if(value instanceof TreeField) {
									TreeField tr = (TreeField)value;
									if(tr.getPath()==null)
										return false;
									if(tr.getIncludeSub()==null)
										tr.setIncludeSub(false);
								}
								return true;
							}},
						// 过滤周期参数
						new Filter(){
								@Override
								public boolean accept(String propName , Object value) {
									if("cycleDate".equals(propName) || "cycleSelector".equals(propName)) 
										return false;
									return true;
								}
							}));
		// 周期参数
		Cycle cycle = getQueryCycle(qb.getCycleDate(), qb.getCycleSelector());
		//
		Cycle curCycle = cc.getCurrentCycle();
		//
		kvs.put("startDate", cycle.getStart());
		kvs.put("endDate", cycle.getEnd());
		// 增加日期参数
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		//
		// 当前考核周期
		if(curCycle.inCycle(cycle.getStart()))
			return queryCurCycleShootingAssessmentsSummary(page, paramNames, values);
		else
			return queryPassCycleShootingAssessmentsSummary(page, paramNames, values);

	}

	// 获取当前考核周期
	@SuppressWarnings("unchecked")
	public Page<ShootingAssessmentSumBean> queryCurCycleShootingAssessmentsSummary(
			Page<ShootingAssessmentSumBean> page,
			String[] paramNames, Object[] values) throws Exception {

		return (Page<ShootingAssessmentSumBean>)rqDao.pageQueryNamedSQL(
				page,
				AssessmentConsts.SQL_KEY_Sum_Of_ShootingAssessment_All,
				paramNames, values,	
				new ResultTransformer() {
					@Override
					public Object transformTuple(
							Object[] tuples, 
							String[] aliases) {
						Long policeId = AssessmentUtils.nullOrLong(tuples[0]);
						String policeCode = (String)tuples[1];
						String policeName = (String)tuples[2];
						String position = (String)tuples[3];
						String sex = (String)tuples[4];
						Integer age = AssessmentUtils.nullOrInteger(tuples[5]);
						Integer uploadDocs = AssessmentUtils.nullOrInteger(tuples[6]);
						Integer assessDocs = AssessmentUtils.nullOrInteger(tuples[7]);
						Float assessRate = AssessmentUtils.nullOrFloat((BigDecimal)tuples[8]);
						ShootingAssessmentSumBean bean =  new ShootingAssessmentSumBean();
						bean.setPoliceId(policeId);
						bean.setAge(age);
						bean.setPosition(position);
						bean.setAssessDocs(assessDocs);
						bean.setAssessRate(assessRate);
						bean.setName(policeName);
						bean.setSex(sex);
						bean.setUploadDocs(uploadDocs);
						bean.setPoliceCode(policeCode);
						return bean;
					}

					@Override
					public List transformList(List collection) {
						return collection;
					}
				});

	}

	// 获取过去考核周期, 从考核报告获取
	@SuppressWarnings("unchecked")
	public Page<ShootingAssessmentSumBean> queryPassCycleShootingAssessmentsSummary(
			Page<ShootingAssessmentSumBean> page,
			String[] paramNames, Object[] values) throws Exception {
		//
		return (Page<ShootingAssessmentSumBean>)rqDao.pageQueryNamedSQL(
				page,
				AssessmentConsts.SQL_KEY_Sum_Of_Pass_ShootingAssessment_All,
				paramNames, values,	
				new ResultTransformer() {
					@Override
					public Object transformTuple(
							Object[] tuples, 
							String[] aliases) {
						Long policeId = AssessmentUtils.nullOrLong(tuples[0]);
						String policeCode = (String)tuples[1];
						String policeName = (String)tuples[2];
						String position = (String)tuples[3];
						String sex = (String)tuples[4];
						Integer age = AssessmentUtils.nullOrInteger(tuples[5]);
						Integer uploadDocs = AssessmentUtils.nullOrInteger(tuples[6]);
						Integer assessDocs = AssessmentUtils.nullOrInteger(tuples[7]);
						Float assessRate = AssessmentUtils.nullOrFloat((BigDecimal)tuples[8]);
						ShootingAssessmentSumBean bean =  new ShootingAssessmentSumBean();
						bean.setPoliceId(policeId);
						bean.setAge(age);
						bean.setPosition(position);
						bean.setAssessDocs(assessDocs);
						bean.setAssessRate(assessRate);
						bean.setName(policeName);
						bean.setSex(sex);
						bean.setUploadDocs(uploadDocs);
						bean.setPoliceCode(policeCode);
						return bean;
					}

					@Override
					public List transformList(List collection) {
						return collection;
					}
				});
	}

	@Override
	public ShootingAssessmentReport getAssessmentReport(Long policeId, Date cycleDate) {
		// 
		return reportDao.getShootingAssessmentReport(policeId, cycleDate);
	}

	@SuppressWarnings("unchecked")
	@Override
	public Page<ShootingAssessmentReport> queryAssessmentReport(Page<ShootingAssessmentReport> page, 
			Long policeId,
			Date startDate, Date endDate) {
		return (Page<ShootingAssessmentReport>)rqDao.pageQueryNamedHQL(
				page,
				AssessmentConsts.SQL_KEY_Query_Assessment_Report,
				new String[]{"policeId", "startDate", "startDate"},
				new Object[]{policeId, startDate, endDate});

	}

	@SuppressWarnings("unchecked")
	@Override  
	public Page<AssessmentDocumentBean> queryAssessmentDocument(Page<AssessmentDocumentBean> page,
			AssessmentDocumentQueryBean qb,Long planId,Long policeId) throws Exception {
		// 转换参数
/*		Map<String, Object> kvs = ReportQueryUtils.queryBean3Map(qb,
				new ChainFilter(
						// 过滤Null参数
						new NullFilter(),
						// 过滤TreeField参数
						new Filter() {
							public boolean accept(String propName, Object value) {
								if(value instanceof TreeField) {
									TreeField tr = (TreeField)value;
									if(tr.getPath()==null)
										return false;
									if(tr.getIncludeSub()==null)
										tr.setIncludeSub(false);
								}
								return true;
							}},
						// 过滤周期参数
						new Filter() {
								@Override
								public boolean accept(String propName, Object value) {
									if ("cycleDate".equals(propName) || "cycleSelector".equals(propName))
										return false;
									return true;
								}
							}));
		// 周期参数
		Cycle cycle = getQueryCycle(qb.getCycleDate(), qb.getCycleSelector());
		//
		
		kvs.put("startDate", cycle.getStart());
		kvs.put("endDate", cycle.getEnd());
		*/
		Map<String, Object> kvs = new HashMap<String, Object>();
		
		//kvs.clear();
		kvs.put("planId", planId);
		kvs.put("policeId", policeId);
		
		// 增加日期参数
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		//
		page = (Page<AssessmentDocumentBean>) rqDao.pageQueryNamedSQL(page,
				AssessmentConsts.SQL_KEY_Query_Assessment_Docs, paramNames, values);
		return page;
	}

	/****************考核*****************/
	@SuppressWarnings("unchecked")
	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.assessment.service.AssessmentService#openAssess(java.lang.Long)
	 */
	// TODO 警情信息
	@Override
	public ShootingAssessmentBean openAssessment(Long docId, Operator operator, String ip) {
		// 检查是否已冻结考核报告
		// ...
		// 文档路径
		String docUrl = docService.getPlayPath(docId, operator, ip);
		//
		List<ShootingAssessmentBean> items  = 
				(List<ShootingAssessmentBean>)rqDao.queryNamedSQL(
						AssessmentConsts.SQL_KEY_Open_Assessment, 
						new String[]{"docId"}, 
						new Object[]{docId},
						new ResultTransformer() {
							@Override
							public Object transformTuple(
									Object[] tuples, 
									String[] aliases) {
								String workNumber = (String)tuples[0];
								String policeName = (String)tuples[1];
								String position = (String)tuples[2];
								String sex = (String)tuples[3];
								Integer age = AssessmentUtils.nullOrInteger(tuples[4]);
								Long docId = AssessmentUtils.nullOrLong(tuples[5]);
								String alarmCode=(String)tuples[6];
								String alarmName=(String)tuples[7];
								String alarmTel=(String)tuples[8];
								Long alarmId = nullOrLong(tuples[9]);
								String alarmContext=(String)tuples[10];
								Integer isRelation=((Integer)tuples[11]);
								String contentType = ((String)tuples[12]);
								ShootingAssessmentBean bean =  new ShootingAssessmentBean();
								bean.setPoliceCode(workNumber);
								bean.setAge(age);
								bean.setPosition(position);
								bean.setName(policeName);
								bean.setSex(sex);
								bean.setDocId(docId);
								bean.setAlarmCode(alarmCode);
								bean.setAlarmName(alarmName);
								bean.setAlarmTel(alarmTel);
								bean.setAlarmId(alarmId);
								bean.setAlarmContext(alarmContext);
								bean.setIsRelation(isRelation);
								bean.setContentType(contentType);
								return bean;
							}

							@Override
							public List transformList(List collection) {
								return collection;
							}
						} 
						);
		ShootingAssessmentBean bean = items.get(0);
		bean.setDocUrl(docUrl);
		bean.setCate(AssessmentCate.NORMAL);
		return bean;
	}

	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.assessment.service.AssessmentService#submitAssess(com.jsdz.digitalevidence.assessment.model.ShootingAssessment)
	 */
	@Override
	@Transactional
	public void submitAssessment(ShootingAssessment assessment) {
		// 考核员
		Employees police = SessionUtils.getCurrentOperator().getEmployees();
		//
		Document doc = docService.getDocument(assessment.getDocument().getId());
		//
		assessment.setDocument(doc);
		assessment.setInspector(police);
		assessment.setAssessDate(new Date());
		//
		saDao.saveOrUpdate(assessment);

	}

	/**************考核报告****************/
	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.assessment.service.AssessmentService#genAccessmentReport(java.lang.Long, java.util.Date, java.util.Date)
	 */
	@Override
	@Transactional
	public ShootingAssessmentReport generateAndSaveAccessmentReport(Long policeId, Date startDate, Date endDate, ReportType type) {
		//
		ShootingAssessmentReport report = this.generateAccessmentReport(policeId, startDate, endDate, type);
		//
		reportDao.saveOrUpdate(report);
		//
		return report;
	}

	@Override
	@Transactional
	public ShootingAssessmentReport generateAccessmentReport(Long policeId, Date startDate, Date endDate, ReportType type) {
		//
		ShootingAssessmentReport report = new ShootingAssessmentReport();
		//
		Employees police = empService.findEmployeesBean(policeId);
		//
		report.setPolice(police);
		report.setSubmitDate(new Date());
		// 考核时段，起始日期
		report.setStartDate(startDate);
		// 
		report.setEndDate(endDate);
		// 拍摄考核统计，拍摄统计
		ShootingAssessmentSummary saSum = getShootingAccessmentSummary(policeId, startDate, endDate);
		ShootingSummary sum = getShootingSummary(policeId, startDate, endDate);
		// 
		report.setSumOfShootingAssessment(saSum);
		report.setSumOfShooting(sum);
		// 冻结标志
		report.setFrozen(false);
		//
		report.setType(type);
		//
		return report;
	}

	@Override
	@Transactional
	public ShootingAssessmentReport frozenAccessmentReport(Long reportId) {
		ShootingAssessmentReport report = reportDao.get(reportId);
		report.setFrozen(true);
		reportDao.saveOrUpdate(report);
		return report;
	}

	@SuppressWarnings("unchecked")
	@Override
	public Page<AssessmentReportBean> queryShootingAccessmentReport(Page<AssessmentReportBean> page,
			AssessmentReportQueryBean qb) throws Exception {		
		// 转换参数
		Map<String, Object> kvs = ReportQueryUtils.queryBean3Map(qb,
				new ChainFilter(
						// 过滤Null参数
						new NullFilter()));
		// 
		// 增加日期参数
		String[] paramNames = kvs.keySet().toArray(new String[0]);
		Object[] values = kvs.values().toArray(new Object[0]);
		//
		Page<AssessmentReportBean> reports  = 
				(Page<AssessmentReportBean>)rqDao.pageQueryNamedHQLWithNewBean(page,
						AssessmentConsts.SQL_KEY_Query_Assessment_Report_Brief,
						paramNames, 
						values);
		return reports;
	}

	@Override
	public Page<AssessmentReportBean> queryMyShootingAccessmentReport(Page<AssessmentReportBean> page, 
			Date startDate,
			Date endDate) throws Exception {
		Employees emp = SessionUtils.getCurrentOperator().getEmployees();
		AssessmentReportQueryBean qb = new AssessmentReportQueryBean();
		qb.setPoliceId(emp.getId());
		qb.setStartDate(startDate);
		qb.setEndDate(endDate);
		return queryShootingAccessmentReport(page, qb);
	}

	@Override
	@Transactional
	public ShootingAssessmentReport getShootingAccessmentReport(Long reportId) {
		return reportDao.get(reportId);
	}

	@Override
	@Transactional(readOnly=true)
	public CompleteAssessmentReportBean getShootingAccessmentReportBean(Long reportId) {
		CompleteAssessmentReportBean reportBean =  reportDao.getShootingAssessmentReport(reportId);
		return reportBean;
	}

	@Override
	public void commentShootingAccessmentReport(Long reportId, String comment) {
		ShootingAssessmentReport report = getShootingAccessmentReport(reportId);
		report.setComments(comment);
		reportDao.saveOrUpdate(report);
	}

	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.assessment.service.AssessmentService#genShootingAccessmentSummary(java.lang.Long, java.util.Date, java.util.Date)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public ShootingAssessmentSummary getShootingAccessmentSummary(Long policeId, Date startDate, Date endDate) {
		//
		List<ShootingAssessmentSummary> items  = 
				(List<ShootingAssessmentSummary>)rqDao.queryNamedSQL(
						AssessmentConsts.SQL_KEY_Sum_Of_ShootingAssessment, 
						new String[]{"policeId", "startDate", "endDate"}, 
						new Object[]{policeId, startDate, endDate},
						new ResultTransformer() {
							@Override
							public Object transformTuple(
									Object[] tuples, 
									String[] aliases) {
								Integer countOfWMGF = nullOrInteger((BigDecimal)tuples[0]);
								Integer countOfNoWFWJ = nullOrInteger((BigDecimal)tuples[1]);
								Integer countOfAYQPS = nullOrInteger((BigDecimal)tuples[2]);
								Integer countOfYYYY = nullOrInteger((BigDecimal)tuples[3]);
								Integer countOfShootingAngleA = nullOrInteger((BigDecimal)tuples[4]);
								Integer countOfShootingAngleG = nullOrInteger((BigDecimal)tuples[5]);
								Integer countOfShootingAngleE = nullOrInteger((BigDecimal)tuples[6]);
								Integer countOfShootingContentA = nullOrInteger((BigDecimal)tuples[7]);
								Integer countOfShootingContentG = nullOrInteger((BigDecimal)tuples[8]);
								Integer countOfShootingContentE = nullOrInteger((BigDecimal)tuples[9]);			        	
								Integer countOfShootingResolutionH = nullOrInteger((BigDecimal)tuples[10]);
								Integer countOfShootingResolutionG = nullOrInteger((BigDecimal)tuples[11]);
								Integer countOfShootingResolutionL = nullOrInteger((BigDecimal)tuples[12]);
								Integer totalAssessingDoc = nullOrInteger(((BigInteger)tuples[13]));
								Integer totalDoc = nullOrInteger(((BigInteger)tuples[14]));
								// 
								ShootingAssessmentSummary bean =  new ShootingAssessmentSummary();
								bean.setCountOfAYQPS(countOfAYQPS);
								bean.setCountOfNoWFWJ(countOfNoWFWJ);
								bean.setCountOfWMGF(countOfWMGF);
								bean.setCountOfYYYY(countOfYYYY);
								bean.setCountOfShootingAngleA(countOfShootingAngleA);
								bean.setCountOfShootingAngleE(countOfShootingAngleE);
								bean.setCountOfShootingAngleG(countOfShootingAngleG);

								bean.setCountOfShootingContentA(countOfShootingContentA);
								bean.setCountOfShootingContentE(countOfShootingContentE);
								bean.setCountOfShootingContentG(countOfShootingContentG);

								bean.setCountOfShootingResolutionG(countOfShootingResolutionG);
								bean.setCountOfShootingResolutionH(countOfShootingResolutionH);
								bean.setCountOfShootingResolutionL(countOfShootingResolutionL);

								bean.setTotalAssessingDoc(totalAssessingDoc);
								bean.setTotalDoc(totalDoc);

								return bean;
							}

							@Override
							public List transformList(List collection) {
								return collection;
							}
						});
		return items.get(0);
	}

	/* (non-Javadoc)
	 * @see com.jsdz.digitalevidence.assessment.service.AssessmentService#genShootingSummary(java.lang.Long, java.util.Date, java.util.Date)
	 */
	@SuppressWarnings("unchecked")
	@Override
	public ShootingSummary getShootingSummary(Long policeId, Date startDate, Date endDate) {
		// 获取警员考核项
		List<ShootingItem> items  = (List<ShootingItem>)rqDao.queryNamedHQL(
				"sumOfShootingItem", 
				new String[]{"policeId", "startTime", "endTime"}, 
				new Object[]{policeId, startDate, endDate},
				new ResultTransformer() {
					@Override
					public Object transformTuple(
							Object[] tuples, 
							String[] aliases) {
						DocumentCate cate = (DocumentCate)tuples[0];
						Long count = (Long)tuples[1];
						Long size = (Long)tuples[2];
						Long duration = (Long)tuples[3];
						//
						ShootingItem bean =  new ShootingItem();
						bean.setCate(cate);
						bean.setCount(count.intValue());
						bean.setDuration(duration);
						bean.setSize(size);
						return bean;
					}

					@SuppressWarnings("rawtypes")
					@Override
					public List transformList(List collection) {
						return collection;
					}
				} 
				);
		// 
		if(items.size()==0)
			return null;
		// 组装拍摄统计
		ShootingSummary sum = new ShootingSummary();
		for(ShootingItem item : items) {
			// 音频
			if(item.getCate()==DocumentCate.AUDIO) {
				audioShootingSum(sum, item);
			}
			// 视频
			if(item.getCate()==DocumentCate.VEDIO) {
				vedioShootingSum(sum, item);
			}
			// 图片
			if(item.getCate()==DocumentCate.PIC) {
				picShootingSum(sum, item);
			}
			// 日志
			if(item.getCate()==DocumentCate.TXT) {
				logShootingSum(sum, item);
			}			
		}
		return sum;
	}

	@Override
	public Cycle getCurCycle() {
		return cc.getCurrentCycle();
	}

	@Override
	public Cycle getCycle(Date cycleDate, CycleSelector selector) {
		Cycle cycle = cc.fromDate(cycleDate);
		if(selector.equals(CycleSelector.PREV))
			return cc.prevCycle(cycle);
		else if(selector.equals(CycleSelector.NEXT))
			return cc.nextCycle(cycle);
		else
			return cycle;
	}

	// 音频统计项
	private void audioShootingSum(ShootingSummary sum, ShootingItem item) {
		sum.setCountOfAudio(item.getCount());
		sum.setLengthOfAudio(item.getDuration());
		sum.setSizeOfAudio(item.getSize());

	}

	// 视频统计项
	private void vedioShootingSum(ShootingSummary sum, ShootingItem item) {
		sum.setCountOfVedio(item.getCount());
		sum.setLengthOfVedio(item.getDuration());
		sum.setSizeOfVedio(item.getSize());

	}

	// 图片统计项
	private void picShootingSum(ShootingSummary sum, ShootingItem item) {
		sum.setCountOfPic(item.getCount());
		sum.setSizeOfPic(item.getSize());

	}

	// 日志统计项
	private void logShootingSum(ShootingSummary sum, ShootingItem item) {
		sum.setCountOfLog(item.getCount());
		sum.setSizeOfLog(item.getSize());
	}

	private Cycle getQueryCycle(Date cycleDate, CycleSelector selector) {
		Cycle cycle = cc.fromDate(cycleDate);
		if(selector==null)
			return cycle;
		if(selector.equals(CycleSelector.PREV))
			cycle = cc.prevCycle(cycle);
		else if(selector.equals(CycleSelector.NEXT))
			cycle = cc.nextCycle(cycle);
		return cycle;
	}
	@Override
	@SuppressWarnings("unchecked")
	public Page<AssessmentWorkStatisticsBean> searchWorkStatisticsBean(Page<AssessmentWorkStatisticsBean> page,
			String[] paramNames, Object[] values) {
		page =  rqDao.pageQueryNamedSQL(page, "searchWorkStatisticsBeanQuery", paramNames, values);
		return page;
	}
	public CycleConfiguration getCc() {
		return cc;
	}

	public void setCc(CycleConfiguration cc) {
		this.cc = cc;
	}

	public ShootingAssessmentReportDao getReportDao() {
		return reportDao;
	}

	public void setReportDao(ShootingAssessmentReportDao reportDao) {
		this.reportDao = reportDao;
	}

	public ReportQueryDao getRqDao() {
		return rqDao;
	}

	public void setRqDao(ReportQueryDao rqDao) {
		this.rqDao = rqDao;
	}
/*
	public JobService getJobService() {
		return jobService;
	}

	public void setJobService(JobService jobService) {
		this.jobService = jobService;
	}

	public SchedulerService getSchService() {
		return schService;
	}

	public void setSchService(SchedulerService schService) {
		this.schService = schService;
	}
*/
	public DocumentService getDocService() {
		return docService;
	}

	public void setDocService(DocumentService docService) {
		this.docService = docService;
	}

	public EmployeesService getEmpService() {
		return empService;
	}

	public void setEmpService(EmployeesService empService) {
		this.empService = empService;
	}

	public ShootingAssessmentDao getSaDao() {
		return saDao;
	}

	public void setSaDao(ShootingAssessmentDao saDao) {
		this.saDao = saDao;
	}

	public Logger getLog() {
		return log;
	}

	public void setLog(Logger log) {
		this.log = log;
	}


}
