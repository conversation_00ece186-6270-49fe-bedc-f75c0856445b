package com.jsdz.digitalevidence.analysis.bean;

import java.io.Serializable;

/**
 * 
 * @类名: BriefReport
 * @说明: 系统简报
 *
 * @author: kenny
 * @Date	2018年3月27日下午5:02:30
 * 修改记录：
 *
 * @see
 */

public class BriefReport implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4200061876616003538L;
	private Long id;
	private String orgName;//单位名称
	private Long fileCount;//文件数量
	private Long assessmentCount;//考核监督数量
	private Double assessmentRate;//考核率
	private Long signCount;//标注数量
	private Double signRate;//标注率
	private Long alarmCount;//警情关联数量
	private Double alarmRate;//警情关联率
	private Long caseCount;//案件关联数量
	private Double caseRate;//案件关联率
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public Long getFileCount() {
		return fileCount;
	}
	public void setFileCount(Long fileCount) {
		this.fileCount = fileCount;
	}
	public Long getAssessmentCount() {
		return assessmentCount;
	}
	public void setAssessmentCount(Long assessmentCount) {
		this.assessmentCount = assessmentCount;
	}
	public Double getAssessmentRate() {
		return assessmentRate;
	}
	public void setAssessmentRate(Double assessmentRate) {
		this.assessmentRate = assessmentRate;
	}
	public Long getSignCount() {
		return signCount;
	}
	public void setSignCount(Long signCount) {
		this.signCount = signCount;
	}
	public Double getSignRate() {
		return signRate;
	}
	public void setSignRate(Double signRate) {
		this.signRate = signRate;
	}
	public Long getAlarmCount() {
		return alarmCount;
	}
	public void setAlarmCount(Long alarmCount) {
		this.alarmCount = alarmCount;
	}
	public Double getAlarmRate() {
		return alarmRate;
	}
	public void setAlarmRate(Double alarmRate) {
		this.alarmRate = alarmRate;
	}
	public Long getCaseCount() {
		return caseCount;
	}
	public void setCaseCount(Long caseCount) {
		this.caseCount = caseCount;
	}
	public Double getCaseRate() {
		return caseRate;
	}
	public void setCaseRate(Double caseRate) {
		this.caseRate = caseRate;
	}
	
	

}
