package TestService;

import java.util.Date;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.jsdz.admin.org.model.Department;
import com.jsdz.admin.org.service.DepartmentService;
import com.jsdz.core.AjaxResult;

import net.sf.json.JSONArray;

/**
 * 
 * @类名: TestDepartment.java
 * @说明: 
 *
 * @author: kenny
 * @Date	2017年5月10日下午3:15:57
 * 修改记录：
 *
 * @see
 */
@RunWith(SpringJUnit4ClassRunner.class)  
@TransactionConfiguration(transactionManager = "transactionManager", defaultRollback = false)
@ContextConfiguration(locations = { "classpath:/testApplicationContext-common.xml" })
public class TestDepartment {
	@Autowired
	private DepartmentService departmentService;
	
	@Before
	public void setUp() throws Exception {
	}
	
	@After
	public void tearDown() throws Exception {
	}

	@Test
	@Transactional
	public void testAddDepartment()
	{
		Department department = new Department();
		department.setName("财务部");
		department.setCreateTime(new Date());
		AjaxResult result = departmentService.addDepartment(department);
		

		JSONArray regionStr = JSONArray.fromObject(department);//将java对象转换为json对象
		System.out.println("【region】:\r\n" +  regionStr.toString());//将json对象转换为字符串
		JSONArray json = JSONArray.fromObject(result);//将java对象转换为json对象
		System.out.println("【结果】:\r\n" +  json.toString());//将json对象转换为字符串
	}	
}
